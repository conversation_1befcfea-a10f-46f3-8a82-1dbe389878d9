<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<!-- 定义变量 ， 文件输出格式  -->
	<springProperty scope="context" name="app.name" source="spring.application.name" defaultValue="app-service"/>
	<property name="PATTERN" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c [%L] -| %msg%n" />
    <!-- 定义变量 ，日志文件输出路径 -->
	<property name="logs.path" value="logs" />

	<!-- 输出到控制台 -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!--错误日志单独输出-->
	<appender name="FILE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
	　　　<file>${logs.path}/${app.name}-error.log</file>
		  <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		 </filter>
	　　　<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			　<!-- 文件名称 -->
			<fileNamePattern>${logs.path}/${app.name}-error.%i.log</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>10</maxIndex>
		　</rollingPolicy>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>100MB</maxFileSize>
		</triggeringPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
			<pattern>${PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>


	<!-- 按照每天和固定大小(5MB)生成日志文件【最新的日志，是没有日期没有数字的】 -->
	<appender name="FILE-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logs.path}/${app.name}.log</file>
		<append>true</append>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${logs.path}/${app.name}_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--日志文件保留天数-->
			<MaxHistory>7</MaxHistory>
			<!--日志文件最大的大小-->
			<MaxFileSize>100MB</MaxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
			<pattern>${PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>


	<!--开发环境日志输出到控制台-->
	<springProfile name="local">
		<root level="info">
			<appender-ref ref="CONSOLE" />
		</root>
	</springProfile>

	<!--开发环境日志输出到控制台-->
	<springProfile name="dev">
	    <root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
	    </root>
  	</springProfile>

	<!--外置配置文件专用-->
	<springProfile name="other">
		<root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
	</springProfile>

    <!--测试环境  -->
    <springProfile name="test">  
         <root level="info">
			 <appender-ref ref="FILE-INFO" />
			 <appender-ref ref="FILE-ERROR" />
         </root>
    </springProfile>
    
    <!-- 生产环境-->
    <springProfile name="prod">  
		<root level="info">
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
    </springProfile>


	<!--         以下为 nacos 加载时专用        -->

	<springProfile name="nacos-local">
		<root level="info">
			<appender-ref ref="CONSOLE" />
		</root>
	</springProfile>

	<!--开发环境日志输出到控制台-->
	<springProfile name="nacos-dev">
		<root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
	</springProfile>

	<!--外置配置文件专用-->
	<springProfile name="nacos-other">
		<root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
	</springProfile>

	<!--测试环境  -->
	<springProfile name="nacos-test">
		<root level="info">
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
	</springProfile>

	<!-- 生产环境-->
	<springProfile name="nacos-prod">
		<root level="info">
			<appender-ref ref="FILE-INFO" />
			<appender-ref ref="FILE-ERROR" />
		</root>
	</springProfile>

 </configuration>