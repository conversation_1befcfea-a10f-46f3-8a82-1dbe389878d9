<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.pay.mapper.PayOrderItemMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.pay.mapper.po.PayOrderItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="INTEGER"/>
        <result column="discount_price" property="discountPrice" jdbcType="INTEGER"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        order_no,
        sku_id,
        sku_name,
        price,
        discount_price,
        num,
        create_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from pay_order_item
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByOrderId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from pay_order_item
        where order_no = #{orderNo,jdbcType=VARCHAR} limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from pay_order_item
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrderItem">
        insert into pay_order_item (id, order_no, sku_id,
        sku_name, price, discount_price,
        num, create_time)
        values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{skuId,jdbcType=VARCHAR},
        #{skuName,jdbcType=VARCHAR}, #{price,jdbcType=INTEGER}, #{discountPrice,jdbcType=INTEGER},
        #{num,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrderItem">
        insert into pay_order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="discountPrice != null">
                discount_price,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=INTEGER},
            </if>
            <if test="discountPrice != null">
                #{discountPrice,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByOrderNoSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrderItem">
        update pay_order_item
        <set>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="discountPrice != null">
                discount_price = #{discountPrice,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrderItem">
        update pay_order_item
        set order_no = #{orderNo,jdbcType=VARCHAR},
        sku_id = #{skuId,jdbcType=VARCHAR},
        sku_name = #{skuName,jdbcType=VARCHAR},
        price = #{price,jdbcType=INTEGER},
        discount_price = #{discountPrice,jdbcType=INTEGER},
        num = #{num,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
