<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.pay.mapper.SysExchangeCodeMapper">

    <resultMap type="com.tydic.nbchat.pay.mapper.po.SysExchangeCode" id="SysExchangeCodeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="exCode" column="ex_code" jdbcType="VARCHAR"/>
        <result property="exNum" column="ex_num" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="exType" column="ex_type" jdbcType="VARCHAR"/>
        <result property="exStatus" column="ex_status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
        <result property="skuNum" column="sku_num" jdbcType="INTEGER"/>
        <result property="exInfo" column="ex_info" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id
        , ex_code, ex_num, start_time, end_time, ex_type, ex_status, remark, sku_id, sku_num, ex_info,
  is_valid, create_user, create_time, update_time</sql>

    <!--查询单个-->
    <select id="selectByExCode" resultMap="SysExchangeCodeMap">
        select
        <include refid="Base_Column_List"/>
        from sys_exchange_code
        where ex_code = #{exCode} and is_valid = '1'
    </select>

    <!--    查询剩余次数-->
    <select id="querySurplusNum" resultType="Integer">
        select (select ex_num from sys_exchange_code where ex_code = #{exCode})
                   -
               (select count(1) from sys_exchange_code_detail where ex_code = #{exCode})
    </select>

    <select id="queryById" resultMap="SysExchangeCodeMap">
        select
        <include refid="Base_Column_List"/>
        from sys_exchange_code
        where id = #{id}
    </select>

    <!-- 运营平台条件查询-->
    <select id="queryExchangeCode" resultMap="SysExchangeCodeMap"
            parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCodeSelectCondition">
        select
        t1.id,
        t1.sku_id,
        t1.sku_num,
        t1.ex_code,
        t1.ex_status,
        t1.start_time,
        t1.ex_type,
        (select count(*) from sys_exchange_code_detail t2 where t2.ex_code = t1.ex_code) as exchangedCount,
        t1.ex_num,
        t1.end_time,
        t1.create_user,
        t1.create_time,
        t1.ex_info,
        t1.is_valid,
        t1.update_time,
        t1.remark
        FROM
        sys_exchange_code t1
        LEFT JOIN
        sys_exchange_code_detail t2
        ON
        t1.ex_code = t2.ex_code
        <where>
            <if test="id != null and id != ''">
                and t1.id = #{id}
            </if>
            <if test="exCode != null and exCode != ''">
                and t1.ex_code = #{exCode}
            </if>
            <if test="skuId != null and skuId != ''">
                and t1.sku_id = #{skuId}
            </if>
            <if test="exPhone != null and exPhone != ''">
                and t2.ex_phone = #{exPhone}
            </if>
            <if test="exStatus != null and exStatus != ''">
                and t1.ex_status = #{exStatus}
            </if>
            <if test="createStartTime != null and createEndTime != null">
                AND t1.create_time BETWEEN #{createStartTime} AND #{createEndTime}
            </if>
            <if test="isValid != null and isValid != ''">
                and t1.is_valid = #{isValid}
            </if>
        </where>
        GROUP BY
        t1.ex_code
        ORDER BY
        t1.create_time DESC
    </select>



    <select id="selectAll" resultMap="SysExchangeCodeMap"
            parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCode">
        select
        <include refid="Base_Column_List"/>
        from sys_exchange_code
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="exCode != null and exCode != ''">
                and ex_code = #{exCode}
            </if>
            <if test="exNum != null">
                and ex_num = #{exNum}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="exType != null and exType != ''">
                and ex_type = #{exType}
            </if>
            <if test="exStatus != null and exStatus != ''">
                and ex_status = #{exStatus}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="skuId != null and skuId != ''">
                and sku_id = #{skuId}
            </if>
            <if test="skuNum != null">
                and sku_num = #{skuNum}
            </if>
            <if test="exInfo != null and exInfo != ''">
                and ex_info = #{exInfo}
            </if>
            <if test="isValid != null">
                and is_valid = #{isValid}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCode">
        insert into sys_exchange_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="exCode != null and exCode != ''">
                ex_code,
            </if>
            <if test="exNum != null">
                ex_num,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="exType != null and exType != ''">
                ex_type,
            </if>
            <if test="exStatus != null and exStatus != ''">
                ex_status,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="skuId != null and skuId != ''">
                sku_id,
            </if>
            <if test="skuNum != null">
                sku_num,
            </if>
            <if test="exInfo != null and exInfo != ''">
                ex_info,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="exCode != null and exCode != ''">
                #{exCode},
            </if>
            <if test="exNum != null">
                #{exNum},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="exType != null and exType != ''">
                #{exType},
            </if>
            <if test="exStatus != null and exStatus != ''">
                #{exStatus},
            </if>
            <if test="remark != null and remark != ''">
                #{remark},
            </if>
            <if test="skuId != null and skuId != ''">
                #{skuId},
            </if>
            <if test="skuNum != null">
                #{skuNum},
            </if>
            <if test="exInfo != null and exInfo != ''">
                #{exInfo},
            </if>
            <if test="isValid != null">
                #{isValid},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_exchange_code
        <set>
            <if test="exCode != null and exCode != ''">
                ex_code = #{exCode},
            </if>
            <if test="exNum != null">
                ex_num = #{exNum},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="exType != null and exType != ''">
                ex_type = #{exType},
            </if>
            <if test="exStatus != null and exStatus != ''">
                ex_status = #{exStatus},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="skuId != null and skuId != ''">
                sku_id = #{skuId},
            </if>
            <if test="skuNum != null">
                sku_num = #{skuNum},
            </if>
            <if test="exInfo != null and exInfo != ''">
                ex_info = #{exInfo},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid},
            </if>
            <if test="createUser != null and createUser != ''">
                create_user = #{createUser},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from sys_exchange_code
        where id = #{id}
    </delete>

</mapper>
