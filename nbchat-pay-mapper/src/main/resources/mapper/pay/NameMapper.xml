<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.pay.mapper.NameMapper">
    <select id="queryDeptId" resultType="java.lang.String">
        select
        dept_id
        from sys_dept_user_rel
        where user_id = #{userId} and tenant_code = #{tenantCode}
        limit 1
    </select>

    <select id="querySubDepts" resultType="java.lang.String">
        select
        dept_id
        from sys_dept
        where find_in_set(#{deptId}, ancestors)
    </select>

    <select id="queryDeptName" resultType="java.lang.String">
        select
        dept_name
        from sys_dept
        where dept_id = #{deptId}
        limit 1
    </select>

    <select id="queryDeptAncestors" resultType="java.lang.String">
        select
        ancestors
        from sys_dept
        where dept_id = #{deptId}
        limit 1
    </select>

    <select id="queryPostName" resultType="java.lang.String">
        select post_name
        from sys_post
        where post_Id = #{postId} limit 1
    </select>

    <select id="queryDeptPostUser" resultType="java.lang.String">
        select
        t1.user_id
        from sys_dept_user_rel t1 join sys_post_user_rel t2
        on t1.user_id = t2.user_id and t2.tenant_code = t1.tenant_code
        where
        t2.post_id = #{postId} and t2.tenant_code = #{tenantCode}
        <choose>
            <when test='supportSubDept == "1"'>
                and t1.dept_id in (select dept_id from sys_dept where find_in_set(#{deptId},ancestors))
            </when>
            <when test='supportSubDept == "0"'>
                and t1.dept_id = #{deptId}
            </when>
        </choose>
    </select>

    <select id="queryPostId" resultType="java.lang.String">
        select
        post_id
        from sys_post_user_rel
        where user_id = #{userId} and tenant_code = #{tenantCode}
    </select>

    <select id="queryTaskCount" resultType="java.lang.Integer">
        select
        count(1)
        from nbchat_train_task_course_rel
        where task_id = #{taskId}
    </select>

    <select id="queryUserName" resultType="java.lang.String">
        select
        user_reality_name
        from nbchat_sys_user_tenant
        where user_id = #{userId} and tenant_code = #{tenantCode}
    </select>

    <select id="queryUserPhone" resultType="java.lang.String">
        select
        phone
        from nbchat_user
        where user_id = #{userId} and tenant_code = #{tenantCode}
    </select>

    <select id="queryUserScore" resultType="java.lang.Integer">
        select IFNULL(FLOOR(avg(score)),0)
        from (select max(score) as score
        from nbchat_exam_test_record
        where user_id = #{userId}
        and course_id in (select course_id from nbchat_train_task_course_rel where task_id = #{taskId} and is_after_test
        = 1)
        and end_time is not null
        group by course_id) t1
    </select>

    <select id="queryUserInfo" resultType="com.alibaba.fastjson.JSONObject">
        select user_reality_name,
        avatar,
        id_card,
        birthday,
        gender
        from nbchat_sys_user_tenant
        where user_id = #{userId}
        and tenant_code = #{tenantCode}
    </select>

    <select id="queryOrganizeName" resultType="java.lang.String">
        SELECT GROUP_CONCAT(t2.dept_name order by t2.ancestors SEPARATOR '-') AS dept_names
        FROM sys_dept t1
        JOIN sys_dept t2
        ON FIND_IN_SET(t2.dept_id, t1.ancestors) > 0
        WHERE t1.dept_id = #{deptId}
    </select>

    <select id="queryTenantNameByCode" resultType="java.lang.String">
        select tenant_name
        from nbchat_sys_tenant
        where tenant_code = #{tenantCode}
    </select>

    <select id="queryVideoTmpl" resultType="java.lang.String">
        select content from tdh_video_temp where tmp_id = #{tmpId}
    </select>

    <select id="queryBackImg" resultType="com.alibaba.fastjson.JSONObject">
        select object_url from tdh_background where object_id = #{backId}
    </select>

    <select id="queryVoice" resultType="com.alibaba.fastjson.JSONObject">
        select name,type from tdh_virtual_anchor where anchor_id = #{anchorId}
    </select>

    <select id="queryTdhId" resultType="com.alibaba.fastjson.JSONObject">
        select tdh_img,tdh_type from tdh_virtual_human where tdh_id = #{tdhId}
    </select>

    <select id="queryTdhTask" resultType="String">
        select video_url from tdh_creation_task where task_id = #{taskId}
    </select>
</mapper>
