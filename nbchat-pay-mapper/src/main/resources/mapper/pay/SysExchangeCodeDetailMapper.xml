<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.pay.mapper.SysExchangeCodeDetailMapper">

    <resultMap type="com.tydic.nbchat.pay.mapper.po.SysExchangeCodeDetail" id="SysExchangeCodeDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="exCode" column="ex_code" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="exPhone" column="ex_phone" jdbcType="VARCHAR"/>
        <result property="exTime" column="ex_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, ex_code, tenant_code, user_id, ex_phone, ex_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysExchangeCodeDetailMap">
        select
          <include refid="Base_Column_List" />
        from sys_exchange_code_detail
        where id = #{id}
    </select>

    <!--根据兑换码查询兑换信息-->
    <select id="queryByExCode" resultMap="SysExchangeCodeDetailMap" parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCodeDetail">
        select
        a.*,
        u.name as userName
        from sys_exchange_code_detail a
        left join nbchat_user u on a.user_id = u.user_id
        where a.ex_code = #{exCode}
        order by ex_time desc
    </select>

    <select id="selectAll" resultMap="SysExchangeCodeDetailMap" parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCodeDetail">
        select
          <include refid="Base_Column_List" />
        from sys_exchange_code_detail
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="exCode != null and exCode != ''">
                and ex_code = #{exCode}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="exPhone != null and exPhone != ''">
                and ex_phone = #{exPhone}
            </if>
            <if test="exTime != null">
                and ex_time = #{exTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.pay.mapper.po.SysExchangeCodeDetail">
        insert into sys_exchange_code_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="exCode != null and exCode != ''">
                ex_code,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="exPhone != null and exPhone != ''">
                ex_phone,
            </if>
            <if test="exTime != null">
                ex_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id},
            </if>
            <if test="exCode != null and exCode != ''">
                #{exCode},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="exPhone != null and exPhone != ''">
                #{exPhone},
            </if>
            <if test="exTime != null">
                #{exTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_exchange_code_detail(ex_codetenant_codeuser_idex_phoneex_time)
        values (#{exCode}#{tenantCode}#{userId}#{exPhone}#{exTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_exchange_code_detail
        <set>
            <if test="exCode != null and exCode != ''">
                ex_code = #{exCode},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="exPhone != null and exPhone != ''">
                ex_phone = #{exPhone},
            </if>
            <if test="exTime != null">
                ex_time = #{exTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_exchange_code_detail where id = #{id}
    </delete>

</mapper>

