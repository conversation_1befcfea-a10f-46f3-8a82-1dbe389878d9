<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.pay.mapper.PayOrderMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.pay.mapper.po.PayOrder">
        <id column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="total_price" property="totalPrice" jdbcType="INTEGER"/>
        <result column="discount_price" property="discountPrice" jdbcType="INTEGER"/>
        <result column="order_status" property="orderStatus" jdbcType="CHAR"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="CHAR"/>
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        order_no,
        tenant_code,
        user_id,
        channel,
        total_price,
        discount_price,
        order_status,
        order_time,
        pay_time,
        pay_type,
        trade_no,
        update_time,
        is_valid
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from pay_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from pay_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrder">
        insert into pay_order (order_no, tenant_code, user_id,
        channel, total_price, discount_price,
        order_status, order_time, pay_time,
        pay_type, trade_no, update_time,
        is_valid)
        values (#{orderNo,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
        #{channel,jdbcType=VARCHAR}, #{totalPrice,jdbcType=INTEGER}, #{discountPrice,jdbcType=INTEGER},
        #{orderStatus,jdbcType=CHAR}, #{orderTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP},
        #{payType,jdbcType=CHAR}, #{tradeNo,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{isValid,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrder">
        insert into pay_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="discountPrice != null">
                discount_price,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="orderTime != null">
                order_time,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="tradeNo != null">
                trade_no,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=INTEGER},
            </if>
            <if test="discountPrice != null">
                #{discountPrice,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="orderTime != null">
                #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=CHAR},
            </if>
            <if test="tradeNo != null">
                #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByOrderNoSelective" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrder">
        update pay_order
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=INTEGER},
            </if>
            <if test="discountPrice != null">
                discount_price = #{discountPrice,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="orderTime != null">
                order_time = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=CHAR},
            </if>
            <if test="tradeNo != null">
                trade_no = #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.pay.mapper.po.PayOrder">
        update pay_order
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        channel = #{channel,jdbcType=VARCHAR},
        total_price = #{totalPrice,jdbcType=INTEGER},
        discount_price = #{discountPrice,jdbcType=INTEGER},
        order_status = #{orderStatus,jdbcType=CHAR},
        order_time = #{orderTime,jdbcType=TIMESTAMP},
        pay_time = #{payTime,jdbcType=TIMESTAMP},
        pay_type = #{payType,jdbcType=CHAR},
        trade_no = #{tradeNo,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_valid = #{isValid,jdbcType=CHAR}
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
</mapper>
