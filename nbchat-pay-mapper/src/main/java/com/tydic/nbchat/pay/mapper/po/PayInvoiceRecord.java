package com.tydic.nbchat.pay.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 发票开具记录表
 */
@Data
public class PayInvoiceRecord {
    /**
     * 主键
     */
    private String invoiceId;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 发票类型 1-个人 2-公司
     */
    private String invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 发票金额: 分
     */
    private Integer invoiceAmount;

    /**
     * 发票状态 0-待开具 1-开票中 2-已开票 3-作废
     */
    private String invoiceStatus;

    /**
     * 邮寄地址
     */
    private String invoiceAddress;

    /**
     * 联系电话
     */
    private String invoicePhone;

    /**
     * 邮箱
     */
    private String invoiceEmail;

    /**
     * 开具时间
     */
    private Date invoiceTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}