package com.tydic.nbchat.pay.mapper;

import com.tydic.nbchat.pay.mapper.po.PayGoodsSpu;

import java.util.List;

public interface PayGoodsSpuMapper {
    int deleteByPrimaryKey(String spuId);

    int insert(PayGoodsSpu record);

    int insertSelective(PayGoodsSpu record);

    PayGoodsSpu selectByPrimaryKey(String spuId);

    int updateByPrimaryKeySelective(PayGoodsSpu record);

    int updateByPrimaryKey(PayGoodsSpu record);

    List<PayGoodsSpu> selectAll(PayGoodsSpu spu);
}