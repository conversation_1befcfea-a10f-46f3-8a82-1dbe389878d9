package com.tydic.nbchat.pay.mapper;

import com.tydic.nbchat.pay.mapper.po.PayRebateRecord;

import java.util.List;

/**
 * 支付订单使用积分优惠券记录表(PayRebateRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-27 16:15:10
 */
public interface PayRebateRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PayRebateRecord queryById(Long id);

    List<PayRebateRecord> selectAll(PayRebateRecord payRebateRecord);

    /**
     * 新增数据
     *
     * @param payRebateRecord 实例对象
     * @return 影响行数
     */
    int insert(PayRebateRecord payRebateRecord);


    int insertSelective(PayRebateRecord payRebateRecord);

      /**
     * 修改数据
     *
     * @param payRebateRecord 实例对象
     * @return 影响行数
     */
    int update(PayRebateRecord payRebateRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

