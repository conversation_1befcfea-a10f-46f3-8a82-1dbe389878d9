package com.tydic.nbchat.pay.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface NameMapper {

    List<String> querySubDepts(@Param("deptId") String deptId);

    String queryPostName(@Param("postId") String postId);

    String queryDeptName(@Param("deptId") String deptId);

    String queryDeptAncestors(@Param("deptId") String deptId);

    String queryDeptId(@Param("userId") String userId, @Param("tenantCode") String tenantCode);

    List<String> queryPostId(@Param("userId") String userId, @Param("tenantCode") String tenantCode);

    Set<String> queryDeptPostUser(@Param("deptId") String deptId, @Param("supportSubDept") String supportSubDept,
                                  @Param("postId") String postId, @Param("tenantCode") String tenantCode);

    int queryTaskCount(Integer taskId);

    String queryUserName(@Param("userId") String userId, @Param("tenantCode") String tenantCode);

    String queryUserPhone(@Param("userId") String userId, @Param("tenantCode") String tenantCode);

    Integer queryUserScore(@Param("userId") String userId, @Param("taskId") String taskId);

    JSONObject queryUserInfo(@Param("userId") String userId, @Param("tenantCode") String tenantCode);

    String queryOrganizeName(String deptId);

    String queryTenantNameByCode(@Param("tenantCode") String tenantCode);

    String queryVideoTmpl(String tmpId);

    JSONObject queryBackImg(@Param("backId") String backId);

    JSONObject queryVoice(@Param("anchorId") String anchorId);

    JSONObject queryTdhId(@Param("tdhId") String tdhId);

    String queryTdhTask(@Param("taskId") String taskId);

}
