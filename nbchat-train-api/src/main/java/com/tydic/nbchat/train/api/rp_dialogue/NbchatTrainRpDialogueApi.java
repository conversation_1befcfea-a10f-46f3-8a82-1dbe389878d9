package com.tydic.nbchat.train.api.rp_dialogue;

import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public interface NbchatTrainRpDialogueApi {

    String userTotal = "userTotal";
    String totalScore = "totalScore";
    String totalTimes = "totalTimes";
    String avgScore = "avgScore";
    String level_1 = "优秀";
    String level_2 = "良好";
    String level_3 = "中等";
    String level_4 = "及格";
    String level_5 = "不及格";
    String level_1_code = "level_1";
    String level_2_code = "level_2";
    String level_3_code = "level_3";
    String level_4_code = "level_4";
    String level_5_code = "level_5";
    List<String> jsonItems = new ArrayList<>(Arrays.asList(userTotal,totalScore,totalTimes,level_1,level_2,level_3,level_4,level_5));

    Rsp countItem(QueryReportRequest request);

    Rsp circleScore(QueryReportRequest request);

    RspList analysis(QueryReportRequest request);

    RspList rank(QueryReportRequest request);
}
