package com.tydic.nbchat.train.api.bo.report.course;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainRpCoursePnSaveReqBO implements Serializable {
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 数据日期
     */
    private Date dateDay;
    /**
     * 创建时间
     */
    private Date dateTime;
    /**
     * 是否学完
     */
    private String isFinish;
}
