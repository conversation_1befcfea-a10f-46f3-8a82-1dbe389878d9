package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nbchat.train.api.bo.question.Question;
import com.tydic.nicc.dc.base.bo.Rsp;

import java.util.List;

public interface NbcahtExamQuestionApi {

    /**
     * 保存试题
     * @param saveRequest
     * @return
     */
    Rsp saveQuestion(QuestionSaveRequest saveRequest);

    /**
     * 追加试题
     * @param request
     * @return
     */
    Rsp appendQuestion(QuestionSaveRequest request);

    void consumerQuestion(QuestionSaveRequest saveRequest, Question question,List<String> items,List<Integer> answers);


}
