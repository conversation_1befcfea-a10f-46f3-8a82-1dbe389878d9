package com.tydic.nbchat.train.api.bo.examRule;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class ExamRuleBO implements Serializable {

    private int paperId;
    /**
     * 课程ID
     */
    private String courseId;
    private String tenantCode;

    private String courseName; //课程名称
    private String courseState; //课程状态
    private String catalogName; //分类名称
    /**
     * 试题数量
     */
    private Integer testNum;
    /**
     * 单选题
     */
    private Integer single;
    /**
     * 多选题
     */
    private Integer multiple;
    /**
     * 判断题
     */
    private Integer trueOrFalse;
    /**
     * 合格分数
     */
    private String passingScore;
    /**
     * 出题类型: 1 随机 2 顺序
     */
    private String testType;
    /**
     * 是否需要学完课程
     * 0 否 1 是
     */
    private String trainState;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 问题类型
     */
    private String questionType;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 结构化数据 - gpt提取的试题内容
     */
    private String content;


}
