package com.tydic.nbchat.train.api.bo.report.course;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/07/06
 * @email $EMAIL$
 * @description 课程培训——查询课程排行列表请求BO
 */
@Data
public class TrainRpCourseRankingQueryReqBO extends BasePageInfo implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 选择查询日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序
     */
    private String sort;
}
