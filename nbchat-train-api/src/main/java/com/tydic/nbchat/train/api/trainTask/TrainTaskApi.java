package com.tydic.nbchat.train.api.trainTask;

import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskConfigBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TrainTaskApi {

    String TRAIN_TASK_EXPIRED = "3";
    String TRAIN_TASK_FINISH = "2";
    String TRAIN_TASK_LEARNING = "1";
    String TRAIN_TASK_NOT_START = "0";

    /**
     * 保存学习任务
     * @param request
     * @return
     */
    Rsp save(NbchatTrainTaskConfigBO request);

    /**
     * 查询学习任务列表
     * @param request
     * @return
     */
    RspList list(NbchatTrainTaskBO request);

    Rsp info(NbchatTrainTaskBO request);

    /**
     * 查询用户学习任务
     * @param request
     * @return
     */
    RspList queryTask(NbchatTrainTaskBO request);

    /**
     * 查询不同类型的课程
     * @param request
     * @return
     */
    RspList queryTaskCourse(NbchatTrainTaskBO request);

    RspList analysis(NbchatTrainTaskBO request);

    RspList queryTaskCourseList(NbchatTrainTaskBO request);

}
