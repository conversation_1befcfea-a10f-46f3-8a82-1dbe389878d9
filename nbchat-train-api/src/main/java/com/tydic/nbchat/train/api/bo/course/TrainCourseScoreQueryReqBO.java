package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/07/20
 * @email <EMAIL>
 * @description 培训课程分数查询BO
 */
@Data
public class TrainCourseScoreQueryReqBO extends BasePageInfo implements Serializable {
    /**
     * 电话
     */
    private List<String> phone;
    /**
     * 课程id
     */
    private List<String> courseId;
    /**
     * 学员名称
     */
    private String name;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 最后学习时间
     */
    private Date lastTime;
    /**
     * 学习进度
     */
    private String learnSchedule;
    /**
     * 学员岗位
     */
    private String userPosition;
    /**
     * 租户代码
     */
    @ParamNotEmpty(message = "租户代码不能为空")
    private String tenantCode;
    /**
     * 部门id
     */
    private List<String> deptId;
    /**
     * 部门范围
     */
    private String deptScope;
}
