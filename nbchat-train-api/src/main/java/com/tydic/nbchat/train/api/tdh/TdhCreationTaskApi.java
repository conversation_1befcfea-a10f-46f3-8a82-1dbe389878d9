package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskReqBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskRspBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * @Description 数字人面板-我的作品
 * @Date 2023/8/29 17:50
 * @Created by dong_pc
 */
public interface TdhCreationTaskApi {

    //数字人面板-我的作品
    RspList<TdhCreationTaskRspBo> getCreationList(TdhCreationTaskReqBo request);

    /**
     * 查询创作任务详情
     * @param request
     * @return
     */
    Rsp<TdhCreationTaskRspBo> getCreationInfo(TdhCreationTaskReqBo request);

    Rsp delete(TdhCreationTaskReqBo request);

    Rsp update(TdhCreationTaskReqBo request);
}
