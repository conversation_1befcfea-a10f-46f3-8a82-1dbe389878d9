package com.tydic.nbchat.train.api.bo.eums;

public enum CustomizeType {

   COMMON("2d","2D"),
    WP("2.5d","2.5D"),
    _25d_MTK("2.5d_mtk","2.5d_mtk"),
    AUDIO("audio","音频");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private CustomizeType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (CustomizeType field : CustomizeType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

    public static String getCodeByName(String name){
        for (CustomizeType field : CustomizeType.values()){
            if(field.name.equals(name)){
                return field.code;
            }
        }
        return "";
    }

    public static Boolean isExistCode(String code){
        for (CustomizeType field : CustomizeType.values()){
            if(field.code.equals(code)){
                return true;
            }
        }
        return false;
    }
}
