package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TdhPipQueryReqBO extends BasePageInfo implements Serializable {
    private String sourceType = "0"; //0数字人 1ppt

    private String pipId;
    private String firstFrame;

    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 类别
     */
    private String category;
    /**
     * 名称
     */
    private String name;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 类型 png/jpg/mp4
     */
    private String pipType;
    /**
     * 描述
     */
    private String pipDesc;
    /**
     * 资源链接
     */
    private String pipUrl;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 创建时间
     */
    private Date createTime;
    private List<TdhPipUrlBO> urlList;
    /**
     * 文件类型
     */
    private String fileType;
}
