package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBatchSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainCatalogApi {
    Rsp save(TrainCatalogBO request);
    Rsp saveBatch(TrainCatalogBatchSaveBO request);

    Rsp delete(TrainCatalogBO request);

    RspList query(TrainCatalogBO request);

}
