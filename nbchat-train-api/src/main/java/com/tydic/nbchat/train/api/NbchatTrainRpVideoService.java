package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.video.NbchatVideoQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainRpVideoService {
    /**
     * 查询视频制作数量
     * @return
     */
    Rsp getVideoCount(NbchatVideoQueryReqBO reqBO);

    /**
     * 查询视频列表
     * @param reqBO
     * @return
     */
    RspList getVideoList(NbchatVideoQueryReqBO reqBO);
}
