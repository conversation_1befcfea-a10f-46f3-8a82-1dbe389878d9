package com.tydic.nbchat.train.api.bo.asr_tts.modelscope;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceResult;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TtsResponseData implements Serializable {
    private String fid;
    private String path;
    private String name;
    private String url;
    public String getName(){
        if (StringUtils.isBlank(name)) {
            return fid + ".wav";
        }
        return name;
    }

    private List<TtsVoiceResult> sentences;
}
