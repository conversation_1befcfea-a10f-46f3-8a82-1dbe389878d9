package com.tydic.nbchat.train.api.ppt;

import com.tydic.nbchat.train.api.bo.ppt.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface PPTApi {

    Rsp save(PPTCreationRecordBO request);

    Rsp query(PPTCreationRecordBO request);


    Rsp layoutQuery(PPTLayoutBO request);

    RspList themeQuery(PPTThemeBO request);

    Rsp themeSave(PPTThemeReqBO request);

    RspList imageConversion(ImageConversionReqBO reqBO);

    /**
     * ppt创作记录查询
     * @param request
     * @return
     */
    RspList queryCreateHistory(PPTCreationRecordBO request);

    /**
     * 模板的上下架
     * @param request
     * @return
     */
    Rsp releaseTemplate(PPTThemeReqBO request);

    /**
     * ppt创作记录复制
     * @param request
     * @return
     */
    Rsp copyHistory(PPTCreationRecordBO request);

    /**
     * 删除主题模板
     * @param request
     * @return
     */
    Rsp delete(PPTCreationRecordBO request);


    /**
     * 复制主题模板
     * @param request
     * @return
     */
    Rsp copyTheme(PPTCreationRecordBO request);

    /**
     * ppt排序
     * @param request
     * @return
     */
    Rsp sort(PPTSortReqBO request);

    /**
     * 匹配主题模板
     * @param request
     * @return
     */
    Rsp matchTheme(PPTThemeMatchReqBO request);

    /**
     * 主题模板列表
     * @param request
     * @return
     */
    RspList themeAdminList(PPTThemeBO request);

    /**
     * 主题模板详情
     * @param request
     * @return
     */
    Rsp themeInfo(PPTThemeBO request);

    /**
     * 查询收藏主题模板
     * @param request
     * @return
     */
    RspList themeStarList(PPTThemeBO request);
}
