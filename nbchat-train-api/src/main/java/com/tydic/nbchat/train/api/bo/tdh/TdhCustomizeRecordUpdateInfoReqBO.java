package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhCustomizeRecordUpdateInfoReqBO  extends BaseInfo implements Serializable {

    /**
     * 用户id
     */
    @ParamNotEmpty(message = "用户id不能为空")
    private String userId;

    /**
     * 数字人名称
     */
    private String tdhName;

    /**
     * 发音人名称
     */
    private String voiceName;

    /**
     * 定制类型 2d/2.5d/audio
     */
    @ParamNotEmpty(message = "定制类型不能为空")
    private String customizeType;

    /**
     * 订单id
     */
    @ParamNotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 是否有效 0:删除 1:有效
     */
    private String isValid;
}
