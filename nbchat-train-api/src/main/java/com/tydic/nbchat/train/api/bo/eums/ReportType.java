package com.tydic.nbchat.train.api.bo.eums;


/**
 * <AUTHOR>
 * @date 2023/08/17
 * @email <EMAIL>
 * @description 报表类型
 */
public enum ReportType {
    COURSE("course","课程报表"),
    DIALOGUE("dialogue","场景实践报表"),
    EXAM("exam","测评报表"),
    STUDENTS("students","学员报表");

    private String code;
    private String name;

    ReportType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
