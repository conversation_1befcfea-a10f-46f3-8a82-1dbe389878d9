package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordUpdateInfoReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhCustomizeRecordApi {

    /**
     * 定制数据订单列表查询 user
     * @param reqBO
     * @return
     */
    RspList orderList(TdhCustomizeRecordQueryReqBO reqBO);

    /**
     * 定制数据订单明细查询 user
     * @param reqBO
     * @return
     */
    Rsp orderInfo(TdhCustomizeRecordQueryReqBO reqBO);

    /**
     * 定制数据订单信息更新 user
     * @param request
     * @return
     */
    Rsp updateInfo(TdhCustomizeRecordUpdateInfoReqBO request);

    /**
     * 定制数据订单首次查看时间
     * @param request
     * @return
     */
    Rsp updateFirstViewTime(TdhCustomizeRecordQueryReqBO request);
}
