package com.tydic.nbchat.train.api.rp_course;


import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.course.*;
import com.tydic.nbchat.train.api.bo.report.dialogue.TrainRpDialogueCountItemRspBO;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 课程报表统计
 */
public interface NbcahtTrainRpCourseCountApi {

    /**
     * 统计课程学习指标
     * @param itemReqBO
     * @return
     */
    Rsp<TrainRpCourseCountItemRspBO> countCourseStudyItem(TrainRpCourseCountItemReqBO itemReqBO);

    /**
     * 获取测评人次/获取测评人数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp getEvaluationCount(TrainRpExamStudentsQueryReqBO reqBO);

    /**
     * 查询新增学员数/查询学员总数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp getNewStudentsCount(TrainRpStudentsQueryReqBO reqBO);

    /**
     * 查询分析图表数据
     * @param dataAnalysisReqBO
     * @return
     */
    RspList<TrainRpCourseDataAnalysisRspBO> getCourseItemAnalysisDataList(TrainRpCourseDataAnalysisReqBO dataAnalysisReqBO);


    /**
     * 统计场景实践指标
     * @param request
     * @return
     */
    Rsp<TrainRpDialogueCountItemRspBO> countDialogueItem(TrainRpCourseCountItemReqBO request);

    TrainRpCourseDataAnalysisRspBO calGraph(QueryReportRequest request, RpCourseItemType itemType);

    }
