package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogQueryReqBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.section.SectionsSaveRequest;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionQueryRspBO;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionsBO;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainSectionApi {

    /**
     * 增加学习数
     * @param reqBO
     * @return
     */
    Rsp addTrainCount(TrainCatalogQueryReqBO reqBO);


    Rsp saveCatalog(TrainCatalogBO reqBO);


    /**
     * 目录查询
     * @param reqBO
     * @return
     */
    RspList<TrainCatalogBO> getSectionCatalogs(TrainCatalogQueryReqBO reqBO);


    /**
     * 章节内容查询
     * @param reqBO
     * @return
     */
    Rsp<TrainSectionQueryRspBO> getSectionContents(TrainCatalogQueryReqBO reqBO);

    /**
     * 保存章节内容
     * @param reqBO
     * @return
     */
    Rsp saveSection(SectionsSaveRequest reqBO);

    /**
     * 查询章节内容
     * @param sectionId
     * @return
     */
    Rsp<String> querySection(String sectionId);

    /**
     * 查询课程下章节内容
     * @param courseId
     * @return
     */
    RspList<TrainSectionsBO> queryCourse(String courseId);

    Rsp delete(String courseId);

}
