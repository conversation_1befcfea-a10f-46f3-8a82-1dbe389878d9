package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhForegroundAdminReqBO extends BaseInfo implements Serializable {
    private String sourceType = "0"; //0数字人 1ppt
    private String objectId;
    private String name;
    private String category;
    private String objectUrl;
    private String objectType;
    private String tag;
    private Integer sloop;
    private String objectConfig;
    private String thumbnail;
    private Integer duration;
    private String previewUrl;
    private Integer orderIndex;
    private String userId;
    private String objectSource;
    private String isValid;
    /**
     * 全局 "public"
     * 私有 填写下拉框里的tenantCode
     */
    private String targetTenant;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    /**
     * 0 下架 1 上架
     */
    private String tpState;
    /**
     * 音频地址
     */
    private String audioUrl;
    /**
     * 音频名称
     */
    private String audioName;
    /**
     * 应用场景
     */
    private String scene;

    /**
     * 风格
     */
    private String style;

    /**
     * 配色
     */
    private String color;
}
