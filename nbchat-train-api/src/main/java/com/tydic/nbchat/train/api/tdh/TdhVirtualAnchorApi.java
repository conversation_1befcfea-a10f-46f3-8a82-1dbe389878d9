package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualAnchorQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualAnchorQueryRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 数字人：虚拟主播
 */
public interface TdhVirtualAnchorApi {

    /**
     * 查询虚拟主播列表
     * @param reqBO
     * @return
     */
    RspList<TdhVirtualAnchorQueryRspBO> getVirtualAnchorList(TdhVirtualAnchorQueryReqBO reqBO);

    /**
     * 更新情感
     * @param reqBO
     * @return
     */
    Rsp updateEmotion(TdhVirtualAnchorQueryReqBO reqBO);

}
