package com.tydic.nbchat.train.api.outer;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OuterResourceBO implements Serializable {
    private String requestId;
    private String tenantCode;
    private String userId;
    private String bizId;
    private String bizCode;
    private String sourceUrl;
    private String status;
    private Date createTime;
    private Date updateTime;
    private String resultUrl;
    private String sourceContent;
    private String resultContent;
    //缩略图
    private String thumbUrl;
    //草稿时间
    private Date lastTime;
    //视频任务
    private TdhCreationTaskBO tdhTaskResult;
}
