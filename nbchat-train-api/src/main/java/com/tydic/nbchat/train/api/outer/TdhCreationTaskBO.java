package com.tydic.nbchat.train.api.outer;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhCreationTaskBO implements Serializable {
    private String taskId;
    /**
     * 创作id
     */
    private String creationId;

    private String creationName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 总视频段
     */
    private Integer partCountTotal;
    /**
     * 完成视频段
     */
    private Integer partCountDone;
    /**
     * 视频下载地址
     */
    private String videoUrl;
    /**
     * 预览地址
     */
    private String playUrl;
    /**
     * 0 生成中  1 任务完成 2 任务异常
     */
    private String taskState;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误描述
     */
    private String errorDesc;
}
