package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhIndexHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateSortReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhIndexTemplateApi {
    /**
     * 查询索引模板列表
     * @param request 请求参数
     * @return 索引模板列表
     */
    RspList<TdhIndexTemplateQueryRspBO> list(TdhIndexTemplateQueryReqBO request);

    /**
     * 查询索引模板详情
     * @param reqBO 请求参数
     * @return 索引模板详情
     */
    Rsp info(TdhIndexTemplateQueryReqBO reqBO);

    /**
     * 管理员查询索引模板列表
     * @param request 请求参数
     * @return 索引模板列表
     */
    RspList<TdhIndexTemplateQueryRspBO> adminList(TdhIndexTemplateQueryReqBO request);

    /**
     * 新增首页模板（管理员）
     * @param reqBO 请求参数
     * @return 操作结果
     */
    Rsp addTemplate(TdhIndexTemplateQueryReqBO reqBO);

    /**
     * 更新首页模板（管理员）
     * @param reqBO 请求参数
     * @return 操作结果
     */
    Rsp updateTemplate(TdhIndexTemplateQueryReqBO reqBO);

    /**
     * 删除首页模板（管理员）
     * @param tplId 模板ID
     * @return 操作结果
     */
    Rsp deleteTemplate(String tplId);

    /**
     * 首页模板排序（管理员）
     * @param reqBO 排序请求参数
     * @return 操作结果
     */
    Rsp sortTemplates(TdhIndexTemplateSortReqBO reqBO);

    /**
     * 首页模板上下架（管理员）
     * @param tplId 模板ID
     * @param status 状态：0-下架，1-上架
     * @return 操作结果
     */
    Rsp updateTemplateStatus(String tplId, String status);

    /**
     * 查询首页数字人列表
     * @param request 请求参数
     * @return 首页数字人列表
     */
    RspList<TdhIndexHumanQueryRspBO> getIndexHumans(TdhIndexTemplateQueryReqBO request);
}
