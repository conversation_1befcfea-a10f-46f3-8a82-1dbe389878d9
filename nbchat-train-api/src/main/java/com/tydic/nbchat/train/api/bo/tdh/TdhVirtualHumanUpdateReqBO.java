package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.util.Date;

@Data
public class TdhVirtualHumanUpdateReqBO extends BasePageInfo implements java.io.Serializable {

    /**
     * 虚拟人id
     */
    private String tdhId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 男/女
     */
    private String gender;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 虚拟人图片地址
     */
    private String tdhImg;
    private String tdhImgThu;
    private String tdhType;
    /**
     * 名称
     */
    private String tdhName;
    /**
     * 标签 [{name:’’,color:’’}]
     */
    private String tdhTags;
    /**
     * 1 全身 2 半身 3 大半身 4 坐姿
     */
    private String poseType;
    /**
     * 权限范围   0 系统内置  1 自定义
     */
    private String tdhSource;
    /**
     * 指定租户
     */
    private String specifyTenantCode;
    /**
     * 排序
     */
    private Integer orderIndex;
    /**
     * 1 正常 0 已删除
     */
    private String isValid;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    /**
     * 绑定音色
     */
    private String targetVoice;

}
