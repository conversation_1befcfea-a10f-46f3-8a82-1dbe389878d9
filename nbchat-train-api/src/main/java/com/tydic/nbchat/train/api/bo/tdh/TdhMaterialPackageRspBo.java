package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhMaterialPackageRspBo implements Serializable {
    private String pkgId;

    /**
     * 素材包名称
     */
    private String pkgName;

    /**
     * 素材包类型：1-图片，2-视频
     */
    private String pkgType;

    /**
     * 素材包描述
     */
    private String pkgDesc;

    /**
     * 素材包封面
     */
    private String pkgCover;

    /**
     * 素材包地址
     */
    private String pkgUrl;

    /**
     * 分类1
     */
    private String category1;

    /**
     * 分类2
     */
    private String category2;

    /**
     * 核心关键词
     */
    private String keywords;

    /**
     * 色调
     */
    private String color;

    /**
     * 颜色集合
     */
    private String colorSet;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否有效：0-无效，1-有效
     */
    private String isValid;
    /**
     * 状态：0—下架，1—上架
     */
    private String status;
}
