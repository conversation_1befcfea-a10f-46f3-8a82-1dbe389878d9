package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhVirtualAnchorQueryRspBO implements Serializable {

    private String isExpire = "0"; //1过期 0未过期
    private String orderStatus;

    /**
     * 定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
     */
    private String customizeStatus;
    private String orderNo;

    private String emotion;//情感

    private String anchorId;

    private String userId;

    private String category;

    private String tenantCode;

    private String name;

    private String gender;

    private String type;

    private String ageGroup;

    private String voice;

    private String style;

    private String language;

    private String languageType;

    private String price;

    private String label;
    private String anchorConfig;
    private String anchorSource;
    private String imgAvatar;
    private String demoUrl;

    private Date createTime;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    //参数配置
    private String audioConfig;
}
