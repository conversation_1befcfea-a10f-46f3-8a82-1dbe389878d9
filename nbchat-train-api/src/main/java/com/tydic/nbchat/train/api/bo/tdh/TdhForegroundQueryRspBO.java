package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhForegroundQueryRspBO implements Serializable {
    private String objectId;
    private String tenantCode;
    private String name;
    private String category;
    private String userId;

    private String objectName;

    private String objectUrl;
    private String thumbnail;
    private Integer duration;
    private String objectType;
    private String objectConfig;
    private String previewUrl;

    private Date createTime;

    private String objectSource;

    private Short orderIndex;

    private String isValid;

    private String tag;

    private Integer sloop;

    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    /**
     * 0 下架 1 上架
     */
    private String tpState;
    /**
     * 音频地址
     */
    private String audioUrl;
    /**
     * 音频名称
     */
    private String audioName;

    /**
     * 应用场景
     */
    private String scene;

    /**
     * 风格
     */
    private String style;

    /**
     * 配色
     */
    private String color;

}
