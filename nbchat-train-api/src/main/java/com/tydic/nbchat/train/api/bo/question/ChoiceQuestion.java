package com.tydic.nbchat.train.api.bo.question;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 判断
 * 选择题/单选|多选：
 *
 *  {
 *   "question":"$YOUR_QUESTION_HERE",
 *   "answers":[$YOUR_ANSWER_INDEXS...]  //答案序号,多选可能有多个
 *    "items":[
 *       "$YOUR_ANSWER_ITEM_A","$YOUR_ANSWER_ITEM_B","$YOUR_ANSWER_ITEM_C","$YOUR_ANSWER_ITEM_D"
 *    ]
 *  }
 */
@Data
public class ChoiceQuestion extends Question implements Serializable {
    private List<Integer> answers;
    private List<String> items;
}
