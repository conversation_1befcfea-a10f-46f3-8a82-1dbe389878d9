package com.tydic.nbchat.train.api.bo.dialogue.manage;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueManageReqBO extends BasePageInfo implements Serializable {
    private List<String> courseTypes = Collections.singletonList("1"); //1-普通课程 2-训战课程 3-考试课程

    private String tenantCode;
    private String userId;
    private String courseId;
    private String courseName;
    private String category;
    private String category2;
    private String sceneState;
    private String keyword;
    private String deptId; //归属部门
    private String deptScope; //0 本部门 1 本部门及下级部门
}
