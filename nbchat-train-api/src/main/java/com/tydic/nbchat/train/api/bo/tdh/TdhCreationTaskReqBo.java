package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人-视频生成任务记录(TdhCreationTask)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:53
 */
@Data
public class TdhCreationTaskReqBo extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 480657393124075872L;
    private String userId;
    private String courseId;
    private String sectionId;
    private String tenantCode;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 创作id
     */
    private String creationId;
    
    private String creationName;
    /**
     * 保留创作镜像
     */
    private String creationContent;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 总视频段
     */
    private Integer partCountTotal;
    /**
     * 完成视频段
     */
    private Integer partCountDone;
    /**
     * 视频下载地址
     */
    private String videoUrl;
    /**
     * 0 生成中  1 任务完成 2 任务异常
     */
    private String taskState;
    /**
     * 0 待审核 1 审核通过 2 审核不通过
     */
    private String verifyState;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误描述
     */
    private String errorDesc;
    /**
     * 0个人 1已共享
     */
    private String isShare;
    //指定uid和租户
    private String targetUid;
    private String targetTenant;
    /**
     * 水印;0-无/1-有
     */
    private String waterMark;
    /**
     * 原始文件地址
     */
    private String fileUrl;
}

