package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhForegroundAdminReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhForegroundQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhForegroundQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhForegroundSortReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 数字人：前景管理相关接口
 */
public interface TdhForegroundApi {

    /**
     * 查询前景列表
     * @param reqBO
     * @return
     */
    RspList<TdhForegroundQueryRspBO> getForegroundList(TdhForegroundQueryReqBO reqBO);

    /**
     * 保存前景
     * @param reqBO
     * @return
     */
    RspList save(TdhForegroundQueryReqBO reqBO);
    /**
     * 管理员查询片头片尾、文字模版配置列表
     * @param reqBO
     * @return
     */
    RspList<TdhForegroundQueryRspBO> getForegroundAdminList(TdhForegroundQueryReqBO reqBO);
    /**
     * 保存
     * @param reqBO
     * @return
     */
    RspList saveAdmin(TdhForegroundAdminReqBO reqBO);

    /**
     * 排序
     * @param reqBO
     * @returne
     */
    Rsp sort(TdhForegroundSortReqBO reqBO);
    /**
     * 排序
     * @param reqBO
     * @returne
     */
    Rsp getForeground(TdhForegroundQueryReqBO reqBO);
}
