package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaBO;
import com.tydic.nbchat.train.api.bo.video.VideoExamSubmitReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainVideoQaApi {

    /**
     * 视频-添加试题
     * @param request
     * @return
     */
    Rsp add(NbchatVideoQaBO request);

    /**
     * 视频-查询添加的试题
     * @param request
     * @return
     */
    RspList query(NbchatVideoQaBO request);

    /**
     * 视频-提交答案
     * @param request
     * @return
     */
    Rsp submit(VideoExamSubmitReqBO request);
}
