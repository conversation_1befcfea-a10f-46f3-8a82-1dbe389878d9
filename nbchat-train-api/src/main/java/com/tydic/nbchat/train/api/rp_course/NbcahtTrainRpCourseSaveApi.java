package com.tydic.nbchat.train.api.rp_course;

import com.tydic.nbchat.train.api.bo.report.course.TrainRpCoursePnSaveReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCoursePtSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;

/**
 * 课程报表记录
 */
public interface NbcahtTrainRpCourseSaveApi {

    /**
     * 学习人数明细表记录
     * @param pnSaveReqBO
     * @return
     */
    Rsp saveRpCoursePnRecord(TrainRpCoursePnSaveReqBO pnSaveReqBO);

    /**
     * 学习人次明细表记录
     * @param ptSaveReqBO
     * @return
     */
    Rsp saveRpCoursePtRecord(TrainRpCoursePtSaveReqBO ptSaveReqBO);
}
