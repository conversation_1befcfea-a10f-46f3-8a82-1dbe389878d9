package com.tydic.nbchat.train.api.bo.dialogue;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueBO implements Serializable {
    private String tenantCode;
    private Integer id;
    private String courseId;
    private String role;
    private Date dateTime;
    private String content;
    private String audioUrl;
    private String audioState;
}
