package com.tydic.nbchat.train.api.bo.catalog;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class TrainCourseCategoryBO implements Serializable {
    /**
     * 目录id
     */
    private String cateId;
    /**
     * 目录名称
     */
    private String cateName;
    private String cateNameNick;

    /**
     * 上级id
     */
    private String parentId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 租户
     */
    private String tenantCode;

    private String isValid;
    /**
     * 分类级别 1 2
     */
    private Integer cateLevel;
    private Integer limit;
}
