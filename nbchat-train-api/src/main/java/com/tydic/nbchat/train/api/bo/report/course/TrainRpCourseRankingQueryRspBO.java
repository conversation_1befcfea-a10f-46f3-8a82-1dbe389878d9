package com.tydic.nbchat.train.api.bo.report.course;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainRpCourseRankingQueryRspBO extends BaseInfo implements Serializable {
    /**
     * 课程ID
     */
    private String courseId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 学习人数
     */
    private Integer studyCount;
    /**
     * 完结人数
     */
    private Integer studyEndCount;
    /**
     * 完结率
     */
    private Double studyEndRate;
    private Integer topIndex;
}
