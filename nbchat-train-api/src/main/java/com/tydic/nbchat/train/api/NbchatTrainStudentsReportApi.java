package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainStudentsReportApi {
    /**
     * 查询新增学员数/查询学员总数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp getNewStudentsCount(TrainRpStudentsQueryReqBO reqBO);

    /**
     * 查询数据趋势
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    RspList getDataTrend(QueryReportRequest reqBO);

    /**
     * 数据分析
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp analyzeData(TrainRpStudentsQueryReqBO reqBO);
}
