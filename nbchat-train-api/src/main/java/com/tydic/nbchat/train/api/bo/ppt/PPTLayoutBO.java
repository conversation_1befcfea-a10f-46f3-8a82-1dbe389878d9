package com.tydic.nbchat.train.api.bo.ppt;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (PptLayout)实体类
 *
 * <AUTHOR>
 * @since 2024-01-02 17:26:31
 */
@Data
public class PPTLayoutBO implements Serializable {
    private static final long serialVersionUID = -61491612591333150L;

    /**
     * 组合类型：0主题组合 1布局组合
     */
    private String composeType;
    /**
     * id
     */
    private String layoutId;
    /**
     * 名称
     */
    private String layoutName;
    /**
     * 关联所属主题：不关联则为通用布局
     */
    private String themeId;
    /**
     * 布局类型
     * 封面页：01 目录页：02 过渡页：03 内容页：04 结束页：05
     */
    private String layoutType;
    /**
     * 0 系统内置  1 自定义
     */
    private String layoutSource;
    /**
     * 布局数量
     */
    private Integer layoutCount;
    /**
     * 布局内容
     */
    private String layoutContent;
    /**
     * 配色
     */
    private String color;
    /**
     * 分类
     */
    private String category;
    /**
     * 排序
     */
    private int sort;
    /**
     * 优先级：1~9 ，1优先级最高
     */
    private Integer orderIndex;
    /**
     * 是否自适应
     */
    private String isAdaptive;
    /**
     * 是否配图
     */
    private String isImage;
    private String imageNum;
    /**
     * 预览图
     */
    private String previewUrl;

    private String tenantCode;

    private String userId;

    private Date createTime;

    private Date updateTime;
    /**
     * 是否有效
     */
    private String isValid;
    private String isDefault;
    /**
     * 上架状态 0下架 1上架
     */
    private String themeState;
    /**
     * 会员标识；0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;

    private String targetTenant;
}

