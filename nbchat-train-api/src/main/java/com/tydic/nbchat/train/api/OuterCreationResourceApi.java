package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.outer.OuterResourceBO;
import com.tydic.nbchat.train.api.outer.OuterResourceQueryReqBO;
import com.tydic.nbchat.train.api.outer.OuterResourceSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface OuterCreationResourceApi {

    /**
     * 保存资源,返回requestId
     * @param request
     * @return
     */
    Rsp<String> saveResource(OuterResourceSaveReqBO request);

    /**
     * 获取资源
     * @param request
     * @return
     */
    Rsp<OuterResourceBO> getResource(OuterResourceQueryReqBO request);
}
