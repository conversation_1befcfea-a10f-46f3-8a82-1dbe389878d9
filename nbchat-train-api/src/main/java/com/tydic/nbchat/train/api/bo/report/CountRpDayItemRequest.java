package com.tydic.nbchat.train.api.bo.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CountRpDayItemRequest implements Serializable {
    private String itemCode;
    private Date startTime;
    private Date endTime;
    private String courseId;

}
