package com.tydic.nbchat.train.test;


import com.tydic.nbchat.train.mapper.NbchatTrainTaskDeptRelMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@SpringBootTest
public class CourseGeneTest {


    @Resource
    NbchatTrainTaskDeptRelMapper nbchatTrainTaskDeptRelMapper;

    @Test
    public void test(){
        List<String> deptIds = List.of("1","2","3");
        nbchatTrainTaskDeptRelMapper.insertBatch(1,deptIds);
    }



}
