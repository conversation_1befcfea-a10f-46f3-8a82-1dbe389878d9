package com.tydic.nbchat.train.test;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.core.helper.AliAsrTtsHelper;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskContext;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class NlsLongTtsTest {
    @Autowired
    private AliAsrTtsHelper aliAsrTtsHelper;
    @Autowired
    private NlsStrategyInvokeFactory nlsStrategyInvokeFactory;

    @Test
    public void createStrategyTts(){
        TtsVoiceTaskContext context = nlsStrategyInvokeFactory.getApi(AnchorType.VOLCENGINE.getCode()).createAudioTask("0","0",
                "<speak><emotion style=''>最后提取数据，代码如下：<break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break><break time=\"2s\"></break></emotion></speak>",
                "BV123_streaming",false);
        log.info("tts结果 :{}", JSONObject.toJSONString(context));
    }

    @Test
    public void createStrategyAsr(){
        AsrVoiceTaskContext context = nlsStrategyInvokeFactory.getApi(AnchorType.MODELSCOPE.getCode()).
                createAsrTask("0","1","https://chat-test.tydiczt.com/files/nls-sample.mp3",false);
        log.info("asr结果 :{}", JSONObject.toJSONString(context));
    }

    @Test
    public void test(){
        aliAsrTtsHelper.createAudioTask("281160881602727936","",false);
    }


    @Test
    public void createTts(){
        TtsVoiceTaskContext context = aliAsrTtsHelper.createAudioTask("1","1",
                "智能培训产品“铁锤炼老铁”, 这个名字蕴含着产品的核心功能和目标！,即通过培训来提高学员的技能和素质,使其成为更优秀的自己!","aibao",false);
        log.info("tts结果 :{}", JSONObject.toJSONString(context));
    }

    @Test
    public void createAsrTest(){
        AsrVoiceTaskContext context = aliAsrTtsHelper.createAsrTask("281160881602727936",
                "https://chat-test.tydiczt.com/files/nls-sample.mp3","",false);
        System.out.println(JSONObject.toJSONString(context));
    }

}
