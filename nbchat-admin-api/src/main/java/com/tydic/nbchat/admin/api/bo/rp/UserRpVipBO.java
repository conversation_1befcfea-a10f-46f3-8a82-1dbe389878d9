package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25 15:47
 * @description
 */
@Data
public class UserRpVipBO implements Serializable {
    private static final long serialVersionUID = -1658176452773128669L;

    /** 租户id */
    private String tenantCode;
    /** 用户id */
    private String userId;
    /** 类型 0-免费用户/1-体验会员/2-高级会员/3-专业会员 */
    private String vipType;
    /** 状态 0-已过期/1-有效 */
    private String vipStatus;
    /** 累计开通天数 */
    private Integer vipDays;
    /** 开通次数 */
    private Integer vipTimes;
    /** 累计付费金额，单位：分 */
    private Integer totalAmount;
    /** 首次开通日期 */
    private Date firstVipTime;
    /** 最终失效日期 */
    private Date lastVipTime;
}
