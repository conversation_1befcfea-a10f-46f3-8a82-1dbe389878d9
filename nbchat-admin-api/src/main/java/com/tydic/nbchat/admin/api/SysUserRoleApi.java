package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.dept.*;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SysUserRoleApi {
    /**
     * 角色列表查询
     * @param @param reqBO
     * @return @return {@link RspList }
     */
    RspList getSelectRoleList(BaseInfo reqBO);

    /**
     * 查询用户管理的租户列表
     * @param reqBO
     * @return
     */
    RspList getRoleTenants(BaseInfo reqBO);

}
