package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户增长数据查询返回BO
 */
@Data
public class RpUserGrowQueryRspBO implements Serializable {
    /**
     * 新增付费用户
     */
    private Integer newVipUser=0;
    /**
     * 新增免费用户
     */
    private Integer newFreeUser=0;
    /**
     * 累计注册人数
     */
    private Integer totalUser=0;
    /**
     * 新增注册用户
     */
    private Integer newUser=0;
    /**
     * 新注册使用人数
     */
    private Integer newUseUser=0;
    /**
     * 新注册使用率
     */
    private String newRegRate="0%";

    @Override
    public String toString() {
        return "-增长数据\n新增付费用户：" + newVipUser + "\n" +
                "新增免费用户：" + newFreeUser + "\n" +
                "累计注册人数：" + totalUser + "\n" +
                "新增注册人数：" + newUser + "\n" +
                "新注册使用人数：" + newUseUser + "\n" +
                "新注册使用率：" + newRegRate;
    }
}
