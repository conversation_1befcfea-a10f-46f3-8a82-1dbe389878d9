package com.tydic.nbchat.admin.api.bo.eum;

/**
 * 变更状态枚举
 */
public enum ChangeStatusEnum {
    NOT_CHANGED("0", "未更换"),
    CHANGED("1", "已更换");

    private final String code;
    private final String desc;

    ChangeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}