package com.tydic.nbchat.admin.api.bo.api_token;

import com.tydic.nbchat.admin.api.bo.permission.ApiConfigPermission;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysApiTokenRefreshRspBO implements Serializable {
    private Integer appId;
    private String appName;
    private String accessKey;
    private String secretKey;
    private Integer expireTime;
    private Date refreshTime;
    private String appUserId;
    private String appTenantCode;
    private List<String> subsystem;
    private ApiConfigPermission permission;
}
