package com.tydic.nbchat.admin.api.sentence;

import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ExportCommSentReqBo;
import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ImportCommSentReqBO;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * @Description: 知识导入导出
 * @version: 0.0.1
 * @Author: <PERSON>
 * @Date 2021/10/24
 **/
public interface CommSentenceBatchApi {

    /**
     * 知识列表导出功能
     *
     * @param req
     * @return
     */
    RspList exportCommSentList(ExportCommSentReqBo req);
    /**
     * 知识列表导入功能
     *
     * @param reqBO
     * @return
     */
    RspList importCommSentList(ImportCommSentReqBO reqBO) throws Exception;
}
