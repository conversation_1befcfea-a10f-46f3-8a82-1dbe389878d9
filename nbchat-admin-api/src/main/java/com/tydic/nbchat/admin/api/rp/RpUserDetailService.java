package com.tydic.nbchat.admin.api.rp;

import com.tydic.nbchat.admin.api.bo.rp.RpUserDetailBO;
import com.tydic.nbchat.admin.api.bo.rp.RpUserDetailQueryReqBO;
import com.tydic.nbchat.user.api.bo.context.UserPayEventContext;
import com.tydic.nbchat.user.api.bo.context.UserScoreChangeContext;
import com.tydic.nicc.common.nbchat.msg.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface RpUserDetailService {
    /**
     * 查询用户明细报表
     * @param queryReqBO
     * @return
     */
    RspList<RpUserDetailBO> getRpUserDetailList(RpUserDetailQueryReqBO queryReqBO);

    /**
     * 导出用户明细报表
     */
    Rsp exportRpUserDetailList(RpUserDetailQueryReqBO queryReqBO);
    /**
     * 处理登录事件
     * @param context
     */
    void handleLoginEvent(UserLoginEventContext context);

    /**
     * 处理vip变更事件
     * @param context
     */
    void handleVipChangeEvent(UserVipChangeContext context);

    /**
     * 处理创作事件
     * @param context
     */
    void handleMakeEventEvent(UserMakeEventContext context);

    /**
     * 算力点消费
     * @param context
     */
    void handleScoreChangeEvent(UserScoreChangeContext context);

    /**
     * 处理支付事件
     * @param context
     */
    void handlePayEvent(UserPayEventContext context);
}
