package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysUserMaterialReqBO extends BasePageInfo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 素材类型: 1-文档/2-音频/3-图片/4-视频
     */
    @ParamNotEmpty(message = "素材类型不能为空")
    private String type;

    /**
     * 素材地址
     */
    @ParamNotEmpty(message = "素材地址不能为空")
    private String url;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材内容
     */
    private String content;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 文档后缀
     */
    private String suffix;
}
