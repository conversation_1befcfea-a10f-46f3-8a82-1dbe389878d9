package com.tydic.nbchat.admin.api.bo.rp;

import com.tydic.nbchat.admin.api.valid.ValidFilter;
import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class RpUserDetailQueryReqBO extends BasePageInfo implements Serializable {
    private Long id;
    //手机号搜索
    private String phone;
    //用户类型 0 普通用户 1 企业用户
    private String userType;
    private String targetUid;
    private String targetTenant;
    private String companyName;
    //检索条件
    private Date regStartTime;
    private Date regEndTime;
    //注册渠道 1 用户平台自注册 2 批量导入 3 人工创建 4 邀请码 5 外部系统接入
    private String regChannel;
    //vip状态：会员状态 0 已过期 1 有效 2 未开通
    private String vipStatus;
    //vip类型：会员类型 1体验会员 2高级会员 3专业会员
    private String vipType;
    //是否付费(0 未付费/ 1 付费)，购买次数>0为付费
    private String isPay;
    private String keyword;
    private String userId;
    private Date payStartTime;
    private Date payEndTime;
    @ValidFilter
    private String videoMakeCountFilter;
    @ValidFilter
    private String pptMakeCountFilter;
    @ValidFilter
    private String examMakeCountFilter;
}
