package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysTenantQueryReqBO extends BasePageInfo implements Serializable {
    private String userId;
    private String targetTenant;
    private String tenantName;
    private Date startTime;
    private Date endTime;

    @Override
    public String toString() {
        return "SysTenantQueryReqBO{" +
                "userId='" + userId + '\'' +
                ", targetTenant='" + targetTenant + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantCode='" + getTenantCode() + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                '}';
    }
}
