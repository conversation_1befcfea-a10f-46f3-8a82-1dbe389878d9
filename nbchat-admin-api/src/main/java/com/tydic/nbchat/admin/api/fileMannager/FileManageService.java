package com.tydic.nbchat.admin.api.fileMannager;

import com.tydic.nbchat.admin.api.bo.file.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateRequest;

import java.io.IOException;


/**
 * <AUTHOR> <br>
 * @Description: 文件管理相关  <br>
 * @date 2021/5/11 7:48 下午  <br>
 * @Copyright tydic.com
 */
public interface FileManageService {

    /**
     * 分片任务完成
     * @param request
     * @return
     */
    Rsp completeMultipartUpload(ChunkUploadCompleteRequest request);

    /**
     * 创建分片任务
     * @param request
     * @return
     */
    Rsp createMultipartUpload(ChunkUploadCreateRequest request);

    /**
     * 文件上传
     * @param fileUploadReqBO
     * @return
     */
    RspList fileUpload(FileUploadReqBO fileUploadReqBO);

    /**
     * 文件上传
     * @return
     */
    RspList fileUploadRequest(FileUploadRequest request);


    /**
     * 删除文件
     * @param request
     * @return
     */
    Rsp fileDelete(FileDeleteRequest request);


    /**
     * 代理远程文件
     * @param request
     * @return
     */
    Rsp fileProxyLoad(FileRemoteLoadRequest request) throws IOException;


    /**
     * 获取文件
     * @param fileId
     * @return
     */
    Rsp<FileSaveBO> getFile(String fileId);

    /**
     * 创建预上传url
     * @param request
     * @return
     */
    Rsp createPrePutUrl(PrePutUrlCreateRequest request);

    /**
     * 上传完成
     * @param request
     * @return
     */
    Rsp createPrePutOk(PrePutUrlOkRequest request);

    /**
     * 创建下载url
     * @param request
     * @return
     */
    Rsp createGetUrl(PreDownloadUrlRequest request);
}
