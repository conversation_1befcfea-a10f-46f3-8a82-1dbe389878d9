package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 绑定用户
 */
@Data
public class SysTenantOptUserReqBO extends BaseInfo implements Serializable {
    private String userId;

    private String tenantCode;

    private List<SysTenantUserInfoBO> userInfos;
    /**
     * 租户ID
     */
    @ParamNotEmpty(message = "租户ID不能为空")
    private String targetTenant;
    //指定部门
    private String deptId;
    /**
     * 创建类型
     */
    private String joinType;
    //1 加入租户 0 移出租户
    private String isValid = EntityValidType.NORMAL.getCode();
}
