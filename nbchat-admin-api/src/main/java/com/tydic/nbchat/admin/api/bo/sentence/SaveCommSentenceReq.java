package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaveCommSentenceReq extends BasePageInfo implements Serializable {
    private String typeId;//分类ID
    @ParamNotEmpty(message = "内容不能为空")
    private String content;//知识内容

    private String sortId;//排序标识

    private String tenantCode;

    //类型 1 文本| 2 文件 | 6 富文本
    private String contentType;
    @ParamNotEmpty(message = "内容名称不能为空")
    private String contentTitle;

    @ParamNotEmpty(message = "目录分类不能为空")
    private String classes;
/*
    private Boolean admin;//是否管理员
    private String userName;//用户名称*/
}
