package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshRspBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserAccessTokenReqBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserSyncReqBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserSyncRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface SysOuterUserSyncApi {

    /**
     * 同步外部系统用户
     * @param sysUserSyncReqBO
     * @return
     */
    Rsp<OuterUserSyncRspBO> syncUser(OuterUserSyncReqBO sysUserSyncReqBO);


    /**
     * 获取用户token
     * @param reqBO
     * @return
     */
    Rsp<SysApiTokenRefreshRspBO> accessToken(OuterUserAccessTokenReqBO reqBO);
}
