package com.tydic.nbchat.admin.api.bo.sentence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryKnowledgeConRsp implements Serializable {
    private Long sentenceId;

    private Long typeId;

    private String content;

    private String contentTitle;

    //类型 1 文本| 2 文件 | 6 富文本
    private String contentType;

    private Date createTime;

    private String createUserId;

    private String createUserName;

    private Date updateTime;

    private String updateUserId;

    private String updateUserName;

}
