package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateCommSentenceTypeReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = -2180444219950783412L;
    private String tenantCode;
    @ParamNotNull(message = "目录id不能为空")
    private Short typeId;
    @ParamNotEmpty(message = "目录名称不能为空")
    private String typeName;
    @ParamNotEmpty(message = "目录分类不能为空")
    private String classes;
    private Long parentId;
    private String userId;
    private Short typeGroup;//1公共  2个人
    private Integer sortId;

    private String typeDesc = "";
    private String admin;

}
