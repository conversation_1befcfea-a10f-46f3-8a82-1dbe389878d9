package com.tydic.nbchat.admin.api.valid;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class FilterValidator implements ConstraintValidator<ValidFilter, String> {

    private static final String[] ALLOWED_OPERATORS = {">=", "<=", ">", "<", "="};

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true; // 允许为空，表示不筛选
        }

        for (String op : ALLOWED_OPERATORS) {
            if (value.startsWith(op)) {
                try {
                    Integer.parseInt(value.substring(op.length()).trim()); // 确保运算符后面是数字
                    return true;
                } catch (NumberFormatException e) {
                    return false; // 不是合法数字
                }
            }
        }

        return false;
    }
}