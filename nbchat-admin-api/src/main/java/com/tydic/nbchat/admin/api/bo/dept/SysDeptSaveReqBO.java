package com.tydic.nbchat.admin.api.bo.dept;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysDeptSaveReqBO extends BaseInfo implements Serializable {
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 上级部门
     */
    private String parentId;
    /**
     * 层级
     */
    private Integer level;
    /**
     * 部门
     */
    private String deptName;
    /**
     * 描述
     */
    private String deptDesc;
    /**
     * 租户编码
     */
    private String deptTenantCode;
    /**
     * 子系统
     */
    private String subsystem;

    private String createBy;
    private String updateBy;
    private String deptType;
    private String ancestors;
    private Integer orderIndex;
    private String leader;
    private String phone;
    private String email;
    private String status;
    /**
     * 1 正常 0 已删除
     */
    private String isValid;
}
