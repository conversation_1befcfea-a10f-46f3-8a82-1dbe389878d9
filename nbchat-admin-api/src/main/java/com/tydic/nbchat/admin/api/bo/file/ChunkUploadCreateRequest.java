package com.tydic.nbchat.admin.api.bo.file;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCompleteRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChunkUploadCreateRequest implements Serializable {
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    @ParamNotNull
    private Integer chunkCount;
    @ParamNotEmpty
    private String fileName;
    @ParamNotEmpty
    private String fileMd5;
    private String contentType;
}
