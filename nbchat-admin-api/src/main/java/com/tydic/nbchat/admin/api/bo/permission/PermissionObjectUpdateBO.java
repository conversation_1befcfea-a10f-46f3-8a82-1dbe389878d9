package com.tydic.nbchat.admin.api.bo.permission;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PermissionObjectUpdateBO implements Serializable {
    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String busiType;
    @ParamNotEmpty
    private String busiId;
    private String userName;
    private String createBy;
    //权限对象
    private List<PermissionObject> permissionObjects;

    @JsonIgnoreProperties
    public static PermissionObjectUpdateBO buildPermissionRequest(String tenantCode, String busiType,
            String busiId, String createBy, List<PermissionObject> permissionObjects) {
        PermissionObjectUpdateBO updateBO = new PermissionObjectUpdateBO();
        updateBO.setTenantCode(tenantCode);
        updateBO.setBusiType(busiType);
        updateBO.setBusiId(busiId);
        updateBO.setCreateBy(createBy);
        updateBO.setPermissionObjects(permissionObjects);
        return updateBO;
    }

}
