package com.tydic.nbchat.admin.api.rp;

import com.tydic.nbchat.admin.api.bo.rp.*;
import com.tydic.nicc.common.nbchat.msg.UserRpEventContext;
import com.tydic.nicc.dc.base.bo.Rsp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 16:41
 * @description 用户维度报表数据记录
 */
public interface UserRpApi {

    /**
     * 根据给定的请求参数查询用户维度报表详细信息。
     *
     * @param param 查询请求参数，包含租户和用户的标识信息
     * @return 包含用户详细信息的响应对象，其中封装了用户的基本信息、支付信息、会员信息以及平台使用情况等
     */
    Rsp<UserRpDetailBO> info(TenantUserReqBO param);

    /**
     * 处理用户维度报表事件。
     *
     * @param context 用户维度报表事件上下文，包含事件处理所需的信息
     */
    void handleUserRpEvent(UserRpEventContext context);

    /**
     * 在Redis中为用户维度报表指定的事件类型进行自增操作
     *
     * @param type       事件类型
     * @param tenantCode 租户代码
     * @param userId     用户ID
     */
    void rpAutoIncrement(String type, String tenantCode, String userId);

    /**
     * 执行用户维度报表的手动更新操作。
     * <p>
     * 该方法用于触发用户维度报表数据的手动更新流程，可能涉及重新计算或刷新报表中的某些数据项。
     * 具体的更新逻辑由实现类定义。
     */
    void manualUpdateRpUserPromotionInfo();

    void manualUpdateRpUserPaymentInfo(List<RpUserPaymentInfoBO> list);

    void manualUpdateRpUserTdhInfo();

    void manualUpdateRpUserPayInvoiceInfo();

    void manualUpdateRpUserActivityInfo();

    void manualUpdateRpPptVideoInfo(List<RpUserPptVideoInfoBO> list);

    void manualUpdateRpUserExamInfo();

    void manualUpdateRpHour(List<RpHourVideoCountBO> list);
}
