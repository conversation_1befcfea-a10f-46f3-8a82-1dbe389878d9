package com.tydic.nbchat.admin.api.bo.dept;

import com.tydic.nbchat.user.api.bo.user.UserPostInfo;
import com.tydic.nicc.common.bo.user.SysRole;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class SysDeptUserQueryRspBO implements Serializable {

    /**
     * id
     */
    private Integer id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 用户真实姓名
     */
    private String userRealityName;
    /**
     * 电话
     */
    private String phone;
    /**
     * 加入类型:
     * 1. 用户注册
     * 2. 批量导入
     * 3. 人工创建
     * 4. 邀请码
     */
    private String joinType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 角色
     */
    private Set<SysRole> roles;
    private String idCard;
    private Date birthday;
    private String gender;
    private String deptId;
    private String deptName;
    //岗位
    private Set<UserPostInfo> postList;
}
