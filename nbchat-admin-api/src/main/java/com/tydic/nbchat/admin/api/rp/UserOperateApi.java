package com.tydic.nbchat.admin.api.rp;

import com.tydic.nbchat.admin.api.bo.rp.UserOperateInfoBO;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.dc.base.bo.Rsp;

/**
 * <AUTHOR>
 * @date 2025/3/20 19:04
 * @description 用户操作记录Service
 */
public interface UserOperateApi {

    /**
     * 保存用户操作记录。
     *
     * @param param 用户操作信息，包括租户ID、用户ID、操作类型、业务ID和操作内容
     * @return 操作结果响应对象，包含操作状态及相关消息
     */
    Rsp<?> save(UserOperateInfoBO param);

    /**
     * 用户操作记录
     *
     * @param context 消息事件数据
     */
    void handleOperateEvent(UserOperateLogContext context);
}
