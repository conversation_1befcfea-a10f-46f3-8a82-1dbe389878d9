package com.tydic.nbchat.admin.api.bo.sentence.xlsx;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class ExportCommSentReqBo extends BaseInfo implements Serializable {
    private static final long serialVersionUID = -4413709625659053762L;
    private Long typeId;//分类ID
    private String content;//知识内容
    private List<Long> typeIdList;
    private List<Long> sentenceIdList;

    private String typeGroup;//分类所属组
    private String tenantCode;
    private String userId;
}
