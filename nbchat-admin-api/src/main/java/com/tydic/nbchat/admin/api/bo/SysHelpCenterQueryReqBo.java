package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 帮助中心查询请求参数
 */
@Data
public class SysHelpCenterQueryReqBo extends BasePageInfo implements Serializable {
    /**
     * 树ID
     */
    private String cateId;
    /**
     * id
     */
    private Integer id;
    /**
     * 分类
     */
    private String category;
    /**
     * 标题
     */
    private String title;
    /**
     * 视频链接
     */
    private String content;
    /**
     * 缩略图
     */
    private String thumbUrl;
    /**
     * 状态 0 下架 / 1 上架
     */
    private String status;
    /**
     * 业务类型
     */
    private String busiType;
    /**
     * 标签
     */
    private String tag;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 排序
     */
    private Integer orderIndex;
    /**
     * 视频时长
     */
    private Integer duration;
    /**
     * 根据id排序
     */
    private List<Integer> sortIds;

}
