package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateCommSentenceReq extends BasePageInfo implements Serializable {
    @ParamNotEmpty
    private String sentenceId;//知识ID
    @ParamNotEmpty
    private String content;//知识内容
    private String userId;//用户id
    private String tenantCode;
    private String sortId;//排序标识

    //类型 1 文本| 2 文件 | 6 富文本
    private String contentType;
    private String contentTitle;
    private String classes;
//    private String userName;//用户姓名
//    private Boolean admin;//是否管理员
}
