package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryCommSentencePageListReqBO extends BasePageInfo implements Serializable {
    private String tenantCode;//租户编码
    private Integer typeGroup;//分类所属组
    //    private String csCode;//客服编码
    private String userId;//客服编码

    private String typeId;//分类ID
    private String content;//知识内容
    private String contentTitle;//知识内容

    @ParamNotEmpty(message = "内容分类不能为空")
    private String classes;
/*
    private Integer admin;
    private Integer pageSize;//分页记录数
    private Integer pageNo;//查询页码*/

}
