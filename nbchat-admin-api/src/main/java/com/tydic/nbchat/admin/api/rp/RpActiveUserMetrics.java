package com.tydic.nbchat.admin.api.rp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活跃用户指标
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RpActiveUserMetrics {
    //活跃用户数
    private Integer activateUserCount = 0;
    //免费产品活跃用户数
    private Integer activateFreeUserCount = 0;
    //付费产品活跃数
    private Integer activateVipUserCount = 0;
    //新注册用户活跃数
    private Integer activateNewUserCount = 0;
    //用户活跃率
    //private String activateUserRate = "0.00";

    @Override
    public String toString(){
        return  "\n-活跃数据\n活跃用户数: " + activateUserCount +
                "\n免费产品活跃用户数: " + activateFreeUserCount +
                "\n付费产品活跃数: " + activateVipUserCount +
                "\n新注册用户活跃数: " + activateNewUserCount
                //"\n用户活跃率: " + activateUserRate + "\n"
                ;
    }

}
