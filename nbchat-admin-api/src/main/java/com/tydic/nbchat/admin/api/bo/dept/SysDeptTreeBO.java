package com.tydic.nbchat.admin.api.bo.dept;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysDeptTreeBO implements Serializable {
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 上级部门
     */
    private String parentId;
    /**
     * 上级部门名称
     */
    private String parentName;
    /**
     * 层级
     */
    private Integer level;
    /**
     * 部门
     */
    private String deptName;
    /**
     * 描述
     */
    private String deptDesc;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 子系统
     */
    private String subsystem;
    /**
     * 创建时间
     */
    private Date createTime;
    private String deptType;
    private String ancestors;
    private Integer orderIndex;
    private String leader;
    private String phone;
    private String email;
    private String status;    /**
     * 1 正常 0 已删除
     */
    private String isValid;
    /**
     * 子节点列表
     */
    private List<SysDeptTreeBO> children;
}
