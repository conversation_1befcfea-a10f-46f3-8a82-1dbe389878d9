package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.permission.PermissionObject;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObjectUpdateBO;
import com.tydic.nbchat.admin.api.bo.permission.UserPermissionQueryRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

import java.util.Set;


public interface SysPermissionOptionApi {

    /**
     * 删除权限
     * @param busiType
     * @param busiId
     */
    Rsp<Integer> deleteDataPermission(String busiType,String busiId);


    /**
     * 更新权限
     * @param update
     * @return
     */
    Rsp<Integer> updateDataPermission(PermissionObjectUpdateBO update);


    /**
     * 删除权限
     * @param busiType
     * @param busiId
     * @return
     */
    Rsp<Integer> deleteTrainPermission(String busiType,String busiId);

    /**
     * 更新权限
     * @param update
     * @return
     */
    Rsp<Integer> updateTrainPermission(PermissionObjectUpdateBO update);

    /**
     * 查询权限
     * @param busiType
     * @param busiId
     * @return
     */
    RspList<PermissionObject> getTrainPermObjects(String tenantCode, String busiType, String busiId);

    /**
     * 查询数据权限
     * @param tenantCode
     * @param busiType
     * @param busiId
     * @return
     */
    RspList<PermissionObject> getDataPermObjects(String tenantCode, String busiType, String busiId);

    /**
     * 查询用户权限
     * @param request
     * @return
     */
    Rsp<Set<String>> getUserDataPermission(UserPermissionQueryRequest request);

    /**
     * 查询用户权限
     * @param request
     * @return
     */
    Rsp<Set<String>> getUserTrainPermission(UserPermissionQueryRequest request);
}
