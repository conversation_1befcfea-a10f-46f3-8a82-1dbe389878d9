package com.tydic.nbchat.admin.api.bo.rp;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class RpUserDetailBO implements Serializable {
    private Long id;

    private String userId;

    private String userName;

    private String phone;

    private String userType;

    private String tenantCode;

    private String companyName;

    private Date regTime;

    private String regChannel;

    private String vipStatus;

    private String vipType;

    private Date vipStartTime;

    private Date vipEndTime;

    private String buyVersion;

    private Integer vipBuyCount;

    private Date loginTime;

    private Integer scoreBalance;


    private Integer loginCount;

    private Integer videoMakeCount;

    private Integer videoSuccessCount;

    private Integer pptMakeCount;

    private Integer pptSuccessCount;

    private Integer examMakeCount;

    private Integer examSuccessCount;

    private Date updateTime;

    private Date vipFirstTime;

    /**
     * 注册来源
     */
    private String regSource;
    /**
     * 关键词
     */
    private String promKey;
    /**
     * 渠道Id
     */
    private String promId;
    /**
     * 推广渠道
     */
    private String promChannel;
    /**
     * 最新支付金额
     */
    @Deprecated
    private String lastPayPrice;
    /**
     * 总支付金额
     */
    @Deprecated
    private String totalRevenue;
    /**
     * 不同版本支付数量
     */
    private Map<String, Integer> differentVersionUser;
    /**
     * 最新购买版本
     */
    @Deprecated // 请使用 lastGoodsName
    private String latestPurchasedVersion;
    /**
     * 算力点加油包购买次数
     */
    @Deprecated
    private Integer diDouRefuelingBag;
    /**
     * 课件帮登次数
     */
    private Integer tdhLoginCount;
    /**
     * 最近支付时间
     */
    @Deprecated
    private Date payTime;
    /**
     * 制作美化PPT次数
     */
    private Integer beautCount;
    /**
     * 充值积分
     */
    @Deprecated
    private Integer scoreRecharge;

    //最近支付时间
    private Date lastPayTime;
    //最近支付金额（分）
    private Integer lastPayAmount;
    //支付总金额（分）
    private Integer totalPayAmount;
    //加油包购买次数
    private Integer scoreBuyCount;
    //累计充值算力点
    private Integer scoreRechargeTotal;

    //最近购买商品名称
    private String lastGoodsName;
    /**
     * 消耗积分
     */
    @JsonAlias("scoreConsume")
    private Integer scoreConsumeTotal;
}
