package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 营收数据查询返回BO
 */
@Data
public class RpRevenueDataRspBO implements Serializable {
    /**
     * 营收总金额
     */
    private String totalRevenue = "0";
    /**
     * 不同版本的用户数
     */
    private Map<String, Integer> differentVersionUser;
    /**
     * 续费用户数
     */
    private Integer renewalUser = 0;
    /**
     * 到期用户数
     */
    private Integer expireUser = 0;
    /**
     * 用户续费率
     */
    private String userRenewalRate = "0.00%";

    @Override
    public String toString() {
        StringBuilder differentVersionUserStr = new StringBuilder();
        if (differentVersionUser != null && !differentVersionUser.isEmpty()) {
            differentVersionUser.forEach((key, value) ->
                    differentVersionUserStr.append(key).append(": ").append(value).append("\n"));
        } else {
            differentVersionUserStr.append("无数据\n");
        }

        return "-营收数据\n营收总金额：" + totalRevenue + "\n" +
                "不同版本的用户数：\n" + differentVersionUserStr +
                "续费用户数：" + renewalUser + "\n" +
                "到期用户数：" + expireUser + "\n" +
                "用户续费率：" + userRenewalRate;
    }
}
