package com.tydic.nbchat.admin.api.menu;

import com.tydic.nbchat.admin.api.bo.menu.SysButtonSaveReqBO;
import com.tydic.nbchat.admin.api.bo.menu.SysButtonDeleteReqBO;
import com.tydic.nbchat.admin.api.bo.menu.SysMenuQueryReqBO;
import com.tydic.nbchat.admin.api.bo.menu.SysMenuSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SysMenuApi {

    /**
     * 获取菜单列表
     * @param queryReqBO
     * @return
     */
    RspList getMenus(SysMenuQueryReqBO queryReqBO);

    /**
     * 保存/更新菜单
     * @param reqBO
     * @return
     */
    Rsp saveMenu(SysMenuSaveReqBO reqBO);

    /**
     * 删除菜单
     * @param menuCode
     * @return
     */
    Rsp deleteMenu(String menuCode);


    /**
     * 新增/更新按钮
     * @param reqBO
     * @return
     */
    Rsp saveButton(SysButtonSaveReqBO reqBO);

    /**
     * 删除按钮
     * @param reqBO
     * @return
     */
    Rsp deleteButton(SysButtonDeleteReqBO reqBO);

}
