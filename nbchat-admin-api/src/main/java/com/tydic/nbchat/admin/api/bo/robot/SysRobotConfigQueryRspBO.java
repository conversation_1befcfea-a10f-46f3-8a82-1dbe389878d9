package com.tydic.nbchat.admin.api.bo.robot;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.Set;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SysRobotConfigQueryRspBO implements Serializable {
    //当前设定值-回显
    private String configValue;
    private String configName;
    //机器人配置列表-下拉展示
    private Set<SysRobotKvConfigBO> configList  = new LinkedHashSet<>();
}
