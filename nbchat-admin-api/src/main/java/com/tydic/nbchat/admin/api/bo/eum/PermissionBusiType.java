package com.tydic.nbchat.admin.api.bo.eum;

public enum PermissionBusiType {

    EXAM_BANK("exam_bank", "题库"),
    EXAM_PAPER("exam_paper", "试卷"),
    EXAM_TEST("exam_test", "考试"),
    TRAIN_TEST("train_test", "AI陪练"),
    TRAIN_COURSE("train_course", "AI课程"),
    TRAIN_MATERIAL("train_material", "AI素材");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private PermissionBusiType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PermissionBusiType field : PermissionBusiType.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }

}
