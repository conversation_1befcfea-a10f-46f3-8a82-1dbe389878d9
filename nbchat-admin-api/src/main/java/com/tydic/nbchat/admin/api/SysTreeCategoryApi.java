package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryReqBO;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryRsqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * <AUTHOR>
 * @datetime：2024/10/16 16:46
 * @description:
 */
public interface SysTreeCategoryApi {

    String ROOT = "-1";

    /**
     * 获取分类树
     *
     * @param reqBO
     * @return
     */
    RspList getCategoryTree(SysTreeCategoryReqBO reqBO);

    /**
     * 获取分类列表
     *
     * @param reqBO
     * @return
     */
    RspList getCategoryList(SysTreeCategoryReqBO reqBO);

    /**
     * 添加分类
     *
     * @param reqBO
     * @return
     */
    Rsp addTreeCategory(SysTreeCategoryReqBO reqBO);

    /**
     * 修改分类
     *
     * @param reqBO
     * @return
     */
    Rsp updateTreeCategory(SysTreeCategoryReqBO reqBO);

    /**
     * 删除分类
     *
     * @param cateId
     * @return
     */
    Rsp deleteTreeCategory(String cateId);

    Rsp<SysTreeCategoryRsqBO> info(String cateId);

}
