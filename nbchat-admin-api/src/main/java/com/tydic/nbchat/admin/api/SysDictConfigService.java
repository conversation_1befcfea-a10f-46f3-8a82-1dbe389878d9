package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.SysDictConfigRspBO;
import com.tydic.nbchat.admin.api.bo.SysDictInsertReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SysDictConfigQueryBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nbchat.admin.api.bo.SysDictConfigQueryReqBO;

/**
 * @Classname SysDictConfigService
 * @Description 系统字典配置
 * @Date 2022/7/13 15:06
 * @Created by kangkang
 */
public interface SysDictConfigService {

    RspList getSysDictValues(SysDictConfigQueryReqBO configReqBO);

    /**
     * 根据字典名称和字典描述对dict_code分组查询
     */
    RspList getSysDictValuesByGroup(SysDictConfigQueryBo reqBO);

    /**
     * 新增字典配置根据dict_code（批量新增）
     */
    RspList insertDictCode(SysDictInsertReqBO reqBO);
    /**
     * 批量字典配置根据dict_code更新状态
     */
    RspList updateDictCode(SysDictConfigRspBO reqBO);

}
