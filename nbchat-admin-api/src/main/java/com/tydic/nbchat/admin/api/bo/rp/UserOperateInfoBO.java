package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/25 10:40
 * @description 用户操作日志实体
 */
@Data
public class UserOperateInfoBO implements Serializable {
    private static final long serialVersionUID = 2098245101722053229L;

    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 操作类型: 参考常量定义
     */
    private String type;
    /**
     * 业务ID
     */
    private String busiId;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 客户端UA
     */
    private String clientUa;
}
