package com.tydic.nicc.dc.boot.starter.minio;

import com.google.common.collect.Multimap;
import io.minio.CreateMultipartUploadResponse;
import io.minio.ListPartsResponse;
import io.minio.MinioAsyncClient;
import io.minio.ObjectWriteResponse;
import io.minio.messages.Part;

import java.util.concurrent.CompletableFuture;

public class KKMinioClient extends MinioAsyncClient {

    protected KKMinioClient(MinioAsyncClient client) {
        super(client);
    }

    //创建分块上传任务
    public CompletableFuture<CreateMultipartUploadResponse> initMultipartUpload(String bucket, String region, String object,
                                      Multimap<String, String> headers,
                                      Multimap<String, String> extraQueryParams) throws Exception {
        return super.createMultipartUploadAsync(bucket, region, object, headers, extraQueryParams);
    }

    //合并指定上传任务的分块文件
    public CompletableFuture<ObjectWriteResponse> mergeMultipartUpload(String bucketName, String region, String objectName,
                                                                       String uploadId, Part[] parts,
                                                                       Multimap<String, String> extraHeaders,
                                                                       Multimap<String, String> extraQueryParams) throws Exception {
        return this.completeMultipartUploadAsync(bucketName, region, objectName, uploadId, parts, extraHeaders, extraQueryParams);
    }

    //获取指定上传任务内的已上传的分块信息
    public CompletableFuture<ListPartsResponse> listMultipart(String bucketName, String region, String objectName, Integer maxParts,
                                                              Integer partNumberMarker, String uploadId,
                                                              Multimap<String, String> extraHeaders,
                                                              Multimap<String, String> extraQueryParams) throws Exception {
        return this.listPartsAsync(bucketName, region, objectName, maxParts, partNumberMarker, uploadId, extraHeaders, extraQueryParams);
    }

}
