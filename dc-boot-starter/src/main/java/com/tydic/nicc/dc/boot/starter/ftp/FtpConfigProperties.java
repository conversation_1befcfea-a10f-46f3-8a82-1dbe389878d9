package com.tydic.nicc.dc.boot.starter.ftp;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@ConfigurationProperties(prefix = "nicc-plugin.ftp")
@Component
public class FtpConfigProperties {

    private String type;
    private String host;
    private Integer port;
    private String username;
    private String password;
    private String homeDir;

    public boolean check(){
        if(this.type == null || "".equals(this.type)){
            log.error("请检查FTP配置文件 type 参数 !");
            return false;
        }
        if(type.equals("ftp") || type.equals("sftp")){
            if(this.host == null || "".equals(this.host)){
                log.error("请检查FTP配置文件 host 参数 !");
                return false;
            }
            if(this.port == null || "".equals(this.port)){
                log.error("请检查FTP配置文件 port 参数 !");
                return false;
            }
            if(this.username == null || "".equals(this.username)){
                log.error("请检查FTP配置文件 username 参数 !");
                return false;
            }
            if(this.homeDir == null || "".equals(this.homeDir)){
                log.error("请检查FTP配置文件 homeDir 参数 !");
                return false;
            }
            if(this.homeDir.lastIndexOf("/") == -1){
                this.homeDir += "/";
            }
        } else {
            log.error("请检查FTP配置文件 ftp类型只能为 ftp/sftp !");
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "FtpConfigProperties{" +
                "type='" + type + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", username='" + username + '\'' +
                ", password='" + "**********" + '\'' +
                ", homeDir='" + homeDir + '\'' +
                '}';
    }
}
