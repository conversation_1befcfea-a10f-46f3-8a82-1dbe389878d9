package com.tydic.nicc.dc.boot.starter.obs;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Classname ObsHelper
 * @Description ObsAutoConfig
 * @Date 2022/4/2 11:43 上午
 * @Created by kangkang
 */
@Slf4j
@ConditionalOnProperty(value = "nicc-plugin.obs.enable",havingValue = "true")
@Configuration
public class ObsAutoConfig {

    @Autowired(required = false)
    private ObsConfigProperties obsConfigProperties;

    @Bean
    public ObsHelper obsHelper() {
        log.info("初始化 OBS 配置...开始");
        if (obsConfigProperties != null && this.obsConfigProperties.check()) {
            log.info("初始化 OBS 配置...完成:{}",obsConfigProperties);
            return new ObsHelper(this.obsConfigProperties);
        } else {
            throw new IllegalArgumentException("OBS 文件上传属性配置错误，请检查配置文件");
        }
    }

}
