package com.tydic.nicc.dc.boot.starter.config.dubbo;

import lombok.Data;

import java.util.Map;

/**
 * @Classname Rest2DubboApiConfigBean
 * @Description Rest2DubboApiConfigBean
 * @Date 2021/5/13 8:47 下午
 * @Created by kangkang
 */
@Data
public class Rest2DubboApiConfigBean {
    private String name;
    private String desc;
    private String path;
    private String mapping;
    private String servlet;
    /**
     * 服务映射 key = requestUri
     */
    private Map<String,Rest2DubboServiceConfig> serviceMapping;

    public String getMapping() {
        if(mapping.endsWith("/")){
            return mapping;
        }
        return mapping + "/";
    }

}
