package com.tydic.nicc.dc.boot.starter.rest2dubbo;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.constants.DcBaseConstants;
import com.tydic.nicc.dc.base.bo.eum.Rest2DubboProxyErrCode;
import com.tydic.nicc.dc.boot.starter.config.dubbo.Rest2DubboApiConfigBean;
import com.tydic.nicc.dc.boot.starter.config.dubbo.Rest2DubboServiceConfig;
import com.tydic.nicc.dc.boot.starter.rest2dubbo.bo.GenericInvokeBO;
import com.tydic.nicc.dc.boot.starter.rest2dubbo.listener.Rest2DubboProcessListener;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.*;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @Classname Rest2DubboProxyServlet
 * @Description rest2dubbo 代理
 * @Date 2021/5/13 8:56 下午
 * @Created by kangkang
 */
@Slf4j
public class Rest2DubboProxyService extends HttpServlet {

    protected Rest2DubboApiConfigBean apiConfigBean;
    protected DubboApiFactory apiFactory;
    protected List<Rest2DubboProcessListener> listeners;

    public Rest2DubboProxyService(Rest2DubboApiConfigBean apiConfigBean, DubboApiFactory apiFactory, List<Rest2DubboProcessListener> listeners) {
        this.apiConfigBean = apiConfigBean;
        this.apiFactory = apiFactory;
        this.listeners = listeners;
        if (this.listeners == null) {
            this.listeners = new ArrayList<>();
        } else {
            this.listeners.sort(Comparator.comparing(Rest2DubboProcessListener::order));
        }

    }

    @Override
    public void init(ServletConfig servletConfig) throws ServletException {
        log.info("{} 初始化完成 serviceMapping:{}", servletConfig.getServletName(), apiConfigBean.getServiceMapping());
    }

    @Override
    public void service(ServletRequest servletRequest, ServletResponse servletResponse) throws ServletException, IOException {
        int errorCode = 0;
        String errReason = "";
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String queryString = request.getQueryString();
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        for (Rest2DubboProcessListener listener : listeners) {
            listener.onBefore(request, apiConfigBean);
        }
        log.info("Rest2Dubbo 代理请求:[{}],[{}],[{}]", method, requestUri, queryString);
        Rest2DubboServiceConfig serviceConfig = apiConfigBean.getServiceMapping().get(requestUri);
        if (apiFactory == null && serviceConfig == null) {
            errorCode = Rest2DubboProxyErrCode.UNKNOW_DUBBO_SERVICE.getCode();
            errReason = "Rest2Dubbo 未匹配到服务,请检查服务列表配置信息: mapping = " + requestUri;
            log.error("Rest2Dubbo 未匹配到服务:requestUri = {}", requestUri);
            invokeError(response, errReason);
            for (Rest2DubboProcessListener listener : listeners) {
                listener.onError(errorCode, errReason, serviceConfig, apiFactory);
            }
            return;
        }
        //反射调用
        GenericInvokeBO invokeBO = GenericInvokeBO.builder().
                api(serviceConfig.getApi()).
                group(serviceConfig.getGroup()).
                version(serviceConfig.getVersion()).
                method(serviceConfig.getMethod()).
                paramClass(serviceConfig.getParamClass()).build();
        if (method.equals(RequestMethod.GET.name())) {
            Map<String, Object> param = getParameterMap(request);
            List<Map<String, Object>> invokeParams = parseDubboInvokeParams(param);
            invokeBO.setParameters(invokeParams);
        } else if (method.equals(RequestMethod.POST.name())) {
            String body = readRequestBody(request);
            if (JSONValidator.from(body).validate() && StringUtils.isNotEmpty(invokeBO.getParamClass())) {
                Map<String, Object> reqData = JSONObject.parseObject(body, Map.class);
                Map<String, Object> param = new LinkedHashMap<>();
                param.put(DubboApiFactory.KEY_PARAM_TYPE, invokeBO.getParamClass());
                param.put(DubboApiFactory.KEY_OBJECT, reqData);
                List<Map<String, Object>> invokeParams = new ArrayList<>();
                invokeParams.add(param);
                invokeBO.setParameters(invokeParams);
            } else {
                log.warn("参数不支持,请传入json格式入参,并配置对应方法参数类名称: mapping = {},reqBody = {}", requestUri, body);
                errorCode = Rest2DubboProxyErrCode.PARAM_ERROR.getCode();
                errReason = "参数错误,请传入json格式入参,并配置对应方法参数类名称: mapping = " + requestUri;

            }
        } else {
            errorCode = Rest2DubboProxyErrCode.REQUEST_METHOD_ERROR.getCode();
            errReason = "请求类型不支持,当前仅支持GET/POST请求类型: method = " + method + ", mapping = " + requestUri;
        }
        if (errorCode == 0) {
            try {
                Object invokeRsp = apiFactory.genericInvoke(invokeBO);
                printRspValue(response, invokeRsp);
                for (Rest2DubboProcessListener listener : listeners) {
                    listener.onAfter(invokeBO, serviceConfig, apiFactory);
                }
            } catch (Exception e) {
                errorCode = Rest2DubboProxyErrCode.RPC_ERROR.getCode();
                errReason = "dubbo泛化调用异常:" + e.getMessage();
                log.error("dubbo泛化调用异常:", e);
                if(errReason.contains("No provider available")){
                    //找不到提供者
                    errReason = "dubbo泛化调用异常: 服务["+ invokeBO.getFullServiceName() +"]未注册,请检查配置!" ;
                } else if(errReason.contains("Failed to invoke the method")) {
                    errReason = "dubbo泛化调用异常: 服务["+ invokeBO.getFullServiceName() +"]调用失败,请检查方法!" ;
                }
            }
        }
        if (errorCode > 0) {
            invokeError(response, errReason);
            for (Rest2DubboProcessListener listener : listeners) {
                listener.onError(errorCode, errReason, serviceConfig, apiFactory);
            }
        }

    }


    @Override
    public String getServletInfo() {
        return "A proxy rest2dubbo servlet by Chenwk, <EMAIL>";
    }

    @Override
    public void destroy() {
        super.destroy();
    }

    /**
     * 转换参数
     *
     * @param pramMap
     * @return
     */
    private List<Map<String, Object>> parseDubboInvokeParams(Map<String, Object> pramMap) {
        List<Map<String, Object>> invokePrams = new ArrayList<>();
        if (!pramMap.isEmpty()) {
            for (Map.Entry<String, Object> entry : pramMap.entrySet()) {
                Map<String, Object> param = new LinkedHashMap<>();
                if (entry.getValue() instanceof java.lang.Integer) {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.Integer");
                } else if (entry.getValue() instanceof java.lang.Long) {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.Long");
                } else if (entry.getValue() instanceof java.lang.Float) {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.Float");
                } else if (entry.getValue() instanceof java.lang.Double) {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.Double");
                } else if (entry.getValue() instanceof java.lang.Boolean) {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.Boolean");
                } else {
                    param.put(DubboApiFactory.KEY_PARAM_TYPE, "java.lang.String");
                }
                param.put(DubboApiFactory.KEY_OBJECT, entry.getValue());
                invokePrams.add(param);
            }
        }
        return invokePrams;
    }

    /**
     * 获取request url参数
     *
     * @param request
     * @return
     */
    private Map<String, Object> getParameterMap(HttpServletRequest request) {
        // 参数Map
        Map<?, ?> properties = request.getParameterMap();
        log.info("request 获取到参数:{}", properties);
        // 返回值Map
        Map<String, Object> returnMap = new LinkedHashMap<>();
        Iterator<?> entries = properties.entrySet().iterator();
        Map.Entry<String, Object> entry;
        String name = "";
        String value = "";
        Object valueObj = null;
        while (entries.hasNext()) {
            entry = (Map.Entry<String, Object>) entries.next();
            name = entry.getKey();
            valueObj = entry.getValue();
            if (null == valueObj) {
                value = "";
            } else if (valueObj instanceof String[]) {
                String[] values = (String[]) valueObj;
                for (int i = 0; i < values.length; i++) {
                    value = values[i] + ",";
                }
                value = value.substring(0, value.length() - 1);
            } else {
                value = valueObj.toString();
            }
            returnMap.put(name, value);
        }
        return returnMap;
    }

    /**
     * 读取requestBody内容
     *
     * @param request
     * @return
     */
    public static String readRequestBody(HttpServletRequest request) {
        int len = request.getContentLength();
        byte[] buffer = new byte[len];
        ServletInputStream in = null;
        try {
            in = request.getInputStream();
            in.read(buffer, 0, len);
            in.close();
            return new String(buffer, "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return "";
    }

    private void printRspValue(HttpServletResponse response, Object object) {
        response.setStatus(HttpStatus.OK.value());
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        try {
            response.getWriter().write(JSONObject.toJSONString(object));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void invokeError(HttpServletResponse response, String msg) {
        Rsp rsp = BaseRspUtils.createErrorRsp(DcBaseConstants.RSP_ERROR_AUTH, msg);
        response.setStatus(HttpStatus.OK.value());
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        try {
            response.getWriter().write(JSONObject.toJSONString(rsp));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
