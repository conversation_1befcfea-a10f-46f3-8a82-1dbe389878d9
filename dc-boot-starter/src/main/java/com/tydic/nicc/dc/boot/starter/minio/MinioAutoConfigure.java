package com.tydic.nicc.dc.boot.starter.minio;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @Classname MinioAutoConfigure
 * @Description MinioAutoConfigure
 * @Date 2023/2/13 12:35
 * @Created by kangkang
 */
@Slf4j
@ConditionalOnProperty(value = "nicc-plugin.minio.enable",havingValue = "true")
@Configuration
public class MinioAutoConfigure {

    @Autowired(required = false)
    private MinioConfigProperties minioConfigProperties;

    @Bean
    public MinioHelper getMinioHelper() {
        log.info("初始化 MINIO 配置...开始");
        if (this.minioConfigProperties != null && this.minioConfigProperties.check()) {
            log.info("初始化 MINIO 配置...完成:{}",minioConfigProperties);
            return new MinioHelper(minioConfigProperties);
        } else {
            throw new IllegalArgumentException("初始化 MINIO 配置...异常: 请检查配置文件!");
        }
    }


}
