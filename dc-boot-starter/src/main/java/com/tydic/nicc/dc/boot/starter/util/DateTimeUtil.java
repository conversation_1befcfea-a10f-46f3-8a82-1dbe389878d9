package com.tydic.nicc.dc.boot.starter.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

public class DateTimeUtil {

    private static Logger logger = LoggerFactory.getLogger(DateTimeUtil.class);

    private static final int ONE_HH_SS = 3600;

    private static final int ONE_MM_SS = 60;

    public static final String TIME_FORMAT_SHORT = "yyyyMMddHHmmss";
    public static final String TIME_FORMAT_SHORT_HOUR = "yyyyMMddHH";
    public static final String TIME_FORMAT_SHORT_MM = "yyyyMMddHHmm";
    public static final String TIME_FORMAT_YMD = "yyyy/MM/dd HH:mm:ss";
    public static final String TIME_FORMAT_NORMAL = "yyyy-MM-dd HH:mm:ss";
    public static final String TIME_FORMAT_ENGLISH = "MM/dd/yyyy HH:mm:ss";
    public static final String TIME_FORMAT_T_FULL = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    public static final String TIME_FORMAT_T_FULL_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String TIME_FORMAT_CHINA = "yyyy年MM月dd日 HH时mm分ss秒";
    public static final String TIME_FORMAT_CHINA_M = "yyyy年MM月dd日 HH时mm分";
    public static final String TIME_FORMAT_CHINA_S = "yyyy年M月d日 H时m分s秒";
    public static final String TIME_FORMAT_SHORT_S = "HH:mm:ss";

    public static final String DATE_FORMAT_SHORT = "yyyyMMdd";
    public static final String DATE_FORMAT_NORMAL = "yyyy-MM-dd";
    public static final String DATE_FORMAT_ENGLISH = "MM/dd/yyyy";
    public static final String DATE_FORMAT_CHINA = "yyyy年MM月dd日";
    public static final String DATE_FORMAT_CHINA_YEAR_MONTH = "yyyy年MM月";
    public static final String MONTH_FORMAT = "yyyyMM";
    public static final String YEAR_MONTH_FORMAT = "yyyy-MM";
    public static final String DATE_FORMAT_MINUTE = "yyyyMMddHHmm";
    public static final String MONTH_DAY_FORMAT = "MM-dd";
    public static final String YEAR_FORMAT = "yyyy";
    public static final String TIME_FORMAT_TIME = "yyyy/MM/dd HH:mm";
    private static final SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_NORMAL);
    private static final SimpleDateFormat sdfTime = new SimpleDateFormat(TIME_FORMAT_NORMAL);
    private static final SimpleDateFormat sdfTimes = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static final SimpleDateFormat sdfTChina = new SimpleDateFormat(TIME_FORMAT_CHINA);


    /**
     *
     * @param second
     * @return
     */
    public static String secondToTime(long second){
        return secondToTime(second,4,"天","小时","分","秒");
    }

    /**
     *
     * @param second 模式-4:天-小时-分-秒 ，模式-3: 小时-分-秒， 模式-2:分-秒
     * @param mode
     * @return
     */
    public static String secondToTime(long second,int mode){
        return secondToTime(second,mode,"天","小时","分","秒");
    }

    /**
     *
     * @param second 秒
     * @param mode 模式-4:天-小时-分-秒 ，模式-3: 小时-分-秒， 模式-2:分-秒
     * @param formatDay 分割符-天
     * @param formatHour 分割符-小时
     * @param formatMinute 分隔符-分钟
     * @param formatSecond 分隔符-秒
     * @return
     */
    public static String secondToTime(long second,int mode,String formatDay,String formatHour,String formatMinute,String formatSecond){
        if(mode == 4){
            long days = second / 86400; //转换天数
            second = second % 86400;    //剩余秒数
            long hours = second / 3600; //转换小时
            second = second % 3600;     //剩余秒数
            long minutes = second /60;  //转换分钟
            second = second % 60;       //剩余秒数
            if(days>0){
                return days + formatDay + hours + formatHour + minutes + formatMinute + second + formatSecond;
            }else{
                return hours + formatHour + minutes + formatMinute + second + formatSecond;
            }
        }
        if(mode == 3){
            long hours = second / 3600; //转换小时
            second = second % 3600;     //剩余秒数
            long minutes = second /60;  //转换分钟
            second = second % 60;       //剩余秒数
            return hours + formatHour + minutes + formatMinute + second + formatSecond;
        }
        if(mode == 2){
            long minutes = second / 60; //分钟
            second = second % 60;       //剩余秒数
            return minutes + formatMinute + second + formatSecond;
        }
        return "";
    }


    /**
     * 把日期字符串转换为日期类型
     *
     * @param dateStr 日期字符串
     * @return 日期
     * @since 0.1
     */
    public static Date convertAsDate(String dateStr) {

        if (dateStr == null || "".equals(dateStr.trim())) {
            return null;
        }
        DateFormat fmt = null;
        if (dateStr.matches("\\d{14}")) {
            fmt = new SimpleDateFormat(TIME_FORMAT_SHORT);
        } else if (dateStr.matches("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}")) {
            fmt = new SimpleDateFormat(TIME_FORMAT_NORMAL);
        } else if (dateStr.matches("\\d{1,2}/\\d{1,2}/\\d{4} \\d{1,2}:\\d{1,2}:\\d{1,2}")) {
            fmt = new SimpleDateFormat(TIME_FORMAT_ENGLISH);
        } else if (dateStr.matches("\\d{4}年\\d{1,2}月\\d{1,2}日 \\d{1,2}时\\d{1,2}分\\d{1,2}秒")) {
            fmt = new SimpleDateFormat(TIME_FORMAT_CHINA);
        } else if (dateStr.matches("\\d{8}")) {
            fmt = new SimpleDateFormat(DATE_FORMAT_SHORT);
        } else if (dateStr.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
            fmt = new SimpleDateFormat(DATE_FORMAT_NORMAL);
        } else if (dateStr.matches("\\d{1,2}/\\d{1,2}/\\d{4}")) {
            fmt = new SimpleDateFormat(DATE_FORMAT_ENGLISH);
        } else if (dateStr.matches("\\d{4}年\\d{1,2}月\\d{1,2}日")) {
            fmt = new SimpleDateFormat(DATE_FORMAT_CHINA);
        } else if (dateStr.matches("\\d{4}\\d{1,2}\\d{1,2}\\d{1,2}\\d{1,2}")) {
            fmt = new SimpleDateFormat(DATE_FORMAT_MINUTE);
        } else if (dateStr.matches("\\d{1,2}:\\d{1,2}:\\d{1,2}")) {
            fmt = new SimpleDateFormat(TIME_FORMAT_SHORT_S);
        } else {
            fmt = new SimpleDateFormat(TIME_FORMAT_NORMAL);
        }
        try {
            return fmt.parse(dateStr);
        } catch (ParseException e) {
            //throw new IllegalArgumentException("Date or Time String is invalid.");
            return null;
        }
    }

    /**
     * 转换时间
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Date convertAsDate(String dateStr, String format) {
        DateFormat fmt = new SimpleDateFormat(format);
        try {
            return fmt.parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 得到时间字符串,格式为 yyyyMMddHHmmss
     *
     * @return 返回当前时间的字符串
     * <AUTHOR>
     * @date 2018/11/1
     */
    public static String getTimeShortString(Date date) {
        return new SimpleDateFormat(TIME_FORMAT_SHORT).format(date);
    }

    public static String getTimeShortString(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 得到十位数的时间戳
     *
     * @param date 时间对象
     * @return long
     * <AUTHOR>
     * @date 2018/9/18
     */
    public static long getTenTimeByDate(Date date) {
        return date.getTime() / 1000;
    }

    /**
     * 得到十位数的时间戳
     *
     * @param dateStr 时间字符串
     * @return long
     * <AUTHOR>
     * @date 2018/9/18
     */
    public static long getTenTimeByDate(String dateStr) {
        return convertAsDate(dateStr).getTime() / 1000;
    }

    /**
     * Description: 比较两个字符串格式的时间大小<br/>
     * 如果第二个时间大于第一个时间返回true,否则返回false
     *
     * @param strFirst  第一个时间
     * @param strSecond 第二个时间
     * @param strFormat 时间格式化方式 eg:"yyyy-MM-dd HH:mm:ss"," yyyy-MM-dd"
     * @return true-第二个时间晚于第一个时间,false-第二个时间不晚于第一个时间
     * <AUTHOR>
     * @date 2018/11/1
     */
    public static boolean latterThan(String strFirst, String strSecond, String strFormat) {
        SimpleDateFormat ft = new SimpleDateFormat(strFormat);
        try {
            Date date1 = ft.parse(strFirst);
            Date date2 = ft.parse(strSecond);
            long quot = date2.getTime() - date1.getTime();
            if (0 < quot) {
                return true;
            } else {
                return false;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;
    }


    public static int differDay(Date beginTime, Date endTime) {
        Calendar startCalendar = new GregorianCalendar();
        startCalendar.setTime(beginTime);
        Calendar endCalendar = new GregorianCalendar();
        endCalendar.setTime(endTime);
        return endCalendar.get(Calendar.DAY_OF_YEAR) - startCalendar.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 根据两个字符串计算相差的年数(用于根据用户填写的生日计算用户的年龄)
     * 格式：yyyy-MM-dd
     */
    public static int differYear(String firstTime, String secondTime) {
        Date beginTime = null;
        Date endTime = null;
        try {
            beginTime = new SimpleDateFormat("yyyy-MM-dd").parse(firstTime);
            endTime = new SimpleDateFormat("yyyy-MM-dd").parse(secondTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar startCalendar = new GregorianCalendar();
        startCalendar.setTime(beginTime);
        Calendar endCalendar = new GregorianCalendar();
        endCalendar.setTime(endTime);
        int diffYear = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
        //截取月日，加上相同的年份，将月日放到同一量级上比较大小，如果前者大于后者，则输出年份减一
        Date timeBegin = null;
        Date timeEnd = null;
        try {
            timeBegin = new SimpleDateFormat("yyyy-MM-dd").parse("2019" + firstTime.substring(4));
            timeEnd = new SimpleDateFormat("yyyy-MM-dd").parse("2019" + secondTime.substring(4));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (timeBegin.getTime() > timeEnd.getTime()) {
            diffYear -= 1;
        }
//        if(diffYear==0){
//            diffYear+=1;
//        }
        return diffYear;
    }

    /**
     * @return int
     * <AUTHOR>
     * @Description 时间格式转毫秒
     * @Date 2019-09-11 11:21
     * @Param [shortTime] 21:12:00
     **/
    public static int ShortTimeToMs(String shortTime) {
        int ms = 0;
        if (shortTime == null) {
            return ms;
        }
        String[] hms = shortTime.trim().split(":");
        try {
            int hh = Integer.valueOf(hms[0]);
            int mm = Integer.valueOf(hms[1]);
            int ss = Integer.valueOf(hms[2]);
            ms = (hh * ONE_HH_SS + mm * ONE_MM_SS + ss) * 1000;
            return ms;
        } catch (Exception e) {
            logger.error("shortTime = {} ,时间不合法：{}", shortTime, e.getMessage());
            return ms;
        }

    }

    /**
     * @return java.lang.String 20:59:07
     * <AUTHOR>
     * @Description 毫秒转换为时间几时格式 hh:mm:ss
     * @Date 2019-09-11 11:22
     * @Param [ms]
     **/
    public static String MsToShortTime(Integer ms) {
        if (ms == null) {
            return "";
        }
        String shortTime = "";
        int ss = (ms / 1000);
        int res = ss % ONE_HH_SS;
        int hh = 0;
        String hhStr = "";
        hh = ss / ONE_HH_SS;
        if (hh < 10) {
            hhStr = "0" + hh;
        } else {
            hhStr = "" + hh;
        }
        if (res == 0) {
            shortTime = hhStr + ":00:00";
        } else {
            int mm = 0;
            String mmStr = "";
            int res1 = res % ONE_MM_SS;
            mm = res / ONE_MM_SS;
            if (mm < 10) {
                mmStr = "0" + mm;
            } else {
                mmStr = "" + mm;
            }
            if (res1 == 0) {
                shortTime = hhStr + ":" + mmStr + ":00";
            } else {
                if (res1 < 10) {
                    shortTime = hhStr + ":" + mmStr + ":0" + res1;
                } else {
                    shortTime = hhStr + ":" + mmStr + ":" + res1;
                }
            }
        }
        return shortTime;
    }


    /**
     * 时间计算-秒
     *
     * @param amount
     * @return
     */
    public static Date DateAddSecond(int amount) {
        return DateAdd(new Date(), Calendar.SECOND, amount);
    }

    /**
     * 时间计算-分钟
     *
     * @param amount
     * @return
     */
    public static Date DateAddMinute(int amount) {
        return DateAdd(new Date(), Calendar.MINUTE, amount);
    }

    /**
     * 时间计算-小时
     *
     * @param amount
     * @return
     */
    public static Date DateAddHour(int amount) {
        return DateAdd(new Date(), Calendar.HOUR, amount);
    }

    /**
     * 时间计算-天
     *
     * @param amount
     * @return
     */
    public static Date DateAddDayOfYear(int amount) {
        return DateAdd(new Date(), Calendar.DAY_OF_YEAR, amount);
    }

    /**
     * 计算-年
     * @param amount
     * @return
     */
    public static Date DateAddYear(int amount) {
        return DateAdd(new Date(), Calendar.YEAR, amount);
    }

    /**
     * 时间计算通用方法
     *
     * @param date
     * @param type
     * @param amount
     * @return
     */
    public static Date DateAdd(Date date, int type, int amount) {
        Calendar nowTime = Calendar.getInstance();
        nowTime.setTime(date);
        nowTime.add(type, amount);
        return nowTime.getTime();
    }

    /**
     * 获取一天的开始时间 00:00:00
     * @return
     */
    public static Date getStartTimeOfDay() {
        return createTime(new Date(),0,0,0);
    }

    /**
     * 获取一天结束时间 23:59:59
     * @return
     */
    public static Date getEndTimeOfDay() {
        return createTime(new Date(),23,59,59);
    }


    /**
     * 获取指定时分秒
     * @param date
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static Date createTime(Date date,int hour,int minute,int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        Date end = calendar.getTime();
        return end;
    }

    /**
     * 获取某月第一天
     * @param addMonth 以当月为基准 加减月份
     * @return
     */
    public static Date getFirstDayOfMonth(int addMonth) {
        Calendar cal = Calendar.getInstance();
        // 当前月＋1，即下个月
        cal.add(cal.MONTH, addMonth);
        // 将下个月1号作为日期初始
        cal.set(cal.DATE, 1);
        return cal.getTime();
    }

    /**
     * 获取某月最后一天
     * @param addMonth 以当月为基准 加减月份
     * @return
     */
    public static Date getLastDayOfMonth(int addMonth) {
        Calendar cal = Calendar.getInstance();
        // 当前月＋1，即下个月
        cal.add(cal.MONTH, addMonth); //得到下个月的月份
        // 将下个月1号作为日期初始
        cal.set(cal.DATE, 1);
        // 下个月1号减去一天，即得到当前月最后一天
        cal.add(cal.DATE, -1);
        return cal.getTime();
    }

    public static void main(String[] args) {
        //System.out.println(DateTimeUtil.getTimeShortString(DateAddDayOfYear(-30),TIME_FORMAT_NORMAL));
        //System.out.println(DateTimeUtil.getTimeShortString(getStartTimeOfDay(),DateTimeUtil.TIME_FORMAT_NORMAL));
    }

}
