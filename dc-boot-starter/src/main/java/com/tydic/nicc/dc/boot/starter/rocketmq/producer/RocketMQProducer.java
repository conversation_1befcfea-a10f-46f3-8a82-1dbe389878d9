/*
package com.tydic.nicc.dc.boot.starter.rocketmq.producer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

*/
/**
 * @Classname RocketMQProducer
 * @Description 生产者
 * @Date 2021/4/19 9:00 下午
 * @Created by kangkang
 *//*

@Slf4j
@Service
public class RocketMQProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producer.send-message-timeout}")
    private Integer messageTimeOut;

    */
/**
     *
     * @param topic
     * @param msgBody 消息体
     * @param callback 回调函数
     *//*

    public void sendAsyncMsg(String topic, String msgBody, SendCallback callback) {
        rocketMQTemplate.asyncSend(topic, MessageBuilder.withPayload(msgBody).build(), callback);
    }

    */
/**
     * 异步消息
     * @param topic
     * @param tagName
     * @param msgBody 消息体
     * @param callback 回调函数
     *//*

    public void sendAsyncMsg(String topic, String tagName, String msgBody, SendCallback callback) {
        rocketMQTemplate.asyncSend(String.format("%s:%s", topic, tagName), MessageBuilder.withPayload(msgBody).build(), callback);
    }

    */
/**
     * 发送延时消息
     * 在start版本中 延时消息一共分为18个等级分别为：1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h<br/>
     *//*

    public void sendDelayMsg(String topic, String msgBody, Integer delayLevel) {
        rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(msgBody).build(), messageTimeOut, delayLevel);
    }

    */
/**
     * 发送带tag的延时消息
     * @param topic
     * @param tagName
     * @param msgBody 消息体
     * @param delayLevel 延时
     *//*

    public void sendDelayMsg(String topic, String tagName,String msgBody, Integer delayLevel) {
        rocketMQTemplate.syncSend(String.format("%s:%s", topic, tagName), MessageBuilder.withPayload(msgBody).build(), messageTimeOut, delayLevel);
    }

    */
/**
     * 普通消息
     * @param topic
     * @param msgBody
     *//*

    public void sendMsg(String topic, String msgBody) {
        rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(msgBody).build());
    }

    */
/**
     * 普通消息
     * @param topic
     * @param tagName
     * @param msgBody
     *//*

    public void sendMsg(String topic, String tagName, String msgBody) {
        rocketMQTemplate.syncSend(String.format("%s:%s", topic, tagName), MessageBuilder.withPayload(msgBody).build());
    }

}
*/
