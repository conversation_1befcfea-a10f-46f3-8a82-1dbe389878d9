package com.tydic.nicc.dc.boot.starter.util;


import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.base.bo.constants.DcBaseConstants;
import com.tydic.nicc.dc.boot.starter.exception.DcBusinessException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DcRspUtils.java
 * @Description 出参创建辅助工具类
 * @createTime 2019年07月20
 */
public class BaseRspUtils {

    private BaseRspUtils(){}

    public final static String SUCCESS_DESC = "业务处理成功!";

    public static Rsp createSuccessRsp(Object data){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setData(data);
        dcRsp.setRspCode(DcBaseConstants.RSP_SUCCESS);
        dcRsp.setRspDesc(SUCCESS_DESC);
        return dcRsp;
    }

    public static Rsp createSuccessRsp(Object data, String msg){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setData(data);
        dcRsp.setRspCode(DcBaseConstants.RSP_SUCCESS);
        dcRsp.setRspDesc(msg);
        return dcRsp;
    }

    public static Rsp createErrorRsp(String errCode, String errMsg){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setRspCode(errCode);
        dcRsp.setRspDesc(errMsg);
        return dcRsp;
    }


    public static Rsp createErrorRsp(Object data, String errMsg){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setRspCode(DcBaseConstants.RSP_ERROR);
        dcRsp.setRspDesc(errMsg);
        return dcRsp;
    }

    public static Rsp createErrorRsp(String errMsg){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setRspCode(DcBaseConstants.RSP_ERROR);
        dcRsp.setRspDesc(errMsg);
        return dcRsp;
    }

    public static Rsp createErrorRsp(DcBusinessException e){
        Rsp<Object> dcRsp = new Rsp<>();
        dcRsp.setRspCode(e.getErrorCode());
        dcRsp.setRspDesc(e.getMessage());
        return dcRsp;
    }

    public static RspList createSuccessRspList(List<?> data){
        RspList rsp = new RspList<>();
        rsp.setRows(data);
        rsp.setCount(data.size());
        rsp.setRspCode(DcBaseConstants.RSP_SUCCESS);
        rsp.setRspDesc(SUCCESS_DESC);
        return rsp;
    }

    public static RspList createSuccessRspList(List<?> data, long count){
        RspList rsp = new RspList<>();
        rsp.setRows(data);
        rsp.setCount(count);
        rsp.setRspCode(DcBaseConstants.RSP_SUCCESS);
        rsp.setRspDesc(SUCCESS_DESC);
        return rsp;
    }


    public static RspList createErrorRspList(String errCode, String errMsg){
        RspList rsp = new RspList<>();
        rsp.setRspCode(errCode);
        rsp.setRspDesc(errMsg);
        return rsp;
    }

    public static RspList createErrorRspList(String errMsg){
        RspList rsp = new RspList<>();
        rsp.setRspCode(DcBaseConstants.RSP_ERROR);
        rsp.setRspDesc(errMsg);
        return rsp;
    }

    public static RspList createErrorRspList(DcBusinessException e){
        RspList rsp = new RspList<>();
        rsp.setRspCode(e.getErrorCode());
        rsp.setRspDesc(e.getMessage());
        return rsp;
    }

}
