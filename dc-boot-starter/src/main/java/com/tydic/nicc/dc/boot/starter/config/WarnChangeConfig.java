package com.tydic.nicc.dc.boot.starter.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @Classname WarnChangeConfig
 * @Description 配置变更提示
 * @Date 2022/4/6 11:40 上午
 * @Created by kangkang
 */
@Slf4j
@ConditionalOnProperty(value = {
        "nicc-dc-config.redis.enable",
        "nicc-dc-config.rest.enable",
        "nicc-dc-config.ftp.enable",
        "nicc-dc-config.oss.enable"})
@Configuration
public class WarnChangeConfig {

    @PostConstruct
    public void init(){
        log.warn("!!!!!!!配置错误:nicc-boot-start[2.6]配置前缀由 [nicc-dc-config] 变更为 [nicc-plugin] ,请尽快修改！!!!!!!!!!");
    }
}
