package com.tydic.nicc.dc.boot.starter.exception;


import com.tydic.nicc.dc.base.bo.constants.DcBaseConstants;

/**
 * 自定义业务异常，参数校验异常
 */
public class ParamCheckException extends RuntimeException{

    /**
     * 错误编码
     */
    private String errorCode;

    public ParamCheckException(String message) {
        super(message);
        this.errorCode = DcBaseConstants.RSP_ERROR;
    }

    public ParamCheckException(String errorCode , String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode(){
        return this.errorCode;
    }
}
