package com.tydic.nicc.dc.boot.starter.config.dubbo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Classname DubboConfigPropertiesBean
 * @Description rest转dubbo服务
 * @Date 2021/5/13 8:28 下午
 * @Created by kangkang
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "nicc-plugin.rest-dubbo")
public class Rest2DubboConfigPropertiesBean {
    /**
     * api配置目录
     */
    private Boolean enable = false;
    private String apiLoad = "local";
    private Boolean printLog = true;
    //服务定义
    private List<Rest2DubboApiConfigBean> services = new ArrayList<>();

}
