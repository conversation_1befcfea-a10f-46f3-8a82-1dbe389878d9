package com.tydic.nicc.dc.boot.starter.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> <br>
 * @Description: RestTemplateAutoConfig  <br>
 * @date 2021/5/12 4:31 下午  <br>
 * @Copyright tydic.com
 */
@Slf4j
@ConditionalOnProperty(value = "nicc-plugin.rest.enable",havingValue = "true")
@Configuration
public class RestTemplateAutoConfig {

    @Resource
    private RestTemplateConfigProperties restTemplateConfigProperties;

    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory factory){
        RestTemplate template =  new RestTemplate(factory);
        template.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return template;
    }

    @Bean
    public ClientHttpRequestFactory componentsClientHttpRequestFactory(){
        try {
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setReadTimeout(restTemplateConfigProperties.getReadTimeout()); //单位为ms
            factory.setConnectTimeout(restTemplateConfigProperties.getConnectTimeout()); //单位为ms
            factory.setConnectionRequestTimeout(restTemplateConfigProperties.getConnectionRequestTimeout());//单位为ms
            // 创建连接池
            PoolingHttpClientConnectionManager connectionManager
                    = new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(restTemplateConfigProperties.getMaxTotal());
            connectionManager.setDefaultMaxPerRoute(restTemplateConfigProperties.getDefaultMaxPerRoute());

            CloseableHttpClient client;
            if(restTemplateConfigProperties.getTrustedAll()){
                SSLContext sslContext = new SSLContextBuilder().
                        loadTrustMaterial(null, (arg0, arg1) -> true).build();
                SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext,
                        new String[] { "TLSv1.2", "TLSv1.1", "TLSv1" },
                        null,
                        NoopHostnameVerifier.INSTANCE);
                if(StringUtils.isNotBlank(restTemplateConfigProperties.getHttpProxyHost()) && restTemplateConfigProperties.getHttpProxyPort() > 0){
                    HttpHost httpHost = new HttpHost(restTemplateConfigProperties.getHttpProxyHost(), restTemplateConfigProperties.getHttpProxyPort(), "http");
                    client = HttpClientBuilder.create().setConnectionManager(connectionManager).
                            setSSLSocketFactory(csf).setProxy(httpHost).build();
                } else {
                    client = HttpClients.custom().setConnectionManager(connectionManager).
                            setSSLSocketFactory(csf).build();
                }
                factory.setHttpClient(client);
                return factory;
            }
            if(StringUtils.isNotBlank(restTemplateConfigProperties.getHttpProxyHost()) && restTemplateConfigProperties.getHttpProxyPort() > 0){
                HttpHost httpHost = new HttpHost(restTemplateConfigProperties.getHttpProxyHost(), restTemplateConfigProperties.getHttpProxyPort(), "http");
                client = HttpClientBuilder.create().
                        setProxy(httpHost).
                        setConnectionManager(connectionManager).
                        build();
            } else {
                SSLContext sslContext = SSLContexts.custom()
                        .loadTrustMaterial(null, TrustSelfSignedStrategy.INSTANCE)
                        .build();
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                        sslContext,
                        new String[] { "TLSv1.2", "TLSv1.1", "TLSv1" },
                        null,
                        SSLConnectionSocketFactory.getDefaultHostnameVerifier());
                client = HttpClients.custom().
                        setConnectionManager(connectionManager)
                        .setSSLSocketFactory(socketFactory)
                        .build();
            }
            factory.setHttpClient(client);
            return factory;
        } catch (Exception e) {
            log.error("componentsClientHttpRequestFactory 创建失败:{}",e.getMessage());
            System.exit(0);
        }
        return null;
    }

}
