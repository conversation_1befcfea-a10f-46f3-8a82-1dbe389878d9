package com.tydic.nicc.dc.boot.starter.redis;

import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.exception.DcBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 */
@ConditionalOnProperty(value = "nicc-plugin.redis.enable",havingValue = "true")
@Component
public class RedisHelper {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    /**
     *
     * 释放锁lua脚本
     */
    private static final String RELEASE_LOCK_LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
    /**
     * 加锁lua脚本
     */
    private static final String LOCK_LUA_SCRIPT = "if redis.call('setNx',KEYS[1],ARGV[1]) then if redis.call('get',KEYS[1])==ARGV[1] then return redis.call('expire',KEYS[1],ARGV[2]) else return 0 end end";
    /**
     * 脚本执行成功返回值
     */
    private static final Long EXEC_SCRIPT_SUCCESS = 1L;

    /***
     * @return
     */
    public RedisTemplate<String, Object> getRedisTemplate(){
        return redisTemplate;
    }

    /**
     * 指定缓存失效时间
     * @param key 键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            logger.error("hasKey error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 删除缓存
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(Arrays.asList(key));
            }
        }
    }

    // ============================String=============================
    /**
     * 普通缓存获取
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     * @param key 键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     * @param key 键
     * @param value 值
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递增
     * @param key 键
     * @param delta 要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     * @param key 键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ================================Map=================================
    /**
     * HashGet
     * @param key 键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * HashSet
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     * @param key 键
     * @param map 对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     * @param key 键
     * @param item 项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     * @param key 键
     * @param item 项
     * @param value 值
     * @param time 时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除hash表中的值
     * @param key 键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        redisTemplate.opsForHash().delete(key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     * @param key 键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     * @param key 键
     * @param item 项
     * @param by 要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }

    /**
     * hash递减
     * @param key 键
     * @param item 项
     * @param by 要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    // ============================set=============================
    /**
     * 根据key获取Set中的所有值
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("sGet error: {}",e.getMessage());
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     * @param key 键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            logger.error("sHasKey error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     * @param key 键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            logger.error("sSet error: {}",e.getMessage());
            return 0;
        }
    }

    /**
     * 带过期时间的key
     * @param key
     * @param time
     * @param values
     * @return
     */
    public long sSetByExpire(String key, long time, Object... values) {
        long add = 0;
        try {
            add = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
        } catch (Exception e) {
            logger.error("sSetByExpire error: {}",e.getMessage());
            return 0;
        }
        return add;
    }

    /**
     * 将set数据放入缓存
     * @param key 键
     * @param time 时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0)
                expire(key, time);
            return count;
        } catch (Exception e) {
            logger.error("sSetAndTime error: {}",e.getMessage());
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 移除值为value的
     * @param key 键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    // ===============================list=================================

    /**
     * 获取list缓存的内容
     * @param key 键
     * @param start 开始
     * @param end 结束 0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            logger.error("lGet error:",e);
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            logger.error("lGetListSize error: {}",e.getMessage());
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     * @param key 键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            logger.error("lGetIndex error: {}",e.getMessage());
            return null;
        }
    }

    /**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            logger.error("lSet error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            logger.error("lSet error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存 - 向右侧追加
     * @param key 键
     * @param value 为list集合
     * @return
     */
    public boolean lSetList(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            logger.error("lSet error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     */
    public boolean lSetList(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean lTrim(String key,long start,long end) {
        try {
            redisTemplate.opsForList().trim(key,start, end);
            return true;
        } catch (Exception e) {
            logger.error("lTrim error: {}",e.getMessage());
            return false;
        }
    }


    /**
     * 根据索引修改list中的某条数据
     * @param key 键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            logger.error("lUpdateIndex error: {}",e.getMessage());
            return false;
        }
    }

    /**
     * 移除N个值为value
     * @param key 键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            logger.error("lRemove error: {}",e.getMessage());
            return 0;
        }
    }


    /**
     * 列表左侧弹出元素
     * @param key
     * @return Object
     */
    public Object lLPop(String key){
        return redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 列表右端弹出元素
     * @param key
     * @return Object
     */
    public Object lRPop(String key){
        return redisTemplate.opsForList().rightPop(key);
    }


    /**
     * 加锁，自旋重试三次
     * @param redisLockEntity 锁实体
     * @param tryLockSleep 重试睡眠时长 ms
     * @param expTime 锁到期时间设定 s
     * @return
     */
    public boolean lock(RedisLockEntity redisLockEntity,long tryLockSleep,long expTime) {
        if(StringUtils.isAnyBlank(redisLockEntity.getLockKey(),redisLockEntity.getRequestId())){
            throw new DcBusinessException("参数异常,加锁参数异常");
        }
        if(expTime < 1){
            throw new DcBusinessException("参数异常,锁的到期时间不得小于1s");
        }
        boolean locked = false;
        int tryCount = 3;
        while (!locked && tryCount > 0) {
            locked = lockLua(redisLockEntity,expTime);
            if(!locked){
                //尝试重新获取锁
                tryCount--;
                try {
                    TimeUnit.MILLISECONDS.sleep(tryLockSleep);
                } catch (InterruptedException e) {
                    logger.error("线程被中断:" + Thread.currentThread().getId(), e);
                }
            }
        }
        return locked;
    }


    /**
     * 通过lua脚本加锁
     * @param entity 加锁实体
     * @param expireTime 到期时间 s
     * @return
     */
    public boolean lockLua(RedisLockEntity entity,long expireTime){
        if (entity == null || entity.getLockKey() == null || entity.getRequestId() == null) {
            logger.warn("lua脚本加锁失败,参数异常:{}",entity);
            return false;
        }
        if(expireTime < 1){
            throw new DcBusinessException("参数异常,锁的到期时间不得小于1s");
        }
        try{
            RedisScript<Long> redisScript = new DefaultRedisScript<>(LOCK_LUA_SCRIPT, Long.class);
            Object result = redisTemplate.execute(redisScript, Collections.singletonList(entity.getLockKey()),entity.getRequestId(),expireTime);
            logger.debug("lua脚本加锁结果:{}",result);
            return Objects.equals(result, EXEC_SCRIPT_SUCCESS);
        } catch(Exception e){
            logger.error("lua脚本加锁失败:{},error:",entity,e);
        }
        return false;
    }

    private static ThreadLocal<Thread> THREAD_LOCAL = new ThreadLocal<>();


    /**
     * 自动续签锁
     * @param lockEntity 加锁参数
     * @param tryLockSleep 加锁重试休眠时间 ms
     * @param expTime 到期时间 s: 过期时间根据业务处理时长设置，一般为业务处理时间的2倍
     * @return
     */
    public boolean lockRenew(RedisLockEntity lockEntity,long tryLockSleep,long expTime) {
        boolean locked = false;
        int tryCount = 3;
        while (!locked && tryCount > 0) {
            locked = lockLua(lockEntity,expTime); //redisTemplate.opsForValue().setIfAbsent(redisLockEntity.getLockKey(), redisLockEntity.getRequestId(), expTime, timeUnit);
            if(!locked){
                //尝试重新获取锁
                tryCount--;
                try {
                    TimeUnit.MILLISECONDS.sleep(tryLockSleep);
                } catch (InterruptedException e) {
                    logger.error("线程被中断:" + Thread.currentThread().getId(), e);
                }
            } else {
                // 续签
                Thread renewal = new Thread(new RenewalLock(lockEntity, expTime));
                renewal.setDaemon(true);
                THREAD_LOCAL.set(renewal);
                renewal.start();
            }
        }
        return locked;
    }

    /**
     * 默认10s到期
     * @param redisLockEntity
     * @param tryLockSleepMill 超时重试的时长 ms
     * @return
     */
    public boolean lock(RedisLockEntity redisLockEntity,long tryLockSleepMill) {
        return lock(redisLockEntity,tryLockSleepMill,10);
    }

    /**
     * 加锁，默认重试等待300ms,默认10s到期
     * @param redisLockEntity
     * @return
     */
    public boolean lock(RedisLockEntity redisLockEntity) {
       return lock(redisLockEntity,300);
    }


    /**
     * 非原子解锁，可能解别人锁，不安全
     *
     * @param redisLockEntity
     * @return
     */
    public boolean unlock(RedisLockEntity redisLockEntity) {
        if (redisLockEntity == null || redisLockEntity.getLockKey() == null || redisLockEntity.getRequestId() == null)
            return false;
        boolean releaseLock = false;
        String requestId = (String) redisTemplate.opsForValue().get(redisLockEntity.getLockKey());
        if (redisLockEntity.getRequestId().equals(requestId)) {
            releaseLock = redisTemplate.delete(redisLockEntity.getLockKey());
        }
        return releaseLock;
    }

    /**
     * 使用lua脚本解锁，不会解除别人锁
     *
     * @param redisLockEntity
     * @return
     */
    public boolean unlockLua(RedisLockEntity redisLockEntity) {
        if (redisLockEntity == null || redisLockEntity.getLockKey() == null || redisLockEntity.getRequestId() == null)
            return false;
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(RELEASE_LOCK_LUA_SCRIPT, Long.class);
        Long result = redisTemplate.execute(redisScript, Collections.singletonList(redisLockEntity.getLockKey()), redisLockEntity.getRequestId());
        return Objects.equals(result, EXEC_SCRIPT_SUCCESS);
    }


    /**
     * 执行lua脚本
     * @param redisScript 脚本
     * @param params 参数列表
     * @return
     */
    public boolean execLua(RedisScript<Long> redisScript,List<String> keys,Object... params) {
        Long result = redisTemplate.execute(redisScript, keys,params);
        return Objects.equals(result, EXEC_SCRIPT_SUCCESS);
    }

    /**
     * 执行lua脚本返回结果
     * @param redisScript 脚本
     * @param params 参数列表
     * @return
     */
    public String execLuaResult(RedisScript<String> redisScript,List<String> keys,Object... params) {
        return redisTemplate.execute(redisScript, keys,params);
    }

    /**
     * 执行lua脚本
     * @param redisScript
     * @param serializer
     * @param keys
     * @param params
     * @return
     */
    public String execLuaResult(RedisScript<String> redisScript,
                                StringRedisSerializer serializer,
                                List<String> keys, Object... params) {
        return redisTemplate.execute(redisScript,serializer,serializer, keys,params);
    }


    /**
     * 续签lock
     */
    private class RenewalLock implements Runnable {
        private RedisLockEntity entity;
        private Long timeout;
        public RenewalLock(RedisLockEntity entity, Long timeout) {
            this.entity = entity;
            this.timeout = timeout;
        }
        @Override
        public void run() {
            while (true) {
                try {
                    if(Thread.currentThread().isInterrupted()) {
                        return;
                    }
                    logger.info("Redis续签守护线程:{}",entity);
                    //2s 内自动续签
                    if(timeout > 3){
                        TimeUnit.SECONDS.sleep(timeout - 3);
                    } else {
                        TimeUnit.SECONDS.sleep(timeout - 1);
                    }
                    redisTemplate.setEnableTransactionSupport(true);
                    redisTemplate.watch(entity.getLockKey());
                    String requestId = (String) redisTemplate.opsForValue().get(entity.getLockKey());
                    if(requestId != null && requestId.equals(entity.getRequestId())) {
                        redisTemplate.multi();
                        redisTemplate.expire(entity.getLockKey(), timeout, TimeUnit.SECONDS);
                        redisTemplate.exec();
                    }
                    redisTemplate.unwatch();
                } catch (InterruptedException e) {
                    logger.error("Redis锁续签异常,守护线程续签中断:",e);
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}