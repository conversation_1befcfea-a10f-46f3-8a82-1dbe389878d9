package com.tydic.nicc.dc.boot.starter.obs;

import com.obs.services.ObsClient;
import com.obs.services.model.*;

import java.io.File;
import java.io.InputStream;

/**
 * @Classname ObsHelper
 * @Description ObsHelper
 * @Date 2022/4/2 11:43 上午
 * @Created by kangkang
 */
public class ObsHelper {

    private ObsConfigProperties obsConfigProperties;
    private ObsClient obsClient;

    public ObsHelper(ObsConfigProperties obsConfigProperties) {
        this.obsConfigProperties = obsConfigProperties;
        // 创建ObsClient实例
        obsClient = new ObsClient(
                obsConfigProperties.getAccessKey(),
                obsConfigProperties.getAccessKeySecret(),
                obsConfigProperties.getEndPoint());

    }

    /**
     *
     * @param file 文件对象
     * @return
     */
    public PutObjectResult upload(File file) {
        return obsClient.putObject(obsConfigProperties.getBucketName(), file.getName(), file);
    }


    /**
     *
     * @param filePathName 完整文件路径 /xx/xx/abc.png
     * @param file 文件对象
     * @return
     */
    public PutObjectResult upload(String filePathName, File file) {
        return obsClient.putObject(obsConfigProperties.getBucketName(), filePathName, file);
    }

    /**
     *
     * @param filePathName 完整文件路径 /xx/xx/abc.png
     * @param inputStream 文件流
     * @return
     */
    public PutObjectResult upload(String filePathName, InputStream inputStream) {
        return obsClient.putObject(obsConfigProperties.getBucketName(), filePathName, inputStream);
    }

    /**
     *
     * @param bucketName 桶名称
     * @param dirPath 文件路径 /xx/xx
     * @param fileName 文件名称 abc.png
     * @param inputStream 文件流
     * @return
     */
    public PutObjectResult upload(String bucketName,String dirPath, String fileName, InputStream inputStream) {
        String path = "";
        if(dirPath.endsWith("/")){
            path = dirPath + fileName;
        } else {
            path = dirPath + File.separator + fileName;
        }
        return obsClient.putObject(bucketName, path, inputStream);
    }


    /**
     *
     * @param bucketName 桶名称
     * @param filePathName 完整文件路径 /xx/xx/abc.png
     * @param inputStream 文件流
     * @return
     */
    public PutObjectResult upload(String bucketName, String filePathName, InputStream inputStream) {
       return obsClient.putObject(bucketName, filePathName, inputStream);
    }

    /**
     *
     * @param request 文件上传请求
     * @return
     */
    public PutObjectResult upload(PutObjectRequest request) {
        return obsClient.putObject(request);
    }

    public PutObjectResult upload(String filePathName, InputStream inputStream, AccessControlList accessControlList) {
        PutObjectRequest request = new PutObjectRequest();
        request.setBucketName(obsConfigProperties.getBucketName());
        request.setInput(inputStream);
        request.setMetadata(null);
        request.setObjectKey(filePathName);
        if(accessControlList != null){
            request.setAcl(accessControlList);
        }
        return obsClient.putObject(request);
    }


    /**
     *
     * @param bucketName 桶名称
     * @param filePathName 完整文件路径 xx/xx/abc.png
     * @param file 文件对象
     * @return
     */
    public PutObjectResult upload(String bucketName, String filePathName, File file) {
        return obsClient.putObject(bucketName,filePathName,file);
    }

    /**
     *
     * @param bucketName 桶名称
     * @param filePathName 完整文件路径 xx/xx/abc.png
     * @return
     */
    public ObsObject download(String bucketName, String filePathName) {
        return obsClient.getObject(bucketName,filePathName);
    }

    /**
     * 下载文件
     * @param filePathName
     * @return
     */
    public ObsObject download(String filePathName) {
        return obsClient.getObject(this.obsConfigProperties.getBucketName(),filePathName);
    }

    public DeleteObjectResult delete(String filePathName) {
        return obsClient.deleteObject(this.obsConfigProperties.getBucketName(),filePathName);
    }

    public DeleteObjectResult delete(String bucketName,String filePathName) {
        return obsClient.deleteObject(bucketName,filePathName);
    }


    public ObsConfigProperties getObsConfigProperties(){
        return obsConfigProperties;
    }

    public ObsClient getObsClient(){
        return obsClient;
    }

}
