package com.tydic.nicc.dc.boot.starter.test;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.tydic.nicc.dc.boot.starter.minio.MinioConfigProperties;
import com.tydic.nicc.dc.boot.starter.minio.MinioHelper;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateResponse;
import io.minio.CreateMultipartUploadResponse;
import io.minio.messages.Part;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MinioTest {
    public static MinioHelper createMinioHelper(){
        MinioConfigProperties properties = new MinioConfigProperties();
        properties.setAccessKey("8P1oTV9MvSTbxJwi");
        properties.setSecretKey("1HcbEcNTzr7EjPEKsZps4gF8ChyfiHIC");
        //properties.setPort(9090);
        properties.setBucketName("nbchat-dev");
        properties.setEndpoint("http://**************:9090");
        properties.setAccessUrl("https://chatfiles-test.tydiczt.com/files");
        MinioHelper helper = new MinioHelper(properties);
        return helper;
    }

    private final static MinioHelper minioHelper = createMinioHelper();

    public static void main(String[] args) {
        Map<String, String> metadata  = new HashMap<>();
        //设置中文文件名
        metadata.put("response-content-disposition", "attachment;filename=" + "测试文件.mp4");
        System.out.println(minioHelper.getPreGetUrl("tmp/test.txt",metadata));
    }

    @Test
    public void testUpload() {
        try {
            minioHelper.createBucket("tmp");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        CreateMultipartUploadResponse response = minioHelper.createUploadId("tmp/test.txt");
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("partNumber", "1");
        queryParams.put("uploadId", response.result().uploadId());
        String uploadUrl = minioHelper.getPreObjectUrl("tmp/test.txt",queryParams);
        log.info("CreateMultipartUploadResponse:{}|{}|{}",response.result().uploadId(),response.result().bucketName(),response.result().objectName());
        log.info("uploadUrl:{}",uploadUrl);
    }


    final static String object_name = "test/test1.txt";
    @Test
    public void createMultipartUpload() throws Exception {
        MultipartUploadCreateRequest request = MultipartUploadCreateRequest.builder().
                objectName(object_name).chunkCount(1).
                contentType("application/octet-stream").build();
        MultipartUploadCreateResponse response = minioHelper.createMultipartUpload(request);
        log.info("createMultipartUpload:{}", JSONObject.toJSONString(response));
    }

    @Test
    public void completeMultipartUpload() {
        minioHelper.completeMultipartUpload(object_name,
                "MzUxNjQ4MGUtNWFjNS00MGY3LWFmMGUtZmE3ZTA1YzA0MjJlLmNlMjNkNGRjLWI4ZjItNDAyZi1iMzZjLTM0NzY4YjYwNTZhNA");
    }

    @Test
    public void listParts() {
        Multimap<String, String> headers = HashMultimap.create();
        headers.put("Content-Type", "application/octet-stream");
        List<Part> parts = minioHelper.listParts(object_name,
                "MzUxNjQ4MGUtNWFjNS00MGY3LWFmMGUtZmE3ZTA1YzA0MjJlLmNlMjNkNGRjLWI4ZjItNDAyZi1iMzZjLTM0NzY4YjYwNTZhNA");
        log.info("{}|{}",parts.size(),JSONObject.toJSONString(parts));
    }
}
