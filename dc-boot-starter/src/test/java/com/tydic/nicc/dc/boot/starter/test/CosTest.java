package com.tydic.nicc.dc.boot.starter.test;

import com.alibaba.fastjson.JSONObject;
import com.qcloud.cos.model.COSObjectSummary;
import com.qcloud.cos.model.UploadResult;
import com.tydic.nicc.dc.boot.starter.cos.CosConfigProperties;
import com.tydic.nicc.dc.boot.starter.cos.CosHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;

@Slf4j
public class CosTest {

    public static CosHelper createHelper(){
        CosConfigProperties properties = new CosConfigProperties("AKID30aAdyBPYY4e8at5zvkdNtGixzyVwOmE",
                "xxx", "kangkang-1257239235");
        CosHelper helper = new CosHelper(properties);
        return helper;
    }

    @Test
    public void testUploadStream() throws FileNotFoundException {
        CosHelper helper = createHelper();
        File file = new File("/Users/<USER>/Downloads/SecretKey.csv");
        UploadResult result = helper.upload("/test2/" + file.getName(), new FileInputStream(file));
        System.out.println(result.getKey());
        System.out.println(result.getETag());
    }

    @Test
    public void testUpload() {
        CosHelper helper = createHelper();
        UploadResult result = helper.upload("test/pic.png", new File("/Users/<USER>/Downloads/11698891715_.pic.jpg"));
        System.out.println(result.getKey());
        System.out.println(result.getETag());
    }

    @Test
    public void testDownload() throws Exception {
        CosHelper helper = createHelper();
        helper.downloadToFile("test/pic.png", "/Users/<USER>/Downloads/11698891715_.pic_download.jpg");
    }

    @Test
    public void listFile() throws Exception {
        CosHelper helper = createHelper();
        List<COSObjectSummary> summaries =  helper.listFiles("test");
        log.info("{}", JSONObject.toJSON(summaries.get(0)));
    }

    @Test
    public void testDelete() throws Exception {
        CosHelper helper = createHelper();
        helper.delete("/test2/SecretKey.csv");
    }
}
