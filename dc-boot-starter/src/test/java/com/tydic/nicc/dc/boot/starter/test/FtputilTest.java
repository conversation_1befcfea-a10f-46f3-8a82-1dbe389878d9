package com.tydic.nicc.dc.boot.starter.test;

import com.jcraft.jsch.ChannelSftp;
import com.tydic.nicc.dc.boot.starter.ftp.FtpConfigProperties;
import com.tydic.nicc.dc.boot.starter.ftp.SFTPHelper;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Vector;
import java.util.regex.Pattern;

/**
 * @Classname FtputilTest
 * @Description FtputilTest
 * @Date 2022/10/19 17:44
 * @Created by kangkang
 */
public class FtputilTest {

    public static void main(String[] args) throws IOException {
        FtpConfigProperties ftpConf = new FtpConfigProperties();
        ftpConf.setPort(22);
        ftpConf.setUsername("imfiles");
        ftpConf.setPassword("imfiles");
        ftpConf.setHost("*************");
        ftpConf.setHomeDir("/home/<USER>/");
        SFTPHelper sftpHelper = new SFTPHelper(ftpConf);
        Vector vector = sftpHelper.listFile("");
        for (Object o : vector) {
            System.out.println(o);
        }
        List<String> fileLines = sftpHelper.readFileLines("readme.txt", "UTF-8");
        System.out.println(fileLines);

        byte[] data = sftpHelper.download2Byte("readme.txt");
        if(data != null){
            System.out.println(new String(data));
        }

        InputStream stream = sftpHelper.downloadFile("readme.txt");
        if(stream != null){
            System.out.println(stream.available());
        }

        Pattern pattern = Pattern.compile("");
        List<ChannelSftp.LsEntry> list = sftpHelper.listFile("", pattern);
        for (ChannelSftp.LsEntry lsEntry : list) {
            System.out.println(lsEntry.getLongname());
        }
        /*sftpHelper.uploadFile(new FileInputStream("/Users/<USER>/fsdownload/client.zip"), "1019/client.zip");
        new Thread(() -> {
            try {
                while (true) {
                    sftpHelper.uploadFile(new FileInputStream("/Users/<USER>/fsdownload/client.zip"), "client.zip");
                    sftpHelper.release();
                    sftpHelper.deleteFile("client.zip");
                    sftpHelper.release();
                    Thread.sleep(200);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).start();
        new Thread(() -> {
            try {
                while (true) {
                    sftpHelper.uploadFile(new FileInputStream("/Users/<USER>/fsdownload/service-client.zip"), "service-client.zip");
                    sftpHelper.release();
                    sftpHelper.deleteFile("service-client.zip");
                    sftpHelper.release();
                    Thread.sleep(200);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).start();*/

    }

}
