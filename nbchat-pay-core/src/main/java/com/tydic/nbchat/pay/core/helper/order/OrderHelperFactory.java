package com.tydic.nbchat.pay.core.helper.order;

import com.tydic.nbchat.pay.api.bo.TransactionDTO;
import com.tydic.nbchat.pay.core.helper.order.api.OrderHelper;
import com.tydic.nbchat.pay.core.helper.AttachDataHelper;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class OrderHelperFactory {

    private Map<String, OrderHelper> orderHelperMap = new HashMap<>();

    public OrderHelperFactory(List<OrderHelper> orderHelpers){
        this.initOrderHelper(orderHelpers);
    }

    private void initOrderHelper(List<OrderHelper> orderHelpers){
        orderHelpers.forEach(orderHelper -> {
            orderHelperMap.put(orderHelper.orderType(), orderHelper);
        });
    }

    public OrderHelper getPayHelper(String orderType){
        return orderHelperMap.get(orderType);
    }

    public Rsp handler(TransactionDTO request, String attachData) {
        String orderType = AttachDataHelper.getValueByName(attachData, AttachDataHelper.KEY_ORDER_TYPE);
        OrderHelper orderHelper = getPayHelper(orderType);
        if(orderHelper == null){
            log.error("当前订单类型不支持：{}",orderType);
            return BaseRspUtils.createErrorRsp("当前订单类型不支持");
        }
        orderHelper.handler(request);
        return BaseRspUtils.createSuccessRsp("订单处理成功");
    }

}
