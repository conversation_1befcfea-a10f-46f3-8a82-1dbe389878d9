package com.tydic.nbchat.pay.core.controller.notify;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.pay.core.service.notify.AliNotifyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/pay/trade")
public class AliNotifyController {

    @Resource
    AliNotifyServiceImpl aliNotifyService;

    /**
     * ali 回调接口
     *
     */
    @PostMapping("/ali/callback")
    public void wxPayNotify(HttpServletRequest request, HttpServletResponse response){
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            log.info("支付宝-回调通知-开始: {}", JSON.toJSONString(parameterMap));
            aliNotifyService.payNotify(parameterMap);
            response.getOutputStream().write("success".getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            log.error("支付宝-回调通知-异常: {}", request, e);
            String rsp = "fail";
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            try {
                response.getOutputStream().write(rsp.getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
        log.info("支付宝-回调通知-完成：{}",response.getStatus());
    }



}
