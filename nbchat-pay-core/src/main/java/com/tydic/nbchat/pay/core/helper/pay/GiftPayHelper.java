package com.tydic.nbchat.pay.core.helper.pay;

import com.tydic.nbchat.pay.core.enums.PayTypeEnum;
import com.tydic.nbchat.pay.core.helper.pay.api.PayHelper;
import com.tydic.nbchat.pay.core.helper.pay.api.bo.ProduceOrderRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GiftPayHelper implements PayHelper {


    @Override
    public String payType() {
        return PayTypeEnum.EXCHANGE.name();
    }

    @Override
    public Rsp<T> produceOrder(ProduceOrderRequest orderInfo) {
        return BaseRspUtils.createSuccessRsp("");
    }

    @Override
    public Rsp<T> orderQuery(Object obj) {
        return null;
    }

    @Override
    public Rsp<T> closeOrder(Object obj) {
        return null;
    }

    @Override
    public Rsp<T> refund(Object obj) {
        return BaseRspUtils.createErrorRsp("不支持退款");
    }

    @Override
    public Rsp<T> refundQuery(Object obj) {
        return null;
    }
}
