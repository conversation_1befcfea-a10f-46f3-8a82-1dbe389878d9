package com.tydic.nbchat.pay.core.helper.order;

import com.tydic.nbchat.pay.api.bo.PayOrderBO;
import com.tydic.nbchat.pay.api.bo.PayTradeRecordBO;
import com.tydic.nbchat.pay.api.bo.PaypreOrderInfo;
import com.tydic.nbchat.pay.api.bo.TransactionDTO;
import com.tydic.nbchat.pay.api.bo.emus.PayRebateType;
import com.tydic.nbchat.pay.core.busi.order.PayOrderService;
import com.tydic.nbchat.pay.core.enums.OrderStatusEnum;
import com.tydic.nbchat.pay.core.enums.OrderTypeEnums;
import com.tydic.nbchat.pay.core.helper.AttachDataHelper;
import com.tydic.nbchat.pay.core.helper.order.api.OrderHelper;
import com.tydic.nbchat.pay.core.service.PromotionService;
import com.tydic.nbchat.pay.core.utils.RedisKeyUtils;
import com.tydic.nbchat.pay.mapper.PayGoodsSkuDdConfMapper;
import com.tydic.nbchat.pay.mapper.PayOrderItemMapper;
import com.tydic.nbchat.pay.mapper.PayRebateRecordMapper;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSkuDdConf;
import com.tydic.nbchat.pay.mapper.po.PayOrder;
import com.tydic.nbchat.pay.mapper.po.PayOrderItem;
import com.tydic.nbchat.pay.mapper.po.PayRebateRecord;
import com.tydic.nbchat.rebate.api.RebatePayApi;
import com.tydic.nbchat.rebate.api.bo.pay.DeductionReqBO;
import com.tydic.nbchat.rebate.api.bo.pay.DeductionRspBO;
import com.tydic.nbchat.user.api.bo.vip.UserVipOrderContext;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class RechargeOrderHelper implements OrderHelper {

    @Resource
    PayGoodsSkuDdConfMapper payGoodsSkuDdConfMapper;
    @Resource
    PayOrderItemMapper payOrderItemMapper;
    @Resource
    PayRebateRecordMapper payRebateRecordMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    RebatePayApi rebatePayApi;

    private final PayOrderService payOrderService;
    private final PromotionService promotionService;
    private final KKMqProducerHelper kkMqProducerHelper;
    private final RedisHelper redisHelper;

    public RechargeOrderHelper(PayOrderService payOrderService,
                               PromotionService promotionService,
                               KKMqProducerHelper kkMqProducerHelper,
                               RedisHelper redisHelper) {
        this.payOrderService = payOrderService;
        this.promotionService = promotionService;
        this.kkMqProducerHelper = kkMqProducerHelper;
        this.redisHelper = redisHelper;
    }

    @Override
    public String orderType() {
        return OrderTypeEnums.recharge.name();
    }

    @Override
    public void handler(TransactionDTO transaction) {
        log.info("处理充值订单:{}", transaction);

        if (isPayOK(transaction.getOutTradeNo())) {
            return;
        }

        PayOrderBO cacheOrder = payOrderService.getCacheOrder(transaction.getOutTradeNo());
        if (cacheOrder == null) {
            return;
        }

        this.saveRebateRecord(cacheOrder);

        this.savePayInfo(transaction, cacheOrder);

        //this.halfPricePromotion(transaction);
    }

    @Override
    public PaypreOrderInfo getOrder(String orderNo) {
        Rsp<PayOrder> orderInfo = payOrderService.queryOrder(orderNo);
        if (ObjectUtils.isNotEmpty(orderInfo.getData())) {
            PayOrder order = orderInfo.getData();
            PaypreOrderInfo payOrder = new PaypreOrderInfo();
            payOrder.setOrderTime(order.getOrderTime());
            payOrder.setOrderNo(order.getOrderNo());
            payOrder.setBusiType(OrderTypeEnums.recharge.getType());
            payOrder.setTenantCode(order.getTenantCode());
            payOrder.setPrice(order.getTotalPrice());
            return payOrder;
        }
        throw new RuntimeException("订单不存在:"+orderNo);
    }

    /**
     * 保存订单记录、刷新缓存状态、发送充值消息
     *
     * @param transaction
     * @param cacheOrder
     */
    public void savePayInfo(TransactionDTO transaction, PayOrderBO cacheOrder) {
        Rsp<PayTradeRecordBO> rsp = payOrderService.saveOrderInfo(transaction, cacheOrder);
        if (rsp.isSuccess()) {
            payOrderService.flushOrderCache(transaction.getOutTradeNo());
            if (ObjectUtils.isNotEmpty(transaction.getIsGift()) && !transaction.getIsGift()) {
                this.sendRechargeMessage(rsp.getData());
            }
        }
    }

    /**
     * 检查订单是否已支付成功
     *
     * @param orderNo
     * @return
     */
    public Boolean isPayOK(String orderNo) {
        Rsp<PayOrder> orderInfo = payOrderService.queryOrder(orderNo);
        if (ObjectUtils.isNotEmpty(orderInfo.getData()) &&
                orderInfo.getData().getOrderStatus().equals(OrderStatusEnum.PAY_SUCCESS.getCode())) {
            log.info("订单已处理:{}", orderNo);
            return true;
        }
        return false;
    }

    /**
     * 发送充值消息
     *
     * @param trade
     */
    public void sendRechargeMessage(PayTradeRecordBO trade) {
        String orderNo = trade.getOrderNo();
        PayOrderItem orderItem = payOrderItemMapper.selectByOrderId(orderNo);
        PayGoodsSkuDdConf skuDdConf = payGoodsSkuDdConfMapper.selectBySkuId(orderItem.getSkuId());

        UserVipOrderContext context = new UserVipOrderContext();
        BeanUtils.copyProperties(trade, context);
        context.setScore(skuDdConf.getScore());
        context.setDays(skuDdConf.getDays());
        context.setCycle(skuDdConf.getCycle());
        context.setVipType(skuDdConf.getVipType());
        kkMqProducerHelper.sendMsg(NbchatTopicsConstants.NBCHAT_USER_RECHARGE_ORDER_TOPIC, context);
    }


    /**
     * 保存返利记录
     *
     * @param cacheOrder
     */
    public void saveRebateRecord(PayOrderBO cacheOrder) {
        log.info("保存返利记录:{}",cacheOrder);
        if (StringUtils.isEmpty(cacheOrder.getCouponId()) && cacheOrder.getDiscountPoints() == null) {
            return;
        }

        DeductionReqBO reqBO = new DeductionReqBO();
        PayRebateRecord po = new PayRebateRecord();

        if (StringUtils.isNotEmpty(cacheOrder.getCouponId())) {
            po.setCouponId(cacheOrder.getCouponId());
            po.setType(PayRebateType.COUPON.getCode());
            reqBO.setType(PayRebateType.COUPON.getCode());
        } else  {
            po.setType(PayRebateType.POINT.getCode());
            reqBO.setType(PayRebateType.POINT.getCode());
        }

        BeanUtils.copyProperties(cacheOrder, reqBO);
        reqBO.setPoint(cacheOrder.getDiscountPoints());
        Rsp<DeductionRspBO> rsp = rebatePayApi.deduction(reqBO);
        if (!rsp.isSuccess()) {
            log.error("扣减失败:{}", rsp);
            //TODO 先让用户支付成功，下版本再处理
            return;
        }

        po.setOrderNo(cacheOrder.getOrderNo());
        po.setUserId(cacheOrder.getUserId());
        po.setTenantCode(cacheOrder.getTenantCode());
        po.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(cacheOrder.getCouponId())) {
            po.setCouponId(cacheOrder.getCouponId());
            payRebateRecordMapper.insertSelective(po);
        } else {
            for (String id : rsp.getData().getPointDetailId()) {
                po.setPointDetailId(id);
                payRebateRecordMapper.insertSelective(po);
            }
        }
    }


    //半价活动
    public void halfPricePromotion(TransactionDTO transaction) {
        // 如果是活动，检查redis里有没有，如果有，删除已经使用的价格，如果没有，添加活动价格列表
        if (AttachDataHelper.containsKey(transaction.getAttach(), AttachDataHelper.KEY_SESSION)) {
            String sessionKey = AttachDataHelper.getValueByName(transaction.getAttach(), AttachDataHelper.KEY_SESSION);
            String key = RedisKeyUtils.getPromotionSessionKey(sessionKey);
            if (redisHelper.hasKey(key)) {
                payOrderService.delPromotionCache(sessionKey, transaction.getAmount());
            } else {
                PayOrderItem orderItem = payOrderItemMapper.selectByOrderId(transaction.getOutTradeNo());
                promotionService.createPromotionCache(orderItem.getSkuId(), sessionKey);
            }
        }
    }

}
