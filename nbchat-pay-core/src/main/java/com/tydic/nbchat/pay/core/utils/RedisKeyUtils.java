package com.tydic.nbchat.pay.core.utils;

public class RedisKeyUtils {
    // 生成订单key
    public static String getOrderKey(String orderId) {
        return "nbchat-pay:order_key:" + orderId;
    }

    // 生成活动sessionKey
    public static String getPromotionSessionKey(String sessionKey) {
        return "nbchat-pay:promotion:session:" + sessionKey;
    }

    // 获取活动价格
    public static String getPromotionPriceKey(String skuId) {
        return "nbchat-pay:promotion:price:" + skuId;
    }
}
