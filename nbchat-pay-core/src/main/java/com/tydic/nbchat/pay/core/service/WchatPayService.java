package com.tydic.nbchat.pay.core.service;

import com.tydic.nbchat.pay.api.bo.PaymentRequest;
import com.tydic.nbchat.pay.api.bo.PaypreOrderInfo;
import com.tydic.nbchat.pay.core.config.properties.WxPayProperties;
import com.tydic.nbchat.pay.core.enums.OrderTypeEnums;
import com.tydic.nbchat.pay.core.helper.order.OrderHelperFactory;
import com.tydic.nbchat.pay.core.helper.order.api.OrderHelper;
import com.tydic.nbchat.pay.core.utils.CommonPayUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WchatPayService {

    private final WxPayProperties wxPayProperties;
    private final OrderHelperFactory orderHelperFactory;

    public WchatPayService(WxPayProperties wxPayProperties,
                           OrderHelperFactory orderHelperFactory) {
        this.wxPayProperties = wxPayProperties;
        this.orderHelperFactory = orderHelperFactory;
    }

    /**
     * 支付
     * @param request
     * @return
     */
    public Rsp payment(PaymentRequest request){
        try {
            log.info("微信jsapi支付：{}", request);
            String code = OrderTypeEnums.getCodeByType(request.getBusiType());
            if(StringUtils.isBlank(code)){
                return BaseRspUtils.createErrorRsp("当前订单类型不支持: " + request.getBusiType());
            }
            OrderHelper orderHelper = orderHelperFactory.getPayHelper(code);
            PaypreOrderInfo order = orderHelper.getOrder(request.getOrderId());
            PrepayWithRequestPaymentResponse response = CommonPayUtil.prepayWithRequestPayment(wxPayProperties,order);
            log.info("微信jsapi支付-返回：{}", response);
            return BaseRspUtils.createSuccessRsp(response);
        } catch (Exception e) {
            log.error("微信jsapi支付-异常：{}", e.getMessage(), e);
            return BaseRspUtils.createErrorRsp("预支付处理异常");
        }
    }
}
