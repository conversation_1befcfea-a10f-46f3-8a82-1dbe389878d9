package com.tydic.nbchat.pay.core.helper.pay.api.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProduceOrderRequest {
    private Integer amount;     // 支付金额/分
    private String description; // 商品描述
    private String orderNo;     // 系统订单号
    private String attach;      // 附加数据

    private String refundNo;    // 退款流水号
}
