package com.tydic.nbchat.pay.core.helper.pay;

import com.tydic.nbchat.pay.core.helper.pay.api.PayHelper;
import com.tydic.nbchat.pay.core.helper.pay.api.bo.ProduceOrderRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PayHelperFactory {

    private Map<String, PayHelper> payHelperMap = new HashMap<>();

    public PayHelperFactory(List<PayHelper> payHelpers) {
        this.initPayHelper(payHelpers);

    }

    public void initPayHelper(List<PayHelper> payHelpers) {
        payHelpers.forEach(payHelper -> {
            payHelperMap.put(payHelper.payType(), payHelper);
        });
    }

    public PayHelper getPayHelper(String payType) {
        return payHelperMap.get(payType);
    }

    public Rsp produce(ProduceOrderRequest request, String payType) {
        PayHelper payHelper = payHelperMap.get(payType);
        if (payHelper == null) {
            log.error("当前支付方式不支持：{}", payType);
            return BaseRspUtils.createErrorRsp("当前支付方式不支持");
        }
        return payHelper.produceOrder(request);
    }

    public Rsp refund(ProduceOrderRequest request, String payType) {
        PayHelper payHelper = payHelperMap.get(payType);
        if (payHelper == null) {
            log.error("支付方式不存在：{}", payType);
            return BaseRspUtils.createErrorRsp("支付方式不存在");
        }
        return payHelper.refund(request);
    }

}
