package com.tydic.nbchat.pay.core.enums;

import lombok.Getter;

@Getter
public enum SpuTypeEnum {
//商品类型：0 实体商品 / 1 算力点会员 / 2 算力点加油包 /3 定制数字人 /4定制语音
    ENTITY_GOODS("0", "实体商品",""),
    VIP_GOODS("1", "课件帮会员",""),
    OIL_GOODS("2", "算力点加油包",""),
    CUSTOMIZE_DIGITAL("3", "定制数字人","2d"),
    CUSTOMIZE_VOICE("4", "定制语音","audio"),
    CUSTOMIZE_25D_DIGITAL("5", "2.5d_mtk视频数字人","2.5d_mtk")
    ;
    private final String code;
    private final String desc;
    private final String type;


    SpuTypeEnum(String code, String desc, String type) {
        this.code = code;
        this.desc = desc;
        this.type = type;
    }

    public static String getType(String code) {
        for (SpuTypeEnum spuTypeEnum : SpuTypeEnum.values()) {
            if (spuTypeEnum.getCode().equals(code)) {
                return spuTypeEnum.getType();
            }
        }
        return null;
    }

}
