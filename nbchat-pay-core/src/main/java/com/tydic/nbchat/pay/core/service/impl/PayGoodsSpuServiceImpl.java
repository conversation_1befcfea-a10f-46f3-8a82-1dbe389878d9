package com.tydic.nbchat.pay.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.pay.api.PayGoodsSpuApi;
import com.tydic.nbchat.pay.api.bo.PayGoodsSkuRspBO;
import com.tydic.nbchat.pay.api.bo.PayGoodsSpuReqBO;
import com.tydic.nbchat.pay.api.bo.PayGoodsSpuRspBO;
import com.tydic.nbchat.pay.mapper.PayGoodsSkuMapper;
import com.tydic.nbchat.pay.mapper.PayGoodsSpuMapper;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSku;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSpu;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class PayGoodsSpuServiceImpl implements PayGoodsSpuApi {
    private final PayGoodsSpuMapper payGoodsSpuMapper;
    private final PayGoodsSkuMapper payGoodsSkuMapper;
    @Override
    public RspList list(PayGoodsSpuReqBO request) {
        PayGoodsSpu spu = new PayGoodsSpu();
        BeanUtils.copyProperties(request, spu);
        spu.setIsValid(EntityValidType.NORMAL.getCode());
        Page<PayGoodsSpu> page = PageHelper.startPage(request.getPage(), request.getLimit());
        payGoodsSpuMapper.selectAll(spu);
        if (page.getTotal() > 0) {
            log.info("查询spu:{}-查询到数据：{}", request,page.getTotal());
            List<PayGoodsSpuRspBO> payGoodsSpuRspBOList = new ArrayList<>();
            List<PayGoodsSpu> res = page.getResult();
            NiccCommonUtil.copyList(res, payGoodsSpuRspBOList, PayGoodsSpuRspBO.class);
            payGoodsSpuRspBOList.forEach(item -> {
                PayGoodsSku record = new PayGoodsSku();
                record.setSpuId(item.getSpuId());
                List<PayGoodsSku> skuList = payGoodsSkuMapper.selectBySelective(record);
                if (CollectionUtils.isNotEmpty(skuList)){
                    log.info("查询spu:{}-查询到sku数据：{}", request,skuList.size());
                    List<PayGoodsSkuRspBO> skuRspBOList = new ArrayList<>();
                    NiccCommonUtil.copyList(skuList, skuRspBOList, PayGoodsSkuRspBO.class);
                    item.setSkuList(skuRspBOList);
                }
            });
            return BaseRspUtils.createSuccessRspList(payGoodsSpuRspBOList, page.getTotal());
        }
        log.info("查询spu:{}-未查询到数据",request);
        return BaseRspUtils.createSuccessRspList(new ArrayList<PayGoodsSpu>(), page.getTotal());
    }
}
