package com.tydic.nbchat.pay.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-pay.config.ali-pay")
public class AliPayProperties {

    /** 回调地址 */
    private String notifyUrl;
    /** 支付宝应用ID */
    private String appId = "2021005107643721";
    /** 商户私钥 */
    private String merchantPrivateKey = "https://chatfiles.tydiczt.com/files/ali/appPrivateKeyRSA2048.txt";
    /** 商户证书序列号 */
    private String merchantCertPath = "https://chatfiles.tydiczt.com/files/ali/appCertPublicKey_2021005107643721.crt";
    /** 支付宝公钥 */
    private String alipayCertPath = "https://chatfiles.tydiczt.com/files/ali/alipayCertPublicKey_RSA2.crt";
    /** 支付宝根证书 */
    private String alipayRootCertPath = "https://chatfiles.tydiczt.com/files/ali/alipayRootCert.crt";
}
