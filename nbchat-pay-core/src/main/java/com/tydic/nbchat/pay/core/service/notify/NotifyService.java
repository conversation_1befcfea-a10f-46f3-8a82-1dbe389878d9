package com.tydic.nbchat.pay.core.service.notify;

import com.tydic.nbchat.pay.api.bo.TransactionDTO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface NotifyService {

    String notifyType();

    /**
     * 支付回调接口
     */
    void payNotify(Object request);

    /**
     * 转换Transaction
     * @param transaction
     * @return
     */
    TransactionDTO convert(Object transaction);

    /**
     * 退款回调接口
     * @param request
     * @return
     */
    Rsp refundNotify(Object request);


}
