package com.tydic.nbchat.pay.core.service.impl;

import com.tydic.nbchat.pay.api.QueryCustomizePriceService;
import com.tydic.nbchat.pay.api.bo.CustomizeReqBO;
import com.tydic.nbchat.pay.api.bo.CustomizeRspBO;
import com.tydic.nbchat.pay.core.enums.SpuTypeEnum;
import com.tydic.nbchat.pay.mapper.PayGoodsSkuMapper;
import com.tydic.nbchat.pay.mapper.PayGoodsSpuMapper;
import com.tydic.nbchat.pay.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSku;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSpu;
import com.tydic.nbchat.pay.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.user.api.UserVipRightsApi;
import com.tydic.nbchat.user.api.bo.eums.RightsTypeEnum;
import com.tydic.nbchat.user.api.bo.vip.NbchatUserVipRightsBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class QueryCustomizeServiceImpl implements QueryCustomizePriceService {

    @Resource
    PayGoodsSpuMapper spuMapper;
    @Resource
    PayGoodsSkuMapper skuMapper;
    @Resource
    TdhCustomizeRecordMapper tdhCustomizeRecordMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private UserVipRightsApi userVipRightsApi;

    private final List<String> person = Arrays.asList("1005","1006","1009");
    private final List<String> enterprise = Arrays.asList("1007","1008","1010");

    @Override
    public Rsp query(CustomizeReqBO request) {
        log.info("查询定制价格信息:{}", request);
        //查询售价
        PayGoodsSku sku = this.queryCustomizeSalePrice(request.getTenantCode(), request.getSpuType());
        if (ObjectUtils.isEmpty(sku)) {
            log.error("未找到定制商品:{}", request.getSpuType());
            return BaseRspUtils.createErrorRsp("未找到定制商品");
        }
        CustomizeRspBO rsp = new CustomizeRspBO();
        rsp.setSkuId(sku.getSkuId());
        rsp.setPrice(sku.getSalePrice());
        rsp.setOriginPrice(sku.getSalePrice());

        //查询vip权益
        RspList rspList = this.queryVipRights(request);
        if (!rspList.isSuccess()) {
            log.error("查询用户权益失败:{}", rspList);
            return BaseRspUtils.createErrorRsp("查询用户权益失败");
        }

        List<NbchatUserVipRightsBO> rows = rspList.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return BaseRspUtils.createSuccessRsp(rsp);
        }
        //计算权益剩余次数
        int sum = rows.stream().mapToInt(v -> v.getRightsCount() - v.getUsedCount()).sum();
        rsp.setCount(sum);

        if (sum > 0) {
            rsp.setPrice(0);
            return BaseRspUtils.createSuccessRsp(rsp);
        } else {
            return BaseRspUtils.createSuccessRsp(rsp);
        }
    }

    public PayGoodsSku queryCustomizeSalePrice(String tenantCode, String spuType) {
        PayGoodsSpu spu = PayGoodsSpu.builder().spuType(spuType).build();
        List<PayGoodsSpu> payGoodsSpus = spuMapper.selectAll(spu);
        if (CollectionUtils.isEmpty(payGoodsSpus)) {
            return null;
        }
        PayGoodsSpu goodsSpu = null;
        if ("00000000".equals(tenantCode)) {
            for (PayGoodsSpu goods : payGoodsSpus) {
                if (person.contains(goods.getSpuId())) {
                    goodsSpu = goods;
                }
            }
        } else {
            for (PayGoodsSpu goods : payGoodsSpus) {
                if (enterprise.contains(goods.getSpuId())) {
                    goodsSpu = goods;
                }
            }
        }
        if (ObjectUtils.isEmpty(goodsSpu)) {
            return null;
        }
        PayGoodsSku sku = PayGoodsSku.builder().spuId(goodsSpu.getSpuId()).build();
        List<PayGoodsSku> payGoodsSkus = skuMapper.selectBySelective(sku);
        if (CollectionUtils.isEmpty(payGoodsSkus)) {
            return null;
        }
        return payGoodsSkus.get(0);
    }

    public RspList queryVipRights(CustomizeReqBO request) {
        NbchatUserVipRightsBO cond = NbchatUserVipRightsBO.builder()
                .userId(request.getUserId()).isValid(EntityValidType.NORMAL.getCode())
                .rightsStatus("0")
                .build();
        if (!"00000000".equals(request.getTenantCode())) {
            cond.setUserId(request.getTenantCode());
        }
        cond.setCustomizeType(SpuTypeEnum.getType(request.getSpuType()));
        if (request.getSpuType().equals(SpuTypeEnum.CUSTOMIZE_DIGITAL.getCode())) {
            cond.setRightsType(RightsTypeEnum.TDH.getCode());
        }
        if (request.getSpuType().equals(SpuTypeEnum.CUSTOMIZE_VOICE.getCode())) {
            cond.setRightsType(RightsTypeEnum.AUDIO.getCode());
        }
        if (request.getSpuType().equals(SpuTypeEnum.CUSTOMIZE_25D_DIGITAL.getCode())) {
            cond.setRightsType(RightsTypeEnum.TDH_25D_MTK.getCode());
        }
        return userVipRightsApi.query(cond);
    }


    @Override
    public Rsp queryOrderInfo(CustomizeReqBO request) {
        if (StringUtils.isEmpty(request.getOrderNo())) {
            return BaseRspUtils.createErrorRsp("订单号不能为空");
        }
        TdhCustomizeRecord customizeRecord = tdhCustomizeRecordMapper.queryByOrderId(request.getOrderNo());
        return BaseRspUtils.createSuccessRsp(customizeRecord);
    }
}
