package com.tydic.nbchat.pay.api.bo.count;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class PayPriceCountBO implements Serializable {
    //分别统计本月，本周，当天的支付金额
    private PriceCount month;
    private PriceCount week;
    private PriceCount day;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceCount {
        private Integer totalPrice;
        //区分不同业务的支付金额
        // tdh: 3300 , vip : 1000
        private Map<String, Integer> countMap;

        public PriceCount(Integer totalAmount, Integer personAmount, Integer personFirstAmount, Integer personRepeatAmount, Integer enterpriseAmount) {
            this.totalPrice = totalAmount;
            countMap = new HashMap<>();
            countMap.put("personAmount", personAmount);
            countMap.put("personFirstAmount", personFirstAmount);
            countMap.put("personRepeatAmount", personRepeatAmount);
            countMap.put("enterpriseAmount", enterpriseAmount);
        }
    }
}
