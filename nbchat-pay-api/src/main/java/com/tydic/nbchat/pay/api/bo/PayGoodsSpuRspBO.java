package com.tydic.nbchat.pay.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PayGoodsSpuRspBO implements Serializable {
    /**
     * 主键
     */
    private String spuId;
    /**
     * spu名称
     */
    private String spuName;
    /**
     * 商品描述
     */
    private String desc1;
    /**
     * 商品描述
     */
    private String desc2;
    /**
     * 商品类型：0 实体商品 / 1 算力点会员 / 2 算力点加油包
     */
    private String spuType;
    /**
     * 商品状态 0下架 1上架
     */
    private String spuStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否有效 0无效 1有效
     */
    private String isValid;
    /**
     * 排序
     */
    private Integer orderIndex;

    private List<PayGoodsSkuRspBO> skuList;
}
