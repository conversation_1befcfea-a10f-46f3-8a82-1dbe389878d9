package com.tydic.nbchat.pay.api.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDTO {

    /**
     * 交易金额 /分
     */
    private Integer amount;
    /**
     * 商户订单号
     */
    private String outTradeNo;
    /**
     * 交易系统订单id
     */
    private String transactionId;
    /**
     * 订单状态
     */
    private String tradeState;
    private String tradeStateDesc;
    /**
     * 交易成功时间
     */
    private String successTime;
    /**
     * 附加数据
     */
    private String attach;
    /**
     * 交易渠道
     */
    private String tradeType;
    /**
     * 交易方式:WX ALI
     */
    private String payType;

    private Date startTime;
    private Date endTime;
    private Boolean isGift = Boolean.FALSE;

}
