package com.tydic.nbchat.pay.api;

import com.tydic.nbchat.pay.api.bo.count.PayOrderSkuBO;
import com.tydic.nbchat.pay.api.bo.count.PayPriceCountBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountReqBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 支付运营数据统计
 */
public interface PayOpDataCountApi {

    /**
     * 获取支付统计数据
     * @param request
     * @return
     */
    RspList<PayOrderCountBO> countPayDataList(PayOrderCountReqBO request);

    /**
     * 获取支付金额统计数据
     * @param request
     * @return
     */
    Rsp<PayPriceCountBO> countPayPrice(PayOrderCountReqBO request);

    /**
     * 获取商品列表
     * @param request
     * @return
     */
    RspList<PayOrderSkuBO> getSkuList(PayOrderCountReqBO request);

    /**
     * 导出支付统计数据
     * @param queryReqBO
     * @return
     */

    Rsp exportPayDataList(PayOrderCountReqBO queryReqBO);
}
