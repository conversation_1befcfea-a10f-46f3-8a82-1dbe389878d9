package com.tydic.nbchat.pay.api.bo;

import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PayPersonalInvoicePriceRspBO implements Serializable {
    /**
     * 总支付金额
     */
    private Integer totalAmount=0;

    /**
     * 已开票金额
     */
    private Integer issuedAmount=0;

    /**
     * 剩余可开票金额
     */
    private Integer remainingAmount=0;

    private List<PayOrderCountBO> payOrderCountBOList;

}
