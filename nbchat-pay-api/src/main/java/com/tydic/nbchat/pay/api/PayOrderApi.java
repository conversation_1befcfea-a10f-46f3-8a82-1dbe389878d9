package com.tydic.nbchat.pay.api;

import com.tydic.nbchat.pay.api.bo.PayCreateOrderReqBO;
import com.tydic.nbchat.pay.api.bo.PayQueryOrderReqBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface PayOrderApi {

    //特权token
    String TOKEN = "QxNjAiLCJleHAiOjE3NDI1MDc3MzEsImlh";


    /**
     * 创建订单
     * @param request
     * @return
     */
    Rsp createOrder(PayCreateOrderReqBO request);

    /**
     * 退款订单
     * @param request
     * @return
     */
    Rsp refundOrder(PayCreateOrderReqBO request);

    /**
     * 查询订单
     * @param request
     * @return
     */
    Rsp queryOrder(PayQueryOrderReqBO request);

    /**
     * 查询我的订单
     * @param request
     * @return
     */
    RspList<PayOrderCountBO> queryMyOrder(PayQueryOrderReqBO request);
}
