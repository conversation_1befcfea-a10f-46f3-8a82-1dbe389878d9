package com.tydic.nbchat.pay.api.bo.emus;

/**
 * 开票状态
 */
public enum InvoiceStatusType {
    PENDING("0", "待开具/审核中"),
    IN_PROGRESS("1", "开票中"),
    ISSUED("2", "已开票"),
    VOID("3", "作废"),
    NOT_INVOICED("4", "未开票"),
    REFUNDED("5", "已退款 不可开票"),
    UNKNOWN("n","未知");

    private String code;
    private String name;


    public boolean equals(String code) {
        return this.code.equals(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private InvoiceStatusType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String InvoiceStatusType(String code) {
        for (InvoiceStatusType field : InvoiceStatusType.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return UNKNOWN.getName();
    }
}
