package com.tydic.nbchat.pay.api.bo.count;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
public class PayOrderCountReqBO extends BasePageInfo implements Serializable {
    // 区分业务类型：1-会员支付 2-数字人/声音定制支付
    @NotEmpty
    private String busiType;
    //查询条件：商品名称、支付时间、租户名称、用户手机号
    @Size(max = 50)
    private String goodsName;
    private Date payStartTime;
    private Date payEndTime;
    @Size(max = 30)
    private String targetTenant;
    @Size(max = 11)
    private String phone;
    @Size(max = 32)
    private String skuId;
    private String targetUserId;
    /**
     * 订单状态
     */
    private String orderStatus;
    private Boolean isPaged = true; // 默认分页
    /**
     * 支付类型
     * 0-个人支付
     * 1-企业支付
     */
    private String paymentType;
    /**
     * 支付属性
     * 0 - 首次-新注册
     * 1 - 首次-历史注册
     * 2 - 老客-附购
     * 3 - 老客-升级
     * 4 - 老客-续费
     * 5 - 老客-回流
     */
    private String paymentAttr;
}
