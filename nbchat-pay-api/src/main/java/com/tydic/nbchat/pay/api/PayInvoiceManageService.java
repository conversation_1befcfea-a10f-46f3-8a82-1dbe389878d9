package com.tydic.nbchat.pay.api;

import com.tydic.nbchat.pay.api.bo.PayInvoiceCountReqBo;
import com.tydic.nbchat.pay.api.bo.PayInvoiceRecordBo;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayInvoiceRecodUpdateReqBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayInvoiceUpdateReqBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayUserOrderReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

import javax.validation.Valid;

public interface PayInvoiceManageService {
    /**
     * 发票记录列表
     * @param request
     * @return
     */
    RspList<PayInvoiceRecordBo> recordList(PayInvoiceCountReqBo request);

    /**
     * 查询用户订单
     * @param request
     * @return
     */
    RspList<PayOrderCountBO> queryUserOrder(@Valid PayUserOrderReqBO request);

    /**
     * 发票记录更新
     * @param request
     * @return
     */
    Rsp recordUpdate(PayInvoiceUpdateReqBO request);

    /**
     * 批量更新发票状态
     * @param request
     * @return
     */
    Rsp batchRecordUpdate(PayInvoiceRecodUpdateReqBO request);
    /**
     * 发票记录导出
     */
    Rsp exportPayDataList(PayInvoiceCountReqBo queryReqBO);
}
