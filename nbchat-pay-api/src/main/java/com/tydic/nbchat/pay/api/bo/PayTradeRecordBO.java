package com.tydic.nbchat.pay.api.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付流水记录(PayTradeRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-06-05 10:37:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayTradeRecordBO implements Serializable {
    private static final long serialVersionUID = -82896310446783708L;

    private Date orderTime;
/**
     * 主键
     */
    private String tradeNo;
/**
     * 租户id
     */
    private String tenantCode;
/**
     * 用户id
     */
    private String userId;
/**
     * 支付流水号
     */
    private String payNo;
/**
     * 支付渠道 JSAPI：公众号支付
                                                    NATIVE：扫码支付
                                                    App：App支付
                                                    MICROPAY：付款码支付
                                                    MWEB：H5支付
                                                    FACEPAY：刷脸支付
     */
    private String channel;
/**
     * 订单号
     */
    private String orderNo;
/**
     * 支付金额/分
     */
    private Integer payPrice;
/**
     * 支付状态: 1成功 2失败
     */
    private String payStatus;
/**
     * 支付状态描述
     */
    private String payDesc;
/**
     * 支付时间
     */
    private Date payTime;
/**
     * 支付方式 1微信 2支付宝 3银联
     */
    private String payType;
/**
     * 通知内容
     */
    private String notifyContent;


}

