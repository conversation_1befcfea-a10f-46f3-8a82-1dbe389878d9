package com.tydic.nbchat.pay.api.bo.emus;

public enum PayChannelEmus {

    NATIVE("NATIVE", "扫码支付"),

    WX_JSAPI("JSAPI", "公众号支付"),
    WX_APP("APP", "App支付"),
    WX_MICROPAY("MICROPAY", "付款码支付"),
    WX_MWEB("MWEB", "H5支付"),
    WX_FACEPAY("FACEPAY", "刷脸支付"),

    ALIPAY_APP("app", "支付宝app支付"),
    ALIPAY_WAP("wap", "支付宝wap支付"),
    ALIPAY_FACEPAY("facepay", "支付宝刷脸支付"),

    DOU("dou", "迪豆支付")
    ;

    private final String code;
    private final String desc;

    PayChannelEmus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
