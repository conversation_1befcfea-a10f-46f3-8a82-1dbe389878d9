package com.tydic.nbchat.pay.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PayInvoiceCountReqBo extends BasePageInfo implements Serializable {
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 开票状态  0-待开具 1-开票中 2-已开票 3-作废
     */
    private String invoiceStatus;
    /**
     * 抬头类型 1-个人 2-公司
     */
    private String invoiceType;
    /**
     * 发票抬头
     */
    @Size(max = 50)
    private String invoiceTitle;
    /**
     * 手机号
     */
    @Size(max = 11)
    private String phone;
    /**
     * 用户id
     */
    private String tradeUserId;
    /**
     * 申请开始时间
     */
    private Date createStartTime;
    /**
     * 申请结束时间
     */
    private Date createEndTime;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 批量导出发票
     */
    private List<String> invokerList;

}
