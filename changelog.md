## 版本变更记录

### v1.0.0

- 课程培训上线
- 数据库变更，参考 init/nbchat.sql

```yaml
nbchat-train:
  config:
    page-join-enable: true #开启拼接
    page-join-word-limit: 500 #按页拼接字符上限
    ali-config:
      access-key: LTAI5tJ542cAJW4YdxMabuix
      secret-key: ******************************
      tts:
        app-key: GjWfzDg7eLGuz32m
        tts-api: https://nls-gateway.cn-shanghai.aliyuncs.com/rest/v1/tts/async
```

### v1.0.1

```sql
-- 新增课程培训视频地址
ALTER TABLE nbchat_train_sections
    ADD COLUMN video_url VARCHAR(100) DEFAULT NULL;
ALTER TABLE nbchat_train_sections
    ADD COLUMN video_img VARCHAR(100) DEFAULT NULL;
```

```sql
-- 新增课程培训视频地址
ALTER TABLE nbchat_train_course
    ADD COLUMN video_url VARCHAR(100) DEFAULT NULL;
ALTER TABLE nbchat_train_course
    ADD COLUMN video_img VARCHAR(100) DEFAULT NULL;
```

```sql
-- 新增用户学习记录表
CREATE TABLE `nbchat_train_user_record`
(
    `id`          int                                     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `catalog_id`  varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录id',
    `user_id`     varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

```sql
-- 删除错误数据
delete
from nbchat_train_record;
DELETE
from nbchat_exam_test_record;
update nbchat_train_catalog
set train_count = 0;
```

### v1.0.2

- 课程支持二级分类
- 新增asr录音文件识别功能
- 新增私有租户、迁移课程到私有租户
- 脚本变更，后执行 20230704.sql

```mysql
-- 课程分类表
CREATE TABLE `nbchat_train_course_category`
(
    `cate_id`     varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录id',
    `cate_name`   varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录名称',
    `parent_id`   varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '-1' COMMENT '上级id',
    `create_time` datetime                                        DEFAULT NULL COMMENT '创建时间',
    `tenant_code` varchar(50) COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '租户',
    `is_valid`    char(1) COLLATE utf8mb4_general_ci              DEFAULT '1',
    `cate_level`  smallint unsigned                               DEFAULT '1' COMMENT '分类级别 1 2 ',
    PRIMARY KEY (`cate_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

ALTER TABLE nbchat_train_course
    ADD COLUMN category2 VARCHAR(50) DEFAULT '' COMMENT '二级目录';

-- 添加索引
alter table nbchat_train_record
    add index idx_user_id (user_id);
alter table nbchat_exam_test_record
    add index idx_user_id (user_id);

-- 课程类目 添加排序字段
ALTER TABLE nbchat_train_course_category
    ADD COLUMN order_index int DEFAULT NULL COMMENT '排序';
ALTER TABLE nbchat_train_course_category
    ADD COLUMN cate_name_nick VARCHAR(100) COMMENT '目录昵称';

-- 更新到私有租户
UPDATE `nbchat_train_course`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_train_catalog`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_train_sections`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_train_files_part`
SET `tenant_code` = '00010010'
WHERE `file_id`
          IN (SELECT file_id
              from nbchat_train_files
              WHERE course_id IN
                    ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                     '281525998031155200A'));
UPDATE `nbchat_train_files`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_train_scene_dialogue`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_exam_question`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');
UPDATE `nbchat_exam_test_record`
SET `tenant_code` = '00010010'
WHERE `course_id` IN ('281551586324992000A', '281568800851423232A', '281538593902120960A', '281534006067548160A',
                      '281525998031155200A');

-- 更新文件资源为cdn路径
UPDATE nbchat_train_course
SET video_url = REPLACE(video_url, 'chat', 'chatfiles');
UPDATE nbchat_train_sections
SET video_url = REPLACE(video_url, 'chat', 'chatfiles');

-- 场景实践提示词模板
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`,
                                             `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`,
                                             `is_valid`)
VALUES ('train_scene_dialogue_eval', '00000000', '-1', NULL,
        '以下是一个场景对话，请对比标准答案对客服的回答进行评分（请你参照标准答案，按照客服的回答话术专业度和回答相似程度评分，评分范围 0~100），请严格评分，并对回答进行分析指出不足：\n问题：{{ #ARG0 }}\n标准答案：{{ #ARG1 }}\n客服回答：{{ #ARG2 }}\n请按照一下模板返回你的结果：\n{\n   \"score\":\"$YOUR_SCORE\",\n   \"explanation\":\"$YOUR_ANSWER_EXPLANATION\"\n}',
        '场景对话评价', '对话评价', '2023-06-29 17:31:45', '', 0, '1');

```

### v1.0.3

- 课程学习相关报表统计
- 新增数据表：
  学习人数明细表 nbchat_train_rp_study_pn （is_finish 记录是否学完）
  学习人次明细表 nbchat_train_rp_study_pt
  指标日报表 nbchat_train_rp_day_item

```mysql

CREATE TABLE `nbchat_train_rp_study_pt`
(
    `id`          bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `tenant_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
    `course_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程id',
    `user_id`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
    `date_day`    date                                                         NOT NULL COMMENT '数据日期',
    `date_time`   datetime                                                     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_date_time` (`date_time`) USING BTREE COMMENT '时间范围索引'
) ENGINE = InnoDB
  AUTO_INCREMENT = 80
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='学习人次明细表';

CREATE TABLE `nbchat_train_rp_study_pn`
(
    `id`          bigint unsigned                                          NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `tenant_code` varchar(30) COLLATE utf8mb4_general_ci                            DEFAULT NULL COMMENT '租户id',
    `course_id`   varchar(50) COLLATE utf8mb4_general_ci                   NOT NULL COMMENT '课程id',
    `user_id`     varchar(50) COLLATE utf8mb4_general_ci                   NOT NULL COMMENT '用户id',
    `date_day`    date                                                     NOT NULL COMMENT '数据日期',
    `date_time`   datetime                                                          DEFAULT NULL COMMENT '创建时间',
    `is_finish`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否学完',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni_pn` (`course_id`, `user_id`, `date_day`) USING BTREE COMMENT '每人每天只能算作一次',
    KEY `idx_date_time` (`date_time`) USING BTREE COMMENT '时间范围查询'
) ENGINE = InnoDB
  AUTO_INCREMENT = 74
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='学习人数明细表';

CREATE TABLE `nbchat_train_rp_day_item`
(
    `id`          bigint unsigned                        NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `tenant_code` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户',
    `course_id`   varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程',
    `item_code`   varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '指标编码',
    `item_value`  double unsigned                        NOT NULL COMMENT '指标值',
    `count_day`   date                                   NOT NULL COMMENT '指标日期',
    `date_time`   datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '数据时间',
    PRIMARY KEY (`id`),
    KEY `idx_count_day` (`count_day`) USING BTREE COMMENT '日期范围'
) ENGINE = InnoDB
  AUTO_INCREMENT = 63
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

```

### v1.1.0

- 课程状态处理

```sql
 alter table nbchat_train_course
    add column `course_state` char(1) comment '0 编辑中 1 预览中 2 上架中 3 已下架 ';
alter table nbchat_train_course
    add column `step_state` char(1) comment '1.基础信息  2.课程文档已解析  3.课程简介生成  4.目录生成  5.学习内容生成 ';
alter table nbchat_train_course
    add column `task_state` char(1) comment '0 任务未执行 1 任务执行中 2 任务异常 3执行结束';

ALTER TABLE `nbchat`.`nbchat_train_course`
    ADD COLUMN `course_file_url` varchar(255) NULL COMMENT '课程课件' AFTER `task_state`;
-- 更改字段长度
ALTER TABLE nbchat_preset_prompt MODIFY COLUMN preset_id VARCHAR (50) COMMENT '主键';
CREATE INDEX idx_course ON nbchat_train_record (course_id);
-- 创建课程原始内容表
CREATE TABLE `nbchat`.`nbchat_train_course_text`
(
    `course_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程id',
    `text`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容-长文本',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`course_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
```

### v1.2.0

- 试题-对话管理

```sql
-- 租户-添加真实姓名字段
ALTER TABLE nbchat_sys_user_tenant
    ADD COLUMN user_reality_name VARCHAR(30) default '';

-- 创建试卷配置表
CREATE TABLE `nbchat_exam_test_paper`
(
    `paper_id`      int                                                      NOT NULL AUTO_INCREMENT,
    `course_id`     varchar(30) CHARACTER SET utf8mb3                        NOT NULL,
    `tenant_code`   varchar(30) CHARACTER SET utf8mb3                        NOT NULL,
    `exam_state`    varchar(10) CHARACTER SET utf8mb3                            DEFAULT NULL,
    `test_num`      int unsigned NOT NULL COMMENT '试题数量',
    `passing_score` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合格分数',
    `test_type`     char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '出题类型: 1顺序  2 随机',
    `train_state`   char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '是否需要学完课程\n0 否 1 是\n',
    `create_time`   datetime                                                     DEFAULT NULL,
    `create_user`   varchar(30) CHARACTER SET utf8mb3                            DEFAULT NULL,
    `update_time`   datetime                                                     DEFAULT NULL,
    PRIMARY KEY (`paper_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 场景对话管理
CREATE TABLE `nbchat_train_scene_dialogue_manage`
(
    `id`           int                                                          NOT NULL AUTO_INCREMENT,
    `course_id`    varchar(30) COLLATE utf8mb4_general_ci                       NOT NULL,
    `tenant_code`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `dialogue_num` int                                                          NOT NULL COMMENT '会话数量',
    `score`        int                                    DEFAULT '90' COMMENT '合格分数',
    `role`         varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色组 [] 下标0: 提问角色 下标1:回答角色\n',
    `create_time`  datetime                               DEFAULT NULL,
    `create_user`  varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
    `update_time`  datetime                               DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 课程表添加场景实践配置状态、考试配置状态
alter table nbchat_train_course
    add column `scene_state` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '4' COMMENT '场景状态  0 编辑中 1 预览中 2 上架中 3 已下架  4待编辑';
alter table nbchat_train_course
    add column `test_paper_state` char(1) COLLATE utf8mb4_general_ci DEFAULT '4' COMMENT '考试配置状态  0 编辑中 1 预览中 2 上架中 3 已下架 4待编辑';

-- 给已有的对话手动加配置
insert into nbchat_train_scene_dialogue_manage (course_id, tenant_code, dialogue_num, role)
select course_id,
       tenant_code,
       count(1) / 2 as        dialogue_num,
       '["user","assistant"]' role
from nbchat_train_scene_dialogue
where is_valid = 1
group by course_id;

-- 更新场景状态为默认值
UPDATE nbchat_train_course
set scene_state = '2';
-- 配置课程的试题状态为待编辑
UPDATE nbchat_train_course
SET test_paper_state = '2';

-- 为课程创建默认配置
INSERT INTO `nbchat`.`nbchat_exam_test_paper`
(`course_id`, `tenant_code`, `exam_state`, `test_num`, `passing_score`, `test_type`, `train_state`, `create_time`,
 `create_user`, `update_time`)
SELECT course_id,
       tenant_code,
       NULL,
       10,
       '90',
       '2',
       '1',
       NOW(),
       'admin',
       NULL
FROM nbchat_train_course
WHERE is_valid = '1';
--将用户表的姓名字段同步到用户租户表
UPDATE nbchat_sys_user_tenant
    INNER JOIN nbchat_user
ON nbchat_sys_user_tenant.user_id = nbchat_user.user_id
    SET nbchat_sys_user_tenant.user_reality_name = nbchat_user.name;
--删除用户租户表里面重复的数据
WITH cte AS
         (SELECT user_id, tenant_code, MIN(create_time) as earliest_time
          FROM nbchat_sys_user_tenant
          GROUP BY user_id, tenant_code)

DELETE
FROM nbchat_sys_user_tenant
WHERE (user_id, tenant_code, create_time) NOT IN
      (SELECT user_id, tenant_code, earliest_time
       FROM cte);
```

### v1.2.1

```mysql
-- 修改用户学习记录表，新增start字段
ALTER TABLE nbchat_train_record
    ADD COLUMN star CHAR(1) DEFAULT '0' COMMENT '是否收藏：0否 1是' AFTER train_sections;
```

### v1.2.2

```mysql
-- 场景实践记录表索引更新
ALTER TABLE `nbchat`.`nbchat_train_scene_dialogue_record`
    ADD INDEX `idx_ten_cour` (`tenant_code`, `course_id`) USING BTREE COMMENT '租户、课程索引';
-- nbchat_train_rp_day_item表新增字段
ALTER TABLE nbchat_train_rp_day_item
    ADD item_values VARCHAR(500)
        AFTER item_value COMMENT 'json数据';
-- 创建nbchat_train_sections_video表
CREATE TABLE `nbchat_train_sections_video`
(
    `id`           bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `course_id`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程id',
    `section_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `tenant_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `user_id`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `create_time`  datetime                                                              DEFAULT NULL,
    `is_star`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '0' COMMENT '1 收藏',
    `is_zan`       char(1) COLLATE utf8mb4_general_ci                           NOT NULL DEFAULT '0' COMMENT '1 点赞',
    `view_process` int                                                          NOT NULL DEFAULT '0' COMMENT '观看进度 s',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`section_id`, `user_id`) USING BTREE COMMENT '唯一索引'
) ENGINE = InnoDB
  AUTO_INCREMENT = 2815742935106151536
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='课程章节视频统计';


-- nbchat_train_sections表新增字段
ALTER TABLE nbchat_train_sections
    ADD view_num INT DEFAULT 0 COMMENT '观看数量',
    ADD star_num INT DEFAULT 0 COMMENT '收藏数量',
    ADD zan_num  INT DEFAULT 0 COMMENT '点赞数';
```

### 1.3.0 数字人模块

```sql
CREATE TABLE `tdh_background`
(
    `object_id`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL,
    `tenant_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL,
    `user_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL,
    `object_url`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `object_size`   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '16:9' COMMENT '4:3 / 16:9 / 9:16 / 3:4',
    `object_type`   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT 'png/mp4',
    `create_time`   datetime                                                               DEFAULT NULL,
    `order_index`   smallint unsigned NOT NULL DEFAULT '0',
    `object_source` char(1) COLLATE utf8mb4_general_ci                            NOT NULL DEFAULT '0' COMMENT '0 系统内置 1 自定义',
    `is_valid`      char(1) COLLATE utf8mb4_general_ci                            NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
    PRIMARY KEY (`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-背景';

CREATE TABLE `tdh_creation_record`
(
    `creation_id`      varchar(50) COLLATE utf8mb4_general_ci                   NOT NULL COMMENT '创作id',
    `course_id`        varchar(50) COLLATE utf8mb4_general_ci                            DEFAULT '' COMMENT '课程id',
    `tenant_code`      varchar(50) COLLATE utf8mb4_general_ci                   NOT NULL COMMENT '租户编码',
    `user_id`          varchar(50) COLLATE utf8mb4_general_ci                   NOT NULL COMMENT '用户id',
    `creation_name`    varchar(100) COLLATE utf8mb4_general_ci                           DEFAULT '' COMMENT '创作名称',
    `creation_config`  varchar(500) COLLATE utf8mb4_general_ci                           DEFAULT '' COMMENT '创作配置',
    `creation_content` text COLLATE utf8mb4_general_ci                          NOT NULL COMMENT '创作内容:参考具体格式',
    `part_count`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '片段数',
    `create_time`      datetime                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime                                                          DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `order_index`      smallint unsigned NOT NULL DEFAULT '1' COMMENT '排序字段',
    `creation_state`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '创作状态： 0 草稿 1 已生成视频',
    `is_valid`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
    PRIMARY KEY (`creation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-用户创作记录';

CREATE TABLE `tdh_creation_task`
(
    `task_id`          varchar(50) COLLATE utf8mb4_general_ci                       NOT NULL COMMENT '任务id',
    `user_id`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '用户id',
    `tenant_code`      varchar(50) COLLATE utf8mb4_general_ci                       NOT NULL COMMENT '租户id',
    `creation_id`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创作id',
    `creation_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT '',
    `creation_content` text COLLATE utf8mb4_general_ci                              NOT NULL COMMENT '保留创作镜像',
    `start_time`       datetime                                                     NOT NULL COMMENT '开始时间',
    `end_time`         datetime                                                              DEFAULT NULL COMMENT '结束时间',
    `part_count_total` smallint unsigned NOT NULL DEFAULT '1' COMMENT '总视频段',
    `part_count_done`  smallint                                                     NOT NULL DEFAULT '0' COMMENT '完成视频段',
    `video_url`        varchar(500) COLLATE utf8mb4_general_ci                               DEFAULT '' COMMENT '视频下载地址',
    `video_duration`   int unsigned DEFAULT '0' COMMENT '视频时长 s',
    `task_state`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '0' COMMENT '0 生成中  1 任务完成 2 任务异常',
    `verify_state`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL DEFAULT '0' COMMENT '0 待审核 1 审核通过 2 审核不通过',
    `error_code`       char(10) COLLATE utf8mb4_general_ci                                   DEFAULT '' COMMENT '错误码',
    `error_desc`       varchar(500) COLLATE utf8mb4_general_ci                               DEFAULT '' COMMENT '错误描述',
    PRIMARY KEY (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-视频生成任务记录';

CREATE TABLE `tdh_foreground`
(
    `object_id`     varchar(50) COLLATE utf8mb4_general_ci                       NOT NULL,
    `tenant_code`   varchar(50) COLLATE utf8mb4_general_ci                       NOT NULL,
    `user_id`       varchar(50) COLLATE utf8mb4_general_ci                       NOT NULL,
    `object_url`    varchar(500) COLLATE utf8mb4_general_ci                      NOT NULL,
    `object_type`   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'png/jpg',
    `create_time`   datetime                                                              DEFAULT NULL,
    `object_source` char(1) COLLATE utf8mb4_general_ci                           NOT NULL DEFAULT '0' COMMENT '0 系统内置 1 自定义',
    `order_index`   smallint unsigned NOT NULL DEFAULT '0',
    `is_valid`      char(1) COLLATE utf8mb4_general_ci                           NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
    PRIMARY KEY (`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-前景';

CREATE TABLE `tdh_pip`
(
    `pip_id`      varchar(50) COLLATE utf8mb4_general_ci  NOT NULL,
    `tenant_code` varchar(50) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '租户',
    `user_id`     varchar(50) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户id',
    `pip_type`    char(10) COLLATE utf8mb4_general_ci     NOT NULL COMMENT '类型 png/jpg/mp4',
    `pip_desc`    varchar(100) COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '描述',
    `pip_url`     varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源链接',
    `is_valid`    char(1) COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
    `create_time` datetime                                NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`pip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-画中画';

CREATE TABLE `tdh_template`
(
    `tp_id`       varchar(50) COLLATE utf8mb4_general_ci                        NOT NULL COMMENT '模板id',
    `user_id`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户id',
    `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '租户编码',
    `tp_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
    `tp_content`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '模板内容',
    `create_time` datetime                                                               DEFAULT NULL COMMENT '创建时间',
    `order_index` smallint unsigned DEFAULT '0' COMMENT '排序字段',
    `tp_source`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '来源：0 系统内定 1 我的模板',
    `is_valid`    char(1) COLLATE utf8mb4_general_ci                            NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
    PRIMARY KEY (`tp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-模板记录';

CREATE TABLE `tdh_virtual_anchor`
(
    `anchor_id`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `user_id`       varchar(40) COLLATE utf8mb4_general_ci                                DEFAULT '' COMMENT '用户id',
    `tenant_code`   varchar(40) COLLATE utf8mb4_general_ci                       NOT NULL COMMENT '租户id',
    `name`          varchar(40) COLLATE utf8mb4_general_ci                       NOT NULL COMMENT '名称',
    `gender`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '性别',
    `type`          varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型： 新闻、小说、纪录片、教育…',
    `age_group`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT '' COMMENT '年龄段：老年、中年、青年、少年',
    `voice`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '声音 标示',
    `style`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '风格',
    `language`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言类型',
    `price`         varchar(20) COLLATE utf8mb4_general_ci                                DEFAULT '' COMMENT '价格',
    `label`         varchar(100) COLLATE utf8mb4_general_ci                               DEFAULT '' COMMENT '标签',
    `anchor_source` char(1) COLLATE utf8mb4_general_ci                           NOT NULL DEFAULT '0' COMMENT '0 系统默认 1 用户自定义',
    `anchor_config` varchar(20) COLLATE utf8mb4_general_ci                       NOT NULL COMMENT '使用语音提供类型',
    `img_avatar`    varchar(300) COLLATE utf8mb4_general_ci                               DEFAULT '' COMMENT '头像地址',
    `create_time`   datetime                                                              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `order_index`   smallint                                                              DEFAULT '0' COMMENT '排序',
    `is_valid`      char(1) COLLATE utf8mb4_general_ci                           NOT NULL DEFAULT '1' COMMENT '是否有效',
    PRIMARY KEY (`anchor_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='主播配置';

CREATE TABLE `tdh_virtual_human`
(
    `tdh_id`      varchar(50) COLLATE utf8mb4_general_ci                        NOT NULL COMMENT '虚拟人id',
    `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '租户id',
    `gender`      varchar(10) COLLATE utf8mb4_general_ci                        NOT NULL COMMENT '男/女',
    `user_id`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户id',
    `tdh_img`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人图片地址',
    `tdh_img_thu` varchar(500) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '缩略图',
    `tdh_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '名称',
    `tdh_tags`    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '标签 [{name:’’,color:’’}]',
    `pose_type`   char(1) COLLATE utf8mb4_general_ci                            NOT NULL DEFAULT '1' COMMENT '1 全身 2 半身 3 大半身 4 坐姿',
    `tdh_source`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '0' COMMENT '0 系统内置  1 自定义',
    `create_time` datetime                                                               DEFAULT NULL COMMENT '创建时间',
    `order_index` smallint unsigned DEFAULT '0' COMMENT '排序',
    `is_valid`    char(1) COLLATE utf8mb4_general_ci                                     DEFAULT '1' COMMENT '1 正常 0 已删除',
    PRIMARY KEY (`tdh_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-虚拟人形象';


```


### 1.3.2  视频添加考试题目
```sql
CREATE TABLE `nbchat_video_qa` (
       `id` int NOT NULL AUTO_INCREMENT,
       `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
       `tenant_code` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
       `course_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
       `catalog_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '章节目录id',
       `key_frame_time` int NOT NULL COMMENT '插入问答的时间点',
       `qid` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题id',
       `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
       `is_valid` int NOT NULL DEFAULT '1' COMMENT '1有效 0失效',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `nbchat_video_qa_record` (
      `id` int unsigned NOT NULL AUTO_INCREMENT,
      `tenant_code` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
      `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
      `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程id',
      `catalog_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程目录id',
      `answer_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '答题内容',
      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

alter table tdh_creation_task add column `is_valid` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '1 有效 0 失效';
alter table tdh_creation_task add column `queue_time` datetime DEFAULT NULL COMMENT '进入排队时间';

```


### 1.3.2 ai帮写模板、背景添加尺寸
```sql
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('tdh_help_write', '00000000', '-1', NULL, '{{ #ARG0 }}\n请对上述内容进行优化，使其更加口语化，便于口述 ', 'ai帮写', 'ai帮写', '2023-10-10 16:45:30', '', 0, '1');
alter table tdh_template add column `object_size` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '16:9' COMMENT '模板尺寸';
alter table tdh_pip add column `pip_source` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '0 系统内置 1 自定义';

-- 初始化课程知识库
insert into nbchat_research_major(major_id,major_name,major_desc,tenant_code,create_time,embed_type)
select
  concat(course_id,"_major"),
  course_name,
  course_name,
  tenant_code,
  now(),
  "api"
from nbchat_train_course where major_id is NULL;
-- 更新课程表知识库id
update nbchat_train_course set major_id = concat(course_id,"_major") where major_id is NULL;
```

### 1.3.3 添加背景音乐
```sql
CREATE TABLE `tdh_audio` (
  `object_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类',
  `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `object_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `object_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'wav/mp3',
  `create_time` datetime DEFAULT NULL,
  `duration` int NOT NULL DEFAULT '0' COMMENT '时长',
  `order_index` smallint unsigned NOT NULL DEFAULT '0',
  `object_source` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '0 系统内置 1 自定义',
  `is_valid` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '0 已删除 1 正常',
  PRIMARY KEY (`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字人-前景';

```

### 1.3.4 自动生成视频
```sql
alter table nbchat_train_course add column `task_fail_desc` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败原因';
alter table tdh_creation_record add column `section_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '章节id,如果有章节表示为内容视频';

CREATE TABLE `tdh_video_temp` (
  `tmp_id` varchar(20) NOT NULL,
  `user_id` varchar(20) DEFAULT NULL,
  `tenant_code` varchar(20) DEFAULT NULL,
  `tmp_type` tinyint(1) DEFAULT NULL COMMENT '1 课程介绍模板\n2 章节内容模板',
  `cata_num` tinyint(1) DEFAULT NULL COMMENT '目录数',
  `content` varchar(500) DEFAULT NULL COMMENT '模板内容',
  `order_num` int DEFAULT '1' COMMENT '排序字段',
  `create_time` datetime DEFAULT NULL,
  `is_valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1有效 0失效',
  `tmp_source` char(1) NOT NULL DEFAULT '1' COMMENT '0系统内置 1用户定制',
  PRIMARY KEY (`tmp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


ALTER TABLE tdh_creation_task add column `course_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '课程id';
ALTER TABLE tdh_creation_task add column `section_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '章节id,如果有章节表示为内容视频';

ALTER TABLE nbchat_train_sections
  ADD video_source SMALLINT DEFAULT 0 COMMENT '0系统生成 1用户上传';

ALTER TABLE nbchat_train_course
  ADD video_source SMALLINT DEFAULT 0 COMMENT '0系统生成 1用户上传';

ALTER TABLE tdh_creation_task
  ADD is_share CHAR(1) NOT NULL DEFAULT '0' COMMENT '0个人 1已共享';

ALTER TABLE tdh_creation_record
  ADD is_share CHAR(1) NOT NULL DEFAULT '0' COMMENT '0个人 1已共享';

UPDATE tdh_creation_task t
  INNER JOIN nbchat_train_sections n
ON t.video_url = n.video_url
  SET t.course_id = n.course_id, t.section_id = n.section_id;
UPDATE tdh_creation_task t
  JOIN nbchat_train_course n ON t.video_url = n.video_url
  SET t.course_id = n.course_id;
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('train_ppt_extract', '00000000', '-1', NULL, '#{ARG0}\n\n\n请根据以上内容帮提炼PPT讲解所需展示关键点，按照以下格式返回，\n要求1：关键点总数最多不能超过6个，每个关键点的字数不能超过10个字。\n要求2：如果关键点需要进行描述的请按有描述格式返回，描述的字数不能超过20个字。不需要描述按无描述格式返回。\n\n\n返回格式：\n[{\n        \"title\": \"关键点1\",\n        \"content\": \"关键点描述1\"\n    },\n    {\n        \"title\": \"关键点2\",\n        \"content\": \"关键点描述2\"\n    },\n...\n]', 'ppt 提炼重点', 'ppt 提炼重点', '2023-10-24 15:03:49', '', 0, '1');
ALTER TABLE tdh_creation_record add column `course_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '课程id';
ALTER TABLE tdh_creation_record add column `section_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '章节id,如果有章节表示为内容视频';
```
### 1.3.5
```mysql
CREATE TABLE `nbchat_train_robot_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `config_type` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '0 租户 \n1 用户',
  `config_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户id/用户id',
  `config_value` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '默认值',
  `config_list` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项列表[“chatgpt”,”chatglm”]',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni` (`config_type`,`config_id`) COMMENT '类型+值唯一'
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;




-- 术语在线
CREATE TABLE `tdh_term_online` (
  `id` int NOT NULL AUTO_INCREMENT,
  `norm_word` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '规范用词',
  `en` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '英文名',
  `also_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '又称',
  `comm_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '全称/俗称',
  `definition` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定义',
  `subject_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学科',
  `source` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源',
  `publish_year` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布年月',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 片段内容替换模板
INSERT INTO `nbchat`.`tdh_video_temp` (`tmp_id`, `user_id`, `tenant_code`, `tmp_type`, `cata_num`, `key_num`, `desc`, `content`, `order_num`, `create_time`, `is_valid`, `tmp_source`) VALUES ('a0', '1', '00000000', 5, NULL, NULL, '片段内容模板', '{\n    \"config\": {\n        \"quality\": \"1\",\n        \"resolution\": \"1080p\",\n        \"size\": \"16:9\"\n    },\n    \"parts\": [\n        {\n            \"backId\": \"bg101\",\n            \"backImg\": \"#url\",\n            \"content\": \"<speak>#parts_content</speak>\",\n            \"voice\": {\n                \"id\": \"1\",\n                \"name\": \"zhimiao_emo\"\n            },\n            \"subtitle\": {\n                \"enable\": true,\n                \"color\": \"#ffffff\",\n                \"name\": \"方正楷体.ttf\",\n                \"size\": 60,\n                \"w\": 1536,\n                \"x\": 192,\n                \"y\": 958\n            },\n            \"tdh\": {\n                \"id\": \"150\",\n                \"url\": \"https://chatfiles.tydiczt.com/files/202308/%E5%BD%A2%E8%B1%A11.png\",\n                \"w\": 576,\n                \"h\": 864,\n                \"x\": 1304,\n                \"y\": 216\n            }\n        }\n    ]\n}', 1, NULL, 1, '0');
-- 提取ppt关键字提示词
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('tdh_pdf_parts_content', '00000000', '-1', NULL, '#ARG0\n\n以上是针对一份演示PPT提取的文字内容，文字可能存在主次标题、位置等错乱，请注意分别。P后面的数字是对应页码，后面的文字该页对应的内容。请按以下要求和返回格式给我输出结果。\n要求1：请帮我逐页提炼生成演讲的文稿。\n要求2：切记文稿内容要与页码的内容一一对应，不要对应错页码与文稿的关系。\n要求3：不要返回非页码对应的文稿以外的文字。\n要求4：如果整体返回内容会超过你的返回限制，请针对每页内容再次提炼，要一次性能返回完整的内容。\n\n返回格式：\n【P1】: 文稿内容\n【P2】: 文稿内容\n【P3】: 文稿内容\n...', '整理pdf片段内容', '整理pdf片段内容', '2023-11-13 17:03:46', '', 0, '1');
-- 
ALTER TABLE tdh_foreground
  ADD category VARCHAR(255) COMMENT '分类' AFTER `tenant_code`;
```


### 1.4.1 
- 数字人支持2.5d
- 视频生成完成短信通知
- 模板支持分类查询

```sql
-- 新增数字人类型
ALTER TABLE tdh_virtual_human ADD COLUMN tdh_type VARCHAR(10) DEFAULT '2d' COMMENT '类型：2d、2.5d、3d';

-- 短信通知
CREATE TABLE `tdh_notice_task` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务id',
  `tenant_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `event_type` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '事件类型：1 视频制作 2 课程制作 ..其他未定义',
  `note_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '通知结果',
  `note_state` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '通知状态 0 待通知 1 已通知',
  `note_type` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'phone' COMMENT '手机号 / 邮箱',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务完成通知';

-- 模版
ALTER TABLE tdh_template
ADD COLUMN video_url VARCHAR(255) COMMENT '视频',
ADD COLUMN duration INT COMMENT '视频时长',
ADD COLUMN is_hot CHAR(1) NOT NULL DEFAULT '0' COMMENT '1 热门模板',
ADD COLUMN category VARCHAR(50) DEFAULT '' COMMENT '分类';
-- 草稿
ALTER TABLE tdh_creation_record ADD COLUMN preview_url VARCHAR(255) COMMENT '预览图';
```

```yaml
#开启短信通知
nbchat-train:
  config:
    notice-timer-enable: true
```
### 1.4.2

```mysql
-- 试卷配置
ALTER TABLE nbchat_exam_test_paper
  ADD COLUMN single INT COMMENT '单选题' AFTER test_num,
  ADD COLUMN multiple INT COMMENT '多选题' AFTER single,
  ADD COLUMN true_or_false INT COMMENT '判断题' AFTER multiple;
ALTER TABLE nbchat_exam_question
  ADD COLUMN difficulty CHAR(2)  COMMENT '试题难度' AFTER part_id,
  ADD COLUMN explan VARCHAR(500) DEFAULT '' COMMENT '答案解析' AFTER difficulty;
UPDATE nbchat_exam_test_paper
SET single = test_num;

-- 数字人共享
CREATE TABLE `tdh_object_share` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `object_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'human' COMMENT '对象类型：human: 数字人 anchor: 音频 template: 模板',
  `object_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对象id',
  `share_to` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '共享给指定租户id、个人id',
  `share_type` char(2) COLLATE utf8mb4_general_ci NOT NULL COMMENT '0 : 租户 1 个人',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_object_id` (`object_id`),
  KEY `idx_share_to` (`share_to`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```


### 1.4.3
- 更新数据模型
- 对接火山引擎语音合成
- ppt转视频功能相关接口

```mysql
-- 添加最大字符限制
alter table nbchat_robot_token add column `max_chars` int DEFAULT '4000' COMMENT '最大字符限制';
-- 修改长度限制
ALTER TABLE tdh_virtual_anchor
  ADD COLUMN emotion VARCHAR(500) COMMENT '情感 {“中文”:”英文”}',
  MODIFY COLUMN style VARCHAR(50),
  MODIFY COLUMN language VARCHAR(50);
```

### 1.4.4
```mysql
-- ppt记录表新增预览图字段
ALTER TABLE ppt_creation_record
  ADD COLUMN preview_url VARCHAR(500) COMMENT '预览图' AFTER ai_content;
ALTER TABLE ppt_creation_record
  MODIFY COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- ppt主题表
ALTER TABLE ppt_theme
  ADD COLUMN compose_type CHAR(1) NOT NULL COMMENT '组合类型：0主题组合 1布局组合' AFTER theme_type,
  ADD COLUMN config JSON NOT NULL COMMENT 'ppt配置-主题配置' AFTER theme_source,
  ADD COLUMN theme_state CHAR(1) DEFAULT '0' COMMENT '上架状态 0下架 1上架',
  MODIFY COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- ppt布局表
ALTER TABLE ppt_layout
MODIFY COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- ppt_svg表
CREATE TABLE `ppt_svg` (
                         `svg_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
                         `tenant_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
                         `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
                         `svg_source` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '0系统内置 1自定义',
                         `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'svg模板内容',
                         `config` json DEFAULT NULL COMMENT '配置。艺术字配置，其他配置',
                         `type` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0形状 1艺术字',
                         `order_index` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '优先级1-9 数字小优先高',
                         `is_valid` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '0失效',
                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                         PRIMARY KEY (`svg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 背景、前景、贴图添加资源类型区分ppt和数字人
ALTER TABLE `nbchat`.`tdh_background`
  ADD COLUMN `source_type` char(1) NULL DEFAULT '0' COMMENT '0数字人模块 1ppt模块' AFTER `is_valid`;
ALTER TABLE `nbchat`.`tdh_foreground`
  ADD COLUMN `source_type` char(1) NULL DEFAULT '0' COMMENT '0数字人模块 1ppt模块' AFTER `is_valid`;
ALTER TABLE `nbchat`.`tdh_pip`
  ADD COLUMN `source_type` char(1) NULL DEFAULT '0' COMMENT '0数字人模块 1ppt模块' AFTER `is_valid`;
-- 修改字典表
ALTER TABLE sys_dict_config
  MODIFY COLUMN dict_value VARCHAR(2000);
-- 生成试题多轮对话
alter table nbchat_session add column `session_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0迪问会话 1生成试题会话';

INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('train_exam_ai_dialog', '00000000', '-1', NULL, '需求【#ARG0】\n上面，需求【】是用户想要出题对应的需求描述，请根据用户的需求帮我生成试题。\n请务必按照以下返回格式进行试题输出。\n\n返回格式：(以下“注：”后面的内容不需要输出)\n\n回答(注：根据用户需求给予对话式简要回复)：内容\n全部试题(注：新试题直接覆盖原试题，其他试题必须保持不变，全部按顺序原样返回，不能生成其他多余试题)：内容', '生成试题多轮对话', '生成试题多轮对话', NULL, '', 0, '1');
```

### 1.4.5
```mysql
-- 修改主题表
ALTER TABLE ppt_theme MODIFY theme_type VARCHAR(20) COMMENT '主题分类：0000：通用主题;9221: 环保, 动物;9220: 酒店, 餐饮;9219: 水彩, 乐器, 介绍';

-- 修改ppt布局表
ALTER TABLE ppt_layout
ADD COLUMN theme_state CHAR(1) DEFAULT '0' COMMENT '上架状态 0下架 1上架';

alter table ppt_layout
  add column `compose_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '组合类型：0主题组合 1布局组合';
ALTER TABLE `nbchat`.`ppt_layout` ADD COLUMN `image_num` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '配图数量' AFTER `is_image`;
-- 新增试听链接
ALTER TABLE tdh_virtual_anchor ADD COLUMN demo_url VARCHAR(300) DEFAULT '' COMMENT '试听链接';

-- svg初始化数据
INSERT INTO `nbchat`.`ppt_svg` (`svg_id`, `tenant_code`, `user_id`, `svg_source`, `content`, `config`, `type`, `order_index`, `is_valid`, `create_time`) VALUES ('shape_1', NULL, NULL, '0', '<svg width=\"#{w}\" height=\"#{h}\"  xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" viewBox=\"0 0 #{viewBox[0]} #{viewBox[1]}\">\n	<path d=\"#{path}\" fill=\"#{fillColor}\" vector-effect=\"non-scaling-stroke\" stroke-linecap=\"butt\" stroke-miterlimit=\"8\"></path>\n</svg>', NULL, '0', '0', '1', '2024-01-23 10:13:29');

-- ppt创作记录保存文件信息
alter table ppt_creation_record add column
  `file_url` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件地址',
  `file_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件md5',
  `file_vector` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '1 向量化完成';
-- 修改前景布局表
ALTER TABLE `nbchat`.`tdh_foreground` ADD COLUMN `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类' AFTER `tenant_code`;

```

```properties
# 新增调用tdh embedding接口配置
nbchat-train:
  config:
    tdh-embedding-api: http://*************:8080/tdh/text/embedding/save
```

### 1.4.6
```mysql
ALTER TABLE tdh_foreground 
ADD COLUMN name VARCHAR(255) COMMENT '前景名称' AFTER tenant_code;
ALTER TABLE tdh_background
  ADD COLUMN name VARCHAR(255) COMMENT '背景名称' AFTER tenant_code;
ALTER TABLE tdh_pip
  ADD COLUMN name VARCHAR(255) COMMENT '背景名称' AFTER tenant_code;

ALTER TABLE tdh_background
  ADD COLUMN category VARCHAR(255) COMMENT '分类' AFTER tenant_code;
ALTER TABLE tdh_pip
  ADD COLUMN category VARCHAR(255) COMMENT '分类' AFTER tenant_code;
```
### 1.4.7
```mysql
-- ppt布局表新增排序字段
ALTER TABLE ppt_layout 
ADD COLUMN sort int  DEFAULT 999 COMMENT '排序'  AFTER category;
```

### 1.4.8
```mysql
CREATE TABLE `op_rp_stst_day_item` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
  `tenant_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户',
  `item_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '指标编码',
  `item_value` int DEFAULT NULL COMMENT '指标值',
  `count_day` date DEFAULT NULL COMMENT '统计时间',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期 默认为当前时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='运营统计_天数';


CREATE TABLE `op_rp_stst_pt` (
                               `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
                               `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
                               `tenant_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户',
                               `item_code` varchar(50) DEFAULT NULL COMMENT '指标编码',
                               `request_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求路径',
                               `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='运营统计表_次数';

-- 统计往期ppt制作数据（ppt创作记录）
INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT ppt.user_id,CASE
                     WHEN ppt.tenant_code = '00000000' THEN ue.company_name
                     ELSE ppt.tenant_code
  END AS tenant_code,'ppt_creators',count(ppt.ppt_id),DATE_FORMAT(ppt.create_time,'%Y-%m-%d'),NOW()
FROM  ppt_creation_record ppt
        LEFT JOIN  nbchat_user_enterprise ue ON ue.user_id = ppt.user_id
        LEFT JOIN nbchat_sys_user_tenant sut ON ue.user_id = sut.user_id AND sut.tenant_code IN ('000TYDIC','00000TMO','0000CSZH')
WHERE ue.company_name NOT REGEXP '迪科|tydic|天源'
  AND sut.user_id IS NULL
GROUP BY ppt.user_id;

-- 视频创作记录
INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT video.user_id,ue.company_name,'video_creators',count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
left join nbchat_user_enterprise ue on video.user_id = ue.user_id
WHERE video.start_time BETWEEN '2023-01-01' AND NOW() AND video.tenant_code = '00000000' AND ue.company_name NOT REGEXP '迪科|tydic|天源'
GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),video.user_id;

INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT video.user_id,video.tenant_code,'video_creators',count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
left join nbchat_sys_user_tenant ut on video.user_id = ut.user_id
WHERE video.start_time BETWEEN '2023-01-01' AND NOW() AND video.tenant_code NOT IN ('00000000','000TYDIC','00000TMO','0000CSZH')
GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),user_id;

-- 视频创作成功记录
INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT video.user_id,video.tenant_code,'video_success',count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
left join nbchat_sys_user_tenant ut on video.user_id = ut.user_id
WHERE video.start_time BETWEEN '2023-01-01' AND NOW() AND video.tenant_code NOT IN ('00000000','000TYDIC','00000TMO','0000CSZH') AND video.task_state = 1
GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),video.user_id;


INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT video.user_id,ue.company_name,'video_success',count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
left join nbchat_user_enterprise ue on video.user_id = ue.user_id
WHERE video.start_time BETWEEN '2023-01-01' AND NOW() AND video.tenant_code = '00000000' AND ue.company_name NOT REGEXP '迪科|tydic|天源' AND video.task_state = 1
GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),video.user_id;

-- 新增用户
INSERT INTO op_rp_stst_day_item (tenant_code,item_code,item_value,count_day,created_time)
SELECT tenant_code,'new_registrations',count(user_id),DATE_FORMAT(created_time,'%Y-%m-%d'),NOW() from nbchat_user
WHERE created_time BETWEEN '2023-01-01' AND NOW() AND tenant_code NOT IN ('00000000','000TYDIC','00000TMO','0000CSZH') GROUP BY DATE_FORMAT(created_time,'%Y-%m-%d'), tenant_code;

INSERT INTO op_rp_stst_day_item (tenant_code,item_code,item_value,count_day,created_time)
SELECT company_name,'new_registrations',count(user_id),DATE_FORMAT(create_time,'%Y-%m-%d'),NOW() from nbchat_user_enterprise
WHERE create_time BETWEEN '2023-01-01' AND NOW() AND company_name NOT REGEXP '迪科|tydic|天源' GROUP BY DATE_FORMAT(create_time,'%Y-%m-%d'),company_name;

UPDATE nbchat_user_enterprise
  INNER JOIN nbchat_user
  ON nbchat_user_enterprise.user_id = nbchat_user.user_id
SET nbchat_user_enterprise.create_time = nbchat_user.created_time;


INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
SELECT upload_user,tenant_code,'question_creators',count(file_no),DATE_FORMAT(upload_time,'%Y-%m-%d'),NOW() from file_upload_record where file_name like '%智能生成试题%' GROUP BY upload_user, upload_time


```

### 1.5.1
- 视频创作新增播放链接字段 play_url
```mysql
ALTER TABLE tdh_creation_task
  ADD COLUMN play_url VARCHAR(500) DEFAULT '' COMMENT '播放链接';
-- 刷新数据
UPDATE tdh_creation_task SET play_url=video_url
UPDATE tdh_creation_task SET video_url=REPLACE(video_url,'player.html?src=','')

-- 外部 ppt、考试等创作的资源同步表
create table ppt_exam_outer_source(
    request_id varchar(50) not null primary key,
    tenant_code varchar(50) not null comment '租户',
    user_id varchar(50) default '' comment '用户id',
    biz_id varchar(50) default '' comment '业务id',
    biz_code varchar(50) default '' comment '业务类型',
    source_url varchar(500) default '' comment '原始文件地址',
    source_content longtext comment '原始文件内容',
    status char(1) default '1' comment '1 处理中 2 处理完成 3 处理失败',
    create_time datetime default current_timestamp comment '创建时间',
    update_time datetime default null comment '更新时间',
    result_content longtext comment '处理结果内容',
    result_url varchar(500) default '' comment '输出的文件'
) comment '外部创作数据同步表';
```

### 1.5.6
```mysql
ALTER TABLE ppt_theme ADD COLUMN order_index int DEFAULT 999999 COMMENT '排序' AFTER theme_id;
```