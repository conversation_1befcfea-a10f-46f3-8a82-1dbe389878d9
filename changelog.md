
## 版本变更记录

### v1.2.4
- 新增租户表、用户-租户关联表
- 新增租户查询接口、用户所属租户查询

- 表结构变更
```mysql

-- 创建租户表
CREATE TABLE `nbchat_sys_tenant` (
     `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户ID',
     `tenant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户名称',
     `tenant_desc` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
     `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域名地址',
     `linkman` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人',
     `contact_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
     `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系地址',
     `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     `status` int DEFAULT NULL COMMENT '状态',
     `is_valid` char(1) COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否已删除',
     `img_avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
     `ext_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '扩展信息',
     PRIMARY KEY (`tenant_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=COMPACT COMMENT='租户表';

-- 创建用户-租户关联表
CREATE TABLE `nbchat_sys_user_tenant` (
      `id` int unsigned NOT NULL AUTO_INCREMENT,
      `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
      `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
      `create_time` datetime DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_uid` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=268 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 导入初始租户
INSERT INTO nbchat_sys_user_tenant (user_id,tenant_code,create_time) SELECT user_id,tenant_code,NOW() FROM nbchat_user

```


### v1.2.6
- 修改用户-租户关联表，新增join_type字段
```mysql
-- 修改用户-租户关联表，新增join_type字段
ALTER TABLE nbchat_sys_user_tenant 
ADD COLUMN join_type CHAR(1) DEFAULT '1' COMMENT '加入类型：1. 用户注册 2. 批量导入 3. 人工创建 4. 邀请码' AFTER tenant_code;
-- 确保所有用户都有公共租户
INSERT INTO nbchat_sys_user_tenant (user_id,user_reality_name, tenant_code,join_type,create_time)
SELECT user_id, name,'00000000',1,NOW()
FROM nbchat_user
WHERE is_deleted = 0
  AND user_id NOT IN
      (SELECT user_id
       FROM nbchat_sys_user_tenant
       WHERE tenant_code = '00000000' AND user_id IN (SELECT user_id FROM nbchat_user where is_deleted=0))
```


### v1.3.1
```mysql
-- 创建企业申请审批表
CREATE TABLE `nbchat_enterprise_try_apply` (
   `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ID',
   `company_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业名称',
   `business_sector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属行业',
   `tenant_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户',
   `position` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
   `name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名',
   `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
   `email` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
   `specific_requirements` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '具体需求',
   `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态：0 待审批；1 预审批通过；2 通过；3拒绝 4预审批',
   `created_time` datetime DEFAULT NULL COMMENT '创建时间',
   `updated_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `try_invite_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邀请码',
   `file_url` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件',
   `file_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='租户申请表';

-- 增加字典表——所属行业
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '农、林、牧、渔业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '采矿业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '制造业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '电力、热力、燃气及水的生产和供应业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '建筑业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '批发和零售业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '交通运输、仓储和邮政业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '住宿和餐饮业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '信息传输、软件和信息技术服务业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '金融业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '房地产业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '租赁和商务服务业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '科学研究和技术服务业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '水利、环境和公共设施管理业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '居民服务、修理和其他服务业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '教育');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '卫生和社会工作');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '文化、体育和娱乐业');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '公共管理、社会保障和社会组织');
INSERT INTO sys_dict_config ( dict_code, dict_name,dict_value) VALUES ( 'industry', '所属行业', '国际组织');

-- 修改配置
mysql修改驱动配置：
    ******************************************************************************************************************************************************************************************
    
spring:        
  datasource:
    dynamic:
      primary: master
      druid:
        wall:
          multi-statement-allow: true

```

### v1.4.0-20231130

- 新增系统角色表
- 新增系统部门表
- 新增角色-用户关系表
- 新增角色-部门关系表
- 新增部门列表查询接口

```mysql
CREATE TABLE `nbchat_user_role_rel` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `subsystem` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子系统',
    `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
    `role` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色',
    `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_uid` (`user_id`) USING BTREE COMMENT '索引'
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `nbchat_user_dept_rel` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增',
  `subsystem` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子系统',
  `user_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `dept_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `nbchat_sys_dept` (
   `dept_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门id',
   `parent_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '上级部门',
   `level` smallint unsigned NOT NULL DEFAULT '0' COMMENT '层级',
   `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门',
   `dept_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
   `tenant_code` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户编码',
   `subsystem` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'nbchat' COMMENT '子系统',
   `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
   `is_valid` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '1 正常 0 已删除',
   PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `nbchat_sys_role` (
   `id` int NOT NULL AUTO_INCREMENT COMMENT '自增',
   `subsystem` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属子系统',
   `role` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色编码',
   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
   `api_permission` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'Api访问权限: /tmo/user,/tmo/api',
   `menu_permission` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '菜单访问权限: /memu1/index,xx',
   `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

```
### 1.4.6
```mysql
-- 创建用户企业信息表
CREATE TABLE `nbchat_user_enterprise` (
  `user_id` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `email` varchar(50) DEFAULT NULL COMMENT '电子邮箱',
  `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `business_sector` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属行业',
  `requirement` varchar(200) DEFAULT NULL COMMENT '需求',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
```
### 1.4.8
```mysql
-- 修改用户企业信息表
ALTER TABLE nbchat_user_enterprise
ADD COLUMN create_time datetime  COMMENT '创建时间' AFTER requirement;
```

### 1.4.9
- 新增用户企业工号绑定表和绑定接口
- 新增用户企业工号绑定查询接口
```mysql
-- 新增用户企业工号绑定表
CREATE TABLE `nbchat_sys_job_no` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tenant_code` varchar(50) NOT NULL COMMENT '企业租户',
  `user_id` varchar(50) DEFAULT '' COMMENT '用户ID',
  `job_no` varchar(50) NOT NULL COMMENT '工号：提前导入',
   phone varchar(20) DEFAULT '' COMMENT '手机号：提前导入',
  `name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '姓名：提前导入',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   bind_status CHAR(1) DEFAULT '0' COMMENT '0 待绑定 1 已绑定',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户企业工号绑定表';

-- 新增系统操作日志记录表
CREATE TABLE sys_operate_log (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_code VARCHAR(50) DEFAULT '' COMMENT '企业租户',
    op_user VARCHAR(50) DEFAULT '' COMMENT '用户ID',
    op_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    op_desc VARCHAR(200) DEFAULT '' COMMENT '操作描述',
    version VARCHAR(30) DEFAULT '' COMMENT '版本号',
    op_backup TEXT COMMENT '数据备份',
    op_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统操作日志记录表';

-- 新增网关token配置表，用于配置外部系统访问token，需要有api权限配置字段
create table sys_api_token_config (
    id int unsigned not null auto_increment,
    app_name varchar(50) not null comment '配置名称',
    tenant_code varchar(50) not null comment '租户',
    user_id varchar(50) default '' comment '用户ID',
    access_key varchar(50) not null comment '访问key',
    secret_key varchar(50) not null comment '密钥',
    expire_time int(11) unsigned not null default 0 comment '过期时间',
    subsystem varchar(50) not null comment '子系统访问权限',
    api_permission json comment 'api权限',
    create_time datetime default current_timestamp comment '创建时间',
    update_time datetime default current_timestamp on update current_timestamp comment '更新时间',
    refresh_time datetime default current_timestamp comment '刷新时间',
    is_valid char(1) default '1' comment '是否有效',
    primary key (id)
) engine=InnoDB default charset=utf8mb4 collate=utf8mb4_general_ci comment='网关token配置表';

-- 文件记录表新增是否删除字段、更新时间字段
ALTER TABLE file_upload_record ADD COLUMN is_valid CHAR(1) DEFAULT '1' COMMENT '是否删除 0 已删除  0 正常';
ALTER TABLE file_upload_record ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
-- file_path 新增索引
CREATE INDEX idx_file_path ON file_upload_record (file_path);
CREATE INDEX idx_busi_code ON file_upload_record (busi_code);
-- 更改 busi_code长度
ALTER TABLE file_upload_record MODIFY COLUMN busi_code VARCHAR(100) COMMENT '业务编码';
```

### 1.5.1
- nbchat_sys_tenant 新增字段
- 新增外部用户同步接口
- 新增外部用户授权接口

```mysql
ALTER TABLE nbchat_sys_tenant ADD COLUMN auth_api CHAR(1) DEFAULT '0' COMMENT '是否授权api: 0 否 1 是';
ALTER TABLE nbchat_sys_tenant ADD COLUMN auth_config JSON COMMENT '授权api范围';
```

### 1.5.4
- 修改用户企业信息表的name字段为非必填
```mysql
ALTER TABLE nbchat_user_enterprise MODIFY name varchar(255) NULL;
```

### 1.5.6
- 新增配置
nbchat-admin:
  config:
    baidu:
      ocpc-token: