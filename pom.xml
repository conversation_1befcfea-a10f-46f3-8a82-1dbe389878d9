<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <name>nicc-boot-starter</name>
    <groupId>com.tydic.nicc.dc</groupId>
    <artifactId>nicc-boot-starter</artifactId>
    <version>2.9.1</version>
    <packaging>pom</packaging>
    <url>http://www.tydic.com</url>

    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <nicc-boot-starter.version>2.9.1</nicc-boot-starter.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <jasypt-bootstarter.version>3.0.4</jasypt-bootstarter.version>
        <fastjson.version>2.0.25</fastjson.version>
        <java-jwt.version>4.0.0</java-jwt.version>
        <dubbo.version>2.7.23</dubbo.version>
        <commons-net.version>3.11.0</commons-net.version>
        <aliyun-sdk-oss.version>3.17.4</aliyun-sdk-oss.version>
        <cos_api.version>5.6.185</cos_api.version>
        <jsch.version>0.2.17</jsch.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <hauwei-obs.version>3.24.3</hauwei-obs.version>
        <aspectjweaver.version>1.9.22</aspectjweaver.version>
        <fastdfs-client.version>1.1.0</fastdfs-client.version>
        <minio.version>8.5.10</minio.version>
        <okhttp.version>4.12.0</okhttp.version>
        <aws-java-sdk.version>1.12.730</aws-java-sdk.version>
        <maven-compiler.version>3.8.1</maven-compiler.version>
        <maven-surefire.version>3.0.0-M8</maven-surefire.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://118.190.78.212:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://118.190.78.212:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <type>pom</type>
                <scope>import</scope>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nicc.dc</groupId>
                <artifactId>dc-base-bo</artifactId>
                <version>${nicc-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt-bootstarter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <!-- dubbo 依赖 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-serialization-kryo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-s3 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.qcloud/cos_api -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                </exclusions>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java-bundle</artifactId>
                <version>${hauwei-obs.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- sftp 依赖-->
            <dependency>
                <groupId>com.github.mwiede</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>
            <!-- ftp 依赖-->
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <!-- minio 依赖-->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- fastdfs 依赖-->
            <dependency>
                <groupId>io.github.ykrenz</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>dc-base-bo</module>
        <module>dc-boot-starter</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire.version}</version>
                <configuration>
                    <!--默认关掉单元测试 -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
