package com.tydic.nbchat.robot.api;

import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface NbchatRobotToolsApi {

    /**
     * 获取结果
     * @param request
     * @return
     */
    Rsp<RobotToolsChatResponse> getChatResult(RobotPromptMessageRequest request);

    /**
     * 消息推送
     * @param request
     * @param callback
     */
    void sendMessage(RobotPromptMessageRequest request, RobotProcessCallback callback);

    /**
     * PPT模版匹配获取结果
     * @param request
     * @return
     */
    Object getPPTChatResult(RobotPromptMessageRequest request);
}
