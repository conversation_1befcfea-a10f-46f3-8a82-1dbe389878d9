package com.tydic.nbchat.robot.api.bo.research;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileResearchDeleteRequest extends BasePageInfo implements Serializable {
    private String fileId;//文档ID
    private String tenantCode;//租户编码
    private String majorId;
    private String userId;
    private String category;//分类ID
    //是否硬删除
    private boolean forceDelete;
}
