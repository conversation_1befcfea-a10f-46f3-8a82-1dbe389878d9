package com.tydic.nbchat.robot.api.bo.eums;

public enum DocSourceType {
    SYSTEM(0, "系统默认"),
    CUSTOM(1, "自定义"),
    MAJOR(2, "专属机器人");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private DocSourceType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (DocSourceType field : DocSourceType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
