package com.tydic.nbchat.robot.api.bo.eums;

public enum ShareObjectType {
    MAJOR("major", "专属机器人"),
    DOC("doc", "单文档会话"),
    SESSION("session", "会话");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ShareObjectType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ShareObjectType field : ShareObjectType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
