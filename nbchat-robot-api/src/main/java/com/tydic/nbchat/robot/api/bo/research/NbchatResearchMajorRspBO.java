
package com.tydic.nbchat.robot.api.bo.research;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
*
*  <AUTHOR>
*/
@Data
public class NbchatResearchMajorRspBO implements Serializable {

    private static final long serialVersionUID = 1681281095414L;
    private String userId;
    private String majorId;
    private String majorName;
    private String tenantCode;
    private String majorDesc;
    private Integer orderIndex;
    /**
     * 0 删除 1 正常
     * isNullAble:0,defaultVal:1
     */
    private String isValid;
    private String isShow;
    //所属类目
    private String category;
    private Integer majorSource;
    private String majorAvatar;
    private String robotType;
    private String embedType;
    /**
     * 创建时间
     */
    private Date createTime;
    private Date updateTime;
}
