package com.tydic.nbchat.robot.api.bo.eums;

public enum ResearchAnalysisState {
    WAITING("0", "解析中"),
    SUCCESS("1", "解析完成"),
    ERROR("2", "解析异常");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ResearchAnalysisState(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ResearchAnalysisState field : ResearchAnalysisState.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
