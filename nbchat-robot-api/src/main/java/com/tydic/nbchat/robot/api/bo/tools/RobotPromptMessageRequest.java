package com.tydic.nbchat.robot.api.bo.tools;

import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.tools.RobotCommonUtil;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class RobotPromptMessageRequest implements Serializable {
    private String appId;
    private String appType;
    private String requestOptions;
    private String sessionId;
    private String text;

    private String requestId;
    private String robotTag;
    private String robotType;
    private String userId;
    private String tenantCode;
    //业务编码-用于区分不同业务场景
    private String busiCode;
    //预设模板
    @NotEmpty
    private String presetId;
    //提示词
    private String promptText;
    //预设参数
    private List<String> presetPrompts;
    //预设参数-系统角色用
    private List<String> presetSysPrompts;
    //历史消息
    private List<ChatMessage> messages;
    private boolean trim;
    private boolean stream = false;
    //联网搜索
    private Boolean searchOnline = false;

    /**
     * 指定模型配置
     * {
     *    "temperature":0.1,
     *    "stop":[""],
     *    "maxTokens":1000,
     *    ...
     * }
     */
    private Map<String,Object> modelConfig;

    //重写toString
    @Override
    public String toString() {
        return "RobotPromptMessageRequest{" +
                "requestId='" + requestId + '\'' +
                ", robotTag='" + robotTag + '\'' +
                ", robotType='" + robotType + '\'' +
                ", userId='" + userId + '\'' +
                ", tenantCode='" + tenantCode + '\'' +
                ", presetId='" + presetId + '\'' +
                ", promptText='" + RobotCommonUtil.subLongText(promptText) + '\'' +
                ", presetPrompts=" + RobotCommonUtil.subLongText(presetPrompts) +
                ", trim=" + trim +
                ", stream=" + stream +
                ", searchOnline=" + searchOnline +
                ", modelConfig=" + modelConfig +
                '}';
    }
}
