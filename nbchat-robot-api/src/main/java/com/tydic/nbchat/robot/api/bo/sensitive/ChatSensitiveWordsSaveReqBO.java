package com.tydic.nbchat.robot.api.bo.sensitive;

import com.tydic.nicc.common.eums.sensitive.SensitiveLoadType;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ChatSensitiveWordsSaveReqBO implements Serializable {
    private String wordId;
    private String loadType = SensitiveLoadType.TABLE.getCode();
    private Integer sensitiveId = 0;
    private String tenantCode;
    @ParamNotNull
    private Iterable<String> wordSet;
    private String wordType;
    private Date crtTime;
    private String isValid;
}
