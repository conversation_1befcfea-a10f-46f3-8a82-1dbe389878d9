package com.tydic.nbchat.robot.api.bo.sensitive;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.common.eums.sensitive.SensitiveLoadType;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChatSensitiveWordsQueryReqBO extends BasePageInfo implements Serializable {
    private String wordId;
    private Integer sensitiveId = 0;
    @ParamNotEmpty
    private String tenantCode;
    private String wordKey;
    private String wordType;
    private String wordText;
    private String isValid;
    private String loadType = SensitiveLoadType.TABLE.getCode();
}
