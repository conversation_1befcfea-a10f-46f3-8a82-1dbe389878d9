package com.tydic.nbchat.robot.api.bo.sensitive;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: 查询原始消息  <br>
 * @date 2021/7/14 2:27 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ChatSensitiveOrignMsgReqBO extends BasePageInfo implements Serializable {
    @ParamNotEmpty
    private String tenantCode;
    private String channelCode;
    private String sessionId;
    private String msgId;
    private String chatType;
    private String chatKey;
    private String fromNo;
    private String toNo;
    private Date msgTimeStart;
    private Date msgTimeEnd;
}
