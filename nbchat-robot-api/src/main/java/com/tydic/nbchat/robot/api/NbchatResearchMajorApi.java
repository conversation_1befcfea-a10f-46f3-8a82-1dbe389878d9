package com.tydic.nbchat.robot.api;

import com.tydic.nbchat.robot.api.bo.research.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;


public interface NbchatResearchMajorApi {

    /**
     * 获取段落检索结果
     * @param requestId
     * @return
     */
    Rsp<ResearchPartResultRsp> getResearchPartResult(String requestId);

    /**
     * 文档检索消息批量查询
     * @param request
     * @return
     */
    RspList getResearchMajors(FileResearchMajorQueryRequest request);

    /**
     * 查询单个
     * @param request
     * @return
     */
    Rsp getResearchMajor(FileResearchMajorQueryRequest request);

    /**
     * 查询专属机器人解析状态
     * @param request
     * @return
     */
    Rsp getMajorAnalysisStatus(FileResearchMajorQueryRequest request);

    /**
     * 保存major
     * @param request
     * @return
     */
    Rsp saveMajor(FileResearchMajorSaveRequest request);

    /**
     * 删除major
     * @param request
     * @return
     */
    Rsp deleteMajor(FileResearchMajorDeleteRequest request);
}
