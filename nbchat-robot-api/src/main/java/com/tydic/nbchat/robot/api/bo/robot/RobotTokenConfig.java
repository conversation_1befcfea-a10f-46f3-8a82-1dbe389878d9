package com.tydic.nbchat.robot.api.bo.robot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RobotTokenConfig implements Serializable {
    private String name;
    private String ak;
    private String sk;
    private Integer expireTime = 3600 * 24 * 3;
    private Integer cacheTime = 3600 * 24;
    private List<String> resourceIds;
}
