package com.tydic.nbchat.robot.api.openai.completion.chat;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * A chat completion generated by GPT-3.5
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChatCompletionChoice {

    Integer index;
    @JsonAlias("delta")
    ChatMessage message;
    @JsonAlias("finishReason")
    @JsonProperty("finish_reason")
    String finishReason;

    public String getFinishReason() {
        if (StringUtils.isBlank(finishReason) || finishReason.equals("null")) {
            return "";
        }
        return finishReason;
    }
}
