package com.tydic.nbchat.robot.api.bo.research.document;

import com.tydic.nbchat.robot.api.bo.eums.DocSourceType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ResearchDocumentSaveRequest implements Serializable {
    private String tenantCode;
    private String userId;
    private String majorId;
    //导入后自动开始解析
    private boolean autoParse;
    private Integer docSource = DocSourceType.MAJOR.getCode();
    private List<ResearchDocumentContent> documents;

}
