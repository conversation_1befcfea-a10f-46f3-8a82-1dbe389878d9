package com.tydic.nbchat.robot.api.bo.research.app;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

@Data
public class ResearchAppQueryRequest extends BasePageInfo {

    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;

    private String appId;

    private String appName;

    private String appTag;

    private String appType;

    private String isShare;

    private String appSource;
    //关键字搜索
    private String keyword;
}
