package com.tydic.nbchat.robot.api.bo.research;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KbRecall implements Serializable {
    /**
     * {
     *         "recallType": "1",
     *         "rerankModel": "test",
     *         "topK": 3,
     *         "score": 0.5,
     *         "majorIds": [
     *           "1",
     *           "2"
     *         ],
     *     }
     */
    private String recallType;
    private String rerankModel;
    private Integer topK;
    private Double score;
    private List<String> majorIds;

    public boolean isValid(){
        return recallType != null && topK != null && score != null && majorIds != null && !majorIds.isEmpty();
    }
}
