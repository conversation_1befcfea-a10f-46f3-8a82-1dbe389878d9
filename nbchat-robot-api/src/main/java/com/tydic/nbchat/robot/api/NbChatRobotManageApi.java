package com.tydic.nbchat.robot.api;

import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenQueryReqBO;
import com.tydic.nbchat.robot.api.bo.robot.SaveRobotConfigReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbChatRobotManageApi {

    /**
     * 查询token列表
     * @param reqBO
     * @return
     */
    RspList<RobotToken> getRobotTokes(RobotTokenQueryReqBO reqBO);


    /**
     * 新增模型配置
     * @param reqBO
     * @return
     */
    Rsp saveRobotConfig(SaveRobotConfigReqBO reqBO);

}
