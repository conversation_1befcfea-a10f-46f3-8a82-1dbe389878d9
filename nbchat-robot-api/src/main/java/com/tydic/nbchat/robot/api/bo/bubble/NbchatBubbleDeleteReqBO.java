package com.tydic.nbchat.robot.api.bo.bubble;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NbchatBubbleDeleteReqBO extends BaseInfo implements Serializable {
    private List<Integer> bubbleIds;
    @ParamNotNull
    private Integer bubbleId;
    private String userId;

}
