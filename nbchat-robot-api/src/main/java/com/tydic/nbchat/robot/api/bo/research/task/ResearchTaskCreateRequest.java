package com.tydic.nbchat.robot.api.bo.research.task;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ResearchTaskCreateRequest implements Serializable {
    @ParamNotNull
    private String userId;
    private String tenantCode;
    private String requestId;
    private String robotType;
    @ParamNotEmpty
    private String majorId;
    private List<String> fileIds;
    @ParamNotNull
    private List<String> presetIds;
    //private List<ResearchPresetInfo> presetInfos;
    private String presetParam;
}
