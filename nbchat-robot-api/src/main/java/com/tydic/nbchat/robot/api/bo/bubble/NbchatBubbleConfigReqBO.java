package com.tydic.nbchat.robot.api.bo.bubble;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class NbchatBubbleConfigReqBO extends BasePageInfo implements Serializable {

    private String userId;
    private Integer bubbleId;
    private String type;
    @ParamNotEmpty
    private String configLevel;
    @ParamNotEmpty
    private String configLevelVal;

}
