package com.tydic.nbchat.robot.api.bo.sensitive;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: 会话关闭  <br>
 * @date 2021/7/14 2:27 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ChatSensitiveOrignMsgBO implements Serializable {
    private String tenantCode;

    private String channelCode;

    private String sessionId;

    private String msgId;

    private String chatType;

    private String chatKey;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date msgTime;

    private String msgContent;

    private String msgFilter;

    private String fromNo;

    private String toNo;

    private String msgType;

    private String msgForm;

    private Short msgStatus;
}
