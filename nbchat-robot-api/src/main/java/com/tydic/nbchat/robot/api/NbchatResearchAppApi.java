package com.tydic.nbchat.robot.api;

import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppBO;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppSaveRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatResearchAppApi {

    /**
     * 创建/更新应用
     * @param request
     * @return
     */
    Rsp saveApp(ResearchAppSaveRequest request);

    /**
     * 删除应用
     * @param appId
     * @return
     */
    Rsp deleteApp(String appId);

    /**
     * 获取应用列表
     * @param request
     * @return
     */
    RspList<ResearchAppBO> getAppsList(ResearchAppQueryRequest request);

    /**
     * 获取应用
     * @param appId
     * @return
     */
    Rsp<ResearchAppBO> getApp(String appId);
}
