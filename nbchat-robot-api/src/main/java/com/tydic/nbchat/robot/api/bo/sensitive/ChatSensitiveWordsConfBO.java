package com.tydic.nbchat.robot.api.bo.sensitive;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
public class ChatSensitiveWordsConfBO implements Serializable {
    private Integer sensitiveId;
    private String tenantCode;
    private Integer sensitiveState;
    private String sensitiveFile;
    private String wordType;
    private String loadType;
    private String encrypt;
    private String split;
    private Date crtTime;

    public List<String> getFileList(){
        List<String> fileList = new ArrayList<>();
        if(StringUtils.isNotEmpty(sensitiveFile)){
            String[] fileArr = sensitiveFile.split(",");
            return Arrays.asList(fileArr);
        }
        return fileList;
    }
}
