package com.tydic.nbchat.robot.api.bo.research;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResearchPartResultRsp implements Serializable {
    private Boolean researchCitationEnable = false;
    private String researchCitationPrefix = "\n 该回答来自文档:\n";
    private List<ResearchPartResult> parts;
}
