package com.tydic.nbchat.user.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.UserRoleDeptApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.role.SubSystemInfo;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserMapper;
import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class UserInfoBusiService {

    @Resource
    private UserMapper userMapper;

    private final UserVipService userVipService;
    private final UserSettingHelper userSettingHelper;
    private final UserRoleDeptApi userRoleDeptApi;

    public UserInfoBusiService(UserVipService userVipService,
                               UserSettingHelper userSettingHelper,
                               UserRoleDeptApi userRoleDeptApi) {
        this.userVipService = userVipService;
        this.userSettingHelper = userSettingHelper;
        this.userRoleDeptApi = userRoleDeptApi;
    }

    /**
     * 更新用户手机号
     * @param userId
     * @param phone
     */
    public void updatePhone(String userId, String phone) {
        UserPO userPO = new UserPO();
        userPO.setUserId(userId);
        userPO.setPhone(phone);
        userPO.setUpdatedTime(new Date());
        UserPO userInfo = userMapper.findUserByMobile(phone);
        if (userInfo != null && !userInfo.getUserId().equals(userId)) {
            log.error("更新手机号失败-手机号已被其他账户使用: {}|{}",userInfo.getUserId(), phone);
            return;
        }
        userSettingHelper.removeInfo(userId);
        userMapper.updateById(userPO);
    }

    /**
     * 更新用户租户
     * @param userInfo
     * @param platform
     */
    private void parserSettingPlatformTenant(NbchatUserInfo userInfo,String platform) {
        Map<Object,Object> settingMap = userSettingHelper.loadSetting(userInfo.getUserId());
        userInfo.setSettings(settingMap);
        if (StringUtils.isNotBlank(platform) && settingMap.containsKey(platform)) {
            //获取平台对应租户
            if (settingMap.get(platform) instanceof JSONObject) {
                JSONObject param = (JSONObject) settingMap.get(platform);
                String tenantCode = param.getString("tenantCode");
                if(StringUtils.isNotBlank(tenantCode)) {
                    userInfo.setTenantCode(tenantCode);
                }
            }
        }
    }

    private void packageUserInfo(NbchatUserInfo userInfo) {
        //获取用户租户列表
        List<String> tenants = userMapper.getTenantCodeList(userInfo.getUserId());
        if (CollectionUtils.isEmpty(tenants)) {
            tenants = Collections.singletonList(userInfo.getTenantCode());
        }
        userInfo.setTenantCodeList(tenants);
        updateCurrentSubsystem(userInfo);
        userSettingHelper.cacheInfo(userInfo);
    }

    public NbchatUserInfo getUserInfo(String userId) {
        return getUserInfo(userId, "");
    }

    /**
     * 重新缓存用户信息
     * @param userId
     * @param subsystem
     */
    public void reloadUserInfo(String userId,String subsystem) {
        UserPO userPO = userMapper.findUserByUserId(userId);
        NbchatUserInfo userInfo = new NbchatUserInfo();
        BeanUtils.copyProperties(userPO, userInfo);
        userInfo.setTenantCode(subsystem);
        userInfo.setRegTime(userPO.getCreatedTime());
        parserSettingPlatformTenant(userInfo,"");
        packageUserInfo(userInfo);
        userSettingHelper.cacheInfo(userInfo);
    }

    /**
     * 获取指定平台用户信息
     * @param userId
     * @param platform
     * @return
     */
    public NbchatUserInfo getUserInfo(String userId,String platform) {
        NbchatUserInfo userInfo = userSettingHelper.getUserInfo(userId);
        if (userInfo == null) {
            UserPO userPO = userMapper.findUserByUserId(userId);
            if (userPO != null) {
                userInfo = new NbchatUserInfo();
                BeanUtils.copyProperties(userPO, userInfo);
                userInfo.setRegTime(userPO.getCreatedTime());
                parserSettingPlatformTenant(userInfo,platform);
                packageUserInfo(userInfo);
                return userInfo;
            }
        } else {
            parserSettingPlatformTenant(userInfo,platform);
            updateCurrentSubsystem(userInfo);
        }
        return userInfo;
    }

    private void updateCurrentSubsystem(NbchatUserInfo userInfo) {
        Map<String, SubSystemInfo> systemInfoMap = new LinkedHashMap<>();
        if (userInfo.getTenantCodeList().contains(UserAttributeConstants.TMO_TENANT)) {
            Rsp<SubSystemInfo> tmoSystem = userRoleDeptApi.getUserSubSysInfo(userInfo.getUserId(),
                    UserAttributeConstants.TMO_TENANT);
            if (tmoSystem.isSuccess()) {
                systemInfoMap.put(UserAttributeConstants.TMO_TENANT, tmoSystem.getData());
            }
        }
        userInfo.setSubSystems(systemInfoMap);
        //重新加载用户子系统信息
        Rsp<SubSystemInfo> systemInfoRsp = userRoleDeptApi.getUserSubSysInfo(userInfo.getUserId(),
                userInfo.getTenantCode());
        if (systemInfoRsp.isSuccess()) {
            systemInfoMap.put(userInfo.getTenantCode(), systemInfoRsp.getData());
        }
        userInfo.setSubSystems(systemInfoMap);
        //获取用户VIP信息
        userInfo.setVipInfo(userVipService.getVipInfoBO(userInfo.getTenantCode(), userInfo.getUserId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public Rsp updateById(UserBO userBO) {
        log.info("更新用户信息: {}", userBO);
        UserPO userPO = new UserPO();
        BeanUtils.copyProperties(userBO, userPO);
        userPO.setTenantCode(userBO.getTargetTenant());
        userPO.setUpdatedTime(new Date());
        userPO.setUpdatedBy(userBO.getUserId());
        log.info("更新用户信息-查询用户租户");
        List<String> tenantCodeList = userMapper.selectTenantCodeByUserId(userBO.getUserId());
        if (CollectionUtils.isEmpty(tenantCodeList)) {
            log.info("更新用户信息-用户租户为空");
            return BaseRspUtils.createErrorRsp("用户租户为空");
        }
        if (!tenantCodeList.contains(userPO.getTenantCode())) {
            log.info("更新用户信息-该用户不属于该租户");
            return BaseRspUtils.createErrorRsp("没有租户权限");
        }
        userMapper.updateById(userPO);
        userSettingHelper.removeInfo(userBO.getUserId());
        return BaseRspUtils.createSuccessRsp("", "更新成功");
    }

    /**
     * 通过手机号查询用户信息
     * @param phone
     * @return
     */
    public Rsp<NbchatUserInfo> getUserByPhone(String phone) {
        if (StringUtils.isEmpty(phone) || !PhoneNumberUtils.validPhoneNumber(phone)) {
            log.error("输入的手机号不合法: {}", phone);
            return BaseRspUtils.createErrorRsp("请输入合法的手机号");
        }
        UserPO user = userMapper.findUserByMobile(phone);
        if (user != null) {
            NbchatUserInfo userInfo = new NbchatUserInfo();
            BeanUtils.copyProperties(user, userInfo);
            userInfo.setRegTime(user.getCreatedTime());
            parserSettingPlatformTenant(userInfo,"");
            packageUserInfo(userInfo);
            return BaseRspUtils.createSuccessRsp(userInfo, "查询成功");
        }
        return BaseRspUtils.createErrorRsp("查询失败");
    }

    /**
     * 获取用户基本信息
     * @param userId
     * @return
     */
    public NbchatUserInfo getUserBaseInfo(String userId) {
        UserPO userPO = userMapper.findUserByUserId(userId);
        if (userPO == null) {
            throw new RuntimeException("用户不存在: uid = " + userId);
        }
        NbchatUserInfo userInfo = new NbchatUserInfo();
        BeanUtils.copyProperties(userPO, userInfo);
        return userInfo;
    }

    /**
     * 批量查询
     * @param userIdList
     * @return
     */
    public RspList<NbchatUserInfo> getUserInfoList(List<String> userIdList) {
        List<UserPO> list = userMapper.selectByUserIdList(userIdList);
        if (CollectionUtils.isEmpty(list)) {
            return BaseRspUtils.createErrorRspList("查询失败:用户信息不存在");
        }
        List<NbchatUserInfo> userInfoList = new ArrayList<>();
        NiccCommonUtil.copyList(list, userInfoList, NbchatUserInfo.class);
        return BaseRspUtils.createSuccessRspList(userInfoList);
    }

}
