package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.setting.CheckSettingBindCodeReqBO;
import com.tydic.nbchat.user.api.bo.setting.CreateSettingBindCodeReqBO;
import com.tydic.nbchat.user.api.bo.setting.UserSettingFlashReqBO;
import com.tydic.nbchat.user.api.bo.setting.UserSettingReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserSettingsController {
    private final UserSettingsApi userSettingsApi;

    public UserSettingsController(UserSettingsApi userSettingsApi) {
        this.userSettingsApi = userSettingsApi;
    }

    /**
     * 批量更新用户默认机器人
     * @param currentRobotType
     * @param setRobotType
     * @return
     */
    @GetMapping("/settings/batch/update/{currentRobotType}/{setRobotType}")
    public Rsp updateDefaultRobot(@PathVariable("currentRobotType") String currentRobotType,
                           @PathVariable("setRobotType") String setRobotType){
        return userSettingsApi.updateDefaultRobot(currentRobotType,setRobotType);
    }

    @PostMapping("/settings/bindcode/check")
    public Rsp checkSettingBindCode(@RequestBody CheckSettingBindCodeReqBO reqBO){
        try {
            return userSettingsApi.checkSettingBindCode(reqBO);
        } catch (Exception e) {
            log.error("校验绑定码异常:",e);
            return BaseRspUtils.createErrorRsp("校验绑定码异常!");
        }
    }

    @PostMapping("/settings/bindcode/generate")
    public Rsp createSettingBindCode(@RequestBody CreateSettingBindCodeReqBO reqBO){
        try {
            return userSettingsApi.createSettingBindCode(reqBO);
        } catch (Exception e) {
            log.error("创建绑定码异常:",e);
            return BaseRspUtils.createErrorRsp("创建绑定码异常!");
        }
    }


    @PostMapping("/settings/save")
    public Rsp saveSettings(@RequestBody UserSettingReqBO reqBO){
        try {
            return userSettingsApi.saveSettings(reqBO);
        } catch (Exception e) {
            log.error("用户设置保存异常:",e);
            return BaseRspUtils.createErrorRsp("用户设置保存异常!");
        }
    }

    @PostMapping("/settings/flash")
    public Rsp flashSettings(@RequestBody UserSettingFlashReqBO reqBO){
        return userSettingsApi.flashSettings(reqBO);
    }

    @PostMapping("/settings")
    public Rsp loadSettings(@RequestBody UserSettingReqBO reqBO){
        return userSettingsApi.loadSettings(reqBO.getUserId());
    }
}
