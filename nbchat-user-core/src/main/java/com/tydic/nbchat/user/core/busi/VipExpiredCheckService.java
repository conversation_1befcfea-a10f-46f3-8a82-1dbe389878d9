package com.tydic.nbchat.user.core.busi;


import com.tydic.nbchat.user.api.bo.eums.UserVipStatusType;
import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import com.tydic.nbchat.user.core.config.WchatConfigProperties;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.core.wx.WxSendMsgService;
import com.tydic.nbchat.user.mapper.NbchatUserVipMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserVip;
import com.tydic.nicc.common.nbchat.emus.VipEventType;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class VipExpiredCheckService {

    @Resource
    private NbchatUserVipMapper nbchatUserVipMapper;
    private final UserMakeEventSender userMakeEventSender;
    private final WxSendMsgService wxSendMsgService;
    private final VipSmsNoticeService vipSmsNoticeService;
    private final WchatConfigProperties wchatConfigProperties;
    private final ScoreAccountService scoreAccountService;
    private final UserSettingHelper userSettingHelper;

    public VipExpiredCheckService(UserMakeEventSender userMakeEventSender, WxSendMsgService wxSendMsgService, VipSmsNoticeService vipSmsNoticeService, WchatConfigProperties wchatConfigProperties, ScoreAccountService scoreAccountService, UserSettingHelper userSettingHelper) {
        this.userMakeEventSender = userMakeEventSender;
        this.wxSendMsgService = wxSendMsgService;
        this.vipSmsNoticeService = vipSmsNoticeService;
        this.wchatConfigProperties = wchatConfigProperties;
        this.scoreAccountService = scoreAccountService;
        this.userSettingHelper = userSettingHelper;
    }

    public void doVipExpiredCheck(Date checkTime) {
        log.info("用户VIP过期检测，开始执行:{}", checkTime);
        List<NbchatUserVip> vips = nbchatUserVipMapper.selectExpiredVip(checkTime);
        for (NbchatUserVip userVip : vips) {
            try {
                log.info("用户VIP过期检测，处理过期账户:{}", userVip);
                NbchatUserVip record = new NbchatUserVip();
                record.setVipStatus(UserVipStatusType.EXPIRED.getCode());
                record.setUpdateTime(new Date());
                record.setId(userVip.getId());
                nbchatUserVipMapper.updateByPrimaryKeySelective(record);
                userVip.setVipStatus(UserVipStatusType.EXPIRED.getCode());
                userMakeEventSender.sendVipChangeEvent(VipEventType.VIP_EXPIRE.getCode(), userVip, 0, 0);
                //冻结积分
                scoreAccountService.freezeScore(userVip.getTenantCode(), userVip.getUserId(), userVip.getVipType());
                //发送微信公众号通知
                String vipCloseNotice = wchatConfigProperties.getVipCloseNotice();
                vipCloseNotice = vipCloseNotice.replace("#{vipType}", UserVipType.getNameByCode(userVip.getVipType()));
                vipCloseNotice = vipCloseNotice.replace("#{endDate}", DateTimeUtil.getTimeShortString(userVip.getVipEnd(), DateTimeUtil.DATE_FORMAT_CHINA));
                wxSendMsgService.sendTextMsg(userVip.getUserId(), vipCloseNotice);
                //发送短信通知
                vipSmsNoticeService.sendVipCloseSms(userVip.getUserId(), UserVipType.getNameByCode(userVip.getVipType()), userVip.getVipEnd());
            } catch (Exception e) {
                log.error("用户VIP过期检测，处理异常:{}", userVip, e);
            } finally {
                try {
                    userSettingHelper.removeVipInfo(userVip.getTenantCode(), userVip.getUserId());
                } catch (Exception e) {
                    log.error("用户VIP过期检测，清除缓存异常:{}", userVip, e);
                }
            }
        }
        log.info("用户VIP过期检测，执行完成:{}", vips.size());
    }


}
