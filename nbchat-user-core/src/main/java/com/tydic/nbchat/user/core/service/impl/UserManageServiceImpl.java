package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.UserManageService;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.manage.ImportUserInfo;
import com.tydic.nbchat.user.api.bo.manage.ImportUserRequest;
import com.tydic.nbchat.user.api.bo.manage.UpdateUserRequest;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceAccountReqBO;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.core.utils.PasswordHasher;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserMapper;
import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class UserManageServiceImpl implements UserManageService {

    private final UserMapper userMapper;
    private final UserSettingHelper userSettingHelper;
    private final TradeBalanceApi tradeBalanceApi;
    public UserManageServiceImpl(UserMapper userMapper,
                                 UserSettingHelper userSettingHelper,
                                 TradeBalanceApi tradeBalanceApi) {
        this.userMapper = userMapper;
        this.userSettingHelper = userSettingHelper;
        this.tradeBalanceApi = tradeBalanceApi;
    }

    @Override
    public Rsp updateInfo(UpdateUserRequest updateUserRequest) {
        log.info("更新用户信息:{}",updateUserRequest);
        if(StringUtils.isAllBlank(updateUserRequest.getTargetUid(),updateUserRequest.getTargetPhone())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        if(StringUtils.isBlank(updateUserRequest.getTargetUid()) &&
                StringUtils.isNotBlank(updateUserRequest.getTargetPhone())){
            UserPO query = userMapper.findUserByMobile(updateUserRequest.getTargetPhone());
            if(query == null){
                return BaseRspUtils.createErrorRsp("未查询到该用户:phone = " + updateUserRequest.getTargetPhone());
            }
            updateUserRequest.setTargetUid(query.getUserId());
        }
        UserPO update = new UserPO();
        BeanUtils.copyProperties(updateUserRequest,update);
        update.setUserId(updateUserRequest.getTargetUid());
        update.setTenantCode(updateUserRequest.getTenant());
        update.setUpdatedTime(new Date());
        update.setUpdatedBy(updateUserRequest.getUserId());
        int i = userMapper.updateById(update);
        if(i > 0){
            //移除缓存信息
            userSettingHelper.removeInfo(updateUserRequest.getTargetUid());
            return BaseRspUtils.createSuccessRsp("更新成功!");
        }
        return BaseRspUtils.createErrorRsp("更新失败!");
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp importUsers(ImportUserRequest importUserRequest) {
        log.info("用户信息导入-开始:{}",importUserRequest);
        if(StringUtils.isAnyBlank(importUserRequest.getUserId(),importUserRequest.getTenant())){
            return BaseRspUtils.createErrorRsp("导入失败：参数异常！");
        }
        JSONObject importDetails = new JSONObject();
        List<ImportUserInfo> errList = Lists.newArrayList();
        int registCount = 0;
        int updateCount = 0;
        String password = importUserRequest.getPassword();
        for (ImportUserInfo user : importUserRequest.getUsers()) {
            if(StringUtils.isAnyBlank(user.getName(),user.getPhone())){
                errList.add(user);
                log.info("用户信息导入-跳过异常用户:{}",user);
                continue;
            }
            if(user.getPhone().length() != 11){
                errList.add(user);
                log.info("用户信息导入-跳过手机号异常:{}",user);
                continue;
            }
            UserPO query = userMapper.findUserByMobile(user.getPhone());
            if(query == null){
                //注册用户
                String uid = IdWorker.nextAutoIdStr();
                UserPO insert = new UserPO();
                insert.setUserId(uid);
                insert.setGender(UserAttributeConstants.DEFAULT_GENDER);
                insert.setName(user.getName());
                insert.setUserName(user.getUsername());
                insert.setEmail(user.getEmail());
                insert.setTenantCode(importUserRequest.getTenant());
                insert.setUpdatedTime(new Date());
                insert.setCreatedTime(new Date());
                insert.setCreatedBy(importUserRequest.getUserId());
                insert.setIsDeleted(false);
                insert.setPassword(password);
                insert.setPhone(user.getPhone());
                if(StringUtils.isBlank(insert.getPassword())){
                    //使用手机号后6位
                    insert.setPassword(user.getPhone().substring(5,11));
                }
                if(StringUtils.isBlank(user.getUsername())){
                    //使用手机号
                    insert.setUserName(user.getPhone());
                }
                log.info("用户信息导入-注册用户:{}",insert);
                if (StringUtils.isNotBlank(insert.getPassword())) {
                    insert.setPassword(PasswordHasher.sha1(insert.getPassword()));
                }
                userMapper.registerUser(insert);
                //注册完成通知
                UserRegistContext registContext = new UserRegistContext();
                BeanUtils.copyProperties(insert,registContext);
                registContext.setUserRealityName(user.getName());
                registContext.setJoinType(JoinTenantType.IMPORTS.getCode());
                userSettingHelper.userRegistNotice(registContext);
                UserBalanceAccountReqBO accountReqBO = new UserBalanceAccountReqBO();
                accountReqBO.setUserId(uid);
                accountReqBO.setTenantCode(importUserRequest.getTenant());
                tradeBalanceApi.createBalanceAccount(accountReqBO);
                registCount ++;
            } else {
                //更新
                UserPO update = new UserPO();
                update.setUserId(query.getUserId());
                update.setName(user.getName());
                update.setEmail(user.getEmail());
                update.setTenantCode(importUserRequest.getTenant());
                update.setUpdatedTime(new Date());
                update.setUpdatedBy(importUserRequest.getUserId());
                log.info("用户信息导入-更新用户:{}",update);
                userMapper.updateById(update);
                updateCount ++;
                //移除缓存信息
                userSettingHelper.removeInfo(query.getUserId());
            }
        }
        importDetails.put("registCount",registCount);
        importDetails.put("updateCount",updateCount);
        importDetails.put("errorList",errList);
        return BaseRspUtils.createSuccessRsp(importDetails,"用户导入成功!");
    }

}
