package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.core.timer.UserRightsExpiredTimer;
import com.tydic.nbchat.user.core.timer.UserScoreExpiredTimer;
import com.tydic.nbchat.user.core.timer.UserScoreRechargeTimer;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user/timer/click")
public class TimerTaskClickController {

    @Autowired(required = false)
    private UserScoreExpiredTimer userScoreExpiredTimer;
    @Autowired(required = false)
    private UserScoreRechargeTimer userScoreRechargeTimer;
    @Autowired(required = false)
    private UserRightsExpiredTimer userRightsExpiredTimer;

    @GetMapping("/userScoreExpiredTimer")
    public Rsp userScoreExpiredTimer(){
        userScoreExpiredTimer.run();
        return BaseRspUtils.createSuccessRsp("执行成功");
    }

    @GetMapping("/userScoreRechargeTimer")
    public Rsp userScoreRechargeTimer(){
        userScoreRechargeTimer.run();
        return BaseRspUtils.createSuccessRsp("执行成功");
    }

    @GetMapping("/userRightsExpiredTimer")
    public Rsp userRightsExpiredTimer(){
        userRightsExpiredTimer.run();
        return BaseRspUtils.createSuccessRsp("执行成功");
    }

}
