package com.tydic.nbchat.user.core.wx.event.handler;

import com.tydic.nbchat.user.core.enmus.WxEventEnum;
import com.tydic.nbchat.user.core.wx.context.WxMsgBaseContext;
import com.tydic.nbchat.user.core.wx.event.api.EventHandler;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TemplateMsgEventHandler  implements EventHandler {

    @Override
    public String event() {
        return WxEventEnum.TEMPLATESENDJOBFINISH.getCode();
    }

    @Override
    public <T extends WxMsgBaseContext> Rsp<?> handle(T eventMsg) {
        log.info("处理模版消息事件:{}",eventMsg);
        return BaseRspUtils.createSuccessRsp("");
    }
}
