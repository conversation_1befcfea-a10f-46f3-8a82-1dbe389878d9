package com.tydic.nbchat.user.core.config;

import com.tydic.nbchat.user.core.busi.UserAuthBusiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ApplicationInitRunner implements CommandLineRunner {

    @Resource
    NbchatUserConfigProperties properties;

    @Override
    public void run(String... args) throws Exception {
        if (StringUtils.isNotBlank(properties.getAuthTokenKey())) {
            UserAuthBusiService.HEADER_TOKEN_KEY = properties.getAuthTokenKey();
        }
    }
}