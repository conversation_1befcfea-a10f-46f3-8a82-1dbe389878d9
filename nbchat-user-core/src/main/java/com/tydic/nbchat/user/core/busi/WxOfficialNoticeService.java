package com.tydic.nbchat.user.core.busi;

import com.tydic.nbchat.user.api.bo.notice.NoticeContext;
import com.tydic.nbchat.user.core.config.WchatConfigProperties;
import com.tydic.nbchat.user.core.wx.WxSendMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WxOfficialNoticeService {

    @Resource
    WchatConfigProperties wxConfigProperties;
    private final WxSendMsgService wxSendMsgService;

    public WxOfficialNoticeService(WxSendMsgService wxSendMsgService) {
        this.wxSendMsgService = wxSendMsgService;
    }

     /**
     * 构建充值通知参数：
     * 单号、充值金额、充值项目
     *
     * 构建视频通知参数：
     * 产品名称、商品名称、完成时间
     */
    public void sendNotice(NoticeContext context) {
        Map<String, WchatConfigProperties.TemplateParam> paramMap = wxConfigProperties.getTemplates();
        if (!paramMap.containsKey(context.getTemplateType())) {
            log.error("模板消息类型不存在,templateType:{}", context.getTemplateType());
            return;
        }
        WchatConfigProperties.TemplateParam templateParam = paramMap.get(context.getTemplateType());
        List<String> keys = templateParam.getKeys();
        List<String> params = context.getParams();
        if (!this.checkParam(keys, params)) {
            return;
        }
        wxSendMsgService.sendTemplateMsg(context);
    }

    public boolean checkParam(List<String> keys, List<String> params) {
        boolean b = keys.size() == params.size();
        if (!b) {
            log.error("模板消息参数不匹配:keys={}|params={}", keys, params);
        }
        return b;
    }
}
