package com.tydic.nbchat.user.core.wx.event.handler;

import com.tydic.nbchat.user.core.enmus.WxEventEnum;
import com.tydic.nbchat.user.core.wx.event.api.EventHandler;
import com.tydic.nbchat.user.core.wx.context.WxMsgBaseContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UnFollowingEventHandler implements EventHandler {

    @Override
    public String event() {
        return WxEventEnum.UNSUBSCRIBE.getCode();
    }

    @Override
    public Rsp<T> handle(WxMsgBaseContext eventMsg) {
        log.info("用户取消关注:{}",eventMsg);
        return BaseRspUtils.createSuccessRsp("");
    }

}
