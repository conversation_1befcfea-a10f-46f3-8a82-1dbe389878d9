package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.api.bo.trade.*;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.api.trade.TradeBillRecordApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 用户交易模块
 */
@Slf4j
@RestController
@RequestMapping("/user/trade")
public class UserTradeController {

    private final TradeBalanceApi tradeBalanceApi;
    private final TradeBillRecordApi tradeBillRecordApi;

    public UserTradeController(TradeBalanceApi tradeBalanceApi, TradeBillRecordApi tradeBillRecordApi) {
        this.tradeBalanceApi = tradeBalanceApi;
        this.tradeBillRecordApi = tradeBillRecordApi;
    }

    @PostMapping("/account/create")
    public Rsp createBalanceAccount(@RequestBody UserBalanceAccountReqBO accountReqBO) {
        return tradeBalanceApi.createBalanceAccount(accountReqBO);
    }

    /**
     * 用户余额查询
     * @param baseInfo
     * @return
     */
    @PostMapping("/balance/info")
    public Rsp balance(@RequestBody UserBalanceQueryReqBO baseInfo) {
        return tradeBalanceApi.getBalance(baseInfo.getTenantCode(), baseInfo.getUserId());
    }


    @PostMapping("/balance/check")
    public Rsp check(@RequestBody UserBalanceRechargeReqBO checkReqBO) {
        return tradeBalanceApi.deductCheck(checkReqBO);
    }

    @PostMapping("/balance/recharge")
    public Rsp recharge(@RequestBody UserBalanceRechargeReqBO rechargeReqBO) {
        return tradeBalanceApi.recharge(rechargeReqBO);
    }

    @PostMapping("/balance/deduct")
    public Rsp deduct(@RequestBody UserBalanceDeductReqBO deductReqBO) {
        return tradeBalanceApi.deduct(deductReqBO);
    }

    @PostMapping("/balance/sys_deduct")
    public Rsp sysDeduct(@RequestBody UserBalanceDeductReqBO deductReqBO) {
        try {
            if (StringUtils.isNoneBlank(deductReqBO.getDeductTenantCode(), deductReqBO.getDeductUserId())) {
                deductReqBO.setTenantCode(deductReqBO.getDeductTenantCode());
                deductReqBO.setUserId(deductReqBO.getDeductUserId());
                return tradeBalanceApi.deduct(deductReqBO);
            }
            return BaseRspUtils.createErrorRsp("余额扣减异常");
        } catch (Exception e) {
            log.error("余额扣减-异常: {}", deductReqBO, e);
        }
        return BaseRspUtils.createErrorRsp("余额扣减异常");
    }

    //退款
    @PostMapping("/balance/refund")
    public Rsp refund(@RequestBody UserBalanceRefundReqBO refundReqBO) {
        return tradeBalanceApi.refund(refundReqBO);
    }


    @PostMapping("/bill/list")
    public RspList<UserBillRecordBO> getBillRecord(@RequestBody UserBillRecordQueryReqBO queryReqBO){
        return tradeBillRecordApi.getBillRecord(queryReqBO);
    }

}
