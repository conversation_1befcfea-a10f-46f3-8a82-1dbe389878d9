package com.tydic.nbchat.user.core.service.sms;

import com.tydic.nbchat.user.api.bo.constants.AreaCodeConstants;
import com.tydic.nbchat.user.core.config.SmsTemplateProperties;
import com.tydic.nbchat.user.core.service.sms.strategy.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 短信模板服务
 * 管理短信模板和区号映射关系
 * 使用策略模式处理不同区号的模板选择
 */
@Slf4j
@Service
public class SmsTemplateService {

    private final SmsTemplateProperties smsTemplateProperties;
    private final TemplateStrategyFactory strategyFactory;

    // 模板代码到模板ID的映射缓存 - 用于反向查找
    private final Map<String, String> templateCodeToIdMap = new ConcurrentHashMap<>();

    public SmsTemplateService(SmsTemplateProperties smsTemplateProperties, TemplateStrategyFactory strategyFactory) {
        this.smsTemplateProperties = smsTemplateProperties;
        this.strategyFactory = strategyFactory;
    }

    @PostConstruct
    public void init() {
        // 初始化模板代码到模板ID的映射缓存，用于反向查找
        smsTemplateProperties.getDefaultTemplates().forEach((templateCode, templateId) -> {
            templateCodeToIdMap.put(templateCode, templateId);
        });

        // 如果配置了 86 区号的模板，将其添加到反向查找缓存
        Map<String, String> chinaTplMap = smsTemplateProperties.getAreaCodes().get(AreaCodeConstants.CHINA_MAINLAND);
        if (chinaTplMap != null && !chinaTplMap.isEmpty()) {
            templateCodeToIdMap.putAll(chinaTplMap);
        }

        log.info("短信模板服务初始化完成，模板代码数量: {}", templateCodeToIdMap.size());
    }

    /**
     * 根据模板代码获取默认模板ID
     * @param templateCode 模板代码
     * @return 默认模板ID
     */
    public String getDefaultTemplateId(String templateCode) {
        TemplateStrategy defaultStrategy = strategyFactory.getStrategy(AreaCodeConstants.CHINA_MAINLAND);
        return defaultStrategy.getTemplateId(templateCode);
    }

    /**
     * 根据区号和模板代码获取适合的模板ID
     * @param areaCode 区号
     * @param templateCode 模板代码
     * @return 适合该区号的模板ID，如果没有特定模板则返回默认模板ID
     */
    public String getTemplateIdByAreaCode(String areaCode, String templateCode) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(templateCode)) {
            return templateCode;
        }

        // 获取适用于该区号的模板策略
        TemplateStrategy strategy = strategyFactory.getStrategy(areaCode);

        // 使用策略获取模板ID
        return strategy.getTemplateId(templateCode);
    }

    /**
     * 获取模板代码
     * 如果传入的是模板ID，尝试反向查找对应的模板代码
     * @param templateIdOrCode 模板ID或模板代码
     * @return 模板代码，如果找不到则返回原值
     */
    public String getTemplateCode(String templateIdOrCode) {
        // 如果是模板代码，直接返回
        if (templateCodeToIdMap.containsKey(templateIdOrCode)) {
            return templateIdOrCode;
        }

        // 尝试反向查找模板代码
        for (Map.Entry<String, String> entry : templateCodeToIdMap.entrySet()) {
            if (entry.getValue().equals(templateIdOrCode)) {
                return entry.getKey();
            }
        }

        // 如果找不到，返回原值
        return templateIdOrCode;
    }
}
