package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.AuthenticationService;
import com.tydic.nbchat.user.api.bo.ShareSignReqBO;
import com.tydic.nbchat.user.core.wx.WxAuthHelper;
import com.tydic.nbchat.user.core.config.WchatConfigProperties;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.core.enmus.WxChannelEnum;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2023/04/11
 * @email <EMAIL>
 * @description 身份验证impl
 */
@Slf4j
@Service("authentication")
public class AuthenticationImpl implements AuthenticationService {
    /**
     * wchat配置属性
     */
    private final WchatConfigProperties wchatConfigProperties;

    private final RedisHelper redisHelper;
    private final RestApiHelper restApiHelper;

    private final WxAuthHelper wxAuthHelper;
    public AuthenticationImpl(WchatConfigProperties wchatConfigProperties, RedisHelper redisHelper, RestApiHelper restApiHelper, WxAuthHelper wxAuthHelper) {
        this.wchatConfigProperties = wchatConfigProperties;
        this.redisHelper = redisHelper;
        this.restApiHelper = restApiHelper;
        this.wxAuthHelper = wxAuthHelper;
    }

    /**
     * 对url进行签名
     * @param @param url url
     * @return @return {@link Rsp }
     */
    @MethodParamVerifyEnable
    @Override
    public Rsp sign(ShareSignReqBO shareSignReqBO) {
        log.info("签名-入参：{}",shareSignReqBO);
        String url = shareSignReqBO.getUrl();
        Object jsk = redisHelper.get(RedisConstants.WX_SIGN);
        String jsapiTicket = jsk == null ? null : jsk.toString();
        //判断jsapi_ticket是否在redis中
        if (StringUtils.isEmpty(jsapiTicket)) {
            log.info("签名-获取access_token");
            //获取access_token
            //String accessToken = getAccessToken();
            String accessToken = wxAuthHelper.getAccessToken(WxChannelEnum.MOBILE.getCode());
            if (accessToken == null) {
                log.error("签名-获取access_token失败");
                return BaseRspUtils.createErrorRsp("获取签名失败-获取access_token失败");
            }
            log.info("签名-获取jsapi_ticket");
            //获取jsapi_ticket
            jsapiTicket = getJsapiTicket(accessToken);
            if (jsapiTicket == null) {
                log.error("签名-获取jsapi_ticket失败");
                return BaseRspUtils.createErrorRsp("获取签名失败-获取jsapi_ticket失败");
            }
            log.info("签名-将jsapi_ticket存入redis");
            //将jsapi_ticket存入redis
            redisHelper.set(RedisConstants.WX_SIGN, jsapiTicket, 7000);
        }
        log.info("签名-进行拼接字符串");
        String timestamp = String.valueOf(getCurrentTimestamp());
        String nonceStr = generateRandomString(16);
        //拼接字符串
        StringBuilder str = new StringBuilder();
        str.append("jsapi_ticket=").append(jsapiTicket).append("&noncestr=").append(nonceStr).append("&timestamp=").append(timestamp).append("&url=").append(url);
        log.info("签名-拼接字符串:{}", str);
        log.info("签名-进行字符串签名");
        //字符串签名
        String signature = getSHA1(str.toString());
        log.info("签名-签名成功:{}", signature);
        //封装返回数据
        HashMap<String, Object> map = new HashMap<>();
        map.put("appId", wchatConfigProperties.getChannels().get(WxChannelEnum.MOBILE.getCode()).getAppId());
        map.put("timestamp",timestamp);
        map.put("nonceStr",nonceStr);
        map.put("signature", signature);
        log.info("签名-返回数据:{}", map);
        return BaseRspUtils.createSuccessRsp(map, "签名成功");
    }

    /**
     * 获取access_token
     * @param
     * @return @return {@link String }
     */
   /* private String getAccessToken(){
        String appId = wchatConfigProperties.getChannels().get(WxChannelEnum.MOBILE.getCode()).getAppId();
        String appSecret = wchatConfigProperties.getChannels().get(WxChannelEnum.MOBILE.getCode()).getSecret();
        String url = wchatConfigProperties.getChannels().get(WxChannelEnum.MOBILE.getCode()).getTokenUrl();
        //String appId = wchatConfigProperties.getAppId();
        //String appSecret = wchatConfigProperties.getAppSecret();
        //String url = "https://api.weixin.qq.com/cgi-bin/token";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("grant_type", "client_credential");
        paramMap.put("appid", appId);
        paramMap.put("secret", appSecret);
        String response = restApiHelper.get(url, "", paramMap);
        //获取access_token
        JSONObject accessTokenJson = JSONObject.parseObject(response);
        if (accessTokenJson.containsKey("access_token")) {
            String accessToken = accessTokenJson.getString("access_token");
            log.info("签名-获取access_token成功:{}", accessToken);
            return accessToken;

        }
        // 错误处理
        log.error("签名-获取access_token错误:{}", accessTokenJson.getString("errmsg"));
        return null;
    }*/

    /**
     * 获取jsapi_ticket
     * @param @param accessToken 访问令牌
     * @return @return {@link String }
     */
    private String getJsapiTicket(String accessToken){
        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("access_token", accessToken);
        paramMap.put("type", "jsapi");
        String response = restApiHelper.get(url, "", paramMap);
        //获取access_token
        JSONObject accessTokenJson = JSONObject.parseObject(response);
        if (accessTokenJson.containsKey("ticket")) {
            String jsapiTicket = accessTokenJson.getString("ticket");
            log.info("签名-获取jsapi_ticket成功:{}", jsapiTicket);
            return jsapiTicket;
        }
        log.error("签名-获取jsapi_ticket错误");
        return null;
    }

    /**
     * 对输入字符串进行SHA-1签名
     * @param input 待签名的字符串
     * @return 经过SHA-1签名后的字符串
     */
    public String getSHA1(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(messageDigest);
        } catch (NoSuchAlgorithmException e) {
            log.error("签名-获取SHA-1算法失败");
            throw new RuntimeException("Error: SHA-1 algorithm not found", e);
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 输入的字节数组
     * @return 转换后的十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 获取当前时间戳
     * @return 当前时间戳，以秒为单位
     */
    public long getCurrentTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        ZonedDateTime zdt = now.atZone(ZoneId.systemDefault());
        return zdt.toInstant().getEpochSecond();
    }

    /**
     * 生成随机字符串
     * @param length 随机字符串的长度
     * @return 生成的随机字符串
     */
    public  String generateRandomString(int length) {
        // 定义字符集
        String characterSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder randomString = new StringBuilder(length);
        Random random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            // 从字符集中随机选择一个字符
            int randomIndex = random.nextInt(characterSet.length());
            char randomChar = characterSet.charAt(randomIndex);
            // 将选中的字符添加到结果字符串
            randomString.append(randomChar);
        }
        return randomString.toString();
    }
}
