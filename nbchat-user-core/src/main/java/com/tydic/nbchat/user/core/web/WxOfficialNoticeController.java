package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.api.bo.notice.NoticeContext;
import com.tydic.nbchat.user.core.busi.WxOfficialNoticeService;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user/wx/notice")
public class WxOfficialNoticeController {

    private final WxOfficialNoticeService wxOfficialNoticeService;

    public WxOfficialNoticeController(WxOfficialNoticeService wxOfficialNoticeService) {
        this.wxOfficialNoticeService = wxOfficialNoticeService;
    }


    @PostMapping("send")
    public Rsp sendNotice(@RequestBody NoticeContext context) {
        log.info("发送通知");
        wxOfficialNoticeService.sendNotice(context);
        return BaseRspUtils.createSuccessRsp("发送通知成功");
    }

}
