package com.tydic.nbchat.user.core.service.impl;

import com.tydic.nbchat.user.api.NbchatUserRegLoginApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.login.UserLoginReqBO;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nbchat.user.core.busi.UserAccountUnRegBusiService;
import com.tydic.nbchat.user.core.busi.UserInfoBusiService;
import com.tydic.nbchat.user.core.busi.UserPasswordLoginBusiService;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/06/15
 * @email <EMAIL>
 * @description 用户注册登录实现类
 */
@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class NbchatUserRegLoginServiceImpl implements NbchatUserRegLoginApi {

    private final UserSettingHelper userSettingHelper;
    private final UserPasswordLoginBusiService userPasswordLoginBusiService;
    private final UserAccountUnRegBusiService userAccountUnRegBusiService;
    private final UserInfoBusiService userInfoBusiService;
    private final RedisHelper redisHelper;

    public NbchatUserRegLoginServiceImpl(UserSettingHelper userSettingHelper,
                                         UserPasswordLoginBusiService userPasswordLoginBusiService,
                                         UserAccountUnRegBusiService userAccountUnRegBusiService,
                                         UserInfoBusiService userInfoBusiService,
                                         RedisHelper redisHelper) {
        this.userSettingHelper = userSettingHelper;
        this.userPasswordLoginBusiService = userPasswordLoginBusiService;
        this.userAccountUnRegBusiService = userAccountUnRegBusiService;
        this.userInfoBusiService = userInfoBusiService;
        this.redisHelper = redisHelper;
    }

    @Override
    public Rsp logout(String userId) {
        userSettingHelper.removeInfo(userId);
        return BaseRspUtils.createSuccessRsp(userId);
    }

    /**
     * 登录
     *
     * @param @param loginReqBO 登录要求博
     * @return @return {@link Rsp }
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp login(UserLoginReqBO loginReqBO) {
        return userPasswordLoginBusiService.passwordLogin(loginReqBO);
    }

    /**
     * 注册
     *
     * @param @param registReqBO regist要求博
     * @return @return {@link Rsp }
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp regist(UserRegistReqBO request) {
        String key = RedisConstants.USER_REG_LOCK  + request.getUsername();
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(key).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity,300L,20L);
        try {
            if (locked) {
                log.info("用户注册-开始: {}", request);
                String id = IdWorker.nextAutoIdStr();
                String joinType = request.getJoinType();
                if (StringUtils.isBlank(joinType)) {
                    joinType = JoinTenantType.REGIST.getCode();
                }
                UserPO userPO = userAccountUnRegBusiService.registerUser(request, id);
                //注册完成通知
                UserRegistContext registContext = new UserRegistContext();
                BeanUtils.copyProperties(userPO, registContext);
                registContext.setUserRealityName(userPO.getRealName());
                registContext.setJoinType(joinType);
                userSettingHelper.userRegistNotice(registContext);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException ignored) {}
                NbchatUserInfo nbchatUserInfo = userInfoBusiService.getUserInfo(id);
                log.info("用户注册-完成: {}", nbchatUserInfo);
                return BaseRspUtils.createSuccessRsp(nbchatUserInfo, "注册成功");
            }
            return BaseRspUtils.createErrorRsp("注册中，请稍后再试!");
        } catch (Exception e) {
            log.error("用户注册-异常: {}", request, e);
            return BaseRspUtils.createErrorRsp("注册失败: "+e.getMessage());
        } finally {
            redisHelper.unlock(redisLockEntity);
        }
    }

}
