package com.tydic.nbchat.user.core.wx.event.api;

import com.tydic.nbchat.user.core.wx.context.WxMsgBaseContext;
import com.tydic.nbchat.user.core.wx.context.WxTextMsgContext;
import com.tydic.nicc.dc.base.bo.Rsp;

import java.util.Date;

public interface EventHandler {

    /**
     * 事件类型
     * @return
     */
    String event();


    /**
     * 处理事件
     * @param eventMsg
     * @return
     */
    <T extends WxMsgBaseContext> Rsp<?> handle(T eventMsg);

    default WxTextMsgContext buildResponseContext(WxMsgBaseContext eventMsg, String content){
        WxTextMsgContext context = new WxTextMsgContext();
        context.setToUserName(eventMsg.getFromUserName());
        context.setFromUserName(eventMsg.getToUserName());
        context.setContent(content);
        context.setMsgType("text");
        context.setCreateTime(String.valueOf(new Date().getTime()));
        return context;
    }
}
