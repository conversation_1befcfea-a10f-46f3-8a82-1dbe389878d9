package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.UserSettingKey;
import com.tydic.nbchat.user.api.bo.setting.*;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserSettingsMapper;
import com.tydic.nbchat.user.mapper.po.UserSettings;
import com.tydic.nicc.common.eums.SystemCommonPropKey;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
public class UserSettingsServiceImpl implements UserSettingsApi {

    @Resource
    private UserSettingsMapper userSettingsMapper;
    private final UserSettingHelper userSettingHelper;
    private final NbchatUserConfigProperties nbchatUserConfigProperties;

    public UserSettingsServiceImpl(UserSettingHelper userSettingHelper,
                                   NbchatUserConfigProperties nbchatUserConfigProperties) {
        this.userSettingHelper = userSettingHelper;
        this.nbchatUserConfigProperties = nbchatUserConfigProperties;
    }


    @Override
    public Rsp updateDefaultRobot(String currentRobotType, String setRobotType) {
        if(StringUtils.isAnyBlank(currentRobotType,setRobotType)){
            return BaseRspUtils.createErrorRsp("参数异常！");
        }
        List<UserSettings> settings = userSettingsMapper.selectLikeRobotType(currentRobotType);
        List<Object> retList = new ArrayList<>();
        log.info("批量更新机器人类型:{}->{}|{}",currentRobotType,setRobotType,settings.size());
        for (UserSettings setting : settings) {
            try {
                Map<Object,Object> updateMap = JSONObject.parseObject(setting.getCurrentSetting(),Map.class);
                updateMap.put(UserSettingKey.robotType.getKey(),setRobotType);
                setting.setCurrentSetting(JSONObject.toJSONString(updateMap, SerializerFeature.PrettyFormat));
                userSettingsMapper.updateSetting(setting);
                //刷新缓存
                userSettingHelper.updateSetting(setting.getUserId(),updateMap);
                updateMap.put(SystemCommonPropKey.userId.name(),setting.getUserId());
                retList.add(updateMap);
            } catch (Exception e) {
                log.error("批量更新机器人类型异常:{}",setting,e);
            }
        }
        return BaseRspUtils.createSuccessRsp(retList);
    }

    @Transactional(rollbackFor = Exception.class)
    @MethodParamVerifyEnable
    @Override
    public Rsp saveSettings(UserSettingReqBO reqBO) {
        log.info("保存用户设置:{}",reqBO);
        Rsp<UserSettingContext> settingContext = BaseRspUtils.createErrorRsp("保存失败");
        if(ObjectUtils.isNotEmpty(reqBO.getUserIds()) && !reqBO.getUserIds().isEmpty()){
            for (String userId : reqBO.getUserIds()) {
                settingContext = saveSettings(userId,reqBO.getSettings());
            }
        } else {
            settingContext = saveSettings(reqBO.getUserId(),reqBO.getSettings());
        }
        return settingContext;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp checkSettingBindCode(CheckSettingBindCodeReqBO reqBO) {
        return BaseRspUtils.createSuccessRsp(userSettingHelper.checkBindCode(reqBO.getCode()));
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<CreateSettingBindCodeRspBO> createSettingBindCode(CreateSettingBindCodeReqBO reqBO) {
        /**
         * 1. 校验用户身份是否有创建绑定码的权限
         * 2. 创建绑定码
         */
        Map<Object,Object> map = userSettingHelper.loadSetting(reqBO.getUserId());
        if(map.containsKey(UserSettingKey.userSettingPermission.getKey())){
            //有权限进行操作
            String code = userSettingHelper.createBindCode(reqBO.getExpiryTime());
            String url = UriComponentsBuilder.fromHttpUrl(nbchatUserConfigProperties.getBindSettingUrl()).build(code).toString();
            CreateSettingBindCodeRspBO rspBO = CreateSettingBindCodeRspBO.builder().code(code).url(url).
                    settings(nbchatUserConfigProperties.getBindSettingDefault()).build();
            return BaseRspUtils.createSuccessRsp(rspBO);
        }
        return BaseRspUtils.createErrorRsp("创建失败：非法权限！");
    }


    private Rsp<UserSettingContext> saveSettings(String userId,Map<Object,Object> settingsObj){
        UserSettings settings = userSettingsMapper.selectByUserId(userId);
        //刷新缓存
        userSettingHelper.updateSetting(userId,settingsObj);
        Rsp<UserSettingContext> settingContext = loadSettings(userId);
        String settingsMap = JSONObject.toJSONString(settingContext.getData().getSettings(), SerializerFeature.PrettyFormat);
        if(settings == null){
            //新增
            UserSettings insert = new UserSettings();
            insert.setSettingId(IdWorker.nextAutoIdStr());
            insert.setUserId(userId);
            insert.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
            insert.setCreateTime(new Date());
            insert.setUpdateTime(new Date());
            insert.setCurrentSetting(settingsMap);
            userSettingsMapper.insertSelective(insert);
        } else {
            //更新
            UserSettings update = new UserSettings();
            update.setUserId(userId);
            update.setCurrentSetting(settingsMap);
            userSettingsMapper.updateSetting(update);
        }
        userSettingHelper.removeInfo(userId);
        log.info("保存用户设置-当前设置:{}",settingContext);
        return settingContext;
    }

    @Override
    public Rsp<UserSettingContext> loadSettings(String userId) {
        if(StringUtils.isBlank(userId)){
            return BaseRspUtils.createErrorRsp("用户id不得为空!");
        }
        Map map = userSettingHelper.loadSetting(userId);
        UserSettingContext settingContext = UserSettingContext.builder().userId(userId).settings(map).build();
        log.info("加载用户设置:{}",settingContext);
        return BaseRspUtils.createSuccessRsp(settingContext);
    }

    @Override
    public Rsp flashSettings(UserSettingFlashReqBO reqBO) {
        log.info("刷新用户设置:{}",reqBO);
        List<String> userIds = reqBO.getUserIds();
        if(userIds == null || reqBO.getUserIds().isEmpty()){
           return BaseRspUtils.createErrorRsp("参数异常!");
        }
        List<UserSettings> userSettings = userSettingsMapper.selectUserSettings(reqBO.getUserIds());
        int count = 0;
        for (UserSettings userSetting : userSettings) {
            Map<Object,Object> settMap = JSONObject.parseObject(userSetting.getCurrentSetting(),Map.class);
            if (reqBO.getRemoveKeys() != null && !reqBO.getRemoveKeys().isEmpty()) {
                for (String removeKey : reqBO.getRemoveKeys()) {
                    settMap.remove(removeKey);
                }
                //更新数据库
                String last = userSetting.getCurrentSetting();
                String settingsMap = JSONObject.toJSONString(settMap);
                userSetting.setCurrentSetting(settingsMap);
                userSetting.setUpdateTime(new Date());
                userSetting.setLastSetting(last);
                userSettingsMapper.updateSetting(userSetting);
                //刷新缓存
                userSettingHelper.updateSetting(userSetting.getUserId(),settMap);
            } else {
                //刷新缓存
                userSettingHelper.updateSetting(userSetting.getUserId(),settMap);
            }
            count ++;
        }
        return BaseRspUtils.createSuccessRsp(count);
    }

    @Override
    public void removeUserCache(String tenantCode, String userId) {
        userSettingHelper.cleanUserCache(tenantCode, userId);
    }

}
