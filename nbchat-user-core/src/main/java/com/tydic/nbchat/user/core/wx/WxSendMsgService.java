package com.tydic.nbchat.user.core.wx;

import com.tydic.nbchat.user.api.bo.notice.NoticeContext;
import com.tydic.nbchat.user.mapper.WxUserMapper;
import com.tydic.nbchat.user.mapper.po.WxUserPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class WxSendMsgService {

    private final WxMsgHelper wxMsgHelper;
    @Resource
    WxUserMapper wxUserMapper;

    public WxSendMsgService(WxMsgHelper wxMsgHelper) {
        this.wxMsgHelper = wxMsgHelper;
    }

    public void sendTemplateMsg(NoticeContext context) {
        try {
            WxUserPO wxUserPO = wxUserMapper.queryOAByUserId(context.getUserId());
            if (ObjectUtils.isEmpty(wxUserPO)) {
                log.warn("微信用户不存在,userId:{}", context.getUserId());
                return;
            }
            wxMsgHelper.sendTemplateMsg(wxUserPO.getOpenId(), context.getTemplateType(), context.getParams(), context.getUrl());
        } catch (Exception e) {
            log.error("发送模板消息失败,userId:{},templateType:{},params:{}", context.getUserId(), context.getTemplateType(), context.getParams(), e);
        }
    }

    public void sendTextMsg(String userId, String content) {
        try {
            WxUserPO wxUserPO = wxUserMapper.queryOAByUserId(userId);
            if (ObjectUtils.isEmpty(wxUserPO)) {
                log.warn("微信用户不存在,userId:{}", userId);
                return;
            }
            wxMsgHelper.sendTextMsg(wxUserPO.getOpenId(), content);
        } catch (Exception e) {
            log.error("发送文本消息失败,userId:{},content:{}", userId, content, e);
        }
    }


}
