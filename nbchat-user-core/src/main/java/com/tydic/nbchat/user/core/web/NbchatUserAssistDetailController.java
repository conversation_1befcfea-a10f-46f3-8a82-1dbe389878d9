package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.api.NbchatUserAssistDetailApi;
import com.tydic.nbchat.user.api.bo.NbchatUserAssisReqBo;
import com.tydic.nbchat.user.api.bo.NbchatUserAssistDetailReqBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/1/6 17:42
 * @description 邀请新用户助力接口
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class NbchatUserAssistDetailController {

    private final NbchatUserAssistDetailApi nbchatUserAssistDetailApi;
    public NbchatUserAssistDetailController(NbchatUserAssistDetailApi nbchatUserAssistDetailApi) {
        this.nbchatUserAssistDetailApi = nbchatUserAssistDetailApi;
    }

    /**
     * 发起助力接口
     */
    @PostMapping("/assist/create")
    public Rsp createAssist(@RequestBody NbchatUserAssistDetailReqBo reqBo) {
        return nbchatUserAssistDetailApi.createAssist(reqBo);
    }
    /**
     * 被邀请的用户点击助力接口
     */
    @PostMapping("/assist/submit")
    public Rsp submitAssist(@RequestBody NbchatUserAssistDetailReqBo reqBo) {
        return nbchatUserAssistDetailApi.submitAssist(reqBo);
    }
    /**
     * 利用缓存校验助力是否通过
     */
    @PostMapping("/assist/check")
    public Rsp checkAssist(@RequestBody NbchatUserAssisReqBo reqBo) {
        return nbchatUserAssistDetailApi.checkAssist(reqBo);
    }
    /**
     * 查询助力信息
     */
    @PostMapping("/assist/info")
    public Rsp getAssistInfo(@RequestBody NbchatUserAssisReqBo reqBo) {
        return nbchatUserAssistDetailApi.getAssistInfo(reqBo);
    }

}
