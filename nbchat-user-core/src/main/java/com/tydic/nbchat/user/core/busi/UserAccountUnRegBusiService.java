package com.tydic.nbchat.user.core.busi;

import com.tydic.nbchat.user.api.bo.account.UserUnRegAccountReqBO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.UserStatusType;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nbchat.user.core.utils.PasswordHasher;
import com.tydic.nbchat.user.mapper.UserMapper;
import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class UserAccountUnRegBusiService {
    @Resource
    private UserMapper userMapper;

    public Rsp unRegUser(UserUnRegAccountReqBO reqBO) {
        log.info("用户注销账户: {}", reqBO);
       /* boolean checkCode = verifyPhoneCodeBusiService.verifyPhoneCode(reqBO.getPhone(), reqBO.getCode());
        if (!checkCode) {
            return BaseRspUtils.createErrorRsp("验证码错误!");
        }*/
        UserPO userPO = userMapper.findUserByMobile(reqBO.getPhone());
        if (userPO == null) {
            return BaseRspUtils.createErrorRsp("用户不存在!");
        }
        userPO.setIsDeleted(true);
        userPO.setUpdatedTime(new Date());
        userPO.setPhone("0"+userPO.getPhone());
        userMapper.updateById(userPO);
        return BaseRspUtils.createSuccessRsp("注销成功");
    }


    @Transactional(rollbackFor = Exception.class)
    public UserPO registerUser(UserRegistReqBO registerReqBO, String id) {
        String userName = registerReqBO.getUsername();
        String password = registerReqBO.getPassword();
        String tenantCode = registerReqBO.getTenantCode();
        if (userName.length() > 20 || password.length() > 20) {
            log.warn("用户注册-用户名密码长度超过限制: {}|{}", userName, password);
            throw new RuntimeException("用户名密码长度超过限制");
        }
        String realName = registerReqBO.getUserRealityName();
        String gender = registerReqBO.getGender();
        String name = registerReqBO.getName();
        if (StringUtils.isBlank(registerReqBO.getGender())) {
            gender = UserAttributeConstants.DEFAULT_GENDER;
        }
        if (StringUtils.isBlank(tenantCode)) {
            tenantCode = UserAttributeConstants.DEFAULT_TENANT_CODE;
        }
        //判断用户是否存在
        UserPO user = userMapper.findUserByUserName(userName);
        if (user != null) {
            log.warn("用户注册-注册失败，用户已存在: {}", userName);
            throw new RuntimeException("用户名已存在");
        }
        if (StringUtils.isNotBlank(registerReqBO.getPhone())) {
            boolean valid = PhoneNumberUtils.validPhoneNumber(registerReqBO.getPhone());
            if (!valid) {
                log.warn("用户注册-注册失败，手机号不合法: {}", registerReqBO.getPhone());
                throw new RuntimeException("手机号不合法");
            }
            //判断手机号是否存在
            UserPO phoneUser = userMapper.findUserByMobile(registerReqBO.getPhone());
            if (phoneUser != null) {
                log.warn("用户注册-注册失败，手机号已存在: {}", registerReqBO.getPhone());
                throw new RuntimeException("手机号已存在");
            }
            //使用手机号后6位
            realName = "用户" + registerReqBO.getPhone().substring(7);
        } else {
            if (StringUtils.isBlank(realName)) {
                realName = "用户" + NiccCommonUtil.getRandomNumber(10000, 999999);
            }
        }
        if (StringUtils.isBlank(name)) {
            name = realName;
        }
        //封装UserPO
        UserPO userPO = new UserPO();
        userPO.setPhone(registerReqBO.getPhone());
        userPO.setUserId(id);
        userPO.setTenantCode(tenantCode);
        userPO.setAppId(UserAttributeConstants.DEFAULT_APPID);
        userPO.setName(name);
        userPO.setUserName(userName);
        userPO.setRealName(realName);
        userPO.setPassword(password);
        userPO.setAvatar(UserAttributeConstants.DEFAULT_AVATAR);
        userPO.setGender(gender);
        userPO.setCreatedBy(name);
        userPO.setUpdatedBy(name);
        userPO.setCreatedTime(new Date());
        userPO.setStatus(UserStatusType.NORMAL_USER.getCode());
        if (StringUtils.isNotBlank(registerReqBO.getTenantCode())) {
            userPO.setTenantCode(registerReqBO.getTenantCode());
        }
        if (StringUtils.isNotBlank(userPO.getPassword())) {
            userPO.setPassword(PasswordHasher.sha1(userPO.getPassword()));
        }
        userMapper.registerUser(userPO);
        return userPO;
    }
}
