package com.tydic.nbchat.user.core.web;

import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.LineCaptcha;
import com.tydic.nbchat.user.core.utils.KaptchaHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/user")
public class KaptchaController {

    private final KaptchaHelper kaptchaHelper;

    public KaptchaController(KaptchaHelper kaptchaHelper) {
        this.kaptchaHelper = kaptchaHelper;
    }

    @GetMapping("/captcha/get")
    public void createCaptchaImage(HttpServletResponse response) {
        try {
            AbstractCaptcha captcha = kaptchaHelper.generateCaptchaCode();
            captcha.write(response.getOutputStream());
            response.getOutputStream().close();
        } catch (Exception e) {
            log.error("验证码生成-异常:",e);
        }
    }


}
