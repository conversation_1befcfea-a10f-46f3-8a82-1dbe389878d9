package com.tydic.nbchat.user.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.UserSwitchTenantRequest;
import com.tydic.nbchat.user.api.bo.eums.SysPlatformDefine;
import com.tydic.nbchat.user.api.bo.setting.UserSettingReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UserTenantSwitchService {

    private final UserLoginEventService userLoginEventService;
    private final UserSettingsApi userSettingsApi;
    private final UserInfoBusiService userInfoBusiService;


    public UserTenantSwitchService(UserLoginEventService userLoginEventService,
                                   UserSettingsApi userSettingsApi,
                                   UserInfoBusiService userInfoBusiService) {
        this.userLoginEventService = userLoginEventService;
        this.userSettingsApi = userSettingsApi;
        this.userInfoBusiService = userInfoBusiService;
    }

    public Rsp switchPlatformTenant(UserSwitchTenantRequest request) {
        log.info("用户切换平台租户: {}", request);
        if (!SysPlatformDefine.isValidCode(request.getPlatform())) {
            return BaseRspUtils.createErrorRsp("平台参数错误");
        }
        JSONObject paramMap = new JSONObject();
        Map<Object,Object> settingMap = new HashMap<>();
        paramMap.put("sessionKey", request.getSessionKey());
        paramMap.put("loginTime", System.currentTimeMillis());
        paramMap.put("tenantCode", request.getTargetTenant());
        settingMap.put(request.getPlatform(), paramMap);
        UserSettingReqBO reqBO = new UserSettingReqBO();
        reqBO.setUserId(request.getUserId());
        reqBO.setSettings(settingMap);
        Rsp setRsp = userSettingsApi.saveSettings(reqBO);
        if (setRsp.isSuccess()) {
            //重新加载缓存
            userInfoBusiService.reloadUserInfo(request.getUserId(), request.getTargetTenant());
            //切换租户，发送登录事件
            userLoginEventService.sengLoginSwitchTenant(request.getUserId(),
                    "","", request.getTargetTenant());
        }
        return setRsp;
    }
}
