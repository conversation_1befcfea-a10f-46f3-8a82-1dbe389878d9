package com.tydic.nbchat.user.core.web;

import com.alibaba.nacos.common.utils.MD5Utils;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.BindPhoneNumberReqBO;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.UserSwitchTenantRequest;
import com.tydic.nbchat.user.api.bo.auth.UserTokenInfo;
import com.tydic.nbchat.user.api.bo.auth.UserWxReqeust;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.core.busi.UserAuthBusiService;
import com.tydic.nbchat.user.core.busi.UserLoginLockBusiService;
import com.tydic.nbchat.user.core.busi.UserTenantSwitchService;
import com.tydic.nbchat.user.core.busi.WchatApiHelper;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.JWTUtils;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_OPERATE_LOG_TOPIC;
import static com.tydic.nicc.common.nbchat.emus.UserOperationType.LOGOUT;

/**
 * 用户认证相关接口
 */
@Slf4j
@RestController
@RequestMapping("/user/")
public class UserAuthController {

    private final UserAuthBusiService userAuthBusiService;
    private final UserLoginLockBusiService userLoginLockBusiService;
    private final UserTenantSwitchService userTenantSwitchService;
    private final KKMqProducerHelper kkMqProducerHelper;
    private final RedisHelper redisHelper;
    private final NbchatUserApi nbchatUserApi;
    private final WchatApiHelper wchatApiHelper;

    public UserAuthController(UserAuthBusiService userAuthBusiService,
                              UserLoginLockBusiService userLoginLockBusiService,
                              UserTenantSwitchService userTenantSwitchService,
                              RedisHelper redisHelper, NbchatUserApi nbchatUserApi,
                              WchatApiHelper wchatApiHelper, KKMqProducerHelper kkMqProducerHelper) {
        this.userAuthBusiService = userAuthBusiService;
        this.userLoginLockBusiService = userLoginLockBusiService;
        this.userTenantSwitchService = userTenantSwitchService;
        this.redisHelper = redisHelper;
        this.nbchatUserApi = nbchatUserApi;
        this.wchatApiHelper = wchatApiHelper;
        this.kkMqProducerHelper = kkMqProducerHelper;
    }

    @GetMapping("/auth/unlock/0h8Mn9RPMiy7KMHD5vEc/{username}")
    public Rsp unlockAccount(@PathVariable("username") String username, HttpServletResponse response) {
        userLoginLockBusiService.unlockAccount(username);
        return BaseRspUtils.createSuccessRsp(username, "unlock success");
    }

    @GetMapping("/auth/0h8Mn9RPMiy7KMHD5vEc/{uid}")
    public Rsp authUser(@PathVariable("uid") String uid, HttpServletResponse response) {
        try {
            log.info("用户登录认证: uid = {}", uid);
            NbchatUserInfo userInfo = userAuthBusiService.getUserInfo(uid);
            if (userInfo != null) {
                String loginInfo = UserTokenInfo.of(userInfo.getUserId(), userInfo.getTenantCode());
                int expTime = 12 * 3600;
                String token = JWTUtils.createJwtToken(userInfo.getUserId(),
                                                       loginInfo, expTime * 1000L);
                Cookie cookie = new Cookie(UserAuthBusiService.HEADER_TOKEN_KEY, token);
                cookie.setPath("/");
                cookie.setMaxAge(expTime);
                response.addCookie(cookie);
                try {
                    String cacheKey = RedisConstants.USER_SESSION_KEY_PREFIX + userInfo.getUserId();
                    redisHelper.set(cacheKey, MD5Utils.md5Hex(token.getBytes()), expTime);
                } catch (NoSuchAlgorithmException e) {
                    log.warn("token 计算md5异常:", e);
                }
                return BaseRspUtils.createSuccessRsp(token);
            }
            return BaseRspUtils.createErrorRsp("参数异常!");
        } catch (Exception e) {
            log.error("用户登录认证-解析异常: uid = {}", uid, e);
        }
        return BaseRspUtils.createErrorRsp("认证异常");
    }

    @PostMapping("/auth/login")
    public Rsp login(@RequestBody AuthUserReqBO authUserReqBO, HttpServletRequest request, HttpServletResponse response) {
        return userAuthBusiService.login(authUserReqBO, request, response);
    }

    @GetMapping("/auth/logout")
    public Rsp logout(HttpServletRequest request, HttpServletResponse response) {
        String token = userAuthBusiService.getAuthToken(request);
        try {
            if (StringUtils.isNotBlank(token)) {
                String loginInfo = JWTUtils.getLoginInfo(token);
                log.info("用户退出登录-开始:{}", loginInfo);
                UserTokenInfo userInfo = UserTokenInfo.parse(loginInfo);
                userAuthBusiService.authLogout(userInfo, token);
                // 发送用户操作日志
                UserOperateLogContext userOperateLogContext = UserOperateLogContext.build(LOGOUT, userInfo.getTenantCode(), userInfo.getUserId(), null, null, request.getHeader("User-Agent"));
                try {
                    log.info("发送用户操作日志事件消息: {}", userOperateLogContext);
                    kkMqProducerHelper.sendMsg(NBCHAT_USER_OPERATE_LOG_TOPIC, userOperateLogContext);
                } catch (Exception e) {
                    log.error("发送用户操作日志事件消息-异常:{}", userOperateLogContext, e);
                }
            }
        } catch (Exception e) {
            log.error("用户信息解析异常:", e);
        }
        Cookie cookie = new Cookie(UserAuthBusiService.HEADER_TOKEN_KEY, "");
        cookie.setPath("/");
        response.addCookie(cookie);
        return BaseRspUtils.createSuccessRsp("logout success");
    }

    /**
     * 粤问登录检查
     *
     * @param data
     * @param response
     * @return
     */
    @GetMapping("/auth/yuewen/login_check")
    public Rsp yuewenLoginCheck(@RequestParam("data") String data, HttpServletResponse response) {
        return userAuthBusiService.yuwenLoginCheck(data, response);
    }

    /**
     * 公众号二维码扫描
     *
     * @param sessionKey
     * @param response
     * @return
     */
    @GetMapping("/auth/login_check")
    public Rsp loginCheck(@RequestParam("sessionKey") String sessionKey, HttpServletResponse response) {
        NbchatUserInfo userInfo = userAuthBusiService.loginCheck(sessionKey, response);
        if (userInfo == null) {
            return BaseRspUtils.createSuccessRsp(null, "等待扫码");
        }
        return BaseRspUtils.createSuccessRsp(userInfo);
    }

    /**
     * 绑定手机号
     *
     * @param bindPhoneNumber
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/bind/phone")
    public Rsp bindPhone(@RequestBody BindPhoneNumberReqBO bindPhoneNumber,
                         HttpServletRequest request, HttpServletResponse response) {
        return userAuthBusiService.bindPhone(bindPhoneNumber, request, response);
    }

    /**
     * 微信小程序用户绑定手机号
     *
     * @param reqBO
     * @return
     */
    @PostMapping("mp/bindPhone")
    public Rsp bindPhoneByMP(@RequestBody BindPhoneNumberReqBO reqBO) {
        return nbchatUserApi.bindPhoneByMPCode(reqBO);
    }

    /**
     * 获取微信用户信息
     *
     * @param request
     * @return
     */
    @PostMapping("/wx/info")
    public Rsp login(@Validated @RequestBody UserWxReqeust request) {
        try {
            return BaseRspUtils.createSuccessRsp(wchatApiHelper.getWxInfo(request));
        } catch (Exception e) {
            log.error("获取微信用户信息-失败: request = {}", request, e);
            return BaseRspUtils.createErrorRsp("获取用户信息失败");
        }
    }

    /**
     * 获取用户信息
     *
     * @param baseInfo
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/info")
    public Rsp getInfo(@RequestBody BaseInfo baseInfo,
                       HttpServletRequest request, HttpServletResponse response) {
        try {
            String platform = request.getHeader(UserAttributeConstants.HEADER_PLATFORM);
            NbchatUserInfo userInfo = null;
            if (StringUtils.isNotBlank(baseInfo.get_userId())) {
                userInfo = userAuthBusiService.getUserInfo(baseInfo.get_userId(), platform);
                if (userInfo.getTenantCodeList() == null) {
                    userInfo.setTenantCodeList(Collections.singletonList(userInfo.getTenantCode()));
                }
                return BaseRspUtils.createSuccessRsp(userInfo);
            }
            String token = userAuthBusiService.getAuthToken(request);
            UserTokenInfo tokenInfo = UserTokenInfo.parse(token);
            if (tokenInfo == null) {
                return BaseRspUtils.createErrorRsp("用户信息获取失败");
            }
            userInfo = userAuthBusiService.getUserInfo(tokenInfo.getUserId(), platform);
            if (userInfo.getTenantCodeList() == null) {
                userInfo.setTenantCodeList(Collections.singletonList(userInfo.getTenantCode()));
            }
            return BaseRspUtils.createSuccessRsp(userInfo);
        } catch (JWTVerificationException e) {
            //返回401
            response.setStatus(401);
            log.warn("JWT解析异常:", e);
        } catch (Exception e1) {
            log.error("用户信息解析异常:", e1);
        }
        return BaseRspUtils.createErrorRsp("用户信息获取失败");
    }

    @PostMapping("/auth/switch/tenant")
    public Rsp switchTenant(@Validated @RequestBody UserSwitchTenantRequest request) {
        return userTenantSwitchService.switchPlatformTenant(request);
    }

}
