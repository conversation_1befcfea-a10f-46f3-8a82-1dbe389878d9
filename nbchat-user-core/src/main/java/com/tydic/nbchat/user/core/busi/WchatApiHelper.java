package com.tydic.nbchat.user.core.busi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.bo.auth.UserWxReqeust;
import com.tydic.nbchat.user.api.bo.auth.UserWxResponse;
import com.tydic.nbchat.user.api.bo.eums.AuthType;
import com.tydic.nbchat.user.api.bo.exception.UserLoginException;
import com.tydic.nbchat.user.core.config.WchatConfigProperties;
import com.tydic.nbchat.user.core.enmus.WxChannelEnum;
import com.tydic.nbchat.user.core.vo.WxUserVO;
import com.tydic.nbchat.user.core.wx.WxAuthHelper;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;

@Service
@Slf4j
public class WchatApiHelper {

    private final WxAuthHelper wxAuthHelper;
    private final RestApiHelper restApiHelper;
    private final WchatConfigProperties wchatConfigProperties;

    public WchatApiHelper(WxAuthHelper wxAuthHelper,
                          RestApiHelper restApiHelper,
                          WchatConfigProperties wchatConfigProperties) {
        this.wxAuthHelper = wxAuthHelper;
        this.restApiHelper = restApiHelper;
        this.wchatConfigProperties = wchatConfigProperties;
    }

    /**
     * 根据 code 获取微信用户信息
     * @param reqeust
     * @return
     */
    public UserWxResponse getWxInfo(UserWxReqeust reqeust) {
        log.info("获取微信用户信息: {}", reqeust);
        WchatConfigProperties.Property property;
        property = getProperty(reqeust.getAuthType(), wchatConfigProperties);
        String appId = property.getAppId();
        String appSecret = property.getSecret();
        String code = reqeust.getCode();
        JSONObject accessTokenJson;
        if (AuthType.WCHAT_MP_TDH.getCode().equals(reqeust.getAuthType())
                || AuthType.WCHAT_MP_TDH_PHONE.getCode().equals(reqeust.getAuthType())) {
            accessTokenJson = getMPInfo(WxChannelEnum.WCHAT_MP_TDH.getCode(), appId, appSecret, code);
        } else {
            accessTokenJson = getAccessToken(appId, appSecret, code);
        }
        if (ObjectUtils.isEmpty(accessTokenJson)) {
            throw new UserLoginException("微信登录异常，认证失败 (access_token 获取失败): " + reqeust);
        }
        String openId = accessTokenJson.getString("openid");
        String sessionKey = accessTokenJson.getString("session_key");
        String unionId = accessTokenJson.getString("unionid");
        UserWxResponse userWxResponse = new UserWxResponse();
        userWxResponse.setOpenId(openId);
        userWxResponse.setSessionKey(sessionKey);
        userWxResponse.setUnionId(unionId);
        return userWxResponse;
    }

    static WchatConfigProperties.Property getProperty(String authType,
                                                      WchatConfigProperties wchatConfigProperties) {
        WchatConfigProperties.Property property;
        if (AuthType.WCHAT.getCode().equals(authType)) {
            property = wchatConfigProperties.getChannels().get(WxChannelEnum.MOBILE.getCode());
        } else if (AuthType.WCHAT_MP_TDH.getCode().equals(authType)
                || AuthType.WCHAT_MP_TDH_PHONE.getCode().equals(authType)) {
            property = wchatConfigProperties.getChannels().get(WxChannelEnum.WCHAT_MP_TDH.getCode());
        } else {
            property = wchatConfigProperties.getChannels().get(WxChannelEnum.PC.getCode());
        }
        return property;
    }

    public JSONObject getAccessToken(String appId, String appSecret, String code) {
        //返回url
        String baseUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("appid", appId);
        paramMap.put("secret", appSecret);
        paramMap.put("code", code);
        paramMap.put("grant_type", "authorization_code");
        String response = restApiHelper.get(baseUrl, "", paramMap);
        //获取access_token
        JSONObject accessTokenJson = JSONObject.parseObject(response);
        if (accessTokenJson.containsKey("errcode")) {
            // 错误处理
            log.error("用户认证-获取access_token错误:{}", accessTokenJson);
            return null;
        }
        return accessTokenJson;
    }

    /**
     *
     * {
     * 	"session_key": "TV6V4ZhioZZRSPe0n8Y09w==",
     * 	"openid": "oFWLv6_UizNvSJzWyGDz4UuqLXcg",
     * 	"unionid": "onLMw1BQ9DdknC4_OCXb6T78mSLY"
     * }
     *
     * @param channel
     * @param appId
     * @param appSecret
     * @param code
     * @return
     */
    public JSONObject getMPInfo(String channel, String appId, String appSecret, String code) {
        String accessToken = wxAuthHelper.getAccessToken(channel);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("用户认证-获取access_token失败:{}",channel);
            throw new UserLoginException("微信登录异常，认证失败 (token 获取失败): " + channel);
        }
        String baseUrl = "https://api.weixin.qq.com/sns/jscode2session";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("appid", appId);
        paramMap.put("secret", appSecret);
        paramMap.put("js_code", code);
        paramMap.put("grant_type", "authorization_code");
        String response = restApiHelper.get(baseUrl, "", paramMap);
        //获取access_token
        JSONObject session = JSONObject.parseObject(response);
        if (ObjectUtils.isEmpty(session) || session.containsKey("errcode")) {
            throw new UserLoginException("微信登录异常，认证失败 (session 获取失败): " + channel);
        }
        return session;
    }


    /**
     * 刷新access_token
     *
     * @param
     * @return @return {@link String }
     */
    public String refreshAccessToken(String appId, String refreshToken) {
        log.info("用户认证-刷新access_token");
        //返回url
        String baseUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("appid", appId);
        paramMap.put("grant_type", "refresh_token");
        paramMap.put("refresh_token", refreshToken);
        String response = restApiHelper.get(baseUrl, "", paramMap);
        //获取access_token
        JSONObject accessTokenJson = JSONObject.parseObject(response);
        log.info("用户认证-刷新access_token结果：{}", accessTokenJson);
        if (accessTokenJson.containsKey("access_token")) {
            String accessToken = accessTokenJson.getString("access_token");
            log.info("用户认证-刷新access_token成功：{}", accessToken);
            return accessToken;
        }
        log.info("用户认证-刷新access_token失败");
        return null;
    }



    /**
     * 获取微信用户信息
     *
     * @param @param      openId
     * @param accessToken 访问令牌
     * @return @return {@link WxUserVO }
     */
    public WxUserVO getWechatUserInfo(String openId, String accessToken) {
        String baseUrl = "https://api.weixin.qq.com/sns/userinfo";
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("access_token", accessToken);
        paramMap.put("openid", openId);
        paramMap.put("lang", "zh_CN");
        //获取微信用户信息
        String response = restApiHelper.get(baseUrl, "", paramMap);
        WxUserVO user = JSON.parseObject(response, WxUserVO.class);
        log.info("用户认证-查询微信用户信息: {}|{}", openId, user);
        return user;
    }

    /**
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"phone_info": {
     * 		"phoneNumber": "***********",
     * 		"purePhoneNumber": "***********",
     * 		"countryCode": "86",
     * 		"watermark": {
     * 			"timestamp": 1743071229,
     * 			"appid": "wx0e73f2df7a19a7ce"
     * 		        }    * 	}
     * }
     *
     * 获取微信小程序用户手机号
     * @param code
     * @return
     */
    public String queryPhone(String code) {
        String baseUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token="
                + wxAuthHelper.getAccessToken(WxChannelEnum.WCHAT_MP_TDH.getCode());
        HashMap<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("code", code);
        //获取微信用户信息
        String response = restApiHelper.post(baseUrl, paramMap);
        JSONObject phoneJson = JSONObject.parseObject(response);
        if (phoneJson.containsKey("errcode") && phoneJson.getInteger("errcode") != 0) {
            // 错误处理
            log.error("用户认证-获取手机号错误:{}", phoneJson);
            return null;
        }
        JSONObject phoneInfo = phoneJson.getJSONObject("phone_info");
        return phoneInfo.getString("phoneNumber");
    }

}
