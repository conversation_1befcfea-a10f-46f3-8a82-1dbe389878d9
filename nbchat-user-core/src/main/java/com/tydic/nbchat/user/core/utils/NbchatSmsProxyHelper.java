package com.tydic.nbchat.user.core.utils;

import com.tydic.nbchat.user.api.NbChatSmsHelper;
import com.tydic.nbchat.user.api.bo.SendSmsRequest;
import com.tydic.nbchat.user.api.bo.eums.SmsHelperType;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/04/06
 * @email <EMAIL>
 * @description 短信代理
 */
@Slf4j
@Component
public class NbchatSmsProxyHelper {

    private final NbchatUserConfigProperties nbchatUserConfigProperties;
    /**
     * 助手列表
     */
    final private List<NbChatSmsHelper> helperList;

    public NbchatSmsProxyHelper(NbchatUserConfigProperties nbchatUserConfigProperties, List<NbChatSmsHelper> helperList) {
        this.nbchatUserConfigProperties = nbchatUserConfigProperties;
        this.helperList = helperList;
    }

    /**
     * 短信发送
     * @param @param  type 类型
     * @param request 请求
     * @return @return {@link Rsp }
     */
    public Rsp send(SmsHelperType type, SendSmsRequest request){
        if (nbchatUserConfigProperties.getSms().isEnable()) {
            for (NbChatSmsHelper nbChatSmsHelper : helperList) {
                if(nbChatSmsHelper.type().equals(type.getCode())){
                    return nbChatSmsHelper.sendSms(request);
                }
            }
            throw new RuntimeException("找不到短信发送服务！");
        }
        return BaseRspUtils.createSuccessRsp("未开启短信服务");
    }
}
