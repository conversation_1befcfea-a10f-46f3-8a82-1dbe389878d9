package com.tydic.nbchat.user.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EncryptionUtil {

    public final static Base64.Encoder encoder = Base64.getEncoder();
    public final static Base64.Decoder decoder = Base64.getDecoder();
    public final static String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS7Padding";
    public final static String iv = "1234567890123456";
    public final static String AES_KEY = "66vw4aoKSLqmfBM0";
    private final static Pattern encryptPattern = Pattern.compile("@(.*?)@");
    static {
        Security.addProvider(new BouncyCastleProvider());
    }
    /**
     * 加密
     *
     * @param value 数据
     * @param key   密钥
     * @return 加密后内容
     */
    public static String encrypt(byte[] value, String key) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE,
                    new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES"));
            byte[] encrypt = cipher.doFinal(value);
            System.out.println(encrypt.length);
            return encoder.encodeToString(encrypt);
        } catch (Exception e) {
            log.error("加密失败:", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密
     *
     * @param value 数据
     * @param key   密钥
     * @return 解密后内容
     */
    public static byte[] decrypt(byte[] value, String key) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            //使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            //执行操作
            return cipher.doFinal(value);
        } catch (Exception e) {
           log.error("解密失败:", e);
           throw new RuntimeException(e);
        }
    }

    public static String encrypt(String value, String key) {
        return encrypt(value.getBytes(StandardCharsets.UTF_8), key);
    }

    public static String decrypt(String value, String key) {
        return new String(Objects.requireNonNull(decrypt(decoder.decode(value), key)),
                StandardCharsets.UTF_8);
    }


    public static String encrypt(String value) {
        return encrypt(value, AES_KEY);
    }

    public static String decrypt(String value) {
        return decrypt(value, AES_KEY);
    }


    public static String decryptField(String field) {
        if (field.startsWith("@") && field.endsWith("@")) {
            return decrypt(field.replaceAll("@", ""));
        }
        return field;
    }

    public static String decryptBody(String body,String requestUri, String encryptList) {
        List<String> encrypts = List.of(encryptList.split(","));
        if (encrypts.contains(requestUri)) {
            StringBuilder result = new StringBuilder();
            Matcher matcher = encryptPattern.matcher(body);
            while (matcher.find()) {
                try {
                    String encryptedText = matcher.group(1);
                    String decryptedText = decrypt(encryptedText);
                    matcher.appendReplacement(result, decryptedText);
                } catch (Exception e) {
                    log.error("解密失败: err = {} , body = {}", e.getMessage(), body);
                }
            }
            matcher.appendTail(result);
            return result.toString();
        }
        return body;
    }

    public static void main(String[] args) {
        String value = "@bjBu7T2GJ1jXZcanbEXeHA==@";
        System.out.println(decryptField(value));
    }

}
