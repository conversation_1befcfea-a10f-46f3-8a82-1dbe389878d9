#!/bin/bash

## 自动build

WORK_HOME=`pwd`
type=$1

cd ${WORK_HOME}/../nicc-boot-starter
mvn clean && mvn install

echo "nicc-common build ..."
cd ${WORK_HOME}/../nicc-common
mvn clean && mvn install

echo "nicc-user-center build ..."
cd ${WORK_HOME}/../nicc-user-center
mvn clean && mvn install

echo "nicc-[im|opdata]-api build ..."
cd ${WORK_HOME}/nicc-im/nicc-im-api
mvn clean && mvn install
cd ${WORK_HOME}/../nicc-op-data/nicc-opdata-api
mvn clean && mvn install

echo "nicc-event-center build ..."
cd ${WORK_HOME}/../nicc-event-center
mvn clean && mvn install

echo "nicc-session build ..."
cd ${WORK_HOME}/../nicc-session
mvn clean && mvn install

echo "nicc-im build ..."
cd ${WORK_HOME}/nicc-im
mvn clean && mvn install

echo "nicc-opdata build ..."
cd ${WORK_HOME}/../nicc-op-data
mvn clean && mvn install

echo "nicc-csm build ..."
cd ${WORK_HOME}/../nicc-csm/
mvn clean && mvn install



