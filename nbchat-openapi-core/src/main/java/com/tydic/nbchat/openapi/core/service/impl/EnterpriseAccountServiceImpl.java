package com.tydic.nbchat.openapi.core.service.impl;

import com.tydic.nbchat.openapi.api.EnterpriseAccountApi;
import com.tydic.nbchat.openapi.api.bo.trade.UserAccountQueryRequest;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordQueryReqBO;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.api.trade.TradeBillRecordApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EnterpriseAccountServiceImpl implements EnterpriseAccountApi {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000, retries = 0)
    private TradeBalanceApi tradeBalanceApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private TradeBillRecordApi tradeBillRecordApi;

    @Override
    public Rsp getBalance(UserAccountQueryRequest request) {
        log.info("查询企业用户余额：{}", request);
        try {
            if (StringUtils.isNotBlank(request.getTargetUid())) {
                return tradeBalanceApi.getBalance(request.getTenantCode(),request.getTargetUid());
            } else {
                return tradeBalanceApi.getBalance(request.getTenantCode(),request.getTenantCode());
            }
        } catch (Exception e) {
            log.error("查询企业用户余额-异常: {}",request, e);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        }
    }

    @Override
    public RspList getBillList(UserAccountQueryRequest request) {
        log.info("查询企业用户账单：{}", request);
        try {
            UserBillRecordQueryReqBO queryReqBO = new UserBillRecordQueryReqBO();
            queryReqBO.setTenantCode(request.getTenantCode());
            queryReqBO.setUserId(request.getUserId());
            queryReqBO.setPage(request.getPage());
            queryReqBO.setPayType("1");
            queryReqBO.setLimit(request.getLimit());
            if (StringUtils.isNotBlank(request.getTargetUid())) {
                queryReqBO.setPayType("0");
                queryReqBO.setUserId(request.getTargetUid());
            }
            return tradeBillRecordApi.getBillRecord(queryReqBO);
        } catch (Exception e) {
            log.error("查询企业用户账单-异常: {}",request, e);
            return BaseRspUtils.createErrorRspList("账单查询失败");
        }
    }
}
