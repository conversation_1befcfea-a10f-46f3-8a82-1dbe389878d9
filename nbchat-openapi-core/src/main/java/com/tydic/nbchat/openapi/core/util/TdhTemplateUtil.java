package com.tydic.nbchat.openapi.core.util;

import com.alibaba.fastjson.JSONObject;
import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.tydic.nbchat.openapi.api.bo.creation.TdhCreationRequest;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

public class TdhTemplateUtil {

    //加载远程模板 http://xxxxx.json
    public static JSONObject loadTemplate(TdhCreationRequest param, String urlString) throws Exception{
        StringBuilder template = new StringBuilder();
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                template.append(line);
            }
        }
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(template.toString()), "t");
        Writer writer = new StringWriter();
        mustache.execute(writer, param).flush();
        return JSONObject.parseObject(writer.toString());
    }

    public static void main(String[] args) throws Exception {
        TdhCreationRequest param = new TdhCreationRequest();
        param.getTdh().setId("ceshi");
        param.getTdh().setType("2.5d");
        param.setCreationName("test");
        param.setTenantCode("test");
        param.setUserId("test");
        param.setText("测试数字人");
        System.out.println(JSONObject.toJSON(param));
        JSONObject json = loadTemplate(param, "https://chatfiles.tydiczt.com/tdh/template/9_16.json");
        json.getJSONArray("parts").getJSONObject(0).put("tdh", JSONObject.parseObject(JSONObject.toJSONString(param.getTdh())));
        System.out.println(json);
    }
}
