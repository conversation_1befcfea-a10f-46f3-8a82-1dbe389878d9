package com.tydic.nbchat.openapi.core.timer;

import com.tydic.nbchat.openapi.api.bo.OapiTdhTrainOrderBO;
import com.tydic.nbchat.openapi.api.bo.eums.TdhTrainType;
import com.tydic.nbchat.openapi.core.busi.TdhCustomizeHelper;
import com.tydic.nbchat.openapi.core.busi.TdhCustomizeTrainService;
import com.tydic.nbchat.openapi.core.service.AbstractTdhCustomize;
import com.tydic.nbchat.openapi.core.service.Tdh2DCustomizeTask;
import com.tydic.nbchat.openapi.core.service.TdhMtkCustomizeTask;
import com.tydic.nbchat.openapi.core.service.VoiceCustomizeTask;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@EnableScheduling
@Slf4j
public class TdhCustomizeOrderHandling {

    private static final String key = "nbchat-oapi:customize_order_handle_lock";

    private final RedisHelper redisHelper;
    private final TdhCustomizeTrainService tdhCustomizeTrainService;
    private final TdhCustomizeHelper tdhCustomizeHelper;

    public TdhCustomizeOrderHandling(RedisHelper redisHelper,
                                     TdhCustomizeTrainService tdhCustomizeTrainService, TdhCustomizeHelper tdhCustomizeHelper) {
        this.redisHelper = redisHelper;
        this.tdhCustomizeTrainService = tdhCustomizeTrainService;
        this.tdhCustomizeHelper = tdhCustomizeHelper;
    }

    @Scheduled(cron = "0 */1 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void run() {
        RedisLockEntity lockEntity = RedisLockEntity.builder().lockKey(key).requestId(IdWorker.nextAutoIdStr()).build();
        boolean lock = redisHelper.lockLua(lockEntity, 600);
        if (lock) {
            try {
               //获取待执行任务
                OapiTdhTrainOrderBO order = tdhCustomizeTrainService.getTdhQueueTrainOrder();
                if (order != null) {
                    log.info("数字人定制-排队任务执行: {}", order);
                    //执行任务
                    if (TdhTrainType.TDH_2_5D.getCode().equals(order.getObjType())) {
                        TdhMtkCustomizeTask customizeAbstract = new TdhMtkCustomizeTask(tdhCustomizeHelper, order);
                        customizeAbstract.newThreadCustomize();
                    }
                } else {
                    log.info("数字人定制-排队任务为空");
                }
            } catch (Exception e) {
                log.error("数字人定制-排队任务执行异常: ", e);
            } finally {
                redisHelper.unlock(lockEntity);
            }
        }
    }

}
