package com.tydic.nbchat.openapi.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.openapi.core.config.OpenapiConfigProperties;
import com.tydic.nbchat.openapi.mapper.TdhVirtualMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TdhCustomizePriceBusiService {

    @Resource
    private TdhVirtualMapper tdhVirtualMapper;
    private final OpenapiConfigProperties openapiConfigProperties;

    public TdhCustomizePriceBusiService(OpenapiConfigProperties openapiConfigProperties) {
        this.openapiConfigProperties = openapiConfigProperties;
    }

    /**
     * 获取数字人定制价格
     * @param tenantCode
     * @param objType
     * @return
     */
    public Integer getTdhCustomizePrice(String tenantCode, String objType) {
        /**
         * {
         *     "tdhCustomizePrice": {
         *         "2d": {
         *           "type": 1, // 1 迪豆支付 2 现金支付
         *           "price":200, // 价格
         *         },
         *         "2.5d_mtk": {
         *           "type": 1,
         *           "price":200
         *         },
         *         "voice": {
         *           "type": 1,
         *           "price":200
         *          }
         *     }
         * }
         */
        // 计算定制数字人价格
        String tenantConfig = tdhVirtualMapper.selectTenantConfig(tenantCode);
        Integer priceScore = null;
        try {
            if (StringUtils.isNotBlank(tenantConfig)) {
                // 解析租户配置
                JSONObject tenantConfigJson = JSONObject.parseObject(tenantConfig);
                JSONObject tdhCustomizePrice = tenantConfigJson.getJSONObject("tdhCustomizePrice");
                if (tdhCustomizePrice != null) {
                    JSONObject price = tdhCustomizePrice.getJSONObject(objType);
                    if (price != null) {
                        priceScore = price.getInteger("price");
                    }
                }
            }
            if (priceScore == null) {
                priceScore = openapiConfigProperties.getTdhCustomizePrice().get(objType);
            }
            if (priceScore == null) {
                throw new RuntimeException("计算数字人定制价格异常!");
            }
            return priceScore;
        } catch (Exception e) {
            log.error("计算数字人定制价格异常: {}", e.getMessage());
            throw new RuntimeException("计算数字人定制价格异常!");
        }
    }

}
