package com.tydic.nbchat.openapi.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.openapi.api.bo.OapiTdhTrainOrderBO;
import com.tydic.nbchat.openapi.api.bo.creation.TdhCreationRequest;
import com.tydic.nbchat.openapi.api.bo.eums.CustomizeTaskStatus;
import com.tydic.nbchat.openapi.api.bo.eums.TdhTrainStatus;
import com.tydic.nbchat.openapi.api.bo.tdh.TdhTrainResult;
import com.tydic.nbchat.openapi.core.config.OpenapiConfigProperties;
import com.tydic.nbchat.openapi.mapper.OapiTdhTrainOrderMapper;
import com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder;
import com.tydic.nbchat.openapi.mapper.po.TdhCreationTask;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TdhCustomizeHelper {
    @Resource
    private OapiTdhTrainOrderMapper oapiTdhTrainTaskMapper;
    private final OpenapiConfigProperties openapiConfigProperties;
    private final TdhCreationTaskBusiService tdhCreationTaskBusiService;

    public TdhCustomizeHelper(OpenapiConfigProperties openapiConfigProperties,
                              TdhCreationTaskBusiService tdhCreationTaskBusiService) {
        this.openapiConfigProperties = openapiConfigProperties;
        this.tdhCreationTaskBusiService = tdhCreationTaskBusiService;
    }


    public void updateCustomizeTask(OapiTdhTrainOrder update) {
        update.setUpdateTime(new Date());
        oapiTdhTrainTaskMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新数字人定制任务状态
     *
     * @param orderId
     * @param trainTaskId
     * @param reason
     * @return
     */
    public void updateTdhTrainError(String orderId, String trainTaskId, String reason) {
        OapiTdhTrainOrder update = new OapiTdhTrainOrder();
        update.setOrderId(orderId);
        update.setTrainTaskId(trainTaskId);
        update.setTrainStatus(TdhTrainStatus.TRAIN_ERROR.getCode());
        update.setTrainError(reason);
        update.setUpdateTime(new Date());
        update.setOrderStatus(CustomizeTaskStatus.TRAIN_ERROR.getCode());
        oapiTdhTrainTaskMapper.updateByPrimaryKeySelective(update);
    }

    //提交数字人定制，返回任务ID
    public String submitTdhCustomize(OapiTdhTrainOrderBO task) {
        /**
         * {
         *   "media_url": "http://172.168.1.249:9098/v1/live/download/face/audio_0.mp4"
         * }
         *
         * {
         *   "trans_id": "202412241426-kh2r4xqhT9Q0L",
         *   "node_name": "172.168.1.249_19100_detector",
         *   "cost_time": {},
         *   "code": 0,
         *   "data": {
         *     "task_id": "202412241426-kh2r4xqhT9Q0L"
         *   },
         *   "error": {}
         * }
         */
        String api = openapiConfigProperties.getTdhTrainUrl() + "mtk/v1/mtk_assist/handle";
        JSONObject body = new JSONObject();
        body.put("tdh_id", task.getObjId());
        body.put("media_url", task.getSourceUrl());
        body.put("sex", task.getGender());
        if (EntityValidType.NORMAL.getCode().equals(task.getIsCutout())) {
            body.put("matting_if", true);
        }
        String result = HttpClientHelper.doPost(api, new HashMap<>(), body.toJSONString());
        if (StringUtils.isNotBlank(result)) {
            //获取任务ID
            JSONObject resultJson = JSONObject.parseObject(result);
            if (resultJson.containsKey("code") && resultJson.getInteger("code") == 0) {
                JSONObject data = resultJson.getJSONObject("data");
                if (data.containsKey("task_id")) {
                    log.info("数字人定制[{}]-任务创建成功: {}|{}",task.getOrderId(), body, resultJson);
                    return data.getString("task_id");
                }
            }
            log.error("数字人定制[{}]-任务创建失败: {}|{}",task.getOrderId(), body , result);
        }
        return "";
    }

    //轮询任务结果，返回数字人形象id
    public TdhTrainResult pollTdhCustomize(OapiTdhTrainOrderBO task) {
        /**
         * {
         *      "task_id": "202412241426-kh2r4xqhT9Q0L"
         * }
         *
         * {
         *     "trans_id": "202412250014-Fb0ESGLmHO4Vv",
         *     "node_name": "172.168.1.245_19100_detector",
         *     "cost_time": {},
         *     "code": 0,
         *     "data": {
         *         "task_status": "done",
         *         "failed_reason": null,
         *         "sample_id": "202412250013-gY3Olm0QDcJKZ"
         *     },
         *     "error": {}
         * }
         */
        String api = openapiConfigProperties.getTdhTrainUrl() + "mtk/v1/mtk_assist/search";
        JSONObject body = new JSONObject();
        body.put("task_id", task.getTrainTaskId());
        TdhTrainResult trainResult = new TdhTrainResult("任务异常");
        int pollCount = 0;
        while (++pollCount < 200) {
            try {
                String result = HttpClientHelper.doPost(api, new HashMap<>(), body.toJSONString());
                log.info("数字人定制[{}]-查询任务结果: {}", task.getOrderId(), result);
                if (StringUtils.isNotBlank(result)) {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    if (resultJson.containsKey("code") && resultJson.getInteger("code") == 0) {
                        JSONObject data = resultJson.getJSONObject("data");
                        if (data.containsKey("task_status")) {
                            String taskStatus = data.getString("task_status");
                            String failedReason = data.getString("failed_reason");
                            trainResult.setStatus(taskStatus);
                            trainResult.setReason(failedReason);
                            if ("check_failed".equals(taskStatus)) {
                                return trainResult;
                            }
                            this.updateProgress(data, task.getOrderId());
                            if ("done".equals(taskStatus)) {
                                //训练完成
                                trainResult.setTdhId(data.getString("sample_id"));
                                trainResult.setTdhImg(data.getString("page_url"));
                                trainResult.setVideoUrl(data.getString("http_compose"));
                                return trainResult;
                            }
                        }
                    }
                }
                TimeUnit.SECONDS.sleep(5);
            } catch (Exception e) {
                log.error("数字人定制[{}]-查询任务结果异常: {}",task.getOrderId(), task, e);
                trainResult.setReason(e.getMessage().substring(0, Math.min(100, e.getMessage().length())));
            }
        }
        return trainResult;
    }


    public void updateProgress(JSONObject data, String orderId){
        JSONObject progressing = new JSONObject(true);
        progressing.put("stage", data.get("check_progress_name"));
        progressing.put("checkProgress", data.get("check_progress_value"));
        progressing.put("subCheckProgress", data.get("sub_check_progress_value"));
        progressing.put("totalProgress", String.format("%.2f%%",
                (data.getFloat("check_progress_value")
                        + (data.getFloat("sub_check_progress_value") * data.getFloat("ratio"))) * 100));
        OapiTdhTrainOrder update = new OapiTdhTrainOrder();
        update.setOrderId(orderId);
        update.setProgressing(progressing.toJSONString());
        this.updateCustomizeTask(update);
    }

    //提交输出demo预览视频，轮询获取结果
    public String pollTdhCustomizeDemo(OapiTdhTrainOrderBO task) {
        try {
            TdhCreationRequest request = new TdhCreationRequest();
            request.getTdh().setId(task.getObjId());
            request.getTdh().setType(task.getObjType());
            request.setUserId("1");
            request.setTenantCode("000TYDIC");
            request.setCreationName("数字人定制预览_"+task.getObjId());
            request.setTplId(openapiConfigProperties.getTdhTemplateMap().get(task.getGenderEn()));
            request.setText("");
            log.info("数字人定制[{}]-提交预览视频生成: {}",task.getOrderId(), request);
            String taskId = tdhCreationTaskBusiService.submitCreationTask(request);
            while (true) {
                TimeUnit.SECONDS.sleep(5);
                TdhCreationTask creationTask = tdhCreationTaskBusiService.getTdhTask(taskId);
                if ("2".equals(creationTask.getTaskState())) {
                    //任务异常
                    log.error("数字人定制[{}]-预览视频生成异常: {}",task.getOrderId(), creationTask.getErrorCode());
                    throw new RuntimeException("任务异常: " + creationTask.getErrorCode());
                }
                if ("1".equals(creationTask.getTaskState())) {
                    //任务完成
                    log.info("数字人定制[{}]-预览视频生成成功: {}",task.getOrderId(), creationTask.getVideoUrl());
                    return creationTask.getVideoUrl();
                }
            }
        } catch (Exception e) {
            e.fillInStackTrace();
            throw new RuntimeException(e);
        }
    }

}
