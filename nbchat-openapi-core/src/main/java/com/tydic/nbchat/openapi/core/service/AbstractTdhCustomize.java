package com.tydic.nbchat.openapi.core.service;

import com.tydic.nbchat.openapi.api.bo.OapiTdhTrainOrderBO;
import com.tydic.nbchat.openapi.core.busi.TdhCustomizeHelper;
import com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public abstract class AbstractTdhCustomize {

    protected TdhCustomizeHelper tdhCustomizeHelper;
    protected OapiTdhTrainOrderBO order;

    public AbstractTdhCustomize(TdhCustomizeHelper tdhCustomizeHelper, OapiTdhTrainOrderBO order) {
        this.tdhCustomizeHelper = tdhCustomizeHelper;
        this.order = order;
    }

    public abstract void customize();

    // 重新生成预览视频
    public void updateCustomizeVideoDemo() {
        String demoUrl = tdhCustomizeHelper.pollTdhCustomizeDemo(this.order);
        log.info("数字人定制-任务完成-更新预览视频：{}", demoUrl);
        OapiTdhTrainOrder update = new OapiTdhTrainOrder();
        update.setOrderId(this.order.getOrderId());
        update.setUpdateTime(new Date());
        update.setDemoUrl(demoUrl);
        tdhCustomizeHelper.updateCustomizeTask(update);
    }

    public void newThreadCustomize() {
        try {
            new Thread(this::customize).start();
        } catch (Exception e) {
            log.error("数字人定制-任务执行异常: {}", order, e);
            tdhCustomizeHelper.updateTdhTrainError(order.getOrderId(), null, "任务执行异常");
        }
    }
}
