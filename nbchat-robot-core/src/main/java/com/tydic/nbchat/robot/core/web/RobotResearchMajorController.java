package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbchatResearchMajorApi;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorDeleteRequest;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorSaveRequest;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorQueryRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Deprecated
@Slf4j
@RestController
@RequestMapping("/robot/research")
public class RobotResearchMajorController {

    final private NbchatResearchMajorApi nbchatResearchMajorApi;

    public RobotResearchMajorController(NbchatResearchMajorApi nbchatResearchMajorApi) {
        this.nbchatResearchMajorApi = nbchatResearchMajorApi;
    }

    @PostMapping(value = "/majors")
    public RspList getResearchMajors(@RequestBody FileResearchMajorQueryRequest request) {
        return nbchatResearchMajorApi.getResearchMajors(request);
    }

    @PostMapping(value = "/major")
    public Rsp getResearchMajor(@RequestBody FileResearchMajorQueryRequest request) {
        return nbchatResearchMajorApi.getResearchMajor(request);
    }

    @PostMapping(value = "/major/analysis-status")
    public Rsp getMajorStatus(@RequestBody FileResearchMajorQueryRequest request) {
        return nbchatResearchMajorApi.getMajorAnalysisStatus(request);
    }


    @PostMapping(value = "/major/save")
    public Rsp saveMajor(@RequestBody FileResearchMajorSaveRequest request) {
        return nbchatResearchMajorApi.saveMajor(request);
    }

    @PostMapping(value = "/major/delete")
    public Rsp saveMajor(@RequestBody FileResearchMajorDeleteRequest Request) {
        return nbchatResearchMajorApi.deleteMajor(Request);
    }
}
