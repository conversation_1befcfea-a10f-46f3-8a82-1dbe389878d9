package com.tydic.nbchat.robot.core.openai.exception;

import com.alibaba.fastjson.JSONObject;

public class TianShuException extends OpenAiHttpException {
    public TianShuException(Exception e, String errorBody, int statusCode) {
        super(e, errorBody, statusCode);
    }

    /**
     * 解析错误信息
     * @return
     */
    public TianShuErrorBody parseErrorBody() {
        try {
            return JSONObject.parseObject(this.getErrorBody(), TianShuErrorBody.class);
        } catch (Exception e) {
            return new TianShuErrorBody();
        }
    }
}
