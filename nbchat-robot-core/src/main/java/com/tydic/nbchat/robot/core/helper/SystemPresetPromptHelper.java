package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt;
import com.tydic.nicc.common.eums.EntityValidType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 系统预设模板
 */
@Slf4j
@Component
public class SystemPresetPromptHelper {

    final private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    @Resource
    private NbchatPresetPromptMapper nbchatPresetPromptMapper;

    public SystemPresetPromptHelper(NbchatRobotConfigProperties nbchatRobotConfigProperties) {
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
    }

    public NbchatPresetPrompt getSysPresetPrompt(String preset){
        return nbchatPresetPromptMapper.selectById(preset);
    }


    /**
     * 匹配业务机器人类型
     * @param busiCode
     * @param robotType
     * @return
     */
    public String matchRobotType(String presetId,String busiCode, String robotType) {
        Map<String,String> busiRobotMap = nbchatRobotConfigProperties.getBusiRobotMap();
        Map<String,String> presetRobotMap = nbchatRobotConfigProperties.getPresetRobotMap();
        if(busiRobotMap != null && busiRobotMap.containsKey(busiCode)){
            robotType = busiRobotMap.get(busiCode) == null ? robotType : busiRobotMap.get(busiCode);
            log.info("机器人消息处理[工具]-重设机器人类型: {}|{}",busiCode,robotType);
        }
        if(presetRobotMap != null && presetRobotMap.containsKey(presetId)){
            robotType = presetRobotMap.get(presetId) == null ? robotType : presetRobotMap.get(presetId);
            log.info("机器人消息处理[工具]-重设机器人类型: {}|{}",presetId,robotType);
        }
        return robotType;
    }



    /**
     * 查询原始模板
     * @param presetId
     * @return
     */
    public NbchatPresetPrompt getOriginTemplate(String presetId){
        NbchatPresetPrompt presetPrompt = nbchatPresetPromptMapper.selectById(presetId);
        AtomicReference<NbchatPresetPrompt> template = new AtomicReference<>(presetPrompt);
        Optional.ofNullable(presetPrompt).ifPresent(prompt->{
            if(EntityValidType.NORMAL.getCode().equals(prompt.getIsValid())){
                template.set(prompt);
            }
        });
        if(template.get() == null){
            throw new RuntimeException("请配置预设模板: presetId = " + presetId);
        }
        return template.get();
    }

    /**
     * 获取系统提示
     * @param presetId
     * @param args
     * @return
     */
    public String getSystemRole(String presetId,String... args){
        String tmp = getOriginTemplate(presetId).getSystemRole();
        return replaceTemplate(tmp,args);
    }

    /**
     * 获取模板
     * @param presetId
     * @param args
     * @return
     */
    public String getUserRole(String presetId,String... args){
        String tmp = getOriginTemplate(presetId).getTemplate();
        return replaceTemplate(tmp,args);
    }


    /**
     * 替换模板
     * @param tmp
     * @param args
     * @return
     */
    private String replaceTemplate(String tmp,String... args){
        if(StringUtils.isNotBlank(tmp)){
            int i = 0;
            for (String arg : args) {
                tmp = tmp.replace("#ARG"+i++,arg);
            }
        }
        return tmp;
    }

    /**
     * 获取普通聊天message
     * @param request
     * @return
     */
    public ChatMessageBuilder buildPromptMessage(RobotPromptMessageRequest request){
        final List<ChatMessage> messages = new ArrayList<>();
        NbchatPresetPrompt presetPrompt = getSysPresetPrompt(request.getPresetId());
        if (request.getMessages() != null) {
            messages.addAll(request.getMessages());
        }

        Optional.ofNullable(presetPrompt).ifPresent(obj->{
            String template = obj.getTemplate();
            int i = 0;
            if (CollectionUtils.isNotEmpty(request.getPresetPrompts())) {
                for (String input : request.getPresetPrompts()) {
                    template = template.replace("#ARG"+i++,input.trim());
                }
            }
            if (StringUtils.isNotBlank(obj.getSystemRole())) {
                String sysTemplate = obj.getSystemRole();
                if (CollectionUtils.isNotEmpty(request.getPresetSysPrompts())) {
                    for (String input : request.getPresetSysPrompts()) {
                        sysTemplate = sysTemplate.replace("#ARG"+i++,input.trim());
                    }
                }
                ChatMessage sysMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), sysTemplate);
                //插入到第一条
                messages.add(0,sysMessage);
            }
            if (StringUtils.isNotBlank(template)) {
                ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), template);
                if (JSONObject.isValidArray(template)) {
                    userMessage = new ChatMessage(ChatMessageRole.USER.value(), JSONArray.parseArray(template));
                } else if (JSONObject.isValid(template)) {
                    userMessage = new ChatMessage(ChatMessageRole.USER.value(), JSONArray.parseObject(template));
                }
                messages.add(userMessage);
            }

        });

        if (log.isTraceEnabled()) {
            log.trace("构建预设提示消息:{}",messages);
        }
        if(!messages.isEmpty()){
            //计算token长度
            return ChatMessageBuilder.builder().success(true).messages(messages).build();
        }
        return ChatMessageBuilder.builder().success(false).build();
    }

    public String buildPromptMessage(String promptId,List<String> args){
        log.info("处理提示词：{}|{}",promptId,args);
        NbchatPresetPrompt presetPrompt = getSysPresetPrompt(promptId);
        if (ObjectUtils.isNotEmpty(presetPrompt)) {
            String template = presetPrompt.getTemplate();
            int i = 0;
            for (String input : args) {
                template = template.replace("#ARG"+i++,input.trim());
            }
            return template;
        }
        return "";
    }

}
