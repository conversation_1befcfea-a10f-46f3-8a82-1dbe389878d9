package com.tydic.nbchat.robot.core.config;

import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Slf4j
@Configuration
public class ChatHelperAutoConfig {
    @Resource
    private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    @Resource
    private RobotConfigBusiService robotConfigBusiService;

    @Bean
    public RobotAiHelperFactory createChatgptHelper(){
        return new RobotAiHelperFactory(nbchatRobotConfigProperties,robotConfigBusiService);
    }

}
