package com.tydic.nbchat.robot.core.web;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;


@Slf4j
@RestController
@RequestMapping("/robot/tools")
public class RobotToolsController {

    final private NbchatRobotToolsApi nbchatRobotToolsApi;

    public RobotToolsController(NbchatRobotToolsApi nbchatRobotToolsApi) {
        this.nbchatRobotToolsApi = nbchatRobotToolsApi;
    }

    @PostMapping("/chat/completion")
    public Object getChatResult(@Validated @RequestBody RobotPromptMessageRequest request) {
        try {
            if (request.isStream()) {
                return getStreamingResponseBody(request);
            }
            return nbchatRobotToolsApi.getChatResult(request);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseRspUtils.createErrorRsp("服务器内部异常:"+e.getMessage());
        }
    }

    @PostMapping("/ppt/chat/completion")
    public Object getPPTChatResult(@RequestBody RobotPromptMessageRequest request) {
        return  nbchatRobotToolsApi.getPPTChatResult(request);
    }

    @PostMapping("/chat")
    public StreamingResponseBody chat(@RequestBody RobotPromptMessageRequest request) {
        return getStreamingResponseBody(request);
    }

    /**
     * 获取响应流
     * @param request
     * @return
     */
    @NotNull
    private StreamingResponseBody getStreamingResponseBody(@RequestBody RobotPromptMessageRequest request) {
        Rsp rsp;
        if (StringUtils.isNotEmpty(request.getAppType())) {
            rsp = checkAppChatRequest(request);
        } else {
            rsp = checkChatRequest(request);
        }
        if(rsp != null){
            Rsp finalRsp = rsp;
            return outputStream -> {
                outputStream.write(JSONObject.toJSONBytes(finalRsp));
                outputStream.flush();
            };
        }
        return outputStream -> nbchatRobotToolsApi.sendMessage(request, new RobotProcessCallback() {
            @Override
            public void onMessage(RobotMsgContext context) {
                try {
                    if(log.isTraceEnabled()){
                        log.trace("机器人消息处理:{}->{}",request.getRequestId(),context.getText());
                    }
                    byte[] obj = JSONObject.toJSONBytes(context);
                    outputStream.write(NbchatRobotMsgBuilder.mergeEnter(obj));
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("机器人消息处理-数据流推送异常:{}",context,e);
                }
            }
            @Override
            public void onError(RobotMessageRequest request,Throwable e) {
                //重试
                log.error("机器人消息处理-消息处理异常:{}",request,e);
            }
        });
    }


    /**
     * 校验请求参数
     * @param request
     * @return
     */
    private Rsp checkChatRequest(RobotPromptMessageRequest request){
        Rsp rsp = null;
        String requestId = NbchatRobotMsgBuilder.createRequestId();
        request.setRequestId(requestId);
        if(StringUtils.isAnyBlank(request.getUserId(),request.getPresetId())){
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        if(request.getPresetPrompts() == null || request.getPresetPrompts().isEmpty()){
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        return rsp;
    }

    private Rsp checkAppChatRequest(RobotPromptMessageRequest request) {
        String requestId = NbchatRobotMsgBuilder.createRequestId();
        request.setRequestId(requestId);

        Rsp rsp = null;
        if (StringUtils.isAnyBlank(request.getUserId(), request.getText(),request.getSessionId())) {
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        return rsp;
    }
}
