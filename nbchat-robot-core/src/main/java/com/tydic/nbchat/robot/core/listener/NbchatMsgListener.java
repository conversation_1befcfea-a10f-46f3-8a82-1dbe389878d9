package com.tydic.nbchat.robot.core.listener;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.util.exception.SensitiveIncludeException;

public interface NbchatMsgListener {

    /**
     * 用户请求
     * @param request
     * @return
     */
    RobotMsgContext onRequest(RobotMessageRequest request) throws SensitiveIncludeException;

    /**
     * 收到消息
     * @param context
     * @return
     */
    RobotMsgContext onResponse(RobotMessageRequest context, ChatCompletionChunk chunk);

    /**
     * 收到消息
     * @param context
     * @param result
     * @return
     */
    RobotMsgContext onResponse(RobotMessageRequest context, ChatCompletionResult result);

    /**
     * 异常
     * @param request
     */
    RobotMsgContext onError(RobotMessageRequest request,Throwable e);
}
