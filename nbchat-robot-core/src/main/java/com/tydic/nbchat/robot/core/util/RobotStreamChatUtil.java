package com.tydic.nbchat.robot.core.util;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.NbChatRobotProcessApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class RobotStreamChatUtil {


    /***
     * 将 http://localhost:8080/robot?sessionId={sessionId}&majorId={majorId} 填入url参数
     * @param url
     * @param params
     * @return
     */
    public static String buildUrl(String url, Object... uriVariables) {
        //从urlBuilder构建
        UriBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);
        return uriBuilder.build(uriVariables).toString();
    }

    /**
     * 获取响应流
     *
     * @param request
     * @return
     */
    @NotNull
    public static StreamingResponseBody streamChat(NbChatRobotProcessApi nbChatRobotProcessApi,
                                                   @RequestBody RobotMessageRequest request) {
        return outputStream -> nbChatRobotProcessApi.sendMessage(request, new RobotProcessCallback() {
            @Override
            public void onMessage(RobotMsgContext context) {
                try {
                    if (log.isTraceEnabled()) {
                        log.trace("机器人消息处理:{}->{}", request.getRequestId(), context.getText());
                    }
                    byte[] obj = JSONObject.toJSONBytes(context);
                    outputStream.write(NbchatRobotMsgBuilder.mergeEnter(obj));
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("机器人消息处理-数据流推送异常:{}", context, e);
                }
            }

            @Override
            public void onError(RobotMessageRequest request, Throwable e) {
                //重试
                log.error("机器人消息处理-消息处理异常:{}", request, e);
            }
        });
    }

    public static String unicodeDecode(String string) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(string);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            string = string.replace(matcher.group(1), ch + "");
        }
        return string;
    }


    public static void main(String[] args) {
        System.out.println(unicodeDecode("{\"error\":{\"code\":\"1301\",\"message\":\"\\u7cfb\\u7edf\\u68c0\\u6d4b\\u5230\\u8f93\\u5165\\u6216\\u751f\\u6210\\u5185\\u5bb9\\u53ef\\u80fd\\u5305\\u542b\\u4e0d\\u5b89\\u5168\\u6216\\u654f\\u611f\\u5185\\u5bb9\\uff0c\\u8bf7\\u60a8\\u907f\\u514d\\u8f93\\u5165\\u6613\\u4ea7\\u751f\\u654f\\u611f\\u5185\\u5bb9\\u7684\\u63d0\\u793a\\u8bed\\uff0c\\u611f\\u8c22\\u60a8\\u7684\\u914d\\u5408\\u3002\"}}"));
    }
}
