package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.baidu-config")
public class NbchatBdRobotConfigProperties {
    private String appId;
    private String accessKey;
    private String secretKey;
    private String api = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={ak}&client_secret={sk}";
    // 百度key 一个月有效
    private Integer expiresIn = 2592000;
    private Boolean tokenTimerEnable = true;

    public boolean isValid(){
        return StringUtils.isNoneBlank(api,accessKey,secretKey);
    }
}
