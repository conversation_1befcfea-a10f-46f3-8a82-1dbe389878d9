/*
package com.tydic.nbchat.robot.core.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.spec.SecretKeySpec;
import java.util.Date;

public class JwtUtil {

    public static String generateToken(String appKey, String appSecret, Long exp) {
        Date now = new Date();
        Date expDate = new Date(now.getTime() + exp);

        SecretKeySpec secretKeySpec = new SecretKeySpec(appSecret.getBytes(), SignatureAlgorithm.HS256.getJcaName());

        Claims claims = Jwts.claims();
        claims.put("app_key", appKey);
        claims.setExpiration(expDate);
        claims.setIssuedAt(now);

        return Jwts.builder()
                .setClaims(claims)
                .setHeaderParam("typ", "JWT")
                .signWith(secretKeySpec, SignatureAlgorithm.HS256)
                .compact();
    }


    public static void main(String[] args) {
        System.out.println(generateToken("zbrs2kprc1pnjut76lnjefj6axijad67",
                "97jfe2r9rk39s3t7kqmigvuwzamqjaxrwlx3pck2k73s2yw7w3an8rponleqtdu2", 7 * 24 * 3600 * 1000L));
    }
}*/
