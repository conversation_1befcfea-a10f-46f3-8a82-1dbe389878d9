package com.tydic.nbchat.robot.core.impl;

import com.tydic.nbchat.robot.api.NbchatResearchShareApi;
import com.tydic.nbchat.robot.api.bo.share.RobotShareCheckRspBO;
import com.tydic.nbchat.robot.api.bo.share.RobotShareQueryReqBO;
import com.tydic.nbchat.robot.api.bo.share.RobotShareQueryRspBO;
import com.tydic.nbchat.robot.api.bo.share.RobotShareSaveReqBO;
import com.tydic.nbchat.robot.core.config.ShareConfigProperties;
import com.tydic.nbchat.robot.core.util.RandomStringGenerator;
import com.tydic.nbchat.robot.mapper.NbchatResearchShareMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchShare;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class NbchatResearchShareServiceImpl implements NbchatResearchShareApi {

    @Resource
    private NbchatResearchShareMapper nbchatResearchShareMapper;
    private final ShareConfigProperties shareConfigProperties;

    public NbchatResearchShareServiceImpl(ShareConfigProperties shareConfigProperties) {
        this.shareConfigProperties = shareConfigProperties;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp createShare(RobotShareSaveReqBO reqBO) {
        log.info("创建分享:{}",reqBO);
        if(StringUtils.isBlank(reqBO.getUserId())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        String shareId = NiccCommonUtil.createImUserId(true);
        if (reqBO.isNeedKey() && StringUtils.isEmpty(reqBO.getShareKey())) {
            reqBO.setShareKey(RandomStringGenerator.generateRandomString(shareConfigProperties.getShareKeyLength()));
        }
        NbchatResearchShare sharePO = new NbchatResearchShare();
        BeanUtils.copyProperties(reqBO,sharePO);
        sharePO.setShareId(shareId);
        String url = shareConfigProperties.getShareUrlPrefix() + shareId;
        sharePO.setShareUrl(url);
        if (reqBO.getExpiredDay() != -1) {
            sharePO.setExpiredDate(DateTimeUtil.DateAddDayOfYear(reqBO.getExpiredDay()));
        } else {
            sharePO.setExpiredDate(DateTimeUtil.convertAsDate("9999-01-01 00:00:00"));
        }
        sharePO.setCreatedAt(new Date());
        int i = nbchatResearchShareMapper.insertSelective(sharePO);
        if (i > 0) {
            RobotShareQueryReqBO shareQueryReqBO = new RobotShareQueryReqBO();
            shareQueryReqBO.setShareId(shareId);
            Rsp rsp = getShare(shareQueryReqBO);
            log.info("创建分享-分享记录:{}",rsp.getData());
            return rsp;
        }
        return BaseRspUtils.createErrorRsp("创建分享失败!");
    }

    @Override
    public Rsp getShare(RobotShareQueryReqBO shareQueryReqBO) {
        if(StringUtils.isBlank(shareQueryReqBO.getShareId())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        NbchatResearchShare share = nbchatResearchShareMapper.selectById(shareQueryReqBO.getShareId());
        if(share == null){
            return BaseRspUtils.createErrorRsp("未查询到对象!");
        }
        RobotShareCheckRspBO rspBO = new RobotShareCheckRspBO();
        BeanUtils.copyProperties(share,rspBO);
        if(StringUtils.isNotBlank(share.getShareKey())) {
            rspBO.setNeedKey(EntityValidType.NORMAL.getIntCode());
        }
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp checkShare(RobotShareQueryReqBO reqBO) {
        log.info("校验分享:{}",reqBO);
        if(StringUtils.isAnyBlank(reqBO.getShareId(),reqBO.getShareKey())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        NbchatResearchShare share = nbchatResearchShareMapper.selectById(reqBO.getShareId());
        if(share == null){
            return BaseRspUtils.createErrorRsp("未查询到对象!");
        }
        //验证密码
        if(reqBO.getShareKey().trim().equals(share.getShareKey())){
            RobotShareCheckRspBO rspBO = new RobotShareCheckRspBO();
            BeanUtils.copyProperties(share,rspBO);
            return BaseRspUtils.createSuccessRsp(rspBO);
        }
        return BaseRspUtils.createErrorRsp("验证失败!");
    }
}
