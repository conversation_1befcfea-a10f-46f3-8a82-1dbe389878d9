package com.tydic.nbchat.robot.core.openai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tydic.nbchat.robot.api.bo.eums.BaiduAiModel;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingRequest;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingResult;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingSingleRequest;
import com.tydic.nbchat.robot.core.openai.OpenAiApi;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.exception.OpenAiHttpException;
import com.tydic.nbchat.robot.core.openai.exception.TianShuErrorBody;
import com.tydic.nbchat.robot.core.openai.exception.TianShuException;
import com.tydic.nbchat.robot.core.openai.interceptor.AuthenticationInterceptor;
import com.tydic.nbchat.robot.core.openai.interceptor.ConnectTimoutRetryInterceptor;
import com.tydic.nbchat.robot.core.openai.interceptor.QueryParamsInterceptor;
import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.Single;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import retrofit2.Call;
import retrofit2.HttpException;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;


@Slf4j
public class OpenAiService {

    private static final String defaultApiUrl = "https://api.openai.com/";
    private ServiceConfigProperties configProperties;
    private static final int DEFAULT_CONNECT_TIMEOUT = 2;
    private final ObjectMapper objectMapper = defaultObjectMapper();
    private OpenAiApi openAiApi;
    private OkHttpClient httpClient;
    private ExecutorService executorService;

    public ServiceConfigProperties getConfigProperties() {
        return configProperties;
    }

    public OkHttpClient getHttpClient() {
        return httpClient;
    }

    public ExecutorService getExecutorService() {
        return executorService;
    }

    /**
     * 初始化服务
     */
    private void initOpenAiService(){
        Objects.requireNonNull(configProperties.getTokenKey(), "OpenAi token required");
        Objects.requireNonNull(configProperties.getApiUrl(), "OpenAi apiUrl required");
        this.httpClient = this.defaultClient(configProperties.getTokenKey(),
                Duration.ofSeconds(configProperties.getReadTimeout()));
        this.openAiApi = this.buildApi(httpClient);
        settingExecutor();
    }

    public OpenAiService(final String token) {
        this.configProperties = new ServiceConfigProperties(defaultApiUrl,token);
        initOpenAiService();
    }

    public OpenAiService(final String apiUrl,final String token) {
        this.configProperties = new ServiceConfigProperties(apiUrl,token);
        initOpenAiService();
    }

    public OpenAiService(final ServiceConfigProperties configProperties) {
        this.configProperties = configProperties;
        initOpenAiService();
    }

    public OpenAiService(String apiUrl, String token, String robotType, String robotModel) {
        this.configProperties = new ServiceConfigProperties(apiUrl,token,robotType,robotModel);
        initOpenAiService();
    }

    /*** 文心一言 ****/
    public ChatCompletionResult createBaiduChatCompletion(ChatCompletionRequest request) {
        String model = request.getModel();
        if (BaiduAiModel.ERNIE_BOT_4.getModel().equals(model)) {
            return execute(openAiApi.createBaiduChatCompletionPro(request));
        } else if (BaiduAiModel.ERNIE_BOT_TURBO.getModel().equals(model)) {
            return execute(openAiApi.createBaiduChatCompletionTurbo(request));
        } else {
            return execute(openAiApi.createBaiduChatCompletion(request));
        }
    }


    public Flowable<ChatCompletionChunk> streamBaiduChatCompletion(ChatCompletionRequest request) {
        request.setStream(true);
        String model = request.getModel();
        if (BaiduAiModel.ERNIE_BOT_4.getModel().equals(model)) {
            return stream(openAiApi.createBaiduChatCompletionStreamPro(request), ChatCompletionChunk.class);
        } else if (BaiduAiModel.ERNIE_BOT_TURBO.getModel().equals(model)) {
            return stream(openAiApi.createBaiduChatCompletionStreamTurbo(request), ChatCompletionChunk.class);
        } else {
            return stream(openAiApi.createBaiduChatCompletionStream(request), ChatCompletionChunk.class);
        }
    }

    /*** 文心一言 ****/

    /*** open ai/ fastchat ****/

    public Flowable<ChatCompletionChunk> streamChatCompletion(ChatCompletionRequest request) {
        request.setStream(true);
        if (this.configProperties.checkConfigValue("Accept","text/event-stream")) {
            String key = this.configProperties.getConfigValue("ResultField");
            if (StringUtils.isNotBlank(key)) {
                return readDataStream(openAiApi.createCommonChatCompletionStream("text/event-stream", request),key, ChatCompletionChunk.class);
            } else {
                throw new RuntimeException("模型调用-模型配置异常: "+this.configProperties);
            }
        }
        return stream(openAiApi.createChatCompletionStream(request), ChatCompletionChunk.class);
    }

    public ChatCompletionResult createChatCompletion(ChatCompletionRequest request) {
        request.setStream(false);
        if (this.configProperties.checkConfigValue("ResultField","data")) {
            JSONObject jsonObject = execute(openAiApi.createCommonChatCompletion(request));
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                log.error("模型调用-返回异常: {}|{}",request, jsonObject);
            }
            return objectMapper.convertValue(data, ChatCompletionResult.class);
        }
        return execute(openAiApi.createChatCompletion(request));
    }

    public EmbeddingResult createEmbeddings(EmbeddingRequest request) {
        return execute(openAiApi.createEmbeddings(request));
    }

    public EmbeddingResult createEmbeddings(EmbeddingSingleRequest request) {
        return execute(openAiApi.createEmbeddings(request));
    }

    /*** open ai/ fastchat ****/

    /** 接入智普ai规范 ***/

    public Flowable<ChatCompletionChunk> streamZpChatCompletion(ChatCompletionRequest request) {
        request.setStream(true);
        return stream(openAiApi.createZPChatCompletionStream(request), ChatCompletionChunk.class);
    }

    public ChatCompletionResult createZpChatCompletion(ChatCompletionRequest request) {
        request.setStream(false);
        return execute(openAiApi.createZPChatCompletion(request));
    }

    /** 接入智普ai规范 ***/


    /** 豆包AI ***/
    public Flowable<ChatCompletionChunk> streamDoubaoChatCompletion(ChatCompletionRequest request) {
        request.setStream(true);
        if (request.getSearchOnline() != null && request.getSearchOnline()) {
            return stream(openAiApi.createDoubaoBotsChatCompletionStream(request), ChatCompletionChunk.class);
        }
        return stream(openAiApi.createDoubaoChatCompletionStream(request), ChatCompletionChunk.class);
    }

    public ChatCompletionResult createDoubaoChatCompletion(ChatCompletionRequest request) {
        request.setStream(false);
        if (request.getSearchOnline() != null && request.getSearchOnline()) {
            return execute(openAiApi.createDoubaoBotsChatCompletion(request));
        }
        return execute(openAiApi.createDoubaoChatCompletion(request));
    }
    /** 豆包AI ***/



    /**
     * 构建ok http client
     * @param token
     * @param readTimeout
     * @return
     */
    public OkHttpClient defaultClient(String token, Duration readTimeout) {
        Objects.requireNonNull(token, "OpenAi token required");
        Objects.requireNonNull(configProperties.getApiUrl(), "OpenAi apiUrl required");
        Interceptor authInterceptor;
        if(configProperties.getApiUrl().contains("aip.baidubce.com")){
            //文心一言token加入
            Map<String,String> param = new HashMap<>();
            param.put("access_token",token);
            authInterceptor = new QueryParamsInterceptor(param);
        } else {
            if (configProperties.checkConfigValue("Bearer","false")) {
                //不需要Bearer的token
                authInterceptor = new AuthenticationInterceptor(token);
            } else {
                //其他类别的认证方式
                authInterceptor = new AuthenticationInterceptor("Bearer " + token);
            }
        }
        final ConnectionPool pool = new ConnectionPool(64, 10, TimeUnit.SECONDS);
        return new OkHttpClient.Builder()
                .addInterceptor(authInterceptor)
                .connectionPool(pool)
                .readTimeout(readTimeout)
                .retryOnConnectionFailure(true)
                //超时重试
                .addInterceptor(new ConnectTimoutRetryInterceptor(2))
                .connectTimeout(DEFAULT_CONNECT_TIMEOUT,TimeUnit.SECONDS)
                .build();
    }

    public Retrofit defaultRetrofit(OkHttpClient client, ObjectMapper mapper) {
        return new Retrofit.Builder()
                .baseUrl(this.configProperties.getApiUrl())
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .build();
    }


    public OpenAiApi buildApi(OkHttpClient client) {
        ObjectMapper mapper = defaultObjectMapper();
        Retrofit retrofit = defaultRetrofit(client, mapper);
        return retrofit.create(OpenAiApi.class);
    }

    public ObjectMapper defaultObjectMapper() {
        return ResponseBodyCallback.defaultObjectMapper();
    }

    private void settingExecutor(){
        httpClient.dispatcher().setMaxRequests(200);
        httpClient.dispatcher().setMaxRequestsPerHost(50);
        this.executorService = httpClient.dispatcher().executorService();
    }


    /**
     * Calls the Open AI api, returns the response, and parses error messages if the request fails
     */
    public <T> T execute(Single<T> apiCall) {
        try {
            return apiCall.blockingGet();
        } catch (HttpException e) {
            try {
                try (ResponseBody responseBody = Objects.requireNonNull(e.response()).errorBody()) {
                    if (responseBody != null) {
                        String errorBody = responseBody.string();
                        try {
                            TianShuErrorBody tianShuErr = JSONObject.parseObject(errorBody, TianShuErrorBody.class);
                            if (tianShuErr.getError() != null) {
                                throw new TianShuException(e, errorBody, e.code());
                            }
                        } catch (JSONException ignored) {}
                        log.error("OpenAI http call error: {}", e.message(),e);
                        throw new OpenAiHttpException(e,errorBody,e.code());
                    } else {
                        throw e;
                    }
                }
            } catch (IOException ex) {
                throw e;
            }
        }
    }

    /**
     * Calls the Open AI api and returns a Flowable of SSE for streaming
     * omitting the last message.
     *
     * @param apiCall The api call
     */
    public static Flowable<SSE> stream(Call<ResponseBody> apiCall) {
        return stream(apiCall, false);
    }

    /**
     * Calls the Open AI api and returns a Flowable of SSE for streaming.
     *
     * @param apiCall The api call
     * @param emitDone If true the last message ([DONE]) is emitted
     */
    public static Flowable<SSE> stream(Call<ResponseBody> apiCall, boolean emitDone) {
        return Flowable.create(emitter -> {
            apiCall.enqueue(new ResponseBodyCallback(emitter, emitDone));
        }, BackpressureStrategy.BUFFER);
    }

    /**
     * Calls the Open AI api and returns a Flowable of type T for streaming
     * omitting the last message.
     *
     * @param apiCall The api call
     * @param cl Class of type T to return
     */
    public <T> Flowable<T> stream(Call<ResponseBody> apiCall, Class<T> cl) {
        return stream(apiCall).map(sse -> objectMapper.readValue(sse.getData(), cl));
    }


    public <T> Flowable<T> readDataStream(Call<ResponseBody> apiCall,String key, Class<T> cl) {
        return stream(apiCall).map(sse -> {
            JSONObject object = JSON.parseObject(sse.getData());
            if (object.getJSONObject(key) == null) {
                throw new RuntimeException("模型调用-返回异常: "+ object.toJSONString());
            }
            return object.getObject(key,cl);
        });
    }

    /**
     * Shuts down the OkHttp ExecutorService.
     * The default behaviour of OkHttp's ExecutorService (ConnectionPool)
     * is to shutdown after an idle timeout of 60s.
     * Call this method to shutdown the ExecutorService immediately.
     */
    public void shutdownExecutor(){
        this.executorService.shutdown();
    }

}
