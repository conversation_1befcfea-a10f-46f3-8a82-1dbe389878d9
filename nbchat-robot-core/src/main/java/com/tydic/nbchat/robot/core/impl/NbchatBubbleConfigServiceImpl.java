package com.tydic.nbchat.robot.core.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.robot.api.NbchatBubbleConfigApi;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleConfigBO;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleConfigReqBO;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleDeleteReqBO;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleSaveReqBO;
import com.tydic.nbchat.robot.mapper.NbchatBubbleConfigMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatBubbleConfig;
import com.tydic.nbchat.robot.mapper.po.NbchatBubbleConfigSelectCondition;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class NbchatBubbleConfigServiceImpl implements NbchatBubbleConfigApi {
    @Resource
    private NbchatBubbleConfigMapper nbchatBubbleConfigMapper;

    @MethodParamVerifyEnable
    @Override
    public RspList getBubbleList(NbchatBubbleConfigReqBO req) {
        log.info("查询气泡配置:{}",req);
        NbchatBubbleConfigSelectCondition condition = new NbchatBubbleConfigSelectCondition();
        BeanUtils.copyProperties(req,condition);
        Page<NbchatBubbleConfig> page = PageHelper.startPage(req.getPage(), req.getLimit());
        nbchatBubbleConfigMapper.selectByCondition(condition);
        List<NbchatBubbleConfigBO> result = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), result, NbchatBubbleConfigBO.class);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp saveBubble(NbchatBubbleSaveReqBO saveReqBO) {
        log.info("保存气泡配置:{}",saveReqBO);
        NbchatBubbleConfig save = new NbchatBubbleConfig();
        BeanUtils.copyProperties(saveReqBO,save);
        if(saveReqBO.getBubbleId() == null){
            save.setCrtTime(new Date());
            nbchatBubbleConfigMapper.insertSelective(save);
            return BaseRspUtils.createSuccessRsp(save.getBubbleId());
        } else {
            int update = nbchatBubbleConfigMapper.update(save);
            if(update > 0){
                return BaseRspUtils.createSuccessRsp(update,"更新成功");
            }
            return BaseRspUtils.createErrorRsp("更新失败!");
        }
    }

    @Override
    public Rsp deleteBubble(NbchatBubbleDeleteReqBO deleteReqBO) {
        log.info("删除气泡:{}",deleteReqBO);
        int del = 0;
        if(deleteReqBO.getBubbleIds() != null){
            for (Integer bubbleId : deleteReqBO.getBubbleIds()) {
                del += nbchatBubbleConfigMapper.deleteById(bubbleId);
            }
        } else {
            del += nbchatBubbleConfigMapper.deleteById(deleteReqBO.getBubbleId());
        }
        if(del > 0){
            return BaseRspUtils.createSuccessRsp(del,"删除成功!");
        } else {
            return BaseRspUtils.createErrorRsp("删除失败:该配置不存在!");
        }
    }
}
