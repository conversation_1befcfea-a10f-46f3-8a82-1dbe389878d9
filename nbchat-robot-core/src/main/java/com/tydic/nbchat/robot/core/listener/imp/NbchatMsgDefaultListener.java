package com.tydic.nbchat.robot.core.listener.imp;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChoice;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.busi.NbchatRobotMsgService;
import com.tydic.nbchat.robot.core.busi.TianShuSensitiveCheckBusiService;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.ChatSensitiveHelper;
import com.tydic.nbchat.robot.core.listener.NbchatMsgListener;
import com.tydic.nbchat.robot.core.openai.exception.TianShuErrorBody;
import com.tydic.nbchat.robot.core.openai.exception.TianShuException;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.core.util.exception.SensitiveIncludeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class NbchatMsgDefaultListener implements NbchatMsgListener {

    private final Map<String,StringBuffer> messageMap = new HashMap<>();
    private final NbchatRobotMsgService nbchatRobotMsgService;
    private final ChatSensitiveHelper chatSensitiveHelper;
    private final NbchatRobotConfigProperties nbchatRobotConfigProperties;
    private final TianShuSensitiveCheckBusiService tianShuSensitiveCheckBusiService;

    private static final Logger robotMessageLog = LoggerFactory.getLogger("robotMessageLog");
    private static final Logger robotResearchLog = LoggerFactory.getLogger("robotResearchLog");


    public NbchatMsgDefaultListener(NbchatRobotMsgService nbchatRobotMsgService,
                                    ChatSensitiveHelper chatSensitiveHelper,
                                    NbchatRobotConfigProperties nbchatRobotConfigProperties,
                                    TianShuSensitiveCheckBusiService tianShuSensitiveCheckBusiService) {
        this.nbchatRobotMsgService = nbchatRobotMsgService;
        this.chatSensitiveHelper = chatSensitiveHelper;
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.tianShuSensitiveCheckBusiService = tianShuSensitiveCheckBusiService;
    }

    private String getReasoningId(RobotMessageRequest request) {
        return request.getRequestId() + "_r1";
    }

    @Override
    public RobotMsgContext onRequest(RobotMessageRequest request) {
        //保存用户消息
        //log.info("机器人消息处理-保存请求消息:{}",request);
        messageMap.put(request.getRequestId(),new StringBuffer());
        messageMap.put(getReasoningId(request),new StringBuffer());
        //敏感词过滤
        filterRequestSensitive(request);
        if (!ChatAppType.TOOLS.getCode().equals(request.getAppType())) {
            nbchatRobotMsgService.saveRequestMessage(request);
        }
        return null;
    }

    @Override
    public RobotMsgContext onResponse(RobotMessageRequest request, ChatCompletionChunk chunk) {
        StringBuffer buffer = messageMap.get(request.getRequestId());
        if(buffer != null){
            StringBuffer reasoningBuffer = messageMap.get(getReasoningId(request));
            ChatCompletionChoice choice = chunk.getChoices().get(0);
            //这里必须用isNotEmpty不能用NotBlank
            String content = choice.getMessage().getStrContent();
            if(StringUtils.isNotEmpty(content)){
                buffer.append(content);
            }
            if (reasoningBuffer != null && StringUtils.isNotBlank(choice.getMessage().getReasoning_content())) {
                reasoningBuffer.append(choice.getMessage().getReasoning_content());
            }
            RobotMsgContext context = NbchatRobotMsgBuilder.buildMessage(buffer.toString(),chunk);
            if (reasoningBuffer != null) {
                context.setReasoning(reasoningBuffer.toString());
            }
            //敏感词过滤
            filterResponseSensitive(request,context);
            context.setRequestId(request.getRequestId());
            if(StringUtils.isNotBlank(choice.getFinishReason())){
                log.info("机器人消息处理-流式输出完成:{}|{}",request.getRequestId(), choice);
                messageMap.remove(request.getRequestId());
                messageMap.remove(getReasoningId(request));
                context.setFinished(true);
                if(StringUtils.isNotBlank(context.getText())){
                    //log.info("机器人消息处理-保存响应消息[finish_reason={}]:{}|{}|{}->{}",finish,request.getRequestId(),
                    //        request.getChatModel(),request.getMajorId(), RobotCommonUtil.subLongText(request.getText()));
                    String req = JSONObject.toJSONString(request.getText(),false);
                    String rsp = JSONObject.toJSONString(context.getText(),false);
                    if (ChatAppType.DIC_CHAT.getCode().equals(request.getAppType())) {
                        //记录普通聊天日志
                        robotMessageLog.info("----------------- {} ----------------------",request.getRequestId());
                        robotMessageLog.info("[{},{},{}] | [{} -> {}]",
                                request.getRequestId(),request.getMajorId(),request.getRobotType(),
                                req,rsp);
                    } else {
                        //记录文档检索日志
                        robotResearchLog.info("----------------- {} ----------------------",request.getRequestId());
                        robotResearchLog.info("[{},{},{}] | [{} -> {}]",
                                request.getRequestId(),request.getMajorId(),request.getRobotType(),
                                req,rsp);
                    }
                    printSensitive(request,context.getText());
                    //工具检索不记录表
                    if (!ChatAppType.TOOLS.getCode().equals(request.getAppType())) {
                        nbchatRobotMsgService.saveResponseMessage(request,context);
                    }
                }
            }
            return context;
        }
        return null;
    }


    @Override
    public RobotMsgContext onResponse(RobotMessageRequest request, ChatCompletionResult result) {
        messageMap.remove(request.getRequestId());
        RobotMsgContext context = NbchatRobotMsgBuilder.buildMessage(result);
        context.setRequestId(request.getRequestId());
        //敏感词过滤
        filterResponseSensitive(request,context);
        //修复markdown格式
        //MarkdownLanguageUtil.fixMarkdown(context);
        //保存结果
        nbchatRobotMsgService.saveResponseMessage(request,context);
        return context;
    }

    @Override
    public RobotMsgContext onError(RobotMessageRequest request, Throwable e) {
        String err = e.getMessage();
        if (StringUtils.isNotBlank(err) && err.contains(NbchatRobotMsgBuilder.BILLING_CHECK_ERR)) {
            log.error("机器人消息处理-异常[账户余额不足]:{}|{}", request, err);
            //robotAiHelperFactory.removeService(request);
        }
        if ("fastchat_tianshu".equals(request.getRobotType()) && e instanceof TianShuException) {
            //天枢大模型异常
            TianShuException tianShuException = (TianShuException) e;
            TianShuErrorBody errorBody = tianShuException.parseErrorBody();
            log.error("机器人消息处理-天枢大模型敏感词异常:{}|{}", request, errorBody);
            if (Objects.nonNull(errorBody) && Objects.nonNull(errorBody.getError())) {
                //触发敏感词，记录触发次数，达到限制进行封号处理
                tianShuSensitiveCheckBusiService.incrSensitiveCount(request.getUserId());
                return NbchatRobotMsgBuilder.buildContext(request.getRequestId(), errorBody.getError().getMessage());
            }
        }
        return NbchatRobotMsgBuilder.buildErrorContext(request, nbchatRobotConfigProperties);
    }


    private void filterRequestSensitive(RobotMessageRequest request) {
        //替换敏感词汇
        boolean check = chatSensitiveHelper.include(request.getTenantCode(),request.getText());
        if(check){
            messageMap.remove(request.getRequestId());
            //提取敏感词
            printSensitive(request,request.getText());
            throw new SensitiveIncludeException("请求包含非法敏感词:" + request.getText());
        }
    }

    private void filterResponseSensitive(RobotMessageRequest request,RobotMsgContext context){
        //替换敏感词汇
        if(StringUtils.isNotBlank(context.getText())){
            String content = chatSensitiveHelper.replace(request.getTenantCode(),context.getText());
            context.setText(content);
        }
    }


    private void printSensitive(RobotMessageRequest request,String context){
        List<String> words = chatSensitiveHelper.wordList(request.getTenantCode(),context);
        if(words != null && !words.isEmpty()){
            log.info("提取敏感词:{}|{}",request.getRequestId(),words);
        }
    }

}
