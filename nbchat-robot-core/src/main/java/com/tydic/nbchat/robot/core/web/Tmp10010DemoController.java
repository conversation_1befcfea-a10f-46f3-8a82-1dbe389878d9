package com.tydic.nbchat.robot.core.web;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchPartResult;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChoice;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.KbResearchHelper;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.SystemPresetPromptHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.mapper.NbchatResearchFilesMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchFiles;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/robot/10010/")
public class Tmp10010DemoController {

    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private RobotAiHelperFactory robotAiHelperFactory;
    @Autowired
    private KbResearchHelper kbResearchHelper;
    @Autowired
    private SystemPresetPromptHelper systemPresetPromptHelper;
    @Autowired
    private NbchatResearchFilesMapper nbchatResearchFilesMapper;
    @Autowired
    private NbchatRobotConfigProperties nbchatRobotConfigProperties;

    private final String REDIS_TMP_TEXT_KEY = "robot:10010:tmp_text:";
    private final String REDIS_TMP_DOCS_KEY = "robot:10010:tmp_docs:";


    @PostMapping("/text/save")
    public Rsp<String> textSave(@RequestBody RobotMessageRequest request) {
        // 临时保存文档内容到redis
        String textId = NiccCommonUtil.createMsgId();
        String key = REDIS_TMP_TEXT_KEY + textId;
        if (StringUtils.isBlank(request.getText())) {
            return BaseRspUtils.createErrorRsp("文本内容不能为空");
        }
        redisHelper.set(key, request.getText(), 3600);
        return BaseRspUtils.createSuccessRsp(textId);
    }

    @PostMapping("/text/get")
    public Rsp<String> textGet(@RequestBody RobotMessageRequest request) {
        // 临时保存文档内容到redis
        String key = REDIS_TMP_TEXT_KEY + request.getRequestId();
        String text = (String) redisHelper.get(key);
        return BaseRspUtils.createSuccessRsp(text);
    }


    @PostMapping("/research/docs")
    public Rsp getRelDocs(@RequestBody RobotMessageRequest request) {
        if (StringUtils.isBlank(request.getRequestId())) {
            return BaseRspUtils.createErrorRsp("requestId不能为空");
        }
        String key = REDIS_TMP_DOCS_KEY + request.getRequestId();
        List<DocInfo> docInfos = (List<DocInfo>) redisHelper.get(key);
        return BaseRspUtils.createSuccessRsp(docInfos);
    }


    @Deprecated
    @PostMapping("/research")
    public Object research(@RequestBody RobotMessageRequest request) {
        /***
         * 1. 查询知识库相关文档，缓存相关文档
         * 2. 组合提示词
         * 3. 调用模型流式返回数据
         */
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(RobotType.ZP_AI.getCode());
        }
        String requestId = NiccCommonUtil.createImUserId(true);
        request.setRequestId(requestId);
        FileResearchSearchRequest searchRequest = new FileResearchSearchRequest();
        searchRequest.setUserId(request.getUserId());
        searchRequest.setTenantCode(request.getTenantCode());
        searchRequest.setMajorId(request.getMajorId());
        searchRequest.setText(request.getText());
        searchRequest.setTopK(2);
        RspList<FileResearchSearchPartResult> searchResult = kbResearchHelper.searchResult(searchRequest);
        log.info("演示-文档查询结果:{}", searchResult);
        StringBuffer promptBuffer = new StringBuffer();
        if (searchResult.getCount() > 0) {
            List<FileResearchSearchPartResult> result = searchResult.getRows();
            List<DocInfo> docInfos = Lists.newArrayList();
            for (FileResearchSearchPartResult partResult : result) {
                NbchatResearchFiles files = nbchatResearchFilesMapper.selectByPrimaryKey(partResult.getFileId());
                String accessUrl = "";
                if (files != null) {
                    accessUrl = files.getAccessUrl();
                }
                DocInfo docInfo = new DocInfo();
                docInfo.setFileName(partResult.getFileName());
                docInfo.setFileId(partResult.getFileId());
                docInfo.setAccessUrl(accessUrl);
                List<PartInfo> parts = Lists.newArrayList();
                PartInfo partInfo = new PartInfo();
                partInfo.setPartId(partResult.getPartId());
                partInfo.setContent(partResult.getContent());
                parts.add(partInfo);
                docInfo.setParts(parts);
                docInfos.add(docInfo);
                promptBuffer.append(partResult.getContent()).append("\n");
            }
            String key = REDIS_TMP_DOCS_KEY + requestId;
            log.info("演示-缓存文档:{}-{}", key, docInfos);
            redisHelper.set(key, docInfos, 300);
            //构造提示词
            List<String> promts = Lists.newArrayList();
            promts.add(promptBuffer.toString());
            if (request.getPresetPrompts() != null) {
                promts.addAll(request.getPresetPrompts());
            }
            String promptText = systemPresetPromptHelper.buildPromptMessage("10010_doc_research", promts);
            log.info("演示-构造提示词:{}", promptText);

            final ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), promptText);
            final List<ChatMessage> inputList = Collections.singletonList(userMessage);
            ChatMessageBuilder messageBuilder = ChatMessageBuilder.builder().messages(inputList).build();
            AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                    messageRequest(request).messageBuilder(messageBuilder).build();

            if (request.isStream()) {
                return streamChat(request, messageBuilder);
            } else {
                ChatCompletionResult result1 = robotAiHelperFactory.chat(aiRobotChatRequest);
                RobotMsgContext context = NbchatRobotMsgBuilder.buildMessage(result1);
                context.setRequestId(request.getRequestId());
                return BaseRspUtils.createSuccessRsp(context);
            }
        }
        return NbchatRobotMsgBuilder.buildResearchUnknow(request, nbchatRobotConfigProperties);
    }


    private void processResearch(RobotMessageRequest request,
                                 ChatMessageBuilder messageBuilder,
                                 RobotProcessCallback callback) {
        AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                messageRequest(request).messageBuilder(messageBuilder).build();
        log.info("演示-机器人消息处理-请求:{}", aiRobotChatRequest);
        StringBuffer cacheBuffer = new StringBuffer();
        robotAiHelperFactory.streamChat(aiRobotChatRequest).doOnError(e -> {
            String err = e.getMessage();
            log.error("演示-机器人消息处理-异常:{}|{}", request, err);
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildErrorContext(request, nbchatRobotConfigProperties);
            callback.onMessage(warnContext);
        }).blockingForEach(obj -> {
            //消息转换
            robotAiHelperFactory.convertChuckMessage(aiRobotChatRequest, obj);
            ChatCompletionChoice choice = obj.getChoices().get(0);
            String content = choice.getMessage().getStrContent();
            if (StringUtils.isNotBlank(content)) {
                cacheBuffer.append(content);
            }
            RobotMsgContext context = NbchatRobotMsgBuilder.buildMessage(cacheBuffer.toString(), obj);
            context.setRequestId(request.getRequestId());
            callback.onMessage(context);
        });
    }


    private StreamingResponseBody streamChat(@RequestBody RobotMessageRequest request,
                                             ChatMessageBuilder messageBuilder) {

        return outputStream -> processResearch(request, messageBuilder, new RobotProcessCallback() {
            @Override
            public void onMessage(RobotMsgContext context) {
                try {
                    byte[] obj = JSONObject.toJSONBytes(context);
                    outputStream.write(NbchatRobotMsgBuilder.mergeEnter(obj));
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("机器人消息处理-数据流推送异常:{}", context, e);
                }
            }
            @Override
            public void onError(RobotMessageRequest request, Throwable e) {
                //重试
                log.error("机器人消息处理-消息处理异常:{}", request, e);
            }
        });
    }


    @Data
    static
    class DocInfo {
        private String fileName;
        private String fileId;
        private String accessUrl;
        private List<PartInfo> parts;
    }

    @Data
    static
    class PartInfo {
        private String partId;
        private String content;
    }

}
