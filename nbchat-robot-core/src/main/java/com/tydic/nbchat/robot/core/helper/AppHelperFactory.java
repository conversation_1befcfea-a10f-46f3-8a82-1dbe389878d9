package com.tydic.nbchat.robot.core.helper;

import com.tydic.nbchat.robot.core.helper.api.AppHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class AppHelperFactory {
    private HashMap<String, AppHelper> appMap = new HashMap<>();


    public AppHelperFactory(List<AppHelper> appHelpers) {
        this.register(appHelpers);
    }



    public void register(List<AppHelper> appHelpers) {
        for (AppHelper appHelper : appHelpers) {
            appMap.put(appHelper.type(), appHelper);
        }
    }

    public AppHelper getAppHelper(String type) {
        AppHelper appHelper = appMap.get(type);
        if (appHelper == null) {
            log.info("未找到app类型：{}",type);
            return new DefaultAppHelper();
        }
        return appHelper;

    }

}
