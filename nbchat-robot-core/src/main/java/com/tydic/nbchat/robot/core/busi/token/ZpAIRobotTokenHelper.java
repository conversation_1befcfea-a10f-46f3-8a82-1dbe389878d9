package com.tydic.nbchat.robot.core.busi.token;

import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenConfig;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.config.NbchatZpRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nbchat.robot.core.util.RobotTokenConfigParseUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class ZpAIRobotTokenHelper implements AiTokenHelper {

    private final NbchatZpRobotConfigProperties nbchatZpRobotConfigProperties;
    private final RobotConfigBusiService robotConfigBusiService;
    private final RobotAiHelperFactory robotAiHelperFactory;
    private final RedisHelper redisHelper;

    public ZpAIRobotTokenHelper(NbchatZpRobotConfigProperties nbchatZpRobotConfigProperties,
                                RobotConfigBusiService robotConfigBusiService,
                                RobotAiHelperFactory robotAiHelperFactory,
                                RedisHelper redisHelper) {
        this.nbchatZpRobotConfigProperties = nbchatZpRobotConfigProperties;
        this.robotConfigBusiService = robotConfigBusiService;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.redisHelper = redisHelper;
    }

    @Override
    public String robot() {
        return RobotType.ZP_AI.getCode();
    }

    @Override
    public void freshByRobotType(String robotType) {
        if (!nbchatZpRobotConfigProperties.getTokenTimerEnable()) {
            return;
        }
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        int cacheTime = nbchatZpRobotConfigProperties.getExpiresIn() - 600;
        for (RobotToken row : tokens) {
            String key = tokenCacheKey(row.getTokenId());
            boolean exp = redisHelper.hasKey(key);
            if (!exp) {
                Optional<RobotTokenConfig> config = RobotTokenConfigParseUtil.parse(row.getConfig());
                String access_token;
                if (config.isPresent()) {
                    access_token = generateToken(
                            config.get().getAk(),
                            config.get().getExpireTime());
                    cacheTime = config.get().getCacheTime();
                    log.info("刷新robot-token[{}]-获取新token: config = {},new_token = {}", row.getRobotType(), config.get(), access_token);
                } else {
                    if (!nbchatZpRobotConfigProperties.isValid()) {
                        log.warn("刷新robot-token[{}]-参数异常:{}", robot(), nbchatZpRobotConfigProperties);
                        return;
                    }
                    access_token = generateToken(
                            nbchatZpRobotConfigProperties.getAccessKey(),
                            nbchatZpRobotConfigProperties.getExpiresIn());
                    log.info("刷新robot-token[{}]-获取新token:{}", robot(), access_token);
                }
                Rsp rsp = robotConfigBusiService.updateTokenKey(row.getTokenId(), access_token);
                //重载
                boolean flash = robotAiHelperFactory.reloadHelper(row.getRobotType());
                log.info("刷新robot-token[{}]-重载服务:{}|{}", row.getRobotType(), rsp, flash);
                //提前10分钟失效
                redisHelper.set(key, access_token, cacheTime);
            }
        }
    }

    @Override
    public void freshToken() {
        freshByRobotType(robot());
    }

    /**
     * 刷新token
     *
     * @param apikey
     * @param expSeconds
     * @return
     */
    public String generateToken(String apikey, int expSeconds) {
        try {
            String[] parts = apikey.split("\\.");
            String id = parts[0];
            String secret = parts[1];
            long currentTimeMillis = System.currentTimeMillis();
            long expirationTimeMillis = currentTimeMillis + expSeconds * 1000;
            Map header = JSONObject.parseObject("{\"alg\": \"HS256\", \"sign_type\": \"SIGN\"}").toJavaObject(Map.class);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            String token = JWT.create().withIssuer(id).
                    withIssuedAt(new Date(System.currentTimeMillis())).
                    withHeader(header).
                    withClaim("api_key", id).
                    withClaim("exp", expirationTimeMillis).
                    withClaim("timestamp", currentTimeMillis).
                    withExpiresAt(Instant.ofEpochMilli(expirationTimeMillis)).sign(algorithm);
            return token;
        } catch (Exception e) {
            throw new RuntimeException("刷新robot-token[" + robot() + "]-异常:", e);
        }
    }


    @Override
    public void removeCacheKey() {
        removeCacheKey(robot());
    }

    @Override
    public void removeCacheKey(String robotType) {
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        for (RobotToken row : tokens) {
            String key = tokenCacheKey(row.getTokenId());
            log.info("刷新robot-token[{}]-移除tokenKey:{}", robot(), key);
            redisHelper.del(key);
        }
    }

}
