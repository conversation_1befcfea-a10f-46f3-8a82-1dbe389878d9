package com.tydic.nbchat.robot.core.impl;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.AiAppApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.AiAppRequest;
import com.tydic.nbchat.robot.api.bo.RequestOptions;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.SystemPresetPromptHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.listener.NbchatMsgListener;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.mapper.AppConfigMapper;
import com.tydic.nbchat.robot.mapper.NbchatRobotSessionMsgMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AiAppServiceImpl implements AiAppApi {

    @Resource
    RobotAiHelperFactory robotAiHelperFactory;
    @Resource
    NbchatMsgListener nbchatMsgListener;
    @Resource
    NbchatRobotConfigProperties nbchatRobotConfigProperties;
    @Resource
    SystemPresetPromptHelper systemPresetPromptHelper;

    @Resource
    NbchatRobotSessionMsgMapper nbchatRobotSessionMsgMapper;
    @Resource
    AppConfigMapper appConfigMapper;



    @Override
    public void sendMessage(AiAppRequest request, RobotProcessCallback callback) {
        //TODO 设置系统消息，替换提示词消息，发送用户消息
        nbchatMsgListener.onRequest(request);

        ChatMessageBuilder messageBuilder = parseChatMessage(request);
        AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                robotTag(request.getRobotTag()).
                messageRequest(request).messageBuilder(messageBuilder).build();
        try {
            robotAiHelperFactory.streamChat(aiRobotChatRequest).doOnError(e -> {
                //TODO 异常处理

            }).blockingForEach(obj -> {
                //消息转换
                robotAiHelperFactory.convertChuckMessage(aiRobotChatRequest, obj);
                RobotMsgContext context = nbchatMsgListener.onResponse(request, obj);
                Optional.ofNullable(context).ifPresent(callback::onMessage);
            });
        } catch (Exception e) {
            log.error("机器人消息处理-接口调用异常:{}",request,e);
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildErrorContext(request, nbchatRobotConfigProperties);
            callback.onMessage(warnContext);
        }
    }

    private ChatMessageBuilder parseChatMessage(AiAppRequest request){
        final List<ChatMessage> messages = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getRequestOptions())) {
            try {
                int assistantLimit = 999;
                RequestOptions requestOptions = JSONObject.parseObject(request.getRequestOptions(), RequestOptions.class);
                List<NbchatSessionMsg> sessionMsgs = nbchatRobotSessionMsgMapper.selectLastContents(
                        request.getSessionId(),
                        requestOptions.getParentMessageId(),
                        assistantLimit);
                if (CollectionUtils.isNotEmpty(sessionMsgs)) {
                    sessionMsgs = sessionMsgs.stream().sorted(Comparator.comparing(NbchatSessionMsg::getDateTime)).collect(Collectors.toList());
                    for (NbchatSessionMsg sessionMsg : sessionMsgs) {
                        ChatMessage msg;
                        if (sessionMsg.getUserType().equals("1")) {
                            msg = new ChatMessage(ChatMessageRole.USER.value(), sessionMsg.getText());
                        } else {
                            msg = new ChatMessage(ChatMessageRole.ASSISTANT.value(), sessionMsg.getText());
                        }
                        messages.add(msg);
                    }
                }
            } catch (Exception e) {
                log.error("机器人消息处理-加载上次提问异常:{}", e, request);
            }
        }
        ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), request.getText());
        messages.add(userMessage);

        //构建系统消息，设定ai-app系统角色
        String systemMsg = this.buildSystemMsg(request);
        if (StringUtils.isNotEmpty(systemMsg)) {
            ChatMessage systemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemMsg);
            //系统角色插到第一个
            messages.add(0, systemMessage);
        }
        return ChatMessageBuilder.builder().success(true).messages(messages).build();
    }

    /**
     * {
     *     "examName": "考试名称",
     *     "examScene": "场景描述",
     *     "coreIssues": "我是核心问题",
     *     "openingRemarks": "我是开场白",
     *     "referenceAnswer": "核心问题答案",
     *     "examQuestionsNum": 5,
     *     "examScoringDimensionsStr": "沟通表达（满分10分）\n人格魅力（满分10分）\n人气值（满分10分）\n"
     * }
     */

    public String buildSystemMsg(AiAppRequest request) {
        String appId = request.getAppId();
        String config = appConfigMapper.queryById(appId);
        if (StringUtils.isEmpty(config)) {
            return config;
        }
        JSONObject jsonObject = JSONObject.parseObject(config);
        List<String> args = new ArrayList<>();
        args.add(jsonObject.getString("examScene"));//场景描述
        args.add(jsonObject.getString("examQuestionsNum"));//题目数量
        args.add(jsonObject.getString("examScoringDimensionsStr"));//评分维度
        args.add(jsonObject.getString("coreIssues"));//核心问题
        args.add(jsonObject.getString("referenceAnswer"));//答案参考
        args.add(jsonObject.getString("examScoringResultStr"));//输出格式

        return systemPresetPromptHelper.buildPromptMessage(request.getPresetId(), args);
    }






}
