package com.tydic.nbchat.robot.core.listener.imp;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.robot.core.listener.NbchatToolsListener;
import com.tydic.nbchat.robot.core.openai.exception.OpenAiHttpException;
import com.tydic.nbchat.robot.core.openai.exception.TianShuErrorBody;
import com.tydic.nbchat.robot.core.openai.exception.TianShuException;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class NbchatToolsDefaultListener implements NbchatToolsListener {


    private final KKMqProducerHelper kkMqProducerHelper;

    public NbchatToolsDefaultListener(KKMqProducerHelper kkMqProducerHelper) {
        this.kkMqProducerHelper = kkMqProducerHelper;
    }

    @Override
    public void onRequest(RobotPromptMessageRequest promptMessageRequest, RobotMessageRequest request) {
    }

    @Override
    public void onResponse(RobotPromptMessageRequest promptMessageRequest, RobotToolsChatResponse response) {
    }

    @Override
    public RobotToolsChatResponse onError(RobotPromptMessageRequest request, String reason, Exception e) {
        RobotToolsChatResponse response = RobotToolsChatResponse.builder().
                robotType(request.getRobotType()).
                content("模型调用异常").
                presetId(request.getPresetId()).build();
        if (e != null) {
            if (e instanceof TianShuException) {
                TianShuException err = (TianShuException) e;
                //天枢大模型异常
                TianShuErrorBody errorBody = err.parseErrorBody();
                if (Objects.nonNull(errorBody) && Objects.nonNull(errorBody.getError())) {
                    response.setContent(errorBody.getError().getMessage());
                } else {
                    response.setContent(e.getMessage());
                }
            } else if (e instanceof OpenAiHttpException){
                response.setContent(e.getMessage());
            }
        }
        return response;
    }
}
