package com.tydic.nbchat.robot.core.web;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.AiAppApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.AiAppRequest;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;

@Slf4j
@RestController

@RequestMapping("robot/app")
public class RobotAiAppController {

    final private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    final private AiAppApi aiAppApi;

    public RobotAiAppController(NbchatRobotConfigProperties nbchatRobotConfigProperties, AiAppApi aiAppApi) {
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.aiAppApi = aiAppApi;
    }

    /**
     * ai-app 聊天接口
     *
     * @param request
     * @return
     */
    @PostMapping(value = "chat", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ResponseBody
    public StreamingResponseBody chatProcessBytes(@RequestBody AiAppRequest request) {
        return getStreamingResponseBody(request);
    }

    @NotNull
    private StreamingResponseBody getStreamingResponseBody(@RequestBody AiAppRequest request) {
        Rsp rsp = checkChatRequest(request);
        if (rsp != null) {
            Rsp finalRsp = rsp;
            return outputStream -> {
                outputStream.write(JSONObject.toJSONBytes(finalRsp));
                outputStream.flush();
            };
        }

        return outputStream -> aiAppApi.sendMessage(request, new RobotProcessCallback() {
            @Override
            public void onMessage(RobotMsgContext context) {
                try {
                    log.trace("机器人消息处理:{}->{}", request.getRequestId(), context.getText());
                    byte[] obj = JSONObject.toJSONBytes(context);
                    outputStream.write(NbchatRobotMsgBuilder.mergeEnter(obj));
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("机器人消息处理-数据流推送异常:{}", context, e);
                }
            }

            @Override
            public void onError(RobotMessageRequest request, Throwable e) {
                //重试
                log.error("机器人消息处理-消息处理异常:{}", request, e);
            }
        });
    }


    private Rsp checkChatRequest(RobotMessageRequest request) {
        Rsp rsp = null;
        String requestId = NbchatRobotMsgBuilder.createRequestId();
        request.setRequestId(requestId);
        if (StringUtils.isAnyBlank(request.getUserId(), request.getPresetId())) {
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(nbchatRobotConfigProperties.getRobotTypeDefault());
        }
        return rsp;
    }

    private Rsp checkAppChatRequest(RobotMessageRequest request) {
        Rsp rsp = null;
        if (StringUtils.isAnyBlank(request.getUserId(), request.getText())) {
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        if (StringUtils.isAllBlank(request.getSessionId(), request.getMajorId())) {
            rsp = BaseRspUtils.createErrorRsp("参数异常！");
        }
        //计算sessionId
        if (StringUtils.isBlank(request.getSessionId())) {
            request.setSessionId(IdWorker.nextAutoIdStr());
        }
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(nbchatRobotConfigProperties.getRobotTypeDefault());
        }
        return rsp;
    }
}
