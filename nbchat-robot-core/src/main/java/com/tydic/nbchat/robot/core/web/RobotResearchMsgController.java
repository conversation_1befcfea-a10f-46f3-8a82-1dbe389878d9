package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbchatResearchAppMsgApi;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMsgQueryRequest;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Deprecated
@Slf4j
@RestController
@RequestMapping("/robot/research")
public class RobotResearchMsgController {

    final private NbchatResearchAppMsgApi nbchatResearchAppMsgApi;

    public RobotResearchMsgController(NbchatResearchAppMsgApi nbchatResearchAppMsgApi) {
        this.nbchatResearchAppMsgApi = nbchatResearchAppMsgApi;
    }


    @Deprecated
    @PostMapping(value = "/messages")
    public RspList getResearchMessages(@RequestBody FileResearchMsgQueryRequest request) {
        if (StringUtils.isAllBlank(request.getSessionId(), request.getMajorId())) {
            return BaseRspUtils.createErrorRspList("参数异常!");
        }
        //计算sessionId
        if (StringUtils.isNotBlank(request.getMajorId()) && StringUtils.isBlank(request.getSessionId())) {
            request.setSessionId(NbchatRobotMsgBuilder.getMajorSession(request.getUserId(), request.getMajorId()));
        }
        return nbchatResearchAppMsgApi.getResearchMessages(request);
    }

    @Deprecated
    @PostMapping(value = "/message/delete")
    public Rsp deleteMessage(@RequestBody FileResearchMsgQueryRequest request) {
        return nbchatResearchAppMsgApi.deleteMessage(request);
    }



}
