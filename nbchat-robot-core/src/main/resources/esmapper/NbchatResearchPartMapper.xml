<properties>

    <property name="searchByCondition">
        <![CDATA[{
        "query": {
            "script_score": {
               "min_score": 1.7,
               "query": {
                  "bool": {
                    "must":[
                        {
                            "bool": {
                                "should": [
                                   {
                                        "term": {
                                            "userId": #[userId]
                                        }
                                    },
                                    {
                                        "term": {
                                            "docSource": 0
                                        }
                                    }
                                ]
                            }
                        },
                       #if($category && $category != '')
                        {
                          "term": {
                            "category": #[category]
                          }
                        },
                      #end
                      #if($fileId && $fileId != '')
                       {
                        "term": {
                          "fileId": #[fileId]
                        }
                      },
                      #end
                      {
                          "range": {
                            "parseTime": {
                              "gte": #[startTime],
                              "lte": #[endTime],
                              "format": "yyyy-MM-dd'T'HH:mm:ss.SSS"
                            }
                          }
                      }
                    ]
                }
            }
             ,
            "script": {
                "source": "cosineSimilarity(params.query_vector, 'partVector') +1.0",
                "params": {
                  "query_vector": #[partVector]
                 }
            }
          }
        }
        ,
        "sort": [
             {
              "_score": {
                 "order": "desc"
               }
             }
        ],
        "_source": ["partId","fileId","fileName","tenantCode","userId","category","parseTime","pageNum","content","partIndex","docSource"],
        "size":#[pageSize],
        "explain":false
    }]]>
    </property>

    <!--索引结构 -->
    <property name="createIndex">
        <![CDATA[
            {
                "settings": {
                    "number_of_shards": 3,
                    "number_of_replicas": 1,
                    "index.refresh_interval": "5s"
                },
                "mappings": {
                    "properties": {
                        "partId": {  ## 文档id+段落序号 生成
                            "type": "keyword"
                        },
                        "fileId": { ## 关联文件
                            "type": "keyword"
                        },
                        "tenantCode": { ## 租户编码
                            "type": "keyword"
                        },
                        "userId": { ## 用户id
                            "type": "keyword"
                        },
                        "category": { ## 所属目录分类
                            "type": "keyword"
                        },
                        "parseTime": {  ## 创建时间
                            "type": "date",
                            "format":"yyyy-MM-dd HH:mm:ss.SSS||yyyy-MM-dd'T'HH:mm:ss.SSS"
                        },
                        "pageNum":{  ## 文档片段所在页码
                            "type": "short"
                        },
                        "content":{  ## 文档片段内容
                            "type": "text"
                        },
                        "partIndex":{  ## 段落序号
                            "type": "short"
                        },
                        "docSource":{  ## 0 系统自定 1 用户导入
                            "type": "short"
                        },
                        "partVector": {
                            "type": "dense_vector",
                            "dims": 1536
                        }
                    }
                }
            }
        ]]>
    </property>


</properties>