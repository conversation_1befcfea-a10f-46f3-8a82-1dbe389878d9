<properties>


    <property name="deleteByQuery">
        <![CDATA[
        {
            "query": {
                "bool": {
                    "must": [
                     #if($majorId && $majorId != '')
                        {
                            "term": {
                                "majorId": #[majorId]
                            }
                        },
                     #end
                     #if($fileId && $fileId != '')
                        {
                            "term": {
                                "fileId": #[fileId]
                            }
                        },
                     #end
                        {
                            "term": {
                                "docSource": 2
                            }
                        }
                    ]
                }
            }
        }
        ]]>
    </property>

    <property name="matchContent">
        <![CDATA[
            {
                "min_score": 30,
                "query": {
                    "bool": {
                        "must": [
                        #if($fileId && $fileId != '')
                           {
                            "term": {
                              "fileId": #[fileId]
                            }
                          },
                         #end
                           {
                                "term": {
                                    "majorId": #[majorId]
                                }
                            },
                            {
                                "match": {
                                    "content": {
                                        "query": #[text],
                                        "boost": 2
                                    }
                                }
                            }
                        ]
                    }
                },
                "sort": [{
                    "_score": {
                        "order": "desc"
                    }
                }],
                "_source": [
                    "partId",
                    "fileId",
                    "fileName",
                    "tenantCode",
                    "userId",
                    "majorId",
                    "category",
                    "parseTime",
                    "pageNum",
                    "content",
                    "partIndex",
                    "docSource"
                ],
                "size": 10,
                "explain": false
            }
       ]]>
    </property>

    <property name="searchByCondition">
        <![CDATA[{
        "query": {
            "script_score": {
            #if($chatgpt && $chatgpt != '')
              "min_score": 1.7,
             #else
              "min_score": 1.6,
             #end
               "query": {
                  "bool": {
                    "must":[
                       #if($category && $category != '')
                        {
                          "term": {
                            "category": #[category]
                          }
                        },
                      #end
                      #if($fileId && $fileId != '')
                       {
                        "term": {
                          "fileId": #[fileId]
                        }
                      },
                      #end
                      {
                        "term": {
                          "majorId": #[majorId]
                        }
                      }
                    ]
                }
            }
             ,
            "script": {
                "source": "cosineSimilarity(params.query_vector, 'partVector') +1.0",
                "params": {
                  "query_vector": #[partVector]
                 }
            }
          }
        }
        ,
        "sort": [
             {
              "_score": {
                 "order": "desc"
               }
             }
        ],
        "_source": ["partId","fileId","fileName","tenantCode","userId","majorId","category","parseTime","pageNum","content","partIndex","docSource"],
        "size":#[pageSize],
        "explain":false
    }]]>
    </property>

    <!--索引结构 -->
    <property name="createIndex">
        <![CDATA[
            {
                "settings": {
                    "number_of_shards": 3,
                    "number_of_replicas": 1,
                    "index.refresh_interval": "5s"
                },
                "mappings": {
                    "properties": {
                        "partId": {  ## 文档id+段落序号 生成
                            "type": "keyword"
                        },
                        "fileId": { ## 关联文件
                            "type": "keyword"
                        },
                        "majorId": { ## 所属领域
                            "type": "keyword"
                        },
                        "tenantCode": { ## 租户编码
                            "type": "keyword"
                        },
                        "userId": { ## 用户id
                            "type": "keyword"
                        },
                        "category": { ## 所属目录分类
                            "type": "keyword"
                        },
                        "parseTime": {  ## 创建时间
                            "type": "date",
                            "format":"yyyy-MM-dd HH:mm:ss.SSS||yyyy-MM-dd'T'HH:mm:ss.SSS"
                        },
                        "pageNum":{  ## 文档片段所在页码
                            "type": "short"
                        },
                        "content":{  ## 文档片段内容
                            "type": "text"
                        },
                        "partIndex":{  ## 段落序号
                            "type": "short"
                        },
                        "docSource":{  ## 2 专属机器人
                            "type": "short"
                        },
                        "partVector": {
                            "type": "dense_vector",
                            "dims": #[dims]
                        }
                    }
                }
            }
        ]]>
    </property>


</properties>