<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>com.tydic.nbchat</groupId>
		<artifactId>nbchat-robot</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>nbchat-robot-core</artifactId>
	<description>核心服务</description>
	<properties>
		<rxjava2.version>2.7.2</rxjava2.version>
	</properties>

	<dependencies>
		<!--项目依赖开始 -->
		<dependency>
			<groupId>com.tydic.nicc</groupId>
			<artifactId>nicc-common-tools</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-robot-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-admin-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-robot-mapper</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>adapter-rxjava2</artifactId>
			<version>${rxjava2.version}</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>converter-jackson</artifactId>
			<version>${rxjava2.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox-tools</artifactId>
		</dependency>
    </dependencies>

</project>
