package com.tydic.nbchat.openapi.api.bo.tdh;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TdhTrainResult implements Serializable {
    private String progressing;

    private String tdhId;
    private String status;
    private String reason;
    //封面
    private String tdhImg;
    //预览视频地址
    private String videoUrl;

    public TdhTrainResult(String reason){
        this.reason = reason;
    }

    public boolean isSuccess() {
        return "done".equals(status);
    }
}
