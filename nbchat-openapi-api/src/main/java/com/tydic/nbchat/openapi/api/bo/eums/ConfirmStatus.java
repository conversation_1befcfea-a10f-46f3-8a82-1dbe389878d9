package com.tydic.nbchat.openapi.api.bo.eums;



public enum ConfirmStatus {

    CANCEL("0", "取消"),
    OK("1", "确认");

    private String code;
    private String name;

    public Short getShotCode() {
        return Short.valueOf(code);
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ConfirmStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ConfirmStatus field : ConfirmStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
