package com.tydic.nbchat.openapi.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
public class TdhCustomizeConfirmRequest extends BaseInfo {
    @NotEmpty
    private String userId;
    @NotEmpty
    private String tenantCode;
    @Size(max = 32)
    @NotEmpty(message = "orderId不能为空")
    private String orderId;
    //确认状态 0 作废 ，1 使用
    @NotEmpty
    @Size(max = 1)
    private String status;
}
