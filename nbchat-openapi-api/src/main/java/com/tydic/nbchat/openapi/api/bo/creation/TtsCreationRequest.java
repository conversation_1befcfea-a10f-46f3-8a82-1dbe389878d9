package com.tydic.nbchat.openapi.api.bo.creation;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
public class TtsCreationRequest extends BaseInfo {
    @NotEmpty
    private String userId;
    @NotEmpty(message = "请输入音色id")
    private String voiceId;
    @NotEmpty(message = "text不能为空")
    @Size(max = 5000, message = "text长度不能超过5000")
    private String text;
    //语音类型
    private String anchorType;
    //语速，范围是-500~500，可选，默认是0
    @Min(value = -500, message = "语调范围是-500~500")
    @Max(value = 500, message = "语调范围是-500~500")
    private Integer speechRate;
    //音量，范围是0~100，可选，默认50
    @Min(value = 0, message = "音量范围是0~100")
    @Max(value = 100, message = "音量范围是0~100")
    private Integer volume;
}
