package com.tydic.nbchat.openapi.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OapiTdhTrainOrderBO implements Serializable {
    private String progressing;

    private String orderId;

    private String userId;

    private String tenantCode;

    private String objType;

    private String objId;

    private String objUrl;

    private String objName;

    private String gender;

    private String isCutout;

    private String sourceUrl;

    private String voiceUrl;

    private String demoUrl;

    private Date orderTime;

    private String payType;

    private Integer payPrice;

    private String trainTaskId;
    private String trainStatus;
    private String trainError;

    private String orderStatus;
    private String orderDesc;

    private Date updateTime;

    //获取英文gender
    public String getGenderEn() {
        if ("男".equals(gender)) {
            return "man";
        } else {
            return "woman";
        }
    }
}
