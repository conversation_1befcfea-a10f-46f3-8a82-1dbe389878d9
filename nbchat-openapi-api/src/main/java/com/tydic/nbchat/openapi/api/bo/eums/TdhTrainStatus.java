
package com.tydic.nbchat.openapi.api.bo.eums;


public enum TdhTrainStatus {

    //0-未训练 1-训练中 2-训练完成 3-训练异常'
    NOT_TRAIN("0", "未训练"),
    TRAINING("1", "训练中"),
    TRAINED("2", "训练完成"),
    TRAIN_ERROR("3", "训练异常");

    private String code;
    private String name;

    public Short getShotCode() {
        return Short.valueOf(code);
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private TdhTrainStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (TdhTrainStatus field : TdhTrainStatus.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }


}
