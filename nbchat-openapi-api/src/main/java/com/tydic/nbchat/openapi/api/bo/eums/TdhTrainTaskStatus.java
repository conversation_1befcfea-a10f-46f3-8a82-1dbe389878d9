package com.tydic.nbchat.openapi.api.bo.eums;



public enum TdhTrainTaskStatus {

    //定制状态： 0-审核中 1-审核通过（待确认） 2-确认定制  3-审核不通过 4-取消定制 5-订单超时关闭
    AUDITING("0", "审核中"),
    AUDIT_PASS("1", "审核通过"),
    CONFIRMED("2", "确认定制"),
    AUDIT_FAIL("3", "审核不通过"),
    CANCELED("4", "取消定制"),
    ORDER_CLOSE("5", "订单超时关闭");

    private String code;
    private String name;

    public Short getShotCode() {
        return Short.valueOf(code);
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private TdhTrainTaskStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (TdhTrainTaskStatus field : TdhTrainTaskStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
