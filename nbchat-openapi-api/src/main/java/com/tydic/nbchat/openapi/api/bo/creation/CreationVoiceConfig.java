package com.tydic.nbchat.openapi.api.bo.creation;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

@Data
public class CreationVoiceConfig {

    @Valid
    @NotEmpty(message = "音色ID不能为空")
    private String voiceId = "BV407_V2_streaming";

    @Valid
    @NotEmpty(message = "音色类型不能为空")
    private String anchorType = "volcengine";
    //语速，范围是-500~500，可选，默认是0
    @Min(value = -500, message = "语调范围是-500~500")
    @Max(value = 500, message = "语调范围是-500~500")
    private Integer speechRate;
    //音量，范围是0~100，可选，默认50
    @Min(value = 0, message = "音量范围是0~100")
    @Max(value = 100, message = "音量范围是0~100")
    private Integer volume;

    public String getVoiceId() {
        return StringUtils.isBlank(voiceId) ? "BV407_V2_streaming" : voiceId;
    }

    public String getAnchorType() {
        return StringUtils.isBlank(anchorType) ? "volcengine" : anchorType;
    }
}
