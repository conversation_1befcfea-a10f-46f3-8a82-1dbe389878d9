spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8  # 时区
    serialization: # 不返回时间戳
      write-dates-as-timestamps: false
  redis:
    host: ************** #************** #127.0.0.1
    port: 6389 #16379
    password: Redis235 #Redis235 #Mtj1qJSwLF
    timeout: 1000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-wait: -1
        max-active: 1000
        max-idle: 10
        min-idle: 5
    database: 5
  datasource:
    druid:
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      primary: master
      druid:
        initial-size: 3
        max-active: 10
        min-idle: 2
        max-wait: -1
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 30000
        time-between-eviction-runs-millis: 0
        validation-query: select 1
        validation-query-timeout: -1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        pool-prepared-statements: true
        max-open-prepared-statements: 100
        filters: stat,wall
        share-prepared-statements: true
      datasource:
        master:
          username: root
          password: 010.Tydic
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *******************************************************************************************************************************************************************

# mybatis
mybatis:
  mapperLocations: classpath:mapper/session/*.xml
  type-aliases-package: com.tydic.nbchat.session.mapper.po
# 分页组件
pagehelper:
  helperDialect: mysql
  params: count=countSql


nacos:
  config:
    server-addr: **************:8848

# dubbo 服务配置
dubbo:
  application:
    name: nbchat-session
  registry:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    # 可以通过url ?namespace=nicc-env-dev&group=NICC的形式配置，也可以通过这种parameters配置
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  provider:
    threads: 300
    threadpool: cached
    loadbalance: roundrobin
    version: 1.0
    group: NICC
  protocol:
    name: dubbo
    port: -1
  # 配置中心和元数据
  config-center:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  metadata-report:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  consumer:
    check: false
    version: 1.0
    group: NICC

nicc-dc-config:
  dubbo-provider:
    version: 1.0
    group: NICC

session-config:
  share_url_prefix: https://chat.tydiczt.com/chat/#/shangePage?sessionShareId=

# 数据中心相关组件配置
nicc-plugin:
  redis:
    enable: true # 是否开启redisHelper，开启后必须配置spring.redis，同时直接用RedisHelper
  oss:
    enable: false
  rest:
    enable: true # 开启restTemplate 组件
    connect-timeout: 5000
    read-timeout: 5000
    print-log: true
  kkmq:
    mq-type: rocketmq
    log-level: warn
    name-server: **************:9878 # rocketmq服务地址
    producer:
      group: nbchat_user # 自定义的组名称
logging:
  config: classpath:logback-spring.xml
  level:
    com.tydic.nbchat.session.mapper: debug

# 短信模板配置
sms:
  # 默认模板配置 - JSON风格
  default: {
    "LOGIN": "SMS_148860011",
    "TENANT_APPLY": "SMS_463131159",
    "VIDEO_FINISH": "SMS_464205448",
    "VIDEO_DKZN_FINISH": "SMS_465366522",
    "VIP_OPEN": "SMS_470875038",
    "VIP_CLOSE": "SMS_475250049",
    "VIP_RENEW": "SMS_470695054",
    "RECHARGE": "SMS_470830040",
    "HUMAN_CUSTOMIZATION_COMPLETED": "SMS_473875205",
    "HUMAN_CUSTOMIZATION_CANCEL": "SMS_473850236",
    "VOICE_CUSTOMIZATION_COMPLETED": "SMS_473865235",
    "VOICE_CUSTOMIZATION_CANCEL": "SMS_474035098",
    "HUMAN_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_483180144",
    "VOICE_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_483225137"
  }

  # 区号特定模板配置 - JSON风格
  area_codes: {
    # 中国大陆(86)使用默认模板
    "86": {},

    # 中国香港(852)模板映射
    "852": {
      "LOGIN": "SMS_485820196",
      "TENANT_APPLY": "SMS_485875200",
      "VIDEO_FINISH": "SMS_485785227",
      "VIDEO_DKZN_FINISH": "SMS_485845217",
      "VIP_OPEN": "SMS_485685228",
      "VIP_CLOSE": "SMS_485755209",
      "VIP_RENEW": "SMS_485860182",
      "RECHARGE": "SMS_485690221",
      "HUMAN_CUSTOMIZATION_COMPLETED": "SMS_485675195",
      "HUMAN_CUSTOMIZATION_CANCEL": "SMS_485755208",
      "VOICE_CUSTOMIZATION_COMPLETED": "SMS_485735184",
      "VOICE_CUSTOMIZATION_CANCEL": "SMS_485845221",
      "HUMAN_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_485825208",
      "VOICE_CUSTOMIZATION_COMPLETED_NOPAY": "SMS_485875215"
    },

    # 中国台湾(886)模板映射
    "886": {
      "LOGIN": "SMS_485820196",
      "TENANT_APPLY": "SMS_485875200"
    },

    # 美国(1)模板映射
    "1": {
      "LOGIN": "SMS_485820196"
    }
  }

