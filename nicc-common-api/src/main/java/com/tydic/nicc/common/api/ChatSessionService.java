package com.tydic.nicc.common.api;

import com.tydic.nicc.common.api.bo.UserChatKeyQueryReqBO;
import com.tydic.nicc.common.bo.im.ImMessageMergeReqBO;
import com.tydic.nicc.common.bo.im.admin.ImUserChatListQueryReqBO;
import com.tydic.nicc.common.bo.session.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

import java.util.Set;

/**
 * <AUTHOR> <br>
 * @Description: im会话服务  <br
 * @date 2021/4/26 6:07 下午  <br>
 * @Copyright tydic.com
 */
public interface ChatSessionService {

    /**
     * 查询用户平台模式下-聊天列表
     * @param queryReqBO
     * @return
     */
    RspList<ChatSessionIndexBO> getChatSessionIndexList(ImUserChatListQueryReqBO queryReqBO);
    /**
     * 合并会话
     * @param mergeReqBO
     * @return
     */
    Rsp mergeGuestSession(ImMessageMergeReqBO mergeReqBO);
    /**
     * 查询用户活动的会话列表，用于IM 恢复会话
     * @param sessionReqBO
     * @return
     */
    Rsp<Set<Object>> getUserActiveSessions(GetUserActiveSessionReqBO sessionReqBO);

    /**
     * 查询用户活动会话列表
     * @param sessionReqBO
     * @return
     */
    RspList<GetUserActiveSessionBO> getUserActiveSessionList(GetUserActiveSessionReqBO sessionReqBO);


    /**
     * 创建新会话
     * @param createNewSessionReqBO
     * @return
     */
    Rsp<CreateNewSessionRspBO> createNewSession(CreateNewSessionReqBO createNewSessionReqBO);

    /**
     * 变更会话中的成员信息
     * @param changeSessionUserReqBO
     * @return
     */
    Rsp<ChangeSessionUserRspBO> changeSessionUsers(ChangeSessionUserReqBO changeSessionUserReqBO);

    /**
     * 关闭会话接口
     * @param closeSessionReqBO
     * @return
     */
    Rsp sessionClose(CloseSessionReqBO closeSessionReqBO);

    /**
     * 更新用户会话状态
     * @param sessionReqBO
     * @return
     */
    Rsp updateUserSessionStatus(UpdateUserSessionReqBO sessionReqBO);

    /**
     * 查询会话信息
     * @param sessionId
     * @return
     */
    Rsp<ChatSessionBO> getSessionInfo(String sessionId);

    /**
     * 统计用户会话次数
     * @param sessionCountReqBO
     * @return
     */
    Rsp<Integer> sessionCount(SessionCountReqBO sessionCountReqBO);

    /**
     * 查询用户会话列表
     * @param getUserSessionListReqBO
     * @return
     */
    RspList<GetUserSessionListBO> getUserSessionList(GetUserSessionListReqBO getUserSessionListReqBO);

    /**
     * 根据chatKey获取sessionId
     * @param chatKey
     * @param fromUid
     * @return
     */
    Rsp<String> getSessionIdByChatKey(String tenantCode,String fromUid,String chatKey);

    /**
     * 查询会话id
     * @param chatKey
     * @param fromNo
     * @param toNo
     * @return
     */
    //Rsp<String> getSessionIdByFromTo(String chatKey,String fromNo,String toNo);

    /**
     * @param chatKey
     * @return
     */
    Rsp<String> getCacheSessionId(String chatKey,String fromNo,String toNo);

    /**
     * 获取用户所有chatKey
     * @param userId
     * @return
     */
    RspList<String> getUserChatKeys(String tenantCode,String userId);

    /**
     * 查询用户chatKey
     * @param queryReqBO
     * @return
     */
    RspList<String> getUserChatKeys(UserChatKeyQueryReqBO queryReqBO);
}
