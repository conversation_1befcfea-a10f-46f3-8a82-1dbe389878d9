
# 配置日志
logging:
  config: classpath:logback-spring.xml

# nacos 配置中心
nacos:
  config:
    # 命名空间,区分不同环境
    namespace: nbchat-local
    # nacos服务地址,本机配置host
    server-addr: localhost:8848
    remote-first: true
    # 配置data-id
    data-id: nbchat-user
    # 分组
    group: NBCHAT
    # 配置类型
    type: yaml
    # 自动刷新配置
    auto-refresh: true
    max-retry: 10
    config-retry-time: 2333
    config-long-poll-timeout: 46000
    enable-remote-sync-config: true
    username: nacos
    password: nacos
    #access-key:
    #secret-key:
    bootstrap:
      enable: true