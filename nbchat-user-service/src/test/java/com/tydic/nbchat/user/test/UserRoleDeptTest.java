package com.tydic.nbchat.user.test;


import com.tydic.nbchat.user.api.UserRoleDeptApi;
import com.tydic.nbchat.user.api.bo.role.SubSystemInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@Slf4j
@SpringBootTest
public class UserRoleDeptTest {

    @Autowired
    private UserRoleDeptApi userRoleDeptApi;

    @Test
    public void test(){
        Rsp<SubSystemInfo> infoRsp = userRoleDeptApi.getUserSubSysInfo("295602335551881216","tmo");
        log.info("{}",infoRsp);
    }

}
