package com.tydic.nbchat.user.test;

import com.tydic.nbchat.user.core.config.WchatConfigProperties;
import com.tydic.nbchat.user.core.wx.event.EventFactory;
import com.tydic.nbchat.user.core.wx.context.WxExtMsgContext;
import com.tydic.nbchat.user.mapper.CommonMapper;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Slf4j
@SpringBootTest
public class WxTest {

    @Resource
    EventFactory eventFactory;

    @Test
    public void testHandle() {
        WxExtMsgContext context = new WxExtMsgContext();
        context.setEvent("subscribe");
        context.setFromUserName("2");
        context.setToUserName("1");
        context.setCreateTime("createTime");
        context.setMsgType("msgType");

        Rsp<T> handle = eventFactory.handle(context);
        System.out.println(handle.getData());
    }


    @Resource
    WchatConfigProperties properties;

    @Test
    public void test1() {
        System.out.println(properties.getFollowingContent());
    }


    public static void main(String[] args) {
        String emoji = "🌹";
        int codePoint = emoji.codePointAt(emoji.offsetByCodePoints(0, 0));
        String unicode = "\\u" + Integer.toHexString(codePoint);
        System.out.println(unicode);
    }

    @Resource
    CommonMapper commonMapper;

    @Test
    public void fun() {
        Map<String, Date> dateMap = commonMapper.queryCustomizeRecord("451119935619940352");
        System.out.println(DateTimeUtil.getTimeShortString(dateMap.get("start_time"), "yyyy-MM-dd HH:mm:ss"));
    }

}
