<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatExamTestPaperMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatExamTestPaper" id="NbchatExamTestPaperMap">
        <result property="paperId" column="paper_id" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="passingScore" column="passing_score" jdbcType="VARCHAR"/>
        <result property="testNum" column="test_num" jdbcType="INTEGER"/>
        <result property="single" column="single" jdbcType="INTEGER"/>
        <result property="multiple" column="multiple" jdbcType="INTEGER"/>
        <result property="trueOrFalse" column="true_or_false" jdbcType="INTEGER"/>
        <result property="testType" column="test_type" jdbcType="VARCHAR"/>
        <result property="trainState" column="train_state" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        paper_id, course_id, tenant_code, test_num, single, multiple,true_or_false,passing_score ,test_type, train_state, create_time, create_user, update_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatExamTestPaperMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_exam_test_paper
        where paper_id = #{paperId}
    </select>

    <select id="queryByCourseId" resultMap="NbchatExamTestPaperMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_exam_test_paper
        where course_id = #{courseId} and tenant_code = #{tenantCode}
        order by create_time DESC
        LIMIT 1
    </select>
    
    <select id="selectAll" resultMap="NbchatExamTestPaperMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestPaper">
        select
          <include refid="Base_Column_List" />
        from nbchat_exam_test_paper
         <where>
            <if test="paperId != null and paperId != ''">
                and paper_id = #{paperId}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="testNum != null">
                and test_num = #{testNum}
            </if>
            <if test="single != null">
                and single = #{single}
            </if>
            <if test="multiple != null">
                and multiple = #{multiple}
            </if>
            <if test="trueOrFalse != null">
                and true_or_false = #{trueOrFalse}
            </if>
            <if test="passingScore !=null and passingScore != '' " >
                and passing_score = #{passingScore}
            </if>
            <if test="testType != null and testType != ''">
                and test_type = #{testType}
            </if>
            <if test="trainState != null and trainState != ''">
                and train_state = #{trainState}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="paperId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestPaper">
        insert into nbchat_exam_test_paper
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="testNum != null">
                test_num,
            </if>
            <if test="single != null">
                single,
            </if>
            <if test="multiple != null">
                multiple,
            </if>
            <if test="trueOrFalse != null">
                true_or_false,
            </if>
            <if test="passingScore !=null and passingScore != '' " >
                passing_score,
            </if>
            <if test="testType != null and testType != ''">
                test_type,
            </if>
            <if test="trainState != null and trainState != ''">
                train_state,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="testNum != null">
                #{testNum},
            </if>
            <if test="single != null">
                #{single},
            </if>
            <if test="multiple != null">
                #{multiple},
            </if>
            <if test="trueOrFalse != null">
                #{trueOrFalse},
            </if>
            <if test="passingScore !=null and passingScore != '' " >
                #{passingScore},
            </if>
            <if test="testType != null and testType != ''">
                #{testType},
            </if>
            <if test="trainState != null and trainState != ''">
                #{trainState},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="paperId" useGeneratedKeys="true">
        insert into nbchat_exam_test_paper(course_id, tenant_code, test_num, single, multiple, true_or_false, passing_score ,test_type, train_state, create_time, create_user, update_time)
        values (#{courseId}, #{tenantCode}, #{testNum}, #{single}, #{multiple}, #{trueOrFalse}, #{passingScore},#{testType}, #{trainState}, #{createTime}, #{createUser}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_exam_test_paper
        <set>
            <if test="courseId != null and courseId != ''">
                course_id = #{courseId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="testNum != null">
                test_num = #{testNum},
            </if>
            <if test="passingScore !=null and passingScore != '' " >
                passing_score = #{passingScore},
            </if>
            <if test="testType != null and testType != ''">
                test_type = #{testType},
            </if>
            <if test="trainState != null and trainState != ''">
                train_state = #{trainState},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createUser != null and createUser != ''">
                create_user = #{createUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="single != null">
                single = #{single},
            </if>
            <if test="multiple != null">
                multiple = #{multiple},
            </if>
            <if test="trueOrFalse != null">
                true_or_false = #{trueOrFalse},
            </if>
        </set>
        where paper_id = #{paperId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_exam_test_paper where paper_id = #{paperId}
    </delete>
    <update id="deleteQuestionItem">
        update nbchat_exam_question_items
        set is_valid = '0'
        where  question_id in (select question_id from nbchat_exam_question  where course_id = #{courseId} and tenant_code = #{tenantCode});
    </update>

    <update id="deleteQuestion">
        update nbchat_exam_question set is_valid = '0' where course_id = #{courseId} and tenant_code = #{tenantCode};
    </update>

</mapper>

