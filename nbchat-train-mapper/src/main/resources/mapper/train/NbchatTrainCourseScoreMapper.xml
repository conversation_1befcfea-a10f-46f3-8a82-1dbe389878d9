<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainCourseScoreMapper">

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatCourseScore">
        <result property="tenantCode" column="tenant_code"/>
        <result property="deptId" column="dept_id"/>
        <result property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="createTime" column="create_time"/>
        <result property="lastTime" column="last_time"/>
        <result property="learnEnd" column="learn_end"/>
        <result property="learnSchedule" column="learn_schedule"/>
        <result property="score" column="score"/>
        <result property="testState" column="test_state"/>
        <result property="testCount" column="test_count"/>
    </resultMap>
    <resultMap id="trainUserTenant" type="com.tydic.nbchat.train.mapper.po.NbchatTrainUserTenant">
        <result property="userId" column="user_id"/>
        <result property="userRealityName" column="user_reality_name"/>
    </resultMap>

    <select id="getGrades" resultMap="BaseResultMap">
        SELECT
        tenant_code,dept_id,course_id,course_name,user_id,name,phone,create_time,last_time,learn_end,learn_schedule,test_state
        FROM (SELECT
        r.tenant_code AS tenant_code,
        dur.dept_id as dept_id,
        c.course_id AS course_id,
        c.course_name AS course_name,
        r.user_id AS user_id,
        nut.user_reality_name AS name,
        u.phone AS phone,
        r.create_time AS create_time,
        r.last_time AS last_time,
        IF(r.train_state = '1', '是', '否') AS learn_end,
        CASE
        WHEN (r.class_hour_learned/IF(ISNULL(r.class_hour_total) OR r.class_hour_total = 0, 1, r.class_hour_total)) = 0
        THEN '未开始'
        WHEN (r.class_hour_learned/IF(ISNULL(r.class_hour_total) OR r.class_hour_total = 0, 1, r.class_hour_total)) = 1
        THEN '已完成'
        ELSE CONCAT('进行中 已学',CONCAT(FORMAT((r.class_hour_learned/IF(ISNULL(r.class_hour_total) OR
        r.class_hour_total = 0, 1, r.class_hour_total))*100, 2), '%'))
        END AS learn_schedule,
        IF(r.test_state = '1', '是', '否') AS test_state
        FROM nbchat_train_course c
        left JOIN nbchat_sys_user_tenant nut ON c.tenant_code = nut.tenant_code
        left JOIN nbchat_train_record r ON c.course_id = r.course_id and r.user_id = nut.user_id
        left JOIN nbchat_user u ON r.user_id = u.user_id
        LEFT JOIN sys_dept_user_rel dur on dur.user_id = u.user_id and dur.tenant_code = r.tenant_code
        <if test="userPosition != null and userPosition != '' ">
            left join sys_post_user_rel sp on sp.user_id = u.user_id and sp.tenant_code = nut.tenant_code
        </if>
        WHERE r.tenant_code = #{tenantCode} and u.is_deleted = 0
        <if test="courseId != null and !courseId.isEmpty() ">
            AND c.course_id IN
            <foreach item="item" index="index" collection="courseId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="phone != null and !phone.isEmpty()">
            AND u.phone IN
            <foreach item="item" index="index" collection="phone" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null and endTime!=null ">
            AND r.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="lastTime != null ">
            AND DATE(r.last_time) = DATE(#{lastTime})
        </if>
        <if test="userPosition != null and userPosition != '' ">
            AND sp.post_id = #{userPosition}
        </if>
        <if test="name != null and !name.isEmpty() ">
            AND nut.user_reality_name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="deptId != null and !deptId.isEmpty() ">
            <if test='deptScope != "1"'>
                <!-- 查询当前部门的数据 -->
                AND u.user_id in (select user_id from sys_dept_user_rel where dept_id IN
                <foreach item="item" index="index" collection="deptId" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test='deptScope == "1" and deptId != tenantCode'>
                <!-- 查询当前部门及下级部门的数据 -->
                AND u.user_id in (
                select user_id from sys_dept_user_rel where dept_id in (
                <foreach item="item" index="index" collection="deptId"  separator="UNION" >
                    select dept_id from sys_dept where tenant_code = #{tenantCode} and find_in_set(#{item},ancestors)
                </foreach>
                )
                )
            </if>
        </if>
        )subquery
        <if test="learnSchedule !=null and learnSchedule !='' ">
            WHERE subquery.learn_schedule LIKE CONCAT('%', #{learnSchedule}, '%')
        </if>
        ORDER BY subquery.last_time, subquery.user_id DESC
    </select>
    <select id="getScore" resultMap="BaseResultMap">
        SELECT
        IFNULL(MAX(score), 0) AS score,
        count(*) AS test_count
        FROM nbchat_exam_test_record
        WHERE tenant_code = #{tenantCode}
        AND user_id = #{userId}
        AND course_id = #{courseId}
    </select>
    <select id="getUserInformation" resultMap="trainUserTenant">
        SELECT user_id,user_reality_name
        FROM nbchat_sys_user_tenant
        WHERE user_reality_name LIKE CONCAT('%',#{userRealityName},'%') and tenant_code = #{tenantCode}
    </select>
</mapper>