<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhTemplateMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhTemplate">
        <!--@mbg.generated-->
        <!--@Table tdh_template-->
        <id column="tp_id" jdbcType="VARCHAR" property="tpId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="tp_name" jdbcType="VARCHAR" property="tpName"/>
        <result column="tp_content" jdbcType="LONGVARCHAR" property="tpContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_index" jdbcType="SMALLINT" property="orderIndex"/>
        <result column="tp_source" jdbcType="CHAR" property="tpSource"/>
        <result column="is_valid" jdbcType="CHAR" property="isValid"/>
        <result column="object_size" jdbcType="VARCHAR" property="objectSize"/>
        <result column="content_size" jdbcType="VARCHAR" property="contentSize"/>
        <result column="preview_url" jdbcType="VARCHAR" property="previewUrl"/>
        <result column="tmp_id" jdbcType="VARCHAR" property="tmpId"/>
        <result column="replace_tag" jdbcType="CHAR" property="replaceTag"/>
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
        <result column="duration" jdbcType="INTEGER" property="duration"/>
        <result column="is_hot" jdbcType="CHAR" property="isHot"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="vip_flag" jdbcType="CHAR" property="vipFlag"/>
        <result column="scene" jdbcType="VARCHAR" property="scene"/>
        <result column="style" jdbcType="VARCHAR" property="style"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="tdh_type" jdbcType="VARCHAR" property="tdhType"/>
        <result column="is_tdh" jdbcType="CHAR" property="isTdh"/>
        <result column="tp_state" jdbcType="CHAR" property="tpState"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="tpl_type" jdbcType="CHAR" property="tplType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        tp_id, user_id, tenant_code, tp_name, tp_content, create_time, order_index, tp_source,
        is_valid, object_size, preview_url, tmp_id, replace_tag, video_url, duration, is_hot, content_size,
        category, vip_flag, scene, `style`, color, tdh_type, is_tdh, tp_state, remark,tpl_type
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tdh_template
        where tp_id = #{tpId}
          and is_valid = '1'
    </select>

    <select id="selectByIds" resultMap="BaseResultMap" parameterType="com.tydic.nbchat.train.mapper.po.TdhTemplate">
        select
        <include refid="Base_Column_List"/>
        from tdh_template
        where tp_id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhTemplateSelectCondition">
        select
        <include refid="Base_Column_List"/>
        from tdh_template
        <where>
            <if test="keyword != null and keyword != ''">
                and (tp_name like concat('%', #{keyword}, '%') or tp_content like concat('%', #{keyword}, '%'))
            </if>
            <if test="tpId != null and tpId != ''">
                and tp_id = #{tpId}
            </if>
            <if test="tmpId != null and tmpId != ''">
                and tmp_id = #{tpId}
            </if>
            <if test="tpSource != null and tpSource != ''">
                and tp_source = #{tpSource}
            </if>
            <if test="targetTenant != null and targetTenant != ''">
                and user_id = #{targetTenant}
                and tp_source = '0'
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                <!-- 私有化定制不能被其他人看到, 系统提供通用模板 user_id = 00000000 -->
                and (
                (user_id = 'public' and tp_source = '0')
                <!-- user_id = #{tenantCode} 企业通用 -->
                or (user_id = #{tenantCode} and tp_source = '0')
                <!-- 个人定制 -->
                or (tenant_code = #{tenantCode} and user_id = #{userId} and tp_source = '1')
                    )
            </if>
            <if test="objectSize != null and objectSize != ''">
                and object_size = #{objectSize}
            </if>
            <if test="contentSize != null and contentSize != ''">
                and content_size = #{contentSize}
            </if>
            <if test="tpName != null and tpName != ''">
                and tp_name like concat('%', #{tpName}, '%')
            </if>
            <if test="tpSource != null and tpSource != ''">
                and tp_source = #{tpSource}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="isHot != null and isHot != ''">
                and is_hot = #{isHot}
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                and vip_flag = #{vipFlag}
            </if>
            <if test="tdhType != null and tdhType != ''">
                and tdh_type = #{tdhType}
            </if>
            <if test="isTdh != null and isTdh != ''">
                and is_tdh = #{isTdh}
            </if>
            <if test="tpState != null and tpState != ''">
                and tp_state = #{tpState}
            </if>
            <if test="scene != null and scene != ''">
                <bind name="sceneList" value="scene.split(',')"/>
                AND
                <foreach collection="sceneList" item="scene" open="(" close=")" separator=" OR ">
                    scene LIKE CONCAT('%', #{scene}, '%')
                </foreach>
            </if>
            <if test="style != null and style != ''">
                <bind name="styleList" value="style.split(',')"/>
                AND
                <foreach collection="styleList" item="style" open="(" close=")" separator=" OR ">
                    style LIKE CONCAT('%', #{style}, '%')
                </foreach>
            </if>
            <if test="color != null and color != ''">
                <bind name="colorList" value="color.split(',')"/>
                AND
                <foreach collection="colorList" item="color" open="(" close=")" separator=" OR ">
                    color LIKE CONCAT('%', #{color}, '%')
                </foreach>
            </if>
            <if test="category != null and category != ''">
                and category LIKE CONCAT('%', #{category}, '%')
            </if>
            <if test="tplType != null and tplType != ''">
                and tpl_type = #{tplType}
            </if>
        </where>
        order by order_index, create_time desc
    </select>

    <select id="selectTemp" resultMap="BaseResultMap" parameterType="com.tydic.nbchat.train.mapper.po.TdhTemplate">
        select
        <include refid="Base_Column_List"/>
        from tdh_template
        where is_valid = 1
          and tp_id = #{tpId}
          and replace_tag = #{replaceTag}
          and tmp_id is not null
          and tmp_id != ''
    </select>


    <insert id="insertSelective" keyProperty="tpId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhTemplate">
        <!--@mbg.generated-->
        insert into tdh_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tpId != null and tpId != ''">
                tp_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="tpName != null and tpName != ''">
                tp_name,
            </if>
            <if test="tpContent != null and tpContent != ''">
                tp_content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="tpSource != null and tpSource != ''">
                tp_source,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="objectSize != null and objectSize != ''">
                object_size,
            </if>
            <if test="contentSize != null and contentSize != ''">
                content_size,
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url,
            </if>
            <if test="tmpId != null and tmpId != ''">
                tmp_id,
            </if>
            <if test="replaceTag != null and replaceTag != ''">
                replace_tag,
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="isHot != null and isHot != ''">
                is_hot,
            </if>
            <if test="category != null and category != ''">
                category,
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag,
            </if>
            <if test="scene != null and scene != ''">
                scene,
            </if>
            <if test="style != null and style != ''">
                `style`,
            </if>
            <if test="color != null and color != ''">
                color,
            </if>
            <if test="tdhType != null and tdhType != ''">
                tdh_type,
            </if>
            <if test="isTdh != null and isTdh != ''">
                is_tdh,
            </if>
            <if test="tpState != null and tpState != ''">
                tp_state,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="tplType != null and tplType != ''">
                tpl_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tpId != null and tpId != ''">
                #{tpId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tpName != null and tpName != ''">
                #{tpName,jdbcType=VARCHAR},
            </if>
            <if test="tpContent != null and tpContent != ''">
                #{tpContent,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="tpSource != null and tpSource != ''">
                #{tpSource,jdbcType=CHAR},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="objectSize != null and objectSize != ''">
                #{objectSize,jdbcType=VARCHAR},
            </if>
            <if test="contentSize != null and contentSize != ''">
                #{contentSize,jdbcType=VARCHAR},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                #{previewUrl,jdbcType=VARCHAR},
            </if>
            <if test="tmpId != null and tmpId != ''">
                #{tmpId,jdbcType=VARCHAR},
            </if>
            <if test="replaceTag != null and replaceTag != ''">
                #{replaceTag,jdbcType=CHAR},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=INTEGER},
            </if>
            <if test="isHot != null and isHot != ''">
                #{isHot,jdbcType=CHAR},
            </if>
            <if test="category != null and category != ''">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                #{vipFlag,jdbcType=CHAR},
            </if>
            <if test="scene != null and scene != ''">
                #{scene,jdbcType=VARCHAR},
            </if>
            <if test="style != null and style != ''">
                #{style,jdbcType=VARCHAR},
            </if>
            <if test="color != null and color != ''">
                #{color,jdbcType=VARCHAR},
            </if>
            <if test="tdhType != null and tdhType != ''">
                #{tdhType,jdbcType=VARCHAR},
            </if>
            <if test="isTdh != null and isTdh != ''">
                #{isTdh,jdbcType=CHAR},
            </if>
            <if test="tpState != null and tpState != ''">
                #{tpState,jdbcType=CHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null and tplType != ''">
                #{tplType,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <update id="update">
        update tdh_template
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tpName != null and tpName != ''">
                tp_name = #{tpName,jdbcType=VARCHAR},
            </if>
            <if test="tpContent != null and tpContent != ''">
                tp_content = #{tpContent,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="tpSource != null and tpSource != ''">
                tp_source = #{tpSource,jdbcType=CHAR},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="objectSize != null and objectSize != ''">
                object_size = #{objectSize,jdbcType=VARCHAR},
            </if>
            <if test="contentSize != null and contentSize != ''">
                content_size = #{contentSize,jdbcType=VARCHAR},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url = #{previewUrl,jdbcType=VARCHAR},
            </if>
            <if test="tmpId != null and tmpId != ''">
                tmp_id = #{tmpId,jdbcType=VARCHAR},
            </if>
            <if test="replaceTag != null and replaceTag != ''">
                replace_tag = #{replaceTag,jdbcType=CHAR},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url = #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                duration = #{duration,jdbcType=INTEGER},
            </if>
            <if test="isHot != null and isHot != ''">
                is_hot = #{isHot,jdbcType=CHAR},
            </if>
            <if test="category != null and category != ''">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag = #{vipFlag,jdbcType=CHAR},
            </if>
            <if test="scene != null and scene != ''">
                scene = #{scene,jdbcType=VARCHAR},
            </if>
            <if test="style != null and style != ''">
                `style` = #{style,jdbcType=VARCHAR},
            </if>
            <if test="color != null and color != ''">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="tdhType != null and tdhType != ''">
                tdh_type = #{tdhType,jdbcType=VARCHAR},
            </if>
            <if test="isTdh != null and isTdh != ''">
                is_tdh = #{isTdh,jdbcType=CHAR},
            </if>
            <if test="tpState != null and tpState != ''">
                tp_state = #{tpState,jdbcType=CHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null and tplType != ''">
                tpl_type = #{tplType,jdbcType=CHAR},
            </if>
        </set>
        where tp_id = #{tpId,jdbcType=VARCHAR}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update tdh_template set is_valid = '0' where tp_id = #{tpId}
    </delete>

    <update id="updateList" parameterType="java.util.List">
        update tdh_template
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tp_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tpName != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tpName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tp_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tpContent != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tpContent,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderIndex != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tp_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tpSource != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tpSource,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isValid != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="object_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.objectSize != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.objectSize,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="preview_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.previewUrl != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.previewUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tmp_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tmpId != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tmpId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="replace_tag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.replaceTag != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.replaceTag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoUrl != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.videoUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.duration != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.duration,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_hot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isHot != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.isHot,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.category != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.category,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vip_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vipFlag != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.vipFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="scene = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scene != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.scene,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`style` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.style != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.style,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.color != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.color,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhType != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tdhType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_tdh = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isTdh != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.isTdh,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tp_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tpState != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tpState,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplType != null">
                        when tp_id = #{item.tpId,jdbcType=VARCHAR} then #{item.tplType,jdbcType=CHAR}
                    </if>
                </foreach>
           </trim>
        </trim>
        where tp_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.tpId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectStarTemplateByCondition" resultMap="BaseResultMap">
        select template.*
        from sys_user_star star
                 left join tdh_template template on star.busi_id = template.tp_id
        <where>
            star.user_id = #{userId}
              and star.tenant_code = #{tenantCode}
              and star.busi_type = 'tdh_template'
              and template.is_valid = '1'
            <if test="tpSource != null and tpSource != ''">
                and template.tp_source = #{tpSource}
            </if>
            <if test="targetTenant != null and targetTenant != ''">
                and template.user_id = #{targetTenant}
                and template.tp_source = '0'
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                <!-- 私有化定制不能被其他人看到, 系统提供通用模板 user_id = 00000000 -->
                and (
                (template.user_id = 'public' and template.tp_source = '0')
                <!-- user_id = #{tenantCode} 企业通用 -->
                or (template.user_id = #{tenantCode} and template.tp_source = '0')
                <!-- 个人定制 -->
                or (template.tenant_code = #{tenantCode} and template.user_id = #{userId} and template.tp_source = '1')
                    )
            </if>
            <if test="objectSize != null and objectSize != ''">
                and template.object_size = #{objectSize}
            </if>
            <if test="tpName != null and tpName != ''">
                and template.tp_name like concat('%', #{tpName}, '%')
            </if>
            <if test="tpSource != null and tpSource != ''">
                and template.tp_source = #{tpSource}
            </if>
            <if test="isHot != null and isHot != ''">
                and template.is_hot = #{isHot}
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                and template.vip_flag = #{vipFlag}
            </if>
            <if test="tdhType != null and tdhType != ''">
                and template.tdh_type = #{tdhType}
            </if>
            <if test="isTdh != null and isTdh != ''">
                and template.is_tdh = #{isTdh}
            </if>
            <if test="tpState != null and tpState != ''">
                and template.tp_state = #{tpState}
            </if>
            <if test="scene != null and scene != ''">
                <bind name="sceneList" value="scene.split(',')"/>
                AND
                <foreach collection="sceneList" item="scene" open="(" close=")" separator=" OR ">
                    template.scene LIKE CONCAT('%', #{scene}, '%')
                </foreach>
            </if>
            <if test="style != null and style != ''">
                <bind name="styleList" value="style.split(',')"/>
                AND
                <foreach collection="styleList" item="style" open="(" close=")" separator=" OR ">
                    template.style LIKE CONCAT('%', #{style}, '%')
                </foreach>
            </if>
            <if test="color != null and color != ''">
                <bind name="colorList" value="color.split(',')"/>
                AND
                <foreach collection="colorList" item="color" open="(" close=")" separator=" OR ">
                    template.color LIKE CONCAT('%', #{color}, '%')
                </foreach>
            </if>
            <if test="category != null and category != ''">
                and template.category LIKE CONCAT('%', #{category}, '%')
            </if>
            <if test="tplType != null and tplType != ''">
                and template.tpl_type = #{tplType}
            </if>
        </where>
        order by template.order_index, template.create_time desc
    </select>
</mapper>

