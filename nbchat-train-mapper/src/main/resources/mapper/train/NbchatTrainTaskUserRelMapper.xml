<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainTaskUserRelMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatTrainTaskUserRel" id="NbchatTrainTaskUserRelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatTrainTaskUserRelMap">
        select
            id, task_id, user_id
        from nbchat_train_task_user_rel
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAll" resultMap="NbchatTrainTaskUserRelMap">
        select
            id, task_id, user_id
        from nbchat_train_task_user_rel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="taskId != null">
                and task_id = #{taskId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
        </where>
    </select>

    <select id="queryUserName" resultType="com.alibaba.fastjson.JSONObject" >
        select t1.user_id userId ,
               t2.user_reality_name userName
        from nbchat_train_task_user_rel t1 join nbchat_sys_user_tenant t2
        on t1.user_id = t2.user_id
        where t1.task_id = #{taskId} and t2.tenant_code = #{tenantCode}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_train_task_user_rel(task_id, user_id)
        values (#{taskId}, #{userId})
    </insert>

    <insert id="insertBatch" >
        insert into nbchat_train_task_user_rel(task_id, user_id)
        values
        <foreach collection="userIds" item="userId" separator=",">
        (#{taskId}, #{userId})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_train_task_user_rel
        <set>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByTaskId">
        delete from nbchat_train_task_user_rel where task_id = #{taskId}
    </delete>

</mapper>

