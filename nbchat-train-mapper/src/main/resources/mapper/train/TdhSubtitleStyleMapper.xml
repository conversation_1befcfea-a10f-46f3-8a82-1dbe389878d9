<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhSubtitleStyleMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle">
    <!--@mbg.generated-->
    <!--@Table tdh_subtitle_style-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="style_name" jdbcType="VARCHAR" property="styleName" />
    <result column="style_value" jdbcType="VARCHAR" property="styleValue" />
    <result column="style_desc" jdbcType="VARCHAR" property="styleDesc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="thumb_url" jdbcType="VARCHAR" property="thumbUrl" />
    <result column="order_index" jdbcType="INTEGER" property="orderIndex" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="style_line" jdbcType="VARCHAR" property="styleLine" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, style_name, style_value, style_desc, create_time, thumb_url, order_index, is_valid, 
    style_line
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tdh_subtitle_style
    where id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_subtitle_style (style_name, style_value, style_desc, 
      create_time, thumb_url, order_index, 
      is_valid, style_line)
    values (#{styleName,jdbcType=VARCHAR}, #{styleValue,jdbcType=VARCHAR}, #{styleDesc,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{thumbUrl,jdbcType=VARCHAR}, #{orderIndex,jdbcType=INTEGER}, 
      #{isValid,jdbcType=CHAR}, #{styleLine,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_subtitle_style
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="styleName != null and styleName != ''">
        style_name,
      </if>
      <if test="styleValue != null and styleValue != ''">
        style_value,
      </if>
      <if test="styleDesc != null and styleDesc != ''">
        style_desc,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        thumb_url,
      </if>
      <if test="orderIndex != null">
        order_index,
      </if>
      <if test="isValid != null and isValid != ''">
        is_valid,
      </if>
      <if test="styleLine != null and styleLine != ''">
        style_line,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="styleName != null and styleName != ''">
        #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="styleValue != null and styleValue != ''">
        #{styleValue,jdbcType=VARCHAR},
      </if>
      <if test="styleDesc != null and styleDesc != ''">
        #{styleDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        #{thumbUrl,jdbcType=VARCHAR},
      </if>
      <if test="orderIndex != null">
        #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="isValid != null and isValid != ''">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="styleLine != null and styleLine != ''">
        #{styleLine,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle">
    <!--@mbg.generated-->
    update tdh_subtitle_style
    <set>
      <if test="styleName != null and styleName != ''">
        style_name = #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="styleValue != null and styleValue != ''">
        style_value = #{styleValue,jdbcType=VARCHAR},
      </if>
      <if test="styleDesc != null and styleDesc != ''">
        style_desc = #{styleDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        thumb_url = #{thumbUrl,jdbcType=VARCHAR},
      </if>
      <if test="orderIndex != null">
        order_index = #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="isValid != null and isValid != ''">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="styleLine != null and styleLine != ''">
        style_line = #{styleLine,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle">
    <!--@mbg.generated-->
    update tdh_subtitle_style
    set style_name = #{styleName,jdbcType=VARCHAR},
      style_value = #{styleValue,jdbcType=VARCHAR},
      style_desc = #{styleDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      thumb_url = #{thumbUrl,jdbcType=VARCHAR},
      order_index = #{orderIndex,jdbcType=INTEGER},
      is_valid = #{isValid,jdbcType=CHAR},
      style_line = #{styleLine,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from tdh_subtitle_style
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=INTEGER}
            </if>
            <if test="styleName != null and styleName != ''">
                and style_name like concat('%',#{styleName,jdbcType=VARCHAR},'%')
            </if>
            <if test="styleValue != null and styleValue != ''">
                and style_value like concat('%',#{styleValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="styleDesc != null and styleDesc != ''">
                and style_desc like concat('%',#{styleDesc,jdbcType=VARCHAR},'%')
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="thumbUrl != null and thumbUrl != ''">
                and thumb_url=#{thumbUrl,jdbcType=VARCHAR}
            </if>
            <if test="orderIndex != null">
                and order_index=#{orderIndex,jdbcType=INTEGER}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid=#{isValid,jdbcType=CHAR}
            </if>
            <if test="styleLine != null and styleLine != ''">
                and style_line=#{styleLine,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tdh_subtitle_style
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="style_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.styleName != null and item.styleName != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.styleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="style_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.styleValue != null and item.styleValue != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.styleValue,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="style_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.styleDesc != null and item.styleDesc != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.styleDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="thumb_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thumbUrl != null and item.thumbUrl != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.thumbUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderIndex != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.orderIndex,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null and item.isValid != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="style_line = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.styleLine != null and item.styleLine != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.styleLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_subtitle_style
    (style_name, style_value, style_desc, create_time, thumb_url, order_index, is_valid, 
      style_line)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.styleName,jdbcType=VARCHAR}, #{item.styleValue,jdbcType=VARCHAR}, #{item.styleDesc,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.thumbUrl,jdbcType=VARCHAR}, #{item.orderIndex,jdbcType=INTEGER}, 
        #{item.isValid,jdbcType=CHAR}, #{item.styleLine,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from tdh_subtitle_style where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>