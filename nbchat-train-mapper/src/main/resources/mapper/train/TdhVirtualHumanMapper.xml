<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhVirtualHuman" id="TdhVirtualHumanMap">
        <result property="tdhId" column="tdh_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tdhImg" column="tdh_img" jdbcType="VARCHAR"/>
        <result property="tdhImgThu" column="tdh_img_thu" jdbcType="VARCHAR"/>
        <result property="tdhType" column="tdh_type" jdbcType="VARCHAR"/>
        <result property="tdhName" column="tdh_name" jdbcType="VARCHAR"/>
        <result property="tdhTags" column="tdh_tags" jdbcType="VARCHAR"/>
        <result property="poseType" column="pose_type" jdbcType="VARCHAR"/>
        <result property="tdhSource" column="tdh_source" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="vipFlag" column="vip_flag" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="customizeStatus" column="customize_status" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="targetVoice" column="target_voice" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        tdh_id
        , tenant_code, gender, user_id, tdh_img, tdh_name,tdh_type, tdh_tags, pose_type, tdh_source, create_time,
            order_index, is_valid,tdh_img_thu,vip_flag,order_no,customize_status,avatar,target_voice</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhVirtualHumanMap">
        select
        <include refid="Base_Column_List"/>
        from tdh_virtual_human
        where tdh_id = #{tdhId}
    </select>

    <select id="selectByIds" resultMap="TdhVirtualHumanMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhVirtualHuman">
        select
        <include refid="Base_Column_List"/>
        from tdh_virtual_human
        where tdh_id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--条件查询-->
    <select id="selectByConditions" resultMap="TdhVirtualHumanMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhVirtualHuman">
        SELECT
        a.*,
        b.tenant_name AS tenantName
        FROM tdh_virtual_human a
        LEFT JOIN nbchat_sys_tenant b ON a.tenant_code = b.tenant_code
        <where>
            <if test="tenantCode != null and tenantCode != ''">
                AND (
                user_id = '00000000'
                OR ( user_id = #{tenantCode} and tdh_source = 1 )
                OR ( a.tenant_code = #{tenantCode} and tdh_source = 0 )
                OR ( a.tenant_code = #{tenantCode} and user_id = #{userId} and tdh_source = 1 )
                )
            </if>
            <if test="tdhId != null and tdhId != ''">
                AND a.tdh_id = #{tdhId}
            </if>
            <if test="(tdhSource != null and tdhSource != '' and tdhSource == 1) and (tenantCode == null or tenantCode == '') " >
                AND a.tdh_source = '1' AND a.tenant_code != '00000000'
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                AND a.tdh_source = #{tdhSource}
            </if>
            <if test="gender != null and gender != ''">
                AND a.gender = #{gender}
            </if>
            <if test="poseType != null and poseType != ''">
                AND a.pose_type = #{poseType}
            </if>
            <if test="tdhType != null and tdhType != ''">
                AND a.tdh_type LIKE CONCAT('%', #{tdhType}, '%')
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                AND a.vip_flag = #{vipFlag}
            </if>
            <if test="tdhName != null and tdhName != ''">
                AND a.tdh_name LIKE CONCAT('%', #{tdhName}, '%')
            </if>
            <if test="isValid != null and isValid != ''">
                AND a.is_valid = #{isValid}
            </if>
        </where>
        order by a.order_index asc, a.create_time desc, a.tdh_id desc
    </select>


    <select id="selectAll" resultMap="TdhVirtualHumanMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhVirtualHuman">
        select
        <include refid="Base_Column_List"/>
        from tdh_virtual_human
        <where>
            <if test="tdhId != null and tdhId != ''">
                and tdh_id = #{tdhId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                <!-- 私有化定制不能被其他人看到, 系统提供通用数字人 user_id = 00000000 -->
                and (
                user_id = '00000000'
                <!-- user_id = #{tenantCode} 企业定制 -->
                or ( user_id = #{tenantCode} and tdh_source = 1 )
                <!-- 企业通用 -->
                or ( tenant_code = #{tenantCode} and tdh_source = 0 )
                <!-- 个人定制 -->
                or ( tenant_code = #{tenantCode} and user_id = #{userId} and tdh_source = 1 )
                )
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender}
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                and tdh_img = #{tdhImg}
            </if>
            <if test="tdhName != null and tdhName != ''">
                and tdh_name = #{tdhName}
            </if>
            <if test="tdhTags != null and tdhTags != ''">
                and tdh_tags = #{tdhTags}
            </if>
            <if test="poseType != null and poseType != ''">
                and pose_type = #{poseType}
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                and tdh_source = #{tdhSource}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                and vip_flag = #{vipFlag}
            </if>
        </where>
        order by order_index,create_time desc
    </select>


    <insert id="insertSelective" keyProperty="tdhId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhVirtualHuman">
        insert into tdh_virtual_human
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tdhId != null and tdhId != ''">
                tdh_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="gender != null and gender != ''">
                gender,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                tdh_img,
            </if>
            <if test="tdhImgThu != null and tdhImgThu != ''">
                tdh_img_thu,
            </if>
            <if test="tdhName != null and tdhName != ''">
                tdh_name,
            </if>
            <if test="tdhTags != null and tdhTags != ''">
                tdh_tags,
            </if>
            <if test="poseType != null and poseType != ''">
                pose_type,
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                tdh_source,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag,
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no,
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                customize_status,
            </if>
            <if test="tdhType != null and tdhType != ''">
                tdh_type,
            </if>
            <if test="targetVoice != null and targetVoice != ''">
                target_voice,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tdhId != null and tdhId != ''">
                #{tdhId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="gender != null and gender != ''">
                #{gender},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                #{tdhImg},
            </if>
            <if test="tdhImgThu != null and tdhImgThu != ''">
                #{tdhImgThu},
            </if>
            <if test="tdhName != null and tdhName != ''">
                #{tdhName},
            </if>
            <if test="tdhTags != null and tdhTags != ''">
                #{tdhTags},
            </if>
            <if test="poseType != null and poseType != ''">
                #{poseType},
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                #{tdhSource},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                #{vipFlag},
            </if>
            <if test="orderNo != null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                #{customizeStatus},
            </if>
            <if test="tdhType != null and tdhType != ''">
                #{tdhType},
            </if>
            <if test="targetVoice != null and targetVoice != ''">
                #{targetVoice},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="tdhId" useGeneratedKeys="true">
        insert into tdh_virtual_human(tenant_code, gender, user_id, tdh_img, tdh_name, tdh_tags, pose_type, tdh_source,
                                      create_time, order_index, is_valid)
        values (#{tenantCode}, #{gender}, #{userId}, #{tdhImg}, #{tdhName}, #{tdhTags}, #{poseType}, #{tdhSource},
                #{createTime}, #{orderIndex}, #{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_virtual_human
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                tdh_img = #{tdhImg},
            </if>
            <if test="tdhImgThu != null and tdhImgThu != ''">
                tdh_img_thu = #{tdhImgThu},
            </if>
            <if test="tdhName != null and tdhName != ''">
                tdh_name = #{tdhName},
            </if>
            <if test="tdhTags != null and tdhTags != ''">
                tdh_tags = #{tdhTags},
            </if>
            <if test="poseType != null and poseType != ''">
                pose_type = #{poseType},
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                tdh_source = #{tdhSource},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag = #{vipFlag},
            </if>
            <if test="targetVoice != null and targetVoice != ''">
            target_voice = #{targetVoice},
            </if>
            <if test="tdhType != null and tdhType != ''">
            tdh_type = #{tdhType},
            </if>
        </set>
        where tdh_id = #{tdhId}
    </update>


    <update id="updateByOrderNo">
        update tdh_virtual_human
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                tdh_img = #{tdhImg},
            </if>
            <if test="tdhName != null and tdhName != ''">
                tdh_name = #{tdhName},
            </if>
            <if test="tdhTags != null and tdhTags != ''">
                tdh_tags = #{tdhTags},
            </if>
            <if test="poseType != null and poseType != ''">
                pose_type = #{poseType},
            </if>
            <if test="tdhSource != null and tdhSource != ''">
                tdh_source = #{tdhSource},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag = #{vipFlag},
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                customize_status = #{customizeStatus},
            </if>
        </set>
        where order_no = #{orderNo}
    </update>


    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update tdh_virtual_human
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="gender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.gender != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.gender,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhType != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_img = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhImg != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhImg,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_img_thu = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhImgThu != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhImgThu,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhName != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_tags = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhTags != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhTags,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pose_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.poseType != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.poseType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhSource != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.tdhSource,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderIndex != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isValid != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vip_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vipFlag != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.vipFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customize_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customizeStatus != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.customizeStatus,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when tdh_id = #{item.tdhId,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where tdh_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.tdhId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from tdh_virtual_human
        where tdh_id = #{tdhId}
    </delete>

    <select id="findByOrderNoAndUserId" resultMap="TdhVirtualHumanMap">
        select
        <include refid="Base_Column_List"/>
        from tdh_virtual_human
        where order_no=#{orderNo,jdbcType=VARCHAR} and user_id=#{userId,jdbcType=VARCHAR}
    </select>
</mapper>

