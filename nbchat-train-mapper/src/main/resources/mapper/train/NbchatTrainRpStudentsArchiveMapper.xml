<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainRpStudentsArchiveMapper">
    <resultMap id="SysDeptUserResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatTrainStudentArchivePO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_reality_name" property="userRealityName" jdbcType="VARCHAR"/>
        <result column="join_type" property="joinType" jdbcType="INTEGER"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="score" property="balanceScore" jdbcType="INTEGER"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TrainRpStudentsArchiveMap" type="com.tydic.nbchat.train.mapper.po.NbchatTrainStudentTrainingPO">
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"/>
        <result column="course_id" property="courseId" jdbcType="INTEGER"/>
        <result column="course_name" property="courseName" jdbcType="VARCHAR"/>
        <result column="video_duration" property="videoDuration" jdbcType="TIMESTAMP"/>
        <result column="score" property="score" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="queryArchive" resultMap="SysDeptUserResultMap">
        SELECT sys.id,
        sys.user_id,
        sys.user_reality_name,
        sys.join_type,
        sys.tenant_code,
        sys.create_time,
        user.phone,
        balance.score as score,
        sys.avatar,
        sys.id_card,
        sys.gender


        FROM nbchat_sys_user_tenant AS sys
        INNER JOIN nbchat_user AS user
        INNER JOIN nbchat_user_balance AS balance

        ON sys.user_id = user.user_id
        and sys.tenant_code = balance.tenant_code
        and sys.user_id = balance.user_id

        <where>
            <if test="tenantCode != null and tenantCode != '' ">
                AND sys.tenant_code = #{tenantCode}
            </if>
            and sys.id = #{Id}
        </where>

    </select>

    <select id="queryIds" resultMap="SysDeptUserResultMap">
        SELECT u.id,d.tenant_code,d.user_id,d.dept_id FROM sys_dept_user_rel d
        LEFT JOIN nbchat_sys_user_tenant u on u.user_id=d.user_id
        <where>
            <if test="supportSubDept == 0">
                and d.dept_id = #{deptId}
            </if>
            <if test="supportSubDept == 1">
                and d.dept_id in (
                select dept_id from sys_dept where tenant_code =#{tenantCode} and find_in_set(#{deptId},ancestors)
                )
            </if>
            and u.tenant_code = #{tenantCode}
        </where>
    </select>

    <select id="queryVideoDuration" resultType="java.lang.Double">
        SELECT SUM(video_duration)
        FROM nbchat_train_sections
        <where>
            <if test="tenantCode != null and tenantCode != '' ">
                AND tenant_code = #{tenantCode}
            </if>
            and course_id = #{courseId}
        </where>

    </select>




</mapper>