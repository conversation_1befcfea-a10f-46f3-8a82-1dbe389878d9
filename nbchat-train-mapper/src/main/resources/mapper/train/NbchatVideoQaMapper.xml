<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatVideoQaMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatVideoQa" id="NbchatVideoQaMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="catalogId" column="catalog_id" jdbcType="VARCHAR"/>
        <result property="keyFrameTime" column="key_frame_time" jdbcType="INTEGER"/>
        <result property="qid" column="qid" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isValid" column="is_valid" jdbcType="INTEGER"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, user_id, tenant_code, course_id, catalog_id, key_frame_time, qid, create_time, is_valid</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatVideoQaMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_video_qa
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatVideoQaMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatVideoQa">
        select
          <include refid="Base_Column_List" />
        from nbchat_video_qa
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="catalogId != null and catalogId != ''">
                and catalog_id = #{catalogId}
            </if>
            <if test="keyFrameTime != null">
                and key_frame_time = #{keyFrameTime}
            </if>
            <if test="qid != null and qid != ''">
                and qid = #{qid}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="isValid != null">
                and is_valid = #{isValid}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatVideoQa">
        insert into nbchat_video_qa
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
            <if test="catalogId != null and catalogId != ''">
                catalog_id,
            </if>
            <if test="keyFrameTime != null">
                key_frame_time,
            </if>
            <if test="qid != null and qid != ''">
                qid,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
            <if test="catalogId != null and catalogId != ''">
                #{catalogId},
            </if>
            <if test="keyFrameTime != null">
                #{keyFrameTime},
            </if>
            <if test="qid != null and qid != ''">
                #{qid},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="isValid != null">
                #{isValid},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_video_qa(user_id, tenant_code, course_id, catalog_id, key_frame_time, qid, create_time, is_valid)
        values (#{userId}, #{tenantCode}, #{courseId}, #{catalogId}, #{keyFrameTime}, #{qid}, #{createTime}, #{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_video_qa
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                course_id = #{courseId},
            </if>
            <if test="catalogId != null and catalogId != ''">
                catalog_id = #{catalogId},
            </if>
            <if test="keyFrameTime != null">
                key_frame_time = #{keyFrameTime},
            </if>
            <if test="qid != null and qid != ''">
                qid = #{qid},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_video_qa where id = #{id}
    </delete>

</mapper>

