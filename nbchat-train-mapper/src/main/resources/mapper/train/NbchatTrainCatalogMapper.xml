<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog" >
    <id column="catalog_id" property="catalogId" jdbcType="VARCHAR" />
    <result column="course_id" property="courseId" jdbcType="VARCHAR" />
    <result column="file_id" property="fileId" jdbcType="VARCHAR" />
    <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="catalog_title" property="catalogTitle" jdbcType="VARCHAR" />
    <result column="catalog_level" property="catalogLevel" jdbcType="SMALLINT" />
    <result column="catalog_index" property="catalogIndex" jdbcType="SMALLINT" />
    <result column="parent_id" property="parentId" jdbcType="VARCHAR" />
    <result column="section_ids" property="sectionIds" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="train_count" property="trainCount" jdbcType="INTEGER" />
    <result column="is_valid" property="isValid" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    catalog_id, course_id, file_id, tenant_code, user_id, catalog_title, catalog_level,
    catalog_index, parent_id, section_ids, create_time,train_count,is_valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from nbchat_train_catalog
    where catalog_id = #{catalogId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from nbchat_train_catalog
    where catalog_id = #{catalogId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog" >
    insert into nbchat_train_catalog (catalog_id, course_id, file_id,
      tenant_code, user_id, catalog_title,
      catalog_level, catalog_index, parent_id,
      section_ids, create_time)
    values (#{catalogId,jdbcType=VARCHAR}, #{courseId,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR},
      #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{catalogTitle,jdbcType=VARCHAR},
      #{catalogLevel,jdbcType=SMALLINT}, #{catalogIndex,jdbcType=SMALLINT}, #{parentId,jdbcType=VARCHAR},
      #{sectionIds,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog" >
    insert into nbchat_train_catalog
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="catalogId != null" >
        catalog_id,
      </if>
      <if test="courseId != null" >
        course_id,
      </if>
      <if test="fileId != null" >
        file_id,
      </if>
      <if test="tenantCode != null" >
        tenant_code,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="catalogTitle != null" >
        catalog_title,
      </if>
      <if test="catalogLevel != null" >
        catalog_level,
      </if>
      <if test="catalogIndex != null" >
        catalog_index,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="sectionIds != null" >
        section_ids,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="catalogId != null" >
        #{catalogId,jdbcType=VARCHAR},
      </if>
      <if test="courseId != null" >
        #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="catalogTitle != null" >
        #{catalogTitle,jdbcType=VARCHAR},
      </if>
      <if test="catalogLevel != null" >
        #{catalogLevel,jdbcType=SMALLINT},
      </if>
      <if test="catalogIndex != null" >
        #{catalogIndex,jdbcType=SMALLINT},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="sectionIds != null" >
        #{sectionIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog" >
    update nbchat_train_catalog
    <set >
      <if test="courseId != null" >
        course_id = #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="catalogTitle != null" >
        catalog_title = #{catalogTitle,jdbcType=VARCHAR},
      </if>
      <if test="catalogLevel != null" >
        catalog_level = #{catalogLevel,jdbcType=SMALLINT},
      </if>
      <if test="catalogIndex != null" >
        catalog_index = #{catalogIndex,jdbcType=SMALLINT},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="sectionIds != null" >
        section_ids = #{sectionIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null" >
        is_valid = #{isValid,jdbcType=VARCHAR},
      </if>
    </set>
    where catalog_id = #{catalogId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog" >
    update nbchat_train_catalog
    set course_id = #{courseId,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      catalog_title = #{catalogTitle,jdbcType=VARCHAR},
      catalog_level = #{catalogLevel,jdbcType=SMALLINT},
      catalog_index = #{catalogIndex,jdbcType=SMALLINT},
      parent_id = #{parentId,jdbcType=VARCHAR},
      section_ids = #{sectionIds,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where catalog_id = #{catalogId,jdbcType=VARCHAR}
  </update>


  <select id="selectByCourseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_train_catalog
    where course_id = #{courseId,jdbcType=VARCHAR} and is_valid = '1'
    <if test=" catalogId != null and catalogId != '' ">
      and catalog_id = #{catalogId,jdbcType=VARCHAR}
    </if>
    <if test=" parentId != null and parentId != '' ">
      and parent_id = #{parentId,jdbcType=VARCHAR}
    </if>
    order by catalog_index,create_time
  </select>

  <select id="selectByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_train_catalog
    where length(section_ids) > 2
  </select>
    <select id="selectBySectionId" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM nbchat_train_catalog
      WHERE section_ids IS NOT NULL
      AND JSON_VALID(section_ids) = 1
      AND JSON_CONTAINS(section_ids, JSON_QUOTE(#{sectionId, jdbcType=VARCHAR}));
    </select>
  <select id="selectBySectionIdStr" resultType="java.lang.String" parameterType="java.lang.String">
      select catalog_title
      from nbchat_train_catalog
      where section_ids like concat('%', #{sectionId,jdbcType=VARCHAR}, '%' )
  </select>

  <update id="addTrainCount" >
    update nbchat_train_catalog
    set train_count = train_count + 1
            where course_id = #{courseId,jdbcType=VARCHAR}
              and catalog_id = #{catalogId,jdbcType=VARCHAR}
  </update>

  <update id="update">
    update nbchat_train_catalog
    set is_valid = 0
    where course_id = #{courseId}
  </update>
  <update id="delete">
    update nbchat_train_catalog
    set is_valid = 0
    where section_ids like concat('%',#{sectionId,jdbcType=VARCHAR},'%')
  </update>
  <update id="deleteById">
    update nbchat_train_catalog
    set is_valid = 0
    where catalog_id = #{catalogId,jdbcType=VARCHAR}
  </update>
</mapper>