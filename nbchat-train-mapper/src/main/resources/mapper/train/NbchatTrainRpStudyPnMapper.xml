<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainRpStudyPnMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn" id="NbchatTrainRpStudyPnMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="dateDay" column="date_day" jdbcType="TIMESTAMP"/>
        <result property="dateTime" column="date_time" jdbcType="TIMESTAMP"/>
        <result property="isFinish" column="is_finish" jdbcType="VARCHAR"/>
        <result property="studyCount" column="study_count" jdbcType="VARCHAR"/>
        <result property="studyEndCount" column="study_end_count" jdbcType="VARCHAR"/>
        <result property="studyEndRate" column="study_end_rate" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResMap" type="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn" extends="NbchatTrainRpStudyPnMap">
        <result property="value" column="value" jdbcType="INTEGER" />
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, tenant_code, course_id, user_id, date_day, date_time, is_finish</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatTrainRpStudyPnMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_rp_study_pn
        where id = #{id}
    </select>


    <select id="queryTop5" resultMap="ResMap">
        select
            course_id,
            count(1) as 'value'
        from nbchat_train_rp_study_pn
        where date_day = #{dateDay} and tenant_code = #{tenantCode}
        group by course_id
        order by count(1) desc
        limit 5
    </select>


    <select id="selectAll" resultMap="NbchatTrainRpStudyPnMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_rp_study_pn
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="dateDay != null">
                and date_day = date_format(#{dateDay},'%Y-%m-%d')
            </if>
            <if test="dateTime != null">
                and date_time = #{dateTime}
            </if>
            <if test="isFinish != null and isFinish != ''">
                and is_finish = #{isFinish}
            </if>
        </where>
    </select>

    <select id="getCourseRankingByTenantCode" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPnSelectCondition" resultMap="NbchatTrainRpStudyPnMap">
        SELECT
        course_id,
        COUNT(*) as study_count,
        SUM(CASE WHEN is_finish = '1' THEN 1 ELSE 0 END) as study_end_count,
        CASE WHEN COUNT(*) = 0 THEN 0 ELSE (SUM(CASE WHEN is_finish = '1' THEN 1 ELSE 0 END) / COUNT(*)) END as study_end_rate
        FROM nbchat_train_rp_study_pn pn
        WHERE tenant_code = #{tenantCode}
        <if test="startDate != null and endDate != null">
            AND date_day BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null">
            AND date_day &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null">
            AND date_day &lt;= #{endDate}
        </if>
        <if test="startDate == null and endDate == null">
            AND date_day BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
        </if>
        GROUP BY course_id
        ORDER BY
        <if test="sortField == 'studyCount'">study_count</if>
        <if test="sortField == 'studyEndCount'">study_end_count</if>
        <if test="sortField == 'studyEndRate'">study_end_rate</if>
        <if test="sortField == null or (sortField != 'studyCount' and sortField != 'studyEndCount' and sortField != 'studyEndRate')">study_count</if>
        <if test="sort == 1">DESC</if>
        <if test="sort != 1">ASC</if>
    </select>

    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn">
        insert into nbchat_train_rp_study_pn
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="dateDay != null">
                date_day,
            </if>
            <if test="dateTime != null">
                date_time,
            </if>
            <if test="isFinish != null and isFinish != ''">
                is_finish,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="dateDay != null">
                #{dateDay},
            </if>
            <if test="dateTime != null">
                #{dateTime},
            </if>
            <if test="isFinish != null and isFinish != ''">
                #{isFinish},
            </if>
        </trim>
    </insert>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_train_rp_study_pn(tenant_code, course_id, user_id, date_day, date_time, is_finish)
        values (#{tenantCode}, #{courseId}, #{userId}, #{dateDay}, #{dateTime}, #{isFinish})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_train_rp_study_pn
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                course_id = #{courseId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="dateDay != null">
                date_day = #{dateDay},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
            <if test="isFinish != null and isFinish != ''">
                is_finish = #{isFinish},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_train_rp_study_pn where id = #{id}
    </delete>

</mapper>

