<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhPipMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhPip" id="TdhPipMap">
        <result property="pipId" column="pip_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="pipType" column="pip_type" jdbcType="VARCHAR"/>
        <result property="pipDesc" column="pip_desc" jdbcType="VARCHAR"/>
        <result property="pipUrl" column="pip_url" jdbcType="VARCHAR"/>
        <result property="firstFrame" column="first_frame" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="sourceType" column="source_type" jdbcType="VARCHAR"/>

    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        source_type,pip_id, tenant_code,name,category, user_id, pip_type, pip_desc, pip_url, is_valid, create_time,first_frame</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhPipMap">
        select
          <include refid="Base_Column_List" />
        from tdh_pip
        where pip_id = #{pipId} and is_valid = '1'
    </select>
    
    
    <select id="selectAll" resultMap="TdhPipMap" parameterType="com.tydic.nbchat.train.mapper.po.TdhPip">
        select
          <include refid="Base_Column_List" />
        from tdh_pip
         <where>
            <if test="pipId != null and pipId != ''">
                and pip_id = #{pipId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="category !=null and category !='' ">
                and category = #{category}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="pipType != null and pipType != ''">
                and pip_type = #{pipType}
            </if>
            <if test="pipDesc != null and pipDesc != ''">
                and pip_desc = #{pipDesc}
            </if>
            <if test="pipUrl != null and pipUrl != ''">
                and pip_url = #{pipUrl}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
             <if test="sourceType != null and sourceType != ''">
                 and source_type = #{sourceType}
             </if>
        </where>
    </select>

    <select id="selectPipRecentHistoryByCondition" resultMap="TdhPipMap">
        select <include refid="Base_Column_List" />
        from tdh_pip
        <where>
            is_valid = '1'
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and (user_id = #{userId} or pip_source = '0')
            </if>
            <if test="pipTypes != null and pipTypes.size() > 0">
                and pip_type in
                <foreach item="type" index="index" collection="pipTypes" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
        </where>
        order by create_time desc
    </select>
    <insert id="insertSelective" keyProperty="pipId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.TdhPip">
        insert into tdh_pip
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="pipId != null and pipId != ''">
                pip_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="category !=null and category !='' ">
                category,
            </if>
            <if test="name !=null and name !='' ">
                name,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="pipType != null and pipType != ''">
                pip_type,
            </if>
            <if test="pipDesc != null and pipDesc != ''">
                pip_desc,
            </if>
            <if test="pipUrl != null and pipUrl != ''">
                pip_url,
            </if>
            <if test="firstFrame != null and firstFrame != ''">
                first_frame,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="pipSource != null and pipSource != ''">
                pip_source,
            </if>
            <if test="sourceType != null and sourceType != ''">
                source_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="pipId != null and pipId != ''">
                #{pipId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="category !=null and category !='' ">
                #{category},
            </if>
            <if test="name !=null and name !='' ">
                #{name},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="pipType != null and pipType != ''">
                #{pipType},
            </if>
            <if test="pipDesc != null and pipDesc != ''">
                #{pipDesc},
            </if>
            <if test="pipUrl != null and pipUrl != ''">
                #{pipUrl},
            </if>
            <if test="firstFrame != null and firstFrame != ''">
                #{firstFrame},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="pipSource != null and pipSource != ''">
                #{pipSource},
            </if>
            <if test="sourceType != null and sourceType != ''">
                #{sourceType},
            </if>
        </trim>
    </insert>

    <!--新增所有列-->
    <insert id="insert" keyProperty="pipId" useGeneratedKeys="true">
        insert into tdh_pip(tenant_code, user_id, pip_type, pip_desc, pip_url, is_valid, create_time)
        values (#{tenantCode}, #{userId}, #{pipType}, #{pipDesc}, #{pipUrl}, #{isValid}, #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_pip
        <set>
            <if test="category !=null and category !='' ">
                category = #{category},
            </if>
            <if test="name !=null and name !='' ">
                name = #{name},
            </if>
            <if test="pipType != null and pipType != ''">
                pip_type = #{pipType},
            </if>
            <if test="pipDesc != null and pipDesc != ''">
                pip_desc = #{pipDesc},
            </if>
            <if test="pipUrl != null and pipUrl != ''">
                pip_url = #{pipUrl},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where pip_id = #{pipId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tdh_pip where pip_id = #{pipId}
    </delete>

</mapper>

