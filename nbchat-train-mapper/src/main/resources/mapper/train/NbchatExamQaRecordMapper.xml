<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatExamQaRecordMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord" id="NbchatExamQaRecordMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="questionPaper" column="question_paper" jdbcType="VARCHAR"/>
        <result property="answerContent" column="answer_content" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, tenant_code,course_id, user_id, question_paper, answer_content, create_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatExamQaRecordMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_exam_qa_record
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatExamQaRecordMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord">
        select
          <include refid="Base_Column_List" />
        from nbchat_exam_qa_record
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
             <if test="courseId != null and courseId != ''">
                 and course_id = #{courseId}
             </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="questionPaper != null and questionPaper != ''">
                and question_paper = #{questionPaper}
            </if>
            <if test="answerContent != null and answerContent != ''">
                and answer_content = #{answerContent}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="selectRecord" resultMap="NbchatExamQaRecordMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord">
        select
          <include refid="Base_Column_List" />
        from nbchat_exam_qa_record
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
             <if test="courseId != null and courseId != ''">
                 and course_id = #{courseId}
             </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
        </where>
        order by create_time desc limit 1
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord">
        insert into nbchat_exam_qa_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="questionPaper != null and questionPaper != ''">
                question_paper,
            </if>
            <if test="answerContent != null and answerContent != ''">
                answer_content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null and id != ''">
                #{id},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="questionPaper != null and questionPaper != ''">
                #{questionPaper},
            </if>
            <if test="answerContent != null and answerContent != ''">
                #{answerContent},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
        </trim>
    </insert>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_exam_qa_record(tenant_codeuser_idquestion_paperanswer_contentcreate_time)
        values (#{tenantCode}#{userId}#{questionPaper}#{answerContent}#{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_exam_qa_record
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="questionPaper != null and questionPaper != ''">
                question_paper = #{questionPaper},
            </if>
            <if test="answerContent != null and answerContent != ''">
                answer_content = #{answerContent},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_exam_qa_record where id = #{id}
    </delete>

</mapper>

