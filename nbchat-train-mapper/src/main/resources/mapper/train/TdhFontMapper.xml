<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.TdhFontMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhFont" >
    <id column="font_id" property="fontId" jdbcType="INTEGER" />
    <result column="font_name" property="fontName" jdbcType="VARCHAR" />
    <result column="family" property="family" jdbcType="VARCHAR" />
    <result column="family_lang" property="familyLang" jdbcType="VARCHAR" />
    <result column="font_desc" property="fontDesc" jdbcType="VARCHAR" />
    <result column="full_name" property="fullName" jdbcType="VARCHAR" />
    <result column="font_path" property="fontPath" jdbcType="VARCHAR" />
    <result column="style" property="style" jdbcType="VARCHAR" />
    <result column="style_lang" property="styleLang" jdbcType="VARCHAR" />
    <result column="font_type" property="fontType" jdbcType="VARCHAR" />
    <result column="font_url" property="fontUrl" jdbcType="VARCHAR" />
    <result column="woff2_url" property="woff2Url" jdbcType="VARCHAR" />
    <result column="weight" property="weight" jdbcType="SMALLINT" />
    <result column="width" property="width" jdbcType="SMALLINT" />
    <result column="order_index" property="orderIndex" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    font_id, font_name, family, family_lang, font_desc, full_name, font_path, style, 
    style_lang, font_type, font_url, woff2_url, weight, width, order_index, create_time, 
    update_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tdh_font
    where font_id = #{fontId,jdbcType=INTEGER}
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tdh_font
    where font_id = #{fontId,jdbcType=INTEGER}
  </delete>

  <select id="selectByCondition"
          parameterType="com.tydic.nbchat.train.mapper.po.TdhFontQueryCondition"
          resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tdh_font
        <where>
            <if test="fontId != null">
                and font_id = #{fontId,jdbcType=INTEGER}
            </if>
            <if test="family != null and family!= ''">
                and family = #{family,jdbcType=VARCHAR}
            </if>
            <if test="familyLang != null and familyLang!= ''">
                and family_lang = #{familyLang,jdbcType=VARCHAR}
            </if>
            <if test="style != null and style!= ''">
                and style = #{style,jdbcType=VARCHAR}
            </if>
            <if test="fontType != null and fontType!= ''">
                and font_type = #{fontType,jdbcType=VARCHAR}
            </if>
             <if test="isValid != null">
                and is_valid = #{isValid,jdbcType=VARCHAR}
            </if>
        </where>
        order by order_index ,font_id, create_time desc
  </select>

  <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhFont" >
    insert into tdh_font
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="fontId != null" >
        font_id,
      </if>
      <if test="fontName != null" >
        font_name,
      </if>
      <if test="family != null" >
        family,
      </if>
      <if test="familyLang != null" >
        family_lang,
      </if>
      <if test="fontDesc != null" >
        font_desc,
      </if>
      <if test="fullName != null" >
        full_name,
      </if>
      <if test="fontPath != null" >
        font_path,
      </if>
      <if test="style != null" >
        style,
      </if>
      <if test="styleLang != null" >
        style_lang,
      </if>
      <if test="fontType != null" >
        font_type,
      </if>
      <if test="fontUrl != null" >
        font_url,
      </if>
      <if test="woff2Url != null" >
        woff2_url,
      </if>
      <if test="weight != null" >
        weight,
      </if>
      <if test="width != null" >
        width,
      </if>
      <if test="orderIndex != null" >
        order_index,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="fontId != null" >
        #{fontId,jdbcType=INTEGER},
      </if>
      <if test="fontName != null" >
        #{fontName,jdbcType=VARCHAR},
      </if>
      <if test="family != null" >
        #{family,jdbcType=VARCHAR},
      </if>
      <if test="familyLang != null" >
        #{familyLang,jdbcType=VARCHAR},
      </if>
      <if test="fontDesc != null" >
        #{fontDesc,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null" >
        #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="fontPath != null" >
        #{fontPath,jdbcType=VARCHAR},
      </if>
      <if test="style != null" >
        #{style,jdbcType=VARCHAR},
      </if>
      <if test="styleLang != null" >
        #{styleLang,jdbcType=VARCHAR},
      </if>
      <if test="fontType != null" >
        #{fontType,jdbcType=VARCHAR},
      </if>
      <if test="fontUrl != null" >
        #{fontUrl,jdbcType=VARCHAR},
      </if>
      <if test="woff2Url != null" >
        #{woff2Url,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        #{weight,jdbcType=SMALLINT},
      </if>
      <if test="width != null" >
        #{width,jdbcType=SMALLINT},
      </if>
      <if test="orderIndex != null" >
        #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhFont" >
    update tdh_font
    <set >
      <if test="fontName != null" >
        font_name = #{fontName,jdbcType=VARCHAR},
      </if>
      <if test="family != null" >
        family = #{family,jdbcType=VARCHAR},
      </if>
      <if test="familyLang != null" >
        family_lang = #{familyLang,jdbcType=VARCHAR},
      </if>
      <if test="fontDesc != null" >
        font_desc = #{fontDesc,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null" >
        full_name = #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="fontPath != null" >
        font_path = #{fontPath,jdbcType=VARCHAR},
      </if>
      <if test="style != null" >
        style = #{style,jdbcType=VARCHAR},
      </if>
      <if test="styleLang != null" >
        style_lang = #{styleLang,jdbcType=VARCHAR},
      </if>
      <if test="fontType != null" >
        font_type = #{fontType,jdbcType=VARCHAR},
      </if>
      <if test="fontUrl != null" >
        font_url = #{fontUrl,jdbcType=VARCHAR},
      </if>
      <if test="woff2Url != null" >
        woff2_url = #{woff2Url,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        weight = #{weight,jdbcType=SMALLINT},
      </if>
      <if test="width != null" >
        width = #{width,jdbcType=SMALLINT},
      </if>
      <if test="orderIndex != null" >
        order_index = #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where font_id = #{fontId,jdbcType=INTEGER}
  </update>

</mapper>