<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhCreationTaskMapper">
    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhCreationTask" id="TdhCreationTaskMap">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="sectionId" column="section_id" jdbcType="VARCHAR"/>
        <result property="creationId" column="creation_id" jdbcType="VARCHAR"/>
        <result property="creationName" column="creation_name" jdbcType="VARCHAR"/>
        <result property="creationContent" column="creation_content" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="queueTime" column="queue_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="partCountTotal" column="part_count_total" jdbcType="INTEGER"/>
        <result property="partCountDone" column="part_count_done" jdbcType="INTEGER"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="playUrl" column="play_url" jdbcType="VARCHAR"/>
        <result property="videoDuration" column="video_duration" jdbcType="VARCHAR"/>
        <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
        <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="isShare" column="is_share" jdbcType="VARCHAR"/>
        <result property="waterMark" column="water_mark" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="creationSource" column="creation_source" jdbcType="VARCHAR"/>
        <result property="creationType" column="creation_type" jdbcType="VARCHAR"/>
        <result property="previewUrl" column="preview_url" jdbcType="VARCHAR"/>
        <result property="idleQueue" column="idle_queue" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="videoListMap" type="com.tydic.nbchat.train.mapper.po.NbchatVideoDTO">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="sectionId" column="section_id" jdbcType="VARCHAR"/>
        <result property="creationId" column="creation_id" jdbcType="VARCHAR"/>
        <result property="creationName" column="creation_name" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="queueTime" column="queue_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="partCountTotal" column="part_count_total" jdbcType="INTEGER"/>
        <result property="partCountDone" column="part_count_done" jdbcType="INTEGER"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="playUrl" column="play_url" jdbcType="VARCHAR"/>
        <result property="videoDuration" column="video_duration" jdbcType="VARCHAR"/>
        <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
        <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="isShare" column="is_share" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName"/>
        <result column="creation_source" property="creationSource"/>
        <result column="creator_name" property="creatorName"/>
        <result column="phone" property="phone"/>
        <result column="preview_url" property="previewUrl"/>
        <result column="user_type" property="userType"/>
        <result column="vip_status" property="vipStatus"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="waterMark" column="water_mark" jdbcType="VARCHAR"/>
        <result property="idleQueue" column="idle_queue" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        task_id,
        user_id,
        tenant_code,
        course_id,
        section_id,
        creation_id,
        creation_name,
        creation_content,
        start_time,
        end_time,
        create_time,
        part_count_total,
        part_count_done,
        video_url,
        task_state,
        verify_state,
        play_url,
        user_type,
        idle_queue,
        error_code,
        error_desc,
        video_duration,
        queue_time,
        is_valid,
        is_share,
        water_mark,
        file_url
    </sql>

    <sql id="Base_Simple_Column_List">
        task_id,
        user_id,
        tenant_code,
        course_id,
        section_id,
        creation_id,
        creation_name,
        start_time,
        end_time,
        create_time,
        part_count_total,
        part_count_done,
        video_url,
        task_state,
        verify_state,
        play_url,
        user_type,
        idle_queue,
        error_code,
        error_desc,
        video_duration,
        queue_time,
        is_valid,
        is_share,
        water_mark,
        file_url
    </sql>

    <select id="selectSimpleByPrimaryKey" resultMap="TdhCreationTaskMap">
        select
        <include refid="Base_Simple_Column_List"/>
        from tdh_creation_task
        where task_id = #{taskId}
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhCreationTaskMap">
        select tct.task_id,
        tct.user_id,
        tct.tenant_code,
        tct.course_id,
        tct.section_id,
        tct.creation_id,
        tct.creation_name,
        tct.creation_content,
        tct.start_time,
        tct.end_time,
        tct.part_count_total,
        tct.part_count_done,
        tct.video_url,
        tct.task_state,
        tct.verify_state,
        tct.play_url,
        tcr.preview_url,
        tct.error_code,
        tct.error_desc,
        tct.video_duration,
        tct.queue_time,
        tct.is_valid,
        tct.is_share,
        tct.water_mark,
        tct.file_url,
        tcr.creation_source,
        tcr.creation_type
        from tdh_creation_task tct
        left join tdh_creation_record tcr on tct.creation_id = tcr.creation_id
        where tct.task_id = #{taskId}
    </select>

    <select id="selectQueueTasks" resultType="com.tydic.nbchat.train.mapper.po.TdhQueueInfo">
        select task_id as taskId,
        idle_queue as idleQueue,
        user_type as userType
        from tdh_creation_task where task_state = 'q'
        and is_valid = '1'
        <if test="startTime != null">
            and queue_time > #{startTime}
        </if>
        order by queue_time, start_time
    </select>


    <select id="selectAll" resultMap="TdhCreationTaskMap"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationTask">
        select
        <include refid="Base_Column_List"/>
        from tdh_creation_task
        <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="sectionId != null and sectionId != ''">
                and section_id = #{sectionId}
            </if>
            <if test="creationId != null and creationId != ''">
                and creation_id = #{creationId}
            </if>
            <if test="creationName != null and creationName != ''">
                and creation_name = #{creationName}
            </if>
            <if test="creationContent != null and creationContent != ''">
                and creation_content = #{creationContent}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="partCountTotal != null">
                and part_count_total = #{partCountTotal}
            </if>
            <if test="partCountDone != null">
                and part_count_done = #{partCountDone}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl}
            </if>
            <if test="taskState != null and taskState != ''">
                and task_state = #{taskState}
            </if>
            <if test="verifyState != null and verifyState != ''">
                and verify_state = #{verifyState}
            </if>
            <if test="errorCode != null and errorCode != ''">
                and error_code = #{errorCode}
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                and error_desc = #{errorDesc}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="isShare != null and isShare != ''">
                and is_share = #{isShare}
            </if>
            <if test="waterMark != null and waterMark != ''">
                and water_mark = #{waterMark}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
        </where>
        order by start_time desc
    </select>


    <insert id="insertSelective" keyProperty="taskId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationTask">
        insert into tdh_creation_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creationId != null and creationId != ''">
                creation_id,
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name,
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="partCountTotal != null">
                part_count_total,
            </if>
            <if test="partCountDone != null">
                part_count_done,
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url,
            </if>
            <if test="taskState != null and taskState != ''">
                task_state,
            </if>
            <if test="verifyState != null and verifyState != ''">
                verify_state,
            </if>
            <if test="errorCode != null and errorCode != ''">
                error_code,
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                error_desc,
            </if>
            <if test="waterMark != null and waterMark != ''">
                water_mark,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creationId != null and creationId != ''">
                #{creationId},
            </if>
            <if test="creationName != null and creationName != ''">
                #{creationName},
            </if>
            <if test="creationContent != null and creationContent != ''">
                #{creationContent},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="partCountTotal != null">
                #{partCountTotal},
            </if>
            <if test="partCountDone != null">
                #{partCountDone},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                #{videoUrl},
            </if>
            <if test="taskState != null and taskState != ''">
                #{taskState},
            </if>
            <if test="verifyState != null and verifyState != ''">
                #{verifyState},
            </if>
            <if test="errorCode != null and errorCode != ''">
                #{errorCode},
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                #{errorDesc},
            </if>
            <if test="waterMark != null and waterMark != ''">
                #{waterMark},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="taskId" useGeneratedKeys="true">
        insert into tdh_creation_task(creation_id, creation_name, creation_content, start_time, end_time,
        part_count_total, part_count_done, video_url, task_state, verify_state,
        error_code, error_desc)
        values (#{creationId}, #{creationName}, #{creationContent}, #{startTime}, #{endTime}, #{partCountTotal},
        #{partCountDone}, #{videoUrl}, #{taskState}, #{verifyState}, #{errorCode}, #{errorDesc})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_creation_task
        <set>
            <if test="creationId != null and creationId != ''">
                creation_id = #{creationId},
            </if>
            <if test="courseId != null">
                course_id = #{courseId},
            </if>
            <if test="sectionId != null">
                section_id = #{sectionId},
            </if>
            <if test="creationName != null and creationName != ''">
                creation_name = #{creationName},
            </if>
            <if test="creationContent != null and creationContent != ''">
                creation_content = #{creationContent},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="partCountTotal != null">
                part_count_total = #{partCountTotal},
            </if>
            <if test="partCountDone != null">
                part_count_done = #{partCountDone},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url = #{videoUrl},
            </if>
            <if test="taskState != null and taskState != ''">
                task_state = #{taskState},
            </if>
            <if test="verifyState != null and verifyState != ''">
                verify_state = #{verifyState},
            </if>
            <if test="errorCode != null and errorCode != ''">
                error_code = #{errorCode},
            </if>
            <if test="errorDesc != null and errorDesc != ''">
                error_desc = #{errorDesc},
            </if>
            <if test="isShare != null and isShare != ''">
                is_share = #{isShare},
            </if>
            <if test="waterMark != null and waterMark != ''">
                water_mark = #{waterMark},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
        </set>
        where task_id = #{taskId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update tdh_creation_task
        set is_valid = '0'
        where task_id = #{taskId}
    </delete>


    <select id="getVideoCount" resultType="int">
        SELECT COUNT(*)
        FROM tdh_creation_task
        WHERE
        <choose>
            <!-- 如果没有传入参数或传入0，查询当天的数据 -->
            <when test="timeRange == 0 or timeRange == null">
                DATE(create_time) = CURDATE()
            </when>
            <!-- 如果传入了数字（29或6等），查询前一天开始，直到这个数字之前的数据 -->
            <when test="timeRange != 0 and timeRange != null">
                create_time >= DATE_SUB(CURDATE(), INTERVAL #{timeRange} DAY)
                AND create_time
                &lt; DATE_SUB(CURDATE()
                , INTERVAL 0 DAY)
            </when>
            <!-- 默认情况下查询当天的数据 -->
            <otherwise>
                DATE (create_time) = CURDATE()
            </otherwise>
        </choose>
        <choose>
            <when test="userType != null and userType == 0">
                AND tenant_code = '00000000'
            </when>
            <when test="userType == null or userType == ''">
                <!-- 不添加任何 SQL -->
            </when>
            <otherwise>
                AND tenant_code != '00000000'
            </otherwise>
        </choose>
    </select>

    <select id="getVideoList" resultMap="videoListMap">
        SELECT c.creation_id,
        c.creation_source,
        c.preview_url,
        u.user_name AS creator_name,
        u.phone AS phone,
        u.user_type,
        u.vip_status,
        u.company_name,
        v.task_id,
        v.user_id,
        v.tenant_code,
        v.course_id,
        v.section_id,
        v.creation_id,
        v.creation_name,
        v.start_time,
        v.end_time,
        v.part_count_total,
        v.part_count_done,
        v.video_url,
        v.task_state,
        v.verify_state,
        v.play_url,
        v.error_code,
        v.error_desc,
        v.video_duration,
        v.queue_time,
        v.is_valid,
        v.is_share,
        v.water_mark,
        v.file_url,
        v.idle_queue,
        v.create_time
        FROM tdh_creation_task v
        LEFT JOIN tdh_creation_record c ON c.creation_id = v.creation_id
        LEFT JOIN op_rp_user_detail u
        ON v.user_id = u.user_id
        AND v.tenant_code = u.tenant_code
        <where>
            <!-- 视频类型 -->
            <if test="creationSource != null and creationSource != ''">
                AND c.creation_source = #{creationSource}
            </if>
            <!-- 制作时长 -->
            <if test="minVideoDuration != null">
                AND v.video_duration >= #{minVideoDuration}
            </if>
            <if test="maxVideoDuration != null">
                AND v.video_duration &lt;= #{maxVideoDuration}
            </if>
            <!-- 内容分类 -->
            <if test="creationType != null and creationType != ''">
                AND c.creation_type = #{creationType}
            </if>
            <!-- 付费状态 -->
            <if test="isPay == '1'.toString()">
                and u.total_pay_amount > 0
            </if>
            <if test="isPay == '0'.toString()">
                and u.total_pay_amount = 0
            </if>
            <!-- 用户属性 -->
            <if test="userType != null and userType != ''">
                AND u.user_type = #{userType}
            </if>
            <!-- 企业名称 -->
            <if test="companyName != null and companyName != ''">
                AND u.company_name LIKE CONCAT('%', #{companyName}, '%')
            </if>
            <if test="targetTenantCode != null and targetTenantCode != ''">
                AND v.tenant_code = #{targetTenantCode}
            </if>
            <!-- 时间范围 -->
            <if test="startTime != null">
                AND v.start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND v.start_time &lt;= #{endTime}
            </if>
            <if test="createTimeBegin != null and createTimeEnd != null">
                AND v.create_time between #{createTimeBegin} and #{createTimeEnd}
            </if>
            <!-- 视频状态 -->
            <if test="taskStateList != null and !taskStateList.isEmpty()">
                AND v.task_state IN
                <foreach item="item" collection="taskStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="idleQueue != null and idleQueue != ''">
                AND v.idle_queue = #{idleQueue}
            </if>
            <if test="isValid != null and isValid != ''">
                AND v.is_valid = #{isValid}
            </if>
            <!-- 手机号 -->
            <if test="phone != null and phone != ''">
                AND u.phone = #{phone}
            </if>
        </where>
        ORDER BY v.start_time DESC
    </select>
    <select id="selectTdhForAnalysis" resultType="com.tydic.nbchat.train.mapper.po.TdhAnalysisDTO">
        <![CDATA[
        SELECT task_id                                                                                                                                                                     AS taskId,
               left(regexp_replace(replace(replace(replace(replace(concat(creation_content -> '$.parts[*].content'), '", "', ''), '["', ''), '"]', ''), '\\"', '"'), '<[^>]+>', ''), 5000) AS oralDraft
        FROM tdh_creation_task
        WHERE task_state = '1'
          AND end_time BETWEEN current_date - INTERVAL 1 DAY AND current_date
          AND task_id = 'ecac23c63aad6a8c328c63681c907fbf'
        ]]>
    </select>
</mapper>
