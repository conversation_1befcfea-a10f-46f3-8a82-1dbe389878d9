<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatTrainCourse">
        <id column="course_id" property="courseId" jdbcType="VARCHAR"/>
        <result column="course_name" property="courseName" jdbcType="VARCHAR"/>
        <result column="major_id" property="majorId" jdbcType="VARCHAR"/>
        <result column="course_desc" property="courseDesc" jdbcType="VARCHAR"/>
        <result column="course_source" property="courseSource" jdbcType="SMALLINT"/>
        <result column="labels" property="labels" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_show" property="isShow" jdbcType="CHAR"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
        <result column="order_index" property="orderIndex" jdbcType="INTEGER"/>
        <result column="ext_info" property="extInfo" jdbcType="VARCHAR"/>
        <result column="voice_url" property="voiceUrl" jdbcType="VARCHAR"/>
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR"/>
        <result column="video_img" property="videoImg" jdbcType="VARCHAR"/>
        <result column="category2" property="category2" jdbcType="VARCHAR"/>
        <result column="class_hour" property="classHour" jdbcType="REAL"/>
        <result column="course_state" property="courseState" jdbcType="VARCHAR"/>
        <result column="scene_state" property="sceneState" jdbcType="VARCHAR"/>
        <result column="test_paper_state" property="testPaperState" jdbcType="VARCHAR"/>
        <result column="step_state" property="stepState" jdbcType="VARCHAR"/>
        <result column="task_state" property="taskState" jdbcType="VARCHAR"/>
        <result column="course_file_url" property="courseFileUrl" jdbcType="VARCHAR"/>
        <result column="task_fail_desc" property="taskFailDesc" jdbcType="VARCHAR"/>
        <result column="video_source" property="videoSource" jdbcType="INTEGER"/>
        <result column="fast_study_video" property="fastStudyVideo" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="course_type" property="courseType" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        course_id, course_name, course_desc, course_source, labels, category, img_avatar,
    tenant_code, user_id, create_time, update_time, is_show, is_valid, order_index, ext_info, 
    class_hour,voice_url,video_url,video_img,category2,course_state,step_state,
    task_state,course_file_url,scene_state,test_paper_state,major_id,task_fail_desc,video_source,
      fast_study_video, dept_id,course_type
    </sql>

    <update id="updateClassHour">
        update nbchat_train_course t1,
            (select count(1) tcount from nbchat_train_sections where course_id = #{courseId} and is_valid = 1) t2
        set t1.class_hour = t2.tcount
        where t1.course_id = #{courseId}
    </update>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from nbchat_train_course
        where course_id = #{courseId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from nbchat_train_course
        where course_id = #{courseId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourse">
        insert into nbchat_train_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">
                course_id,
            </if>
            <if test="majorId != null">
                major_id,
            </if>
            <if test="courseName != null">
                course_name,
            </if>
            <if test="courseDesc != null">
                course_desc,
            </if>
            <if test="courseSource != null">
                course_source,
            </if>
            <if test="labels != null">
                labels,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="category2 != null">
                category2,
            </if>
            <if test="imgAvatar != null">
                img_avatar,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isShow != null">
                is_show,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="classHour != null">
                class_hour,
            </if>
            <if test="courseFileUrl != null">
                course_file_url,
            </if>
            <if test="videoUrl != null">
                video_url,
            </if>
            <if test="videoImg != null">
                video_img,
            </if>
            <if test="stepState != null">
                step_state,
            </if>
            <if test="courseState != null">
                course_state,
            </if>
            <if test="taskState != null">
                task_state,
            </if>
            <if test="sceneState != null">
                scene_state,
            </if>
            <if test="testPaperState != null">
                test_paper_state,
            </if>
            <if test="videoSource !=null ">
                video_source,
            </if>
            <if test="fastStudyVideo != null">
                fast_study_video,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="courseType != null">
                course_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">
                #{courseId,jdbcType=VARCHAR},
            </if>
            <if test="majorId != null">
                #{majorId,jdbcType=VARCHAR},
            </if>
            <if test="courseName != null">
                #{courseName,jdbcType=VARCHAR},
            </if>
            <if test="courseDesc != null">
                #{courseDesc,jdbcType=VARCHAR},
            </if>
            <if test="courseSource != null">
                #{courseSource,jdbcType=SMALLINT},
            </if>
            <if test="labels != null">
                #{labels,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="category2 != null">
                #{category2,jdbcType=VARCHAR},
            </if>
            <if test="imgAvatar != null">
                #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isShow != null">
                #{isShow,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="classHour != null">
                #{classHour,jdbcType=REAL},
            </if>
            <if test="courseFileUrl != null">
                #{courseFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null">
                #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="videoImg != null">
                #{videoImg,jdbcType=VARCHAR},
            </if>
            <if test="stepState != null">
                #{stepState,jdbcType=VARCHAR},
            </if>
            <if test="courseState != null">
                #{courseState,jdbcType=VARCHAR},
            </if>
            <if test="taskState != null">
                #{taskState,jdbcType=VARCHAR},
            </if>
            <if test="sceneState != null">
                #{sceneState,jdbcType=VARCHAR},
            </if>
            <if test="testPaperState != null">
                #{testPaperState,jdbcType=VARCHAR},
            </if>
            <if test="videoSource !=null ">
                #{videoSource,jdbcType=INTEGER},
            </if>
            <if test="fastStudyVideo != null">
                #{fastStudyVideo,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="courseType != null">
                #{courseType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourse">
        update nbchat_train_course
        <set>
            <if test="fastStudyVideo != null and fastStudyVideo != ''">
                fast_study_video = #{fastStudyVideo,jdbcType=VARCHAR},
            </if>
            <if test="sceneState != null">
                scene_state = #{sceneState,jdbcType=VARCHAR},
            </if>
            <if test="courseName != null">
                course_name = #{courseName,jdbcType=VARCHAR},
            </if>
            <if test="courseDesc != null">
                course_desc = #{courseDesc,jdbcType=VARCHAR},
            </if>
            <if test="courseSource != null">
                course_source = #{courseSource,jdbcType=SMALLINT},
            </if>
            <if test="labels != null">
                labels = #{labels,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="category2 != null">
                category2 = #{category2,jdbcType=VARCHAR},
            </if>
            <if test="imgAvatar != null">
                img_avatar = #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isShow != null">
                is_show = #{isShow,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="classHour != null">
                class_hour = #{classHour,jdbcType=REAL},
            </if>
            <if test="voiceUrl != null">
                voice_url = #{voiceUrl,jdbcType=VARCHAR},
            </if>
            <if test="courseFileUrl != null">
                course_file_url = #{courseFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null">
                video_url = #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="videoImg != null">
                video_img = #{videoImg,jdbcType=VARCHAR},
            </if>
            <if test="stepState != null">
                step_state = #{stepState,jdbcType=VARCHAR},
            </if>
            <if test="courseState != null">
                course_state = #{courseState,jdbcType=VARCHAR},
            </if>
            <if test="taskState != null">
                task_state = #{taskState,jdbcType=VARCHAR},
            </if>
            <if test="testPaperState != null">
                test_paper_state = #{testPaperState,jdbcType=VARCHAR},
            </if>
            <if test="taskFailDesc != null">
                task_fail_desc = #{taskFailDesc,jdbcType=VARCHAR},
            </if>
            <if test="videoSource !=null">
                video_source = #{videoSource,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
        </set>
        where course_id = #{courseId,jdbcType=VARCHAR}
    </update>

    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourseSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_train_course
        <where>
            <if test="courseName != null and courseName != ''">
                and course_name like concat('%',#{courseName,jdbcType=VARCHAR},'%')
            </if>
            <if test="courseSource != null">
                and course_source = #{courseSource,jdbcType=SMALLINT}
            </if>
            <if test="labels != null and labels != ''">
                and labels = #{labels,jdbcType=VARCHAR}
            </if>
            <if test="category != null and category != ''">
                and category = #{category,jdbcType=VARCHAR}
            </if>
            <if test="category2 != null and category2 != ''">
                and category2 = #{category2,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != ''">
                and (user_id = #{userId,jdbcType=VARCHAR} or course_source = 0 )
            </if>
            <if test="isShow != null and isShow != ''">
                and is_show = #{isShow,jdbcType=CHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
            <if test="keyword != null and keyword !=''">
                and ( course_name like concat('%',#{keyword,jdbcType=VARCHAR},'%')
                or course_desc like concat('%',#{keyword,jdbcType=VARCHAR},'%') )
            </if>
            <if test="courseState != null and courseState != ''">
                and course_state = #{courseState,jdbcType=CHAR}
            </if>
            <if test="stepState != null and stepState != ''">
                and step_state = #{stepState,jdbcType=CHAR}
            </if>
            <if test="taskState != null and taskState != ''">
                and task_state = #{taskState,jdbcType=CHAR}
            </if>
            <if test="sceneState != null and sceneState != ''">
                and scene_state = #{sceneState,jdbcType=CHAR}
            </if>
            <if test="testPaperState != null and testPaperState != ''">
                and test_paper_state = #{testPaperState,jdbcType=CHAR}
            </if>
            <if test="courseTypes != null and courseTypes.size() > 0">
                and course_type in
                <foreach collection="courseTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deptId!=null">
                <if test='deptScope=="0"'>
                    and dept_id = #{deptId}
                </if>
                <if test='deptScope=="1"'>
                    and dept_id in (
                    select dept_id from sys_dept where tenant_code = #{tenantCode} and find_in_set(#{deptId},ancestors)
                    )
                </if>
            </if>
        </where>
        order by order_index asc,update_time desc
    </select>
    <select id="selectCourseByCondition"
            parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourseSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_train_course
        <where>
            <if test="courseName != null and courseName != ''">
                and course_name like concat('%',#{courseName,jdbcType=VARCHAR},'%')
            </if>
            <if test="courseSource != null">
                and course_source = #{courseSource,jdbcType=SMALLINT}
            </if>
            <if test="labels != null and labels != ''">
                and labels = #{labels,jdbcType=VARCHAR}
            </if>
            <if test="category != null and category != ''">
                and category = #{category,jdbcType=VARCHAR}
            </if>
            <if test="category2 != null and category2 != ''">
                and category2 = #{category2,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != ''">
                and (user_id = #{userId,jdbcType=VARCHAR} or course_source = 0 )
            </if>
            <if test="isShow != null and isShow != ''">
                and is_show = #{isShow,jdbcType=CHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
            <if test="keyword != null and keyword !=''">
                and ( course_name like concat('%',#{keyword,jdbcType=VARCHAR},'%')
                or course_desc like concat('%',#{keyword,jdbcType=VARCHAR},'%') )
            </if>
            <if test="courseState != null and courseState != ''">
                and course_state = #{courseState,jdbcType=CHAR}
            </if>
            <if test="stepState != null and stepState != ''">
                and step_state = #{stepState,jdbcType=CHAR}
            </if>
            <if test="taskState != null and taskState != ''">
                and task_state = #{taskState,jdbcType=CHAR}
            </if>
            <if test="sceneState != null and sceneState != ''">
                and scene_state = #{sceneState,jdbcType=CHAR}
            </if>
            <if test="testPaperState != null and testPaperState != ''">
                and test_paper_state = #{testPaperState,jdbcType=CHAR}
            </if>
            <if test="deptId!=null">
              <if test='deptScope=="0"'>
                and dept_id = #{deptId}
              </if>
              <if test='deptScope=="1"'>
                and dept_id in (
                select dept_id from sys_dept where tenant_code = #{tenantCode} and find_in_set(#{deptId},ancestors)
                )
              </if>
            </if>
            <if test="courseTypes != null and courseTypes.size() > 0">
                and course_type in
                <foreach collection="courseTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by update_time
        <if test="sort == 1">ASC</if>
        <if test="sort != 1">DESC</if>
    </select>

    <update id="updateStepState">
        update nbchat_train_course
        set step_state = #{stepState}
        where course_id = #{courseId}
          and #{stepState} > step_state
    </update>
</mapper>