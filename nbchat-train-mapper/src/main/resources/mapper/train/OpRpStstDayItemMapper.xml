<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.OpRpStstDayItemMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.OpRpStstDayItem" id="OpRpStstDayItemMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemValue" column="item_value" jdbcType="INTEGER"/>
        <result property="countDay" column="count_day" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatSysTenant" >
        <id column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR" />
        <result column="tenant_desc" property="tenantDesc" jdbcType="VARCHAR" />
        <result column="domain" property="domain" jdbcType="VARCHAR" />
        <result column="linkman" property="linkman" jdbcType="VARCHAR" />
        <result column="contact_number" property="contactNumber" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="is_valid" property="isValid" jdbcType="CHAR" />
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR" />
        <result column="ext_info" property="extInfo" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        iduser_idtenant_codeitem_codeitem_valuecount_daycreated_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="OpRpStstDayItemMap">
        select
        <include refid="Base_Column_List"/>
        from op_rp_stst_day_item
        where id = #{id}
    </select>


    <select id="selectAll" resultMap="OpRpStstDayItemMap"
            parameterType="com.tydic.nbchat.train.mapper.po.OpRpStstDayItem">
        select
        <include refid="Base_Column_List"/>
        from op_rp_stst_day_item
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code = #{itemCode}
            </if>
            <if test="itemValue != null">
                and item_value = #{itemValue}
            </if>
            <if test="countDay != null">
                and count_day = #{countDay}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
        </where>
    </select>
    <select id="selectLastLoginTime" resultType="java.util.Date">
        SELECT updated_time from nbchat_user
        <where>
            <if test="tenantCode != null and tenantCode != ''">
                AND tenant_code = #{tenantCode}
            </if>
        </where>
         ORDER BY created_time desc limit 1;
    </select>
    <select id="selectCommonTenantLastLoginTime" resultType="java.util.Date">
        SELECT updated_time from nbchat_user
        where user_id in (
            select user_id from nbchat_user_enterprise
            <where>
                <if test="tenantName != null and tenantName != ''">
                    AND company_name = #{tenantName}
                </if>
            </where>
        )
        ORDER BY created_time desc limit 1;
    </select>
    <select id="selectTenantList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT tenant_code, tenant_name, create_time FROM nbchat_sys_tenant WHERE tenant_code NOT IN('00000000','000TYDIC','00000TMO','0000CSZH') and tenant_name NOT REGEXP '迪科|tydic|天源'
        UNION ALL
        SELECT '00000000' AS tenant_code, company_name AS tenant_name, create_time FROM nbchat_user_enterprise where company_name NOT REGEXP '迪科|tydic|天源'
        GROUP BY tenant_name
        ) AS combined_results
        <where>
            <if test="tenantName != null and tenantName != ''">
                AND tenant_name LIKE concat(concat("%",#{tenantName}),"%")
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                AND tenant_code = #{tenantCode}
            </if>
             <if test="startTime != null and endTime !=null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectTenantDataList" resultMap="OpRpStstDayItemMap">
        SELECT sum(item_value) as item_value,tenant_code,item_code
        from  op_rp_stst_day_item op
        <where>
            tenant_code not in ('000TYDIC','0000CSZH','00000000','00000TMO') and tenant_code NOT REGEXP '迪科|tydic|天源'
            <if test="tenantCode != null and tenantCode != ''">
                AND tenant_code = #{tenantCode}
            </if>
            <if test="startTime != null and endTime !=null">
            AND count_day BETWEEN #{startTime} AND #{endTime}
            </if>
            GROUP BY tenant_code,item_code
        </where>

    </select>
    <select id="decideProfessionalTenant" resultMap="BaseResultMap">
        select * from nbchat_sys_tenant
        where tenant_code = #{tenantCode}
    </select>
    <select id="decideCommonTenant" resultMap="BaseResultMap">
        select company_name as tenant_name , create_time from nbchat_user_enterprise
        where company_name = #{tenantName} order by create_time asc  limit 1
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.train.mapper.po.OpRpStstDayItem">
        insert into op_rp_stst_day_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="itemCode != null and itemCode != ''">
                item_code,
            </if>
            <if test="itemValue != null">
                item_value,
            </if>
            <if test="countDay != null">
                count_day,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="itemCode != null and itemCode != ''">
                #{itemCode},
            </if>
            <if test="itemValue != null">
                #{itemValue},
            </if>
            <if test="countDay != null">
                #{countDay},
            </if>
            <if test="createdTime != null">
                #{createdTime},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into op_rp_stst_day_item(user_idtenant_codeitem_codeitem_valuecount_daycreated_time)
        values (#{userId}#{tenantCode}#{itemCode}#{itemValue}#{countDay}#{createdTime})
    </insert>

    <insert id="countUserNewItems" parameterType="com.tydic.nbchat.train.mapper.po.OpRpStstDayItem">
        INSERT INTO op_rp_stst_day_item (tenant_code,item_code,item_value,count_day,created_time)
        SELECT tenant_code,#{itemCode},count(user_id),DATE_FORMAT(created_time,'%Y-%m-%d'),NOW() from nbchat_user
        WHERE created_time BETWEEN #{startTime} AND #{endTime} AND tenant_code NOT IN ('00000000','000TYDIC','00000TMO','0000CSZH')  AND is_deleted ='0' GROUP BY DATE_FORMAT(created_time,'%Y-%m-%d'), tenant_code;
    </insert>
    <insert id="countQuestionCreatorsNewItems">
            INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
            SELECT user_id,tenant_code,#{itemCode},count(id),DATE_FORMAT(created_time,'%Y-%m-%d'),NOW() from op_rp_stst_pt
            WHERE created_time BETWEEN #{startTime} AND #{endTime} AND tenant_code NOT IN ('00000000','000TYDIC','00000TMO','0000CSZH') AND tenant_code NOT REGEXP '迪科|tydic|天源' and item_code = #{itemCode} GROUP BY DATE_FORMAT(created_time,'%Y-%m-%d'), tenant_code;
    </insert>
    <insert id="countVideoCreatorsNewItems">
        INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
        SELECT  video.user_id,video.tenant_code, #{itemCode} as category, count(video.task_id) as task_count, DATE_FORMAT(video.start_time,'%Y-%m-%d') as start_date , NOW() as now_time from tdh_creation_task video
        WHERE
            video.start_time BETWEEN #{startTime} AND #{endTime}
            AND video.tenant_code NOT IN ( '00000000', '000TYDIC', '00000TMO', '0000CSZH' )
            AND video.user_id NOT IN ( SELECT user_id FROM nbchat_user WHERE user_type = '0' )
        GROUP BY
            DATE_FORMAT( video.start_time, '%Y-%m-%d' ),
            video.user_id;
    </insert>
    <insert id="countCommonTenantVideoCreatorsNewItems">
        INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
        SELECT video.user_id,ue.company_name,#{itemCode},count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
        left join nbchat_user_enterprise ue on video.user_id = ue.user_id
        WHERE video.start_time BETWEEN #{startTime} AND #{endTime} AND video.tenant_code = '00000000' AND ue.company_name NOT REGEXP '迪科|tydic|天源'
        GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),video.user_id;
    </insert>
    <insert id="countVideoSuccessNewItems">
        INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
        SELECT  video.user_id,video.tenant_code, #{itemCode} as category, count(video.task_id) as task_count, DATE_FORMAT(video.start_time,'%Y-%m-%d') as start_date , NOW() as now_time from tdh_creation_task video
        WHERE
            video.start_time BETWEEN #{startTime} AND #{endTime}
          AND video.tenant_code NOT IN ( '00000000', '000TYDIC', '00000TMO', '0000CSZH' )
          AND video.user_id NOT IN ( SELECT user_id FROM nbchat_user WHERE user_type = '0' )
          AND video.task_state !='q' AND video.task_state = 1
        GROUP BY
            DATE_FORMAT( video.start_time, '%Y-%m-%d' ),
            video.user_id;
    </insert>
    <insert id="countCommonTenantVideoSuccessNewItems">
        INSERT INTO op_rp_stst_day_item (user_id,tenant_code,item_code,item_value,count_day,created_time)
        SELECT video.user_id,ue.company_name,#{itemCode},count(video.task_id),DATE_FORMAT(video.start_time,'%Y-%m-%d'),NOW() from tdh_creation_task video
        left join nbchat_user_enterprise ue on video.user_id = ue.user_id
        WHERE video.start_time BETWEEN #{startTime} AND #{endTime} AND video.tenant_code = '00000000' AND ue.company_name NOT REGEXP '迪科|tydic|天源' AND video.task_state !='q' AND video.task_state = 1
        GROUP BY DATE_FORMAT(video.start_time,'%Y-%m-%d'),video.user_id;
    </insert>
    <insert id="countEnterpriseUserNewItems">
        INSERT INTO op_rp_stst_day_item (tenant_code,item_code,item_value,count_day,created_time)
        SELECT company_name,#{itemCode},count(user_id),DATE_FORMAT(create_time,'%Y-%m-%d'),NOW() from nbchat_user_enterprise
        WHERE create_time BETWEEN #{startTime} AND #{endTime} AND company_name NOT REGEXP '迪科|tydic|天源' GROUP BY DATE_FORMAT(create_time,'%Y-%m-%d'),company_name;
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update op_rp_stst_day_item
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="itemCode != null and itemCode != ''">
                item_code = #{itemCode},
            </if>
            <if test="itemValue != null">
                item_value = #{itemValue},
            </if>
            <if test="countDay != null">
                count_day = #{countDay},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from op_rp_stst_day_item
        where id = #{id}
    </delete>
    <delete id="deleteItem">
        DELETE FROM op_rp_stst_day_item
        WHERE count_day = DATE_FORMAT(#{startTime},'%Y-%m-%d') AND item_code = #{itemCode}
    </delete>

</mapper>

