<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatExamTestRecordMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecord" >
    <id column="test_id" property="testId" jdbcType="VARCHAR" />
    <result column="course_id" property="courseId" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="score" property="score" jdbcType="SMALLINT" />
    <result column="test_paper_id" property="testPaperId" jdbcType="VARCHAR" />
    <result column="exam_time" property="examTime" jdbcType="SMALLINT" />
    <result column="is_valid" property="isValid" jdbcType="CHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordWithBLOBs" extends="BaseResultMap" >
    <result column="test_paper_content" property="testPaperContent" jdbcType="LONGVARCHAR" />
    <result column="answer_content" property="answerContent" jdbcType="LONGVARCHAR" />
  </resultMap>
  <resultMap id="ResultMapNbchatExamTestRecordCodition" type="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordCodition">
    <result column="failed" property="failed" jdbcType="VARCHAR" />
    <result column="pass" property="pass" jdbcType="VARCHAR" />
    <result column="good" property="good" jdbcType="VARCHAR" />
    <result column="excellent" property="excellent" jdbcType="VARCHAR" />
    <result column="failed_students" property="failedStudents" jdbcType="VARCHAR" />
    <result column="pass_students" property="passStudents" jdbcType="VARCHAR" />
    <result column="good_students" property="goodStudents" jdbcType="VARCHAR" />
    <result column="excellent_students" property="excellentStudents" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    test_id, course_id, user_id, tenant_code, start_time, end_time, score, test_paper_id, 
    exam_time, is_valid
  </sql>
  <sql id="Blob_Column_List" >
    test_id, course_id, user_id, tenant_code, start_time, end_time, score, test_paper_id,
    exam_time, is_valid,test_paper_content, answer_content
  </sql>

  <select id="queryValidTestPaper" resultMap="ResultMapWithBLOBs" >
    select
        <include refid="Blob_Column_List" />
    from nbchat_exam_test_record
    where course_id = #{courseId}
      and user_id = #{userId}
      and tenant_code = #{tenantCode}
      and DATE_ADD(start_time, INTERVAL 120 MINUTE) > NOW()
      and end_time is null
      limit 1
  </select>

  <select id="queryScore" resultType="java.lang.Integer">
    select
        ifnull(score,0) as score
    from nbchat_exam_test_record
    where course_id = #{courseId} and user_id = #{userId} and end_time is not null
    order by end_time desc
    limit 1
  </select>

  <select id="queryByCondition" resultMap="ResultMapWithBLOBs">
    select
        <include refid="Blob_Column_List" />
    from nbchat_exam_test_record
    where course_id = #{courseId}
    and user_id = #{userId}
    and end_time is not null
    <choose>
      <when test="queryType == 1">
        order by end_time desc
      </when>
      <otherwise>
        order by score desc
      </otherwise>
    </choose>
    limit 1
  </select>

  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from nbchat_exam_test_record
    where test_id = #{testId,jdbcType=VARCHAR}
  </select>
  <select id="getCompositeScore" resultMap="ResultMapNbchatExamTestRecordCodition" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordCodition">
    SELECT
      ROUND((SUM(JSON_UNQUOTE(item_values->>'$[0]."0-59"')) / IFNULL(COUNT(*), 1)) * 100, 2) AS failed,
      ROUND((SUM(JSON_UNQUOTE(item_values->>'$[1]."60-79"')) / IFNULL(COUNT(*), 1)) * 100, 2) AS pass,
      ROUND((SUM(JSON_UNQUOTE(item_values->>'$[2]."80-89"')) / IFNULL(COUNT(*), 1)) * 100, 2) AS good,
      ROUND((SUM(JSON_UNQUOTE(item_values->>'$[3]."90-100"')) / IFNULL(COUNT(*), 1)) * 100, 2) AS excellent,
      SUM(JSON_UNQUOTE(item_values->>'$[0]."0-59"')) AS failed_students,
      SUM(JSON_UNQUOTE(item_values->>'$[1]."60-79"')) AS pass_students,
      SUM(JSON_UNQUOTE(item_values->>'$[2]."80-89"')) AS good_students,
      SUM(JSON_UNQUOTE(item_values->>'$[3]."90-100"')) AS excellent_students
    FROM
      nbchat_train_rp_day_item
    WHERE
      tenant_code = #{tenantCode} AND item_code = 'exam_comprehensive_score_count';
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from nbchat_exam_test_record
    where test_id = #{testId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordWithBLOBs" >
    insert into nbchat_exam_test_record (test_id, course_id, user_id, 
      tenant_code, start_time, end_time, 
      score, test_paper_id, exam_time, 
      is_valid, test_paper_content, answer_content
      )
    values (#{testId,jdbcType=VARCHAR}, #{courseId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{tenantCode,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{score,jdbcType=SMALLINT}, #{testPaperId,jdbcType=VARCHAR}, #{examTime,jdbcType=SMALLINT}, 
      #{isValid,jdbcType=CHAR}, #{testPaperContent,jdbcType=LONGVARCHAR}, #{answerContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordWithBLOBs" >
    insert into nbchat_exam_test_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="testId != null" >
        test_id,
      </if>
      <if test="courseId != null" >
        course_id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="tenantCode != null" >
        tenant_code,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="score != null" >
        score,
      </if>
      <if test="testPaperId != null" >
        test_paper_id,
      </if>
      <if test="examTime != null" >
        exam_time,
      </if>
      <if test="isValid != null" >
        is_valid,
      </if>
      <if test="testPaperContent != null" >
        test_paper_content,
      </if>
      <if test="answerContent != null" >
        answer_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="testId != null" >
        #{testId,jdbcType=VARCHAR},
      </if>
      <if test="courseId != null" >
        #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="score != null" >
        #{score,jdbcType=SMALLINT},
      </if>
      <if test="testPaperId != null" >
        #{testPaperId,jdbcType=VARCHAR},
      </if>
      <if test="examTime != null" >
        #{examTime,jdbcType=SMALLINT},
      </if>
      <if test="isValid != null" >
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="testPaperContent != null" >
        #{testPaperContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="answerContent != null" >
        #{answerContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordWithBLOBs" >
    update nbchat_exam_test_record
    <set >
      <if test="courseId != null" >
        course_id = #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="score != null" >
        score = #{score,jdbcType=SMALLINT},
      </if>
      <if test="testPaperId != null" >
        test_paper_id = #{testPaperId,jdbcType=VARCHAR},
      </if>
      <if test="examTime != null" >
        exam_time = #{examTime,jdbcType=SMALLINT},
      </if>
      <if test="isValid != null" >
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="testPaperContent != null" >
        test_paper_content = #{testPaperContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="answerContent != null" >
        answer_content = #{answerContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where test_id = #{testId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordWithBLOBs" >
    update nbchat_exam_test_record
    set course_id = #{courseId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      score = #{score,jdbcType=SMALLINT},
      test_paper_id = #{testPaperId,jdbcType=VARCHAR},
      exam_time = #{examTime,jdbcType=SMALLINT},
      is_valid = #{isValid,jdbcType=CHAR},
      test_paper_content = #{testPaperContent,jdbcType=LONGVARCHAR},
      answer_content = #{answerContent,jdbcType=LONGVARCHAR}
    where test_id = #{testId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamTestRecord" >
    update nbchat_exam_test_record
    set course_id = #{courseId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      score = #{score,jdbcType=SMALLINT},
      test_paper_id = #{testPaperId,jdbcType=VARCHAR},
      exam_time = #{examTime,jdbcType=SMALLINT},
      is_valid = #{isValid,jdbcType=CHAR}
    where test_id = #{testId,jdbcType=VARCHAR}
  </update>
</mapper>