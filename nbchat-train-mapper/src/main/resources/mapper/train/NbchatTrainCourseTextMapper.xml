<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainCourseTextMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatTrainCourseText" id="NbchatTrainCourseTextMap">
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="text" column="text" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        course_id, text, update_time, file_name, file_url</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatTrainCourseTextMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_course_text
        where course_id = #{courseId}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatTrainCourseTextMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourseText">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_course_text
         <where>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="text != null and text != ''">
                and text = #{text}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="courseId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainCourseText">
        insert into nbchat_train_course_text
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
            <if test="text != null and text != ''">
                text,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
            <if test="text != null and text != ''">
                #{text},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="courseId" useGeneratedKeys="true">
        insert into nbchat_train_course_text(text, update_time)
        values (#{text}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_train_course_text
        <set>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where course_id = #{courseId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_train_course_text where course_id = #{courseId}
    </delete>

</mapper>

