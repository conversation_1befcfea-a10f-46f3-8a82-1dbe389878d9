package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhBackground;

import java.util.List;

/**
 * 数字人-背景(TdhBackground)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-22 15:37:29
 */
public interface TdhBackgroundMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param objectId 主键
     * @return 实例对象
     */
    TdhBackground queryById(String objectId);

    List<TdhBackground> selectAll(TdhBackground tdhBackground);

    /**
     * 新增数据
     *
     * @param tdhBackground 实例对象
     * @return 影响行数
     */
    int insert(TdhBackground tdhBackground);


    int insertSelective(TdhBackground tdhBackground);

      /**
     * 修改数据
     *
     * @param tdhBackground 实例对象
     * @return 影响行数
     */
    int update(TdhBackground tdhBackground);

    /**
     * 通过主键删除数据
     *
     * @param objectId 主键
     * @return 影响行数
     */
    int deleteById(String objectId);

}

