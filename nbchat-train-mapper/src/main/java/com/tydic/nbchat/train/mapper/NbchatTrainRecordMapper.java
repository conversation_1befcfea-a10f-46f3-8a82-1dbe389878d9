package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainRecord;
import com.tydic.nbchat.train.mapper.po.NbchatUserTrainRecord;
import com.tydic.nbchat.train.mapper.po.NbchatUserTrainRecordSelectCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatTrainRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(NbchatTrainRecord record);

    int insertSelective(NbchatTrainRecord record);

    NbchatTrainRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(NbchatTrainRecord record);

    int updateByPrimaryKey(NbchatTrainRecord record);

    /**
     * 用户学习记录查询
     * @param condition
     * @return
     */
    List<NbchatUserTrainRecord> selectUserTrainRecord(NbchatUserTrainRecordSelectCondition condition);

    List<NbchatTrainRecord> selectTrainRecord(NbchatTrainRecord condition);

    /**
     * 查询课程
     * @param userId
     * @param courseId
     * @return
     */
    NbchatTrainRecord selectByUserAndCourseId(@Param("userId") String userId,
                                              @Param("courseId") String courseId);

    int countLearnSections(@Param("courseId") String courseId);

    int countTrained(@Param("courseId") String courseId);
    int countStar(@Param("courseId") String courseId);

    //存在视频
    int countVideo(@Param("courseId") String courseId);
}