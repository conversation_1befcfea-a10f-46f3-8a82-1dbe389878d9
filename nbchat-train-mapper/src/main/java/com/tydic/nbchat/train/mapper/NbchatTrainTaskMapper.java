package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainTask;

import java.util.List;

/**
 * 学习任务表：打包配置多个课程(NbchatTrainTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-10 17:50:52
 */
public interface NbchatTrainTaskMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatTrainTask queryById(Integer id);
    NbchatTrainTask queryTask(Integer id);

    List<NbchatTrainTask> selectAll(NbchatTrainTask nbchatTrainTask);
    List<NbchatTrainTask> selectList(NbchatTrainTask nbchatTrainTask);
    List<NbchatTrainTask> selectAllBySubDept(NbchatTrainTask nbchatTrainTask);

    /**
     * 新增数据
     *
     * @param nbchatTrainTask 实例对象
     * @return 影响行数
     */
    int insert(NbchatTrainTask nbchatTrainTask);


    int insertSelective(NbchatTrainTask nbchatTrainTask);

      /**
     * 修改数据
     *
     * @param nbchatTrainTask 实例对象
     * @return 影响行数
     */
    int update(NbchatTrainTask nbchatTrainTask);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

