package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhNoticeTask;

import java.util.List;

/**
 * 任务完成通知(TdhNoticeTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-11 14:21:27
 */
public interface TdhNoticeTaskMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TdhNoticeTask queryById(Long id);

    List<TdhNoticeTask> selectAll(TdhNoticeTask tdhNoticeTask);
    List<TdhNoticeTask> selectFin();
    List<TdhNoticeTask> selectCustFin();

    /**
     * 新增数据
     *
     * @param tdhNoticeTask 实例对象
     * @return 影响行数
     */
    int insert(TdhNoticeTask tdhNoticeTask);


    int insertSelective(TdhNoticeTask tdhNoticeTask);

      /**
     * 修改数据
     *
     * @param tdhNoticeTask 实例对象
     * @return 影响行数
     */
    int update(TdhNoticeTask tdhNoticeTask);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

