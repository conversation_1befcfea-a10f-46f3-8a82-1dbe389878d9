package com.tydic.nbchat.train.mapper;
import org.apache.ibatis.annotations.Param;

import com.tydic.nbchat.train.mapper.po.PptTheme;

import java.util.List;

/**
 * (PptTheme)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-02 17:27:05
 */
public interface PptThemeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param themeId 主键
     * @return 实例对象
     */
    PptTheme queryById(String themeId);

    List<PptTheme> selectAll(PptTheme pptTheme);

    /**
     * 新增数据
     *
     * @param pptTheme 实例对象
     * @return 影响行数
     */
    int insert(PptTheme pptTheme);


    int insertSelective(PptTheme pptTheme);

      /**
     * 修改数据
     *
     * @param pptTheme 实例对象
     * @return 影响行数
     */
    int update(PptTheme pptTheme);

    /**
     * 通过主键删除数据
     *
     * @param themeId 主键
     * @return 影响行数
     */
    int deleteById(String themeId);

    /**
     * 批量更新数据
     * @param pptThemes
     * @return
     */
    int updateList(@Param("list") List<PptTheme> pptThemes);

    List<PptTheme> findByScene(@Param("scene")String scene);

    /**
     * 随机返回一个主题模版ID
     * @return
     */
    String findRandomThemeId();

    /**
     * 查询收藏主题
     * @param cond
     * @return
     */
    List<PptTheme> selectStarThemeByCondition(PptTheme cond);
}

