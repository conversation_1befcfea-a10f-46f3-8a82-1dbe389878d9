package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (PptSvg)实体类
 *
 * <AUTHOR>
 * @since 2024-01-22 15:34:44
 */
@Data
public class PPTSvg implements Serializable {
    private static final long serialVersionUID = 413164468255372320L;
/**
     * 主键id
     */
    private String svgId;

    private String tenantCode;

    private String userId;
/**
     * 0系统内置 1自定义
     */
    private String svgSource;
/**
     * svg模板内容
     */
    private String content;
/**
     * 配置。艺术字配置，其他配置
     */
    private String config;
/**
     * 0形状 1艺术字
     */
    private String type;
/**
     * 优先级1-9 数字小优先高
     */
    private String orderIndex;
/**
     * 0失效
     */
    private String isValid;
/**
     * 创建时间
     */
    private Date createTime;


}

