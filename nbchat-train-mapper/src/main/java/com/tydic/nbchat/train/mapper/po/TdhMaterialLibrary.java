package com.tydic.nbchat.train.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 图库素材表
 */
@Data
public class TdhMaterialLibrary {
    private String id;

    /**
    * 素材包id
    */
    private String pkgId;

    /**
    * 素材名称
    */
    private String name;

    /**
    * 素材地址
    */
    private String url;

    /**
    * 素材描述
    */
    private String desc;

    /**
    * 高度
    */
    private Short height;

    /**
    * 宽度
    */
    private Short width;

    /**
    * 图片比例
    */
    private String ratio;

    /**
    * 资源大小:字节
    */
    private Integer size;

    /**
    * 创建人
    */
    private String createUser;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 是否有效：0-无效，1-有效
    */
    private String isValid;
}