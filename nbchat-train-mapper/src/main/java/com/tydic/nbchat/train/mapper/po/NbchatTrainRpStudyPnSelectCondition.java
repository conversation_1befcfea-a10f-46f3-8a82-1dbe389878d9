package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/07/07
 * @email <EMAIL>
 * @description nbchat火车rp研究pn选择条件
 */
@Data
public class NbchatTrainRpStudyPnSelectCondition {
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 选择查询日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序
     */
    private String sort;
}
