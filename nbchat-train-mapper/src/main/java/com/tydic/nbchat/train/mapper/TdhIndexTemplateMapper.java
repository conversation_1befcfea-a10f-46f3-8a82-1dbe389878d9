package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhIndexTemplate;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TdhIndexTemplateMapper {
    /**
     * delete by primary key
     *
     * @param tplId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String tplId);

    int deleteByPrimaryKeyIn(List<String> list);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(TdhIndexTemplate record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(TdhIndexTemplate record);

    /**
     * select by primary key
     *
     * @param tplId primary key
     * @return object by primary key
     */
    TdhIndexTemplate selectByPrimaryKey(String tplId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TdhIndexTemplate record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TdhIndexTemplate record);

    int updateBatchSelective(@Param("list") List<TdhIndexTemplate> list);

    int batchInsert(@Param("list") List<TdhIndexTemplate> list);

    int insertOrUpdate(TdhIndexTemplate record);

    int insertOrUpdateSelective(TdhIndexTemplate record);

    List<TdhIndexTemplate> selectList(TdhIndexTemplate tdhIndexTemplate);

    int updateBatch(List<TdhIndexTemplate> list);

    int updateBatchList(List<TdhIndexTemplate> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<TdhIndexTemplate> list);
}