package com.tydic.nbchat.train.mapper.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (PptCreationRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-01-02 17:23:48
 */
@Data
public class PptCreationRecord implements Serializable {
    private static final long serialVersionUID = -86888953164651196L;
    private String pptType; //0:普通ppt 1:美化ppt
    private String layout;  //布局列表

    private String fileUrl;
    private String fileId;
    private String fileVector;

    /**
     * 主键
     */
    private String pptId;

    private String tenantCode;
    /**
     * 用户
     */
    private String userId;
    /**
     * 关联主题
     */
    private String themeId;
    /**
     * 创作名称
     */
    private String creationName;
    /**
     * 文档标题:json
     */
    private String titleContent;
    /**
     * ppt创作内容
     */
    private String creationContent;
    //创作方式 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
    private String creationType;
    /**
     * ppt页数
     */
    private Integer partCount;
    /**
     * ai解析内容
     */
    private String aiContent;
    /**
     * 预览图
     */
    private String previewUrl;
    /**
     * 是否有效 0 否 1 是
     */
    private String isValid;
    /**
     * 是否共享 0 否 1 是
     */
    private String isShare;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date updateTime;

}

