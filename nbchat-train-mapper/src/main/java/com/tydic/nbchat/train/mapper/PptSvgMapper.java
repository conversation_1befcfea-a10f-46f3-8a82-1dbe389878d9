package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.PPTSvg;

import java.util.List;

/**
 * (PptSvg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-22 15:34:44
 */
public interface PptSvgMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param svgId 主键
     * @return 实例对象
     */
    PPTSvg queryById(String svgId);

    List<PPTSvg> selectAll(PPTSvg pptSvg);

    /**
     * 新增数据
     *
     * @param pptSvg 实例对象
     * @return 影响行数
     */
    int insert(PPTSvg pptSvg);


    int insertSelective(PPTSvg pptSvg);

      /**
     * 修改数据
     *
     * @param pptSvg 实例对象
     * @return 影响行数
     */
    int update(PPTSvg pptSvg);

    /**
     * 通过主键删除数据
     *
     * @param svgId 主键
     * @return 影响行数
     */
    int deleteById(String svgId);

}

