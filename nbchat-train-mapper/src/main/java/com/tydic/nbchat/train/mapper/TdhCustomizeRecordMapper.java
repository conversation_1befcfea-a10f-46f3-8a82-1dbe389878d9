package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecordCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TdhCustomizeRecordMapper {

    int updateOrderStatus(TdhCustomizeRecord po);

    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(TdhCustomizeRecord record);

    int insertOrUpdate(TdhCustomizeRecord record);

    int insertOrUpdateSelective(TdhCustomizeRecord record);

    int insertSelective(TdhCustomizeRecord record);

    TdhCustomizeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TdhCustomizeRecord record);

    int updateByPrimaryKey(TdhCustomizeRecord record);

    int updateBatch(List<TdhCustomizeRecord> list);

    int updateBatchSelective(List<TdhCustomizeRecord> list);

    int batchInsert(@Param("list") List<TdhCustomizeRecord> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<TdhCustomizeRecord> list);

    List<TdhCustomizeRecord> findByAll(TdhCustomizeRecord tdhCustomizeRecord);

    List<TdhCustomizeRecordCondition> list(TdhCustomizeRecordCondition condition);

    TdhCustomizeRecord findByOrderNoAndUserId(@Param("orderNo")String orderNo,@Param("userId")String userId);

    int updateByOrderNo(@Param("updated")TdhCustomizeRecord updated,@Param("orderNo")String orderNo);

    TdhCustomizeRecord findByOrderNo(@Param("orderNo")String orderNo);

    int updateByOrderNoAndUserId(@Param("updated")TdhCustomizeRecord updated);

    int updateViewTime(@Param("orderNo")String orderNo,@Param("userId")String userId);
}