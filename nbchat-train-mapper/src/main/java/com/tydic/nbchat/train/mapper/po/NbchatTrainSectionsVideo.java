package com.tydic.nbchat.train.mapper.po;

import java.util.Date;
import java.io.Serializable;

/**
 * 课程章节视频统计(NbchatTrainSectionsVideo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-14 16:41:59
 */
public class NbchatTrainSectionsVideo implements Serializable {
    private static final long serialVersionUID = -98835473934801474L;
    /**
     * 主键自增
     */
    private Long id;
    /**
     * 课程id
     */
    private String courseId;
    
    private String sectionId;
    
    private String tenantCode;
    
    private String userId;
    
    private Date createTime;
    /**
     * 1 收藏
     */
    private String isStar;
    /**
     * 1 点赞
     */
    private String isZan;
    /**
     * 观看进度 s
     */
    private Integer viewProcess;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getSectionId() {
        return sectionId;
    }

    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getIsStar() {
        return isStar;
    }

    public void setIsStar(String isStar) {
        this.isStar = isStar;
    }

    public String getIsZan() {
        return isZan;
    }

    public void setIsZan(String isZan) {
        this.isZan = isZan;
    }

    public Integer getViewProcess() {
        return viewProcess;
    }

    public void setViewProcess(Integer viewProcess) {
        this.viewProcess = viewProcess;
    }

}

