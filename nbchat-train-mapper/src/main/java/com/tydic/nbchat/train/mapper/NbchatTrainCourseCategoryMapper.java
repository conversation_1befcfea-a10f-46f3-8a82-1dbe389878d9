package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainCourseCategory;

import java.util.List;

/**
 * (NbchatTrainCourseCategory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-28 17:41:41
 */
public interface NbchatTrainCourseCategoryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param cateId 主键
     * @return 实例对象
     */
    NbchatTrainCourseCategory queryById(String cateId);

    List<NbchatTrainCourseCategory> selectAll(NbchatTrainCourseCategory nbchatTrainCourseCategory);

    /**
     * 新增数据
     *
     * @param nbchatTrainCourseCategory 实例对象
     * @return 影响行数
     */
    int insert(NbchatTrainCourseCategory nbchatTrainCourseCategory);


    int insertSelective(NbchatTrainCourseCategory nbchatTrainCourseCategory);

      /**
     * 修改数据
     *
     * @param nbchatTrainCourseCategory 实例对象
     * @return 影响行数
     */
    int update(NbchatTrainCourseCategory nbchatTrainCourseCategory);

    /**
     * 通过主键删除数据
     *
     * @param cateId 主键
     * @return 影响行数
     */
    int deleteById(String cateId);

}

