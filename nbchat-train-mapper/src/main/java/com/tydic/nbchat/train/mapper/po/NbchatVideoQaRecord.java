package com.tydic.nbchat.train.mapper.po;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatVideoQaRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-09-20 16:07:22
 */
public class NbchatVideoQaRecord implements Serializable {
    private static final long serialVersionUID = 711078095124954748L;
    
    private Integer id;
    
    private String tenantCode;
    
    private String userId;
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 课程目录id
     */
    private String catalogId;
    /**
     * 答题内容
     */
    private String answerContent;
    /**
     * 提交时间
     */
    private Date createTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCatalogId() {
        return catalogId;
    }

    public void setCatalogId(String catalogId) {
        this.catalogId = catalogId;
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}

