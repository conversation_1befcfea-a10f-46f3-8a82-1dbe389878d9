package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatTrainCourseCategory)实体类
 *
 * <AUTHOR>
 * @since 2023-06-28 17:41:41
 */
@Data
public class NbchatTrainCourseCategory implements Serializable {
    private static final long serialVersionUID = 286613025581711194L;
    private Integer limit;

    /**
     * 目录id
     */
    private String cateId;
    /**
     * 目录名称
     */
    private String cateName;
    private String cateNameNick;
    /**
     * 上级id
     */
    private String parentId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 租户
     */
    private String tenantCode;
    
    private String isValid;
    /**
     * 分类级别 1 2 
     */
    private Integer cateLevel;
    private Integer orderIndex;


}

