package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 数字人-视频生成任务记录(TdhCreationTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-22 15:51:12
 */
public interface TdhCreationTaskMapper {

    TdhCreationTask selectSimpleByPrimaryKey(String taskId);

    /**
     * 通过ID查询单条数据
     *
     * @param taskId 主键
     * @return 实例对象
     */
    TdhCreationTask queryById(String taskId);

    /**
     * 查询排队用户
     *
     * @param startTime
     * @return
     */
    List<TdhQueueInfo> selectQueueTasks(@Param("startTime") Date startTime);

    /**
     * 查询全部
     *
     * @param tdhCreationTask
     * @return
     */
    List<TdhCreationTask> selectAll(TdhCreationTask tdhCreationTask);

    /**
     * 新增数据
     *
     * @param tdhCreationTask 实例对象
     * @return 影响行数
     */
    int insert(TdhCreationTask tdhCreationTask);

    int insertSelective(TdhCreationTask tdhCreationTask);

    /**
     * 修改数据
     *
     * @param tdhCreationTask 实例对象
     * @return 影响行数
     */
    int update(TdhCreationTask tdhCreationTask);

    /**
     * 通过主键删除数据
     *
     * @param taskId 主键
     * @return 影响行数
     */
    int deleteById(String taskId);

    /**
     * 查询视频制作数量
     *
     * @param timeRange
     * @return
     */
    int getVideoCount(@Param("timeRange") int timeRange, @Param("userType") String userType);

    /**
     * 查询视频列表
     *
     * @param condition
     * @return
     */
    List<NbchatVideoDTO> getVideoList(NbchatVideoCondition condition);

    List<TdhAnalysisDTO> selectTdhForAnalysis();

}
