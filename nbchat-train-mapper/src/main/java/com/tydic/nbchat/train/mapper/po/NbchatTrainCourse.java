package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;

@Data
public class NbchatTrainCourse {
    private String courseType; //课程类型 1-普通课程 2-训战课程 3-考试课程
    private String taskFailDesc; //失败原因

    private String majorId; //专属机器人id
    private String courseFileUrl; //课程文件地址

    private String courseState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String sceneState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String testPaperState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String stepState; //1.基础信息  2.课程文档已解析  3.课程简介生成  4.目录生成  5.学习内容生成
    private String taskState; //0 任务未执行 1 任务执行中 2 任务异常
    private String fastStudyVideo;    //是否允许快进视频
    private String deptId;

    private String videoUrl;
    private String videoImg;
    private String voiceUrl;
    private String courseId;

    private String courseName;

    private String courseDesc;

    private Short courseSource;

    private String labels; //课件名称

    private String category;
    private String category2;

    private String imgAvatar;

    private String tenantCode;

    private String userId;

    private Date createTime;

    private Date updateTime;

    private String isShow;

    private String isValid;

    private Integer orderIndex;

    private String extInfo;

    private Float classHour;
    /**
     * 0系统生成 1用户上传
     */
    private Integer videoSource;


}