package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourseSelectCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatTrainCourseMapper {
    int deleteByPrimaryKey(String courseId);

    int updateClassHour(@Param("courseId")String courseId);

    int insertSelective(NbchatTrainCourse record);

    NbchatTrainCourse selectByPrimaryKey(String courseId);

    int updateByPrimaryKeySelective(NbchatTrainCourse record);

    List<NbchatTrainCourse> selectByCondition(NbchatTrainCourseSelectCondition condition);

    List<NbchatTrainCourse> selectCourseByCondition(NbchatTrainCourseSelectCondition condition);

    int updateStepState(@Param("courseId")String courseId,@Param("stepState")String stepState);
}