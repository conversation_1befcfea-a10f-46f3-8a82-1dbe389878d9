package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainFilesPart;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface NbchatTrainFilesPartMapper {

    /**
     * 批量插入段落
     * @param parts
     * @return
     */
    int insertPartBatch(@Param("parts") List<NbchatTrainFilesPart> parts);

    /**
     * 查询段落
     * @param fileId
     * @return
     */
    List<NbchatTrainFilesPart> selectByFileId(String fileId);

    /**
     * 根据文档查询多个
     * @param fileIds
     * @return
     */
    List<NbchatTrainFilesPart> selectByFileIds(@Param("fileIds") List<String> fileIds);

    /**
     * 条件更新
     * @param update
     * @return
     */
    int updateByPrimaryKey(NbchatTrainFilesPart update);

    int deleteByCourseId(String majorId);

    int deleteByFileId(String fileId);

    /**
     * 统计已生成向量的段落
     * @param fileId
     * @return
     */
    int countVectored(@Param("fileId") String fileId);
}