package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class NbchatPptCondition implements Serializable {
    /**
     * 租户ID
     */
    private String targetTenantCode;
    /**
     * PPT类型;0:普通ppt 1:美化ppt
     */
    private String pptType;
    /**
     * 创作方式 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
     */
    private List<String> creationTypeList;
    /**
     * 制作时间
     */
    private Date startTime;
    /**
     * 制作时间
     */
    private Date endTime;
    /**
     * PPT页数
     */
    private Integer minPartCount;
    /**
     * PPT页数
     */
    private Integer maxPartCount;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 付费状态
     */
    private String isPay;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 企业名称
     */
    private String companyName;
}
