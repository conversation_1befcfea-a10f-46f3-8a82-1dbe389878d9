
package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
*
*  <AUTHOR>
*/
@Data
public class NbchatTrainFilesPart implements Serializable {

    private static final long serialVersionUID = 1681281095414L;

    private String partId;
    /**
     * 主键
     * 文件id
     * isNullAble:0
     */
    private String fileId;

    /**
     * 租户编码
     * isNullAble:1,defaultVal:
     */
    private String tenantCode;

    /**
     * 用户id
     * isNullAble:0
     */
    private String userId;
    private String pageNums;
    private Integer partIndex;
    private Integer pageNum;
    private List<Integer> pages = new ArrayList<>();
    /**
     * 创建时间
     * isNullAble:1
     */
    private Date createTime;
    private Date parseTime;

    private String content;
    private String courseContent;
    /**
     * 0 删除 1 正常
     * isNullAble:0,defaultVal:1
     */
    private String isValid;
    /**
     * 是否向量化
     */
    private String isVector;
    /**
     * 是否提取qa
     */
    private String isQa;
    private String isSub;
    private String subText;
}
