package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord;

import java.util.List;

/**
 * (NbchatExamQaRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-07 15:29:01
 */
public interface NbchatExamQaRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatExamQaRecord queryById(Integer id);

    List<NbchatExamQaRecord> selectAll(NbchatExamQaRecord nbchatExamQaRecord);

    NbchatExamQaRecord selectRecord(NbchatExamQaRecord nbchatExamQaRecord);


    /**
     * 新增数据
     *
     * @param nbchatExamQaRecord 实例对象
     * @return 影响行数
     */
    int insert(NbchatExamQaRecord nbchatExamQaRecord);


    int insertSelective(NbchatExamQaRecord nbchatExamQaRecord);

      /**
     * 修改数据
     *
     * @param nbchatExamQaRecord 实例对象
     * @return 影响行数
     */
    int update(NbchatExamQaRecord nbchatExamQaRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

