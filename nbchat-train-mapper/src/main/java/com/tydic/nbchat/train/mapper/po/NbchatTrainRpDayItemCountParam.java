package com.tydic.nbchat.train.mapper.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NbchatTrainRpDayItemCountParam implements Serializable {
    private String itemCode;
    private Date startTime;
    private Date endTime;
    private String isFinish;
}

