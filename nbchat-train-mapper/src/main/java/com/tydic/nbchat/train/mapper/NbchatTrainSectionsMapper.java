package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatTrainSectionsMapper {
    int deleteByPrimaryKey(String sectionId);

    int insert(NbchatTrainSections record);

    int insertSelective(NbchatTrainSections record);

    NbchatTrainSections selectByPrimaryKey(String sectionId);

    int updateByPrimaryKeySelective(NbchatTrainSections record);

    int updateByPrimaryKeyWithBLOBs(NbchatTrainSections record);

    int updateByPrimaryKey(NbchatTrainSections record);

    int updateVideoIndex(NbchatTrainSections record);

    List<NbchatTrainSections> selectBySectionIds(@Param("courseId") String courseId,
                                                 @Param("sectionIds") List<String> sectionIds);

    int update(@Param("courseId") String courseId);

    int delete(@Param("sectionIds") List<String> sectionIds);
}