package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 数字人-模板记录(TdhTemplate)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:54
 */
@Data
public class TdhTemplate implements Serializable {
    private static final long serialVersionUID = 188568663924454899L;
    /**
     * 模板id
     */
    private String tpId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     *分类
     */
    private String category;
    /**
     * 模板名称
     */
    private String tpName;
    //画布尺寸
    private String objectSize;
    //内容尺寸 16:9/9:16
    private String contentSize;
    /**
     * 模板内容
     */
    private String tpContent;
    /**
     * 创建时间
     */
    private Date createTime;
    private Date updateTime;
    /**
     * 排序字段
     */
    private Integer orderIndex;
    /**
     * 来源：0 系统内定 1 我的模板
     */
    private String tpSource;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 是否热门
     */
    private String isHot;
    private String previewUrl;
    /**
     * 备注
     */
    private String remark;
    private String tmpId;//替换模板id
    private String replaceTag;//0不可替换 1可替换
    private String videoUrl;
    private Integer duration;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;

    /**
     * 场景
     */
    private String scene;
    /**
     * 风格
     */
    private String style;
    /**
     * 配色
     */
    private String color;
    //1:视频类 2:播报类
    private String tdhType;
    //是否有数字人 0:无 1:有
    private String isTdh;
    //0 下架 1 上架
    private String tpState;

    /**
     * 0 静态 1动态
     */
    private String tplType;
}

