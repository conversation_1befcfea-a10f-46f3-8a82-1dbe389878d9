package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhMaterialLibrary;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TdhMaterialLibraryMapper {
    /**
     * 根据素材包pkgId 查询图片素材
     */
    List<TdhMaterialLibrary> selectByPkgId(@Param("pkgId") String pkgId);
    /**
     * 根据素材包pkgId 批量新增图片素材
     */
    int batchInsert(@Param("list") List<TdhMaterialLibrary> list);
    /**
     * 根据素材包pkgId 批量删除图片素材
     */
    int updateByPkgId(TdhMaterialLibrary record);
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(TdhMaterialLibrary record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(TdhMaterialLibrary record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    TdhMaterialLibrary selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TdhMaterialLibrary record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TdhMaterialLibrary record);

    List<TdhMaterialLibrary> selectByAll(TdhMaterialLibrary tdhMaterialLibrary);

    int updateBatchSelective(@Param("list") List<TdhMaterialLibrary> list);

    int deleteByPrimaryKeyIn(List<Integer> list);
}