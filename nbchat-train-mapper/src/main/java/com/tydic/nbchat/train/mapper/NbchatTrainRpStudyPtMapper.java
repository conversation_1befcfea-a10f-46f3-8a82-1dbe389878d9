package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPt;

import java.util.List;

/**
 * 学习人次明细表(NbchatTrainRpStudyPt)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-05 14:21:29
 */
public interface NbchatTrainRpStudyPtMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatTrainRpStudyPt queryById(Long id);

    List<NbchatTrainRpStudyPt> selectAll(NbchatTrainRpStudyPt nbchatTrainRpStudyPt);

    /**
     * 新增数据
     *
     * @param nbchatTrainRpStudyPt 实例对象
     * @return 影响行数
     */
    int insert(NbchatTrainRpStudyPt nbchatTrainRpStudyPt);


    int insertSelective(NbchatTrainRpStudyPt nbchatTrainRpStudyPt);

      /**
     * 修改数据
     *
     * @param nbchatTrainRpStudyPt 实例对象
     * @return 影响行数
     */
    int update(NbchatTrainRpStudyPt nbchatTrainRpStudyPt);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

