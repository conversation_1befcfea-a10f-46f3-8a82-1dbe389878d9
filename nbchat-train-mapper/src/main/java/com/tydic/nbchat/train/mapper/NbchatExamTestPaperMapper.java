package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatExamTestPaper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (NbchatExamTestPaper)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-27 16:10:18
 */
public interface NbchatExamTestPaperMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param paperId 主键
     * @return 实例对象
     */
    NbchatExamTestPaper queryById(String paperId);

    NbchatExamTestPaper queryByCourseId(@Param("courseId") String courseId,
                                        @Param("tenantCode") String tenantCode);

    List<NbchatExamTestPaper> selectAll(NbchatExamTestPaper nbchatExamTestPaper);

    /**
     * 新增数据
     *
     * @param nbchatExamTestPaper 实例对象
     * @return 影响行数
     */
    int insert(NbchatExamTestPaper nbchatExamTestPaper);


    int insertSelective(NbchatExamTestPaper nbchatExamTestPaper);

      /**
     * 修改数据
     *
     * @param nbchatExamTestPaper 实例对象
     * @return 影响行数
     */
    int update(NbchatExamTestPaper nbchatExamTestPaper);

    /**
     * 通过主键删除数据
     *
     * @param paperId 主键
     * @return 影响行数
     */
    int deleteById(String paperId);

    /**
     * 删除试题
     * @param @param     courseId 进程id
     * @param tenantCode 租户代码
     * @return @return int
     */
    int deleteQuestion(@Param("courseId") String courseId, @Param("tenantCode") String tenantCode);

    /**
     * 删除试题选项
     * @param @param     courseId 进程id
     * @param tenantCode 租户代码
     * @return @return int
     */
    int deleteQuestionItem(@Param("courseId") String courseId, @Param("tenantCode") String tenantCode);
}

