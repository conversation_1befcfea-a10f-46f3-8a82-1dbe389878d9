package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatTrainSceneDialogueRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 16:09:21
 */
@Data
public class NbchatTrainSceneDialogueRecord implements Serializable {
    private static final long serialVersionUID = 869201374706711093L;
    private String dialogueSessionId;
    /**
     * 主键
     */
    private Long id;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 课程
     */
    private String courseId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 问题
     */
    private String question;
    /**
     * 用户回答
     */
    private String answer;
    /**
     * 建议
     */
    private String suggestion;
    /**
     * 得分
     */
    private Float score;
    /**
     * 创建时间
     */
    private Date createTime;
    private String keyPoints;
    private String analysis;
    private String standardAnswer;

    private String customAnalysis; //自定义解析
    private String questionNo; //问题序号
}

