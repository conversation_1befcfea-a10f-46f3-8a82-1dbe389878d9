package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPnSelectCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学习人数明细表(NbchatTrainRpStudyPn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-05 14:21:13
 */
public interface NbchatTrainRpStudyPnMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatTrainRpStudyPn queryById(Long id);

    List<NbchatTrainRpStudyPn> queryTop5(@Param("dateDay") String dateDay,
                                         @Param("tenantCode") String tenantCode);



    List<NbchatTrainRpStudyPn> selectAll(NbchatTrainRpStudyPn nbchatTrainRpStudyPn);

    /**
     * 新增数据
     *
     * @param nbchatTrainRpStudyPn 实例对象
     * @return 影响行数
     */
    int insert(NbchatTrainRpStudyPn nbchatTrainRpStudyPn);


    int insertSelective(NbchatTrainRpStudyPn nbchatTrainRpStudyPn);

      /**
     * 修改数据
     *
     * @param nbchatTrainRpStudyPn 实例对象
     * @return 影响行数
     */
    int update(NbchatTrainRpStudyPn nbchatTrainRpStudyPn);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 课程排行统计查询
     * @param @param nbchatTrainRpStudyPnSelectCondition nbchat火车rp研究pn选择条件
     * @return @return {@link List }<{@link NbchatTrainRpStudyPn }>
     */
    List<NbchatTrainRpStudyPn> getCourseRankingByTenantCode(NbchatTrainRpStudyPnSelectCondition nbchatTrainRpStudyPnSelectCondition);
}

