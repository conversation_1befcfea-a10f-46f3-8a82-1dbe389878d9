package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 证书发放表(NbchatTrainTaskDegree)实体类
 *
 * <AUTHOR>
 * @since 2024-07-29 16:20:14
 */
@Data
public class NbchatTrainTaskDegree implements Serializable {
    private static final long serialVersionUID = 616054352764547522L;

    private String tenantCode;
    /**
     * 证书编号
     */
    private String id;
    /**
     * 任务id
     */
    private String taskId;

    private String taskName;
    /**
     * 学员id
     */
    private String userId;
    /**
     * 学员名称
     */
    private String userName;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 归属机构
     */
    private String deptId;
    private String deptName;
    private String topDeptId;
    /**
     * 所属岗位
     */
    private String postId;
    private List<String> postIds;

    /**
     * 证书名称
     */
    private String degreeName;
    private Integer degreeCount;//证书数量
    /**
     *目标机构
     */
    private String targetId;
    private List<String> targetIds;
    private String deptContain;
    /**
     * 证书发放日期
     */
    private Date issueDate;
    /**
     * 证书有效期/月
     */
    private Integer validityPeriod;
    /**
     * 证书状态 0到期失效 1生效中 2已吊销
     */
    private String status;

    private String startStatus;


    private String supportSubDept;
    /**
     * 部门范围
     */
    private String deptScope;

    private String isDegree;
    /**
     * 证书下载地址
     */
    private String degreeUrl;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;


}

