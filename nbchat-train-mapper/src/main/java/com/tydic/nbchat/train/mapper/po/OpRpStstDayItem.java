package com.tydic.nbchat.train.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 运营统计_天数(OpRpStstDayItem)实体类
 *
 * <AUTHOR>
 * @since 2024-03-27 17:14:46
 */
@Data
public class OpRpStstDayItem implements Serializable {
    private static final long serialVersionUID = -61462344457555555L;
    /**
     * 编号
     */
    private int id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 指标编码
     */
    private String itemCode;
    /**
     * 指标值
     */
    private int itemValue;
    /**
     * 统计时间
     */
    private Date countDay;
    /**
     * 创建日期 默认为当前时间
     */
    private Date createdTime;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public int getItemValue() {
        return itemValue;
    }

    public void setItemValue(int itemValue) {
        this.itemValue = itemValue;
    }

    public Date getCountDay() {
        return countDay;
    }

    public void setCountDay(Date countDay) {
        this.countDay = countDay;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

}

