package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhFont;
import com.tydic.nbchat.train.mapper.po.TdhFontQueryCondition;

import java.util.List;

public interface TdhFontMapper {
    int deleteByPrimaryKey(Integer fontId);

    int insertSelective(TdhFont record);

    TdhFont selectByPrimaryKey(Integer fontId);

    List<TdhFont> selectByCondition(TdhFontQueryCondition condition);

    int updateByPrimaryKeySelective(TdhFont record);

}