package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.tydic.nbchat.train.api.CourseVoiceAsrListener;
import com.tydic.nbchat.train.api.CourseVoiceListener;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.asr_tts.modelscope.AsrResponse;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceAsrOnSuccess;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;
import com.tydic.nbchat.train.core.config.VolcengineConfigProperties;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class VolcengineNlsHelper implements NlsHelperApi {
    private final VolcengineConfigProperties configProperties;
    private final CourseVoiceListener courseVoiceListener;
    private final CourseVoiceAsrListener courseVoiceAsrListener;
    private final NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;

    private static Map<String,String> langMap = new HashMap<>();
    private static Map<String,String> langKeyMap = new HashMap<>();

    static {
        langMap.put("中文", "cmn");
        langMap.put("英语", "eng");
        langMap.put("日语", "jpn");
        langMap.put("葡萄牙语", "por");
        langMap.put("西班牙语", "spa");
        langMap.put("印尼语", "ind");
        langMap.put("越南语", "vie");
        langMap.put("泰语", "tha");
        langMap.put("法语", "fra");
        langMap.put("德语", "deu");
        langMap.put("意大利语", "ita");
        langMap.put("阿拉伯语", "ara");
        langKeyMap.put("cmn", "中文");
        langKeyMap.put("eng", "英语");
        langKeyMap.put("jpn", "日语");
        langKeyMap.put("por", "葡萄牙语");
        langKeyMap.put("spa", "西班牙语");
        langKeyMap.put("ind", "印尼语");
        langKeyMap.put("vie", "越南语");
        langKeyMap.put("tha", "泰语");
        langKeyMap.put("fra", "法语");
        langKeyMap.put("deu", "德语");
        langKeyMap.put("ita", "意大利语");
        langKeyMap.put("ara", "阿拉伯语");
    }

    private static final ObjectMapper mapper = defaultObjectMapper();

    public static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        return mapper;
    }

    public VolcengineNlsHelper(VolcengineConfigProperties configProperties,
                               CourseVoiceListener courseVoiceListener,
                               CourseVoiceAsrListener courseVoiceAsrListener,
                               NbchatTrainSectionApi nbchatTrainSectionApi) {
        this.configProperties = configProperties;
        this.courseVoiceListener = courseVoiceListener;
        this.courseVoiceAsrListener = courseVoiceAsrListener;
        this.nbchatTrainSectionApi = nbchatTrainSectionApi;
    }
    @Override
    public String anchorConfig() {
        return AnchorType.VOLCENGINE.getCode();
    }

    @Override
    public void createAudioTask(String courseId, String sectionId, boolean courseAll) {
        if (!courseAll && StringUtils.isNotEmpty(courseId)) {
            NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(courseId);
            createAudioTask(courseId, null, course.getCourseDesc(), true);
        }
        if (StringUtils.isNotEmpty(sectionId)) {
            Rsp<String> rsp = nbchatTrainSectionApi.querySection(sectionId);
            createAudioTask(null, sectionId, rsp.getData(), true);
        }
        if (courseAll && StringUtils.isNotEmpty(courseId)) {
            List<NbchatTrainSections> nbchatTrainSections = nbchatTrainSectionsMapper.selectBySectionIds(courseId, null);
            for (NbchatTrainSections section : nbchatTrainSections) {
                Rsp<String> rsp = nbchatTrainSectionApi.querySection(section.getSectionId());
                createAudioTask(null, section.getSectionId(), rsp.getData(), true);
            }
        }
    }


    public TtsVoiceTaskContext createAiAudioTask(TtsVoiceTaskRequest request,Float speedRatio) {
        String text = TrainCommonUtil.removeXMLTags(request.getText());
        String courseId = request.getCourseId();
        String sectionId = request.getSectionId();
        String voice = request.getVoice();
        final String api = "https://openspeech.bytedance.com/api/v1/tts";
        final String appId =configProperties.getAppId();
        final String token = configProperties.getAccessKey();
        final String taskId = NiccCommonUtil.createGuestUID(true);
        if (speedRatio == null) {
            speedRatio = 1.0f;
        }
        TtsVoiceTaskContext ttsVoiceTaskContext = new TtsVoiceTaskContext();
        ttsVoiceTaskContext.setCourseId(courseId);
        ttsVoiceTaskContext.setSectionId(sectionId);
        try {
            String body = "{\n" +
                    "    \"app\": {\n" +
                    "        \"appid\": \""+appId+"\",\n" +
                    "        \"token\": \""+token+"\",\n" +
                    "        \"cluster\": \"volcano_tts\"\n" +
                    "    },\n" +
                    "    \"user\": {\n" +
                    "        \"uid\": \"nbchat_train_001\"\n" +
                    "    },\n" +
                    "    \"audio\": {\n" +
                    "        \"voice_type\": \""+voice+"\",\n" +
                    "        \"encoding\": \"wav\",\n" +
                    "        \"speed_ratio\": "+speedRatio+"\n" +
                    "    }," +
                    "   \"request\":{\n" +
                    "        \"reqid\": \""+NiccCommonUtil.createGuestUID(true)+"\",\n" +
                    "        \"text\": \""+text+"\",\n" +
                    "        \"operation\": \"query\"\n" +
                    "    }" +
                    "}";
            JSONObject bodyJson = JSON.parseObject(body);
            log.info("火山大模型语音合成-任务开始[{}]: {}", sectionId, bodyJson.toJSONString());
            Map<String,String> headers = new HashMap<>();
            headers.put("Content-Type","application/json");
            headers.put("Authorization","Bearer; " + token);
            String resp = HttpClientHelper.doPost(api,headers,bodyJson,120000);
            /***
             * {
             *         "reqid": "reqid",
             *         "code": 3000,
             *         "operation": "query",
             *         "message": "Success",
             *         "sequence": -1,
             *         "data": "base64 encoded binary data",
             *         "addition": {
             *                 "duration": "1960",
             *         }
             * }
             */
            //保存data为音频文件
            JSONObject resultJson = JSON.parseObject(resp);
            if (resultJson.containsKey("code") && resultJson.getIntValue("code") == 3000) {
                //TrainCommonUtil.writeWavFromBase64(resultJson.getString("data"),"/Volumes/Public/"+sectionId+".wav");
                //回调写入数据
                byte[] data = Base64.getDecoder().decode(resultJson.getString("data"));
                final CourseVoiceOnSuccess onSuccess = CourseVoiceOnSuccess.builder().
                        courseId(courseId).sectionId(sectionId).taskId(taskId).file(data).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener ->
                        ttsVoiceTaskContext.setAudio_address(listener.onSuccess(onSuccess)));
                //获取 duration
                int duration = resultJson.getJSONObject("addition").getIntValue("duration");
                ttsVoiceTaskContext.setDuration(duration);
                List<TtsVoiceResult> sentences = new ArrayList<>();
                sentences.add(TtsVoiceResult.builder().text(text).
                        begin_Time(0).end_time(duration).build());
                ttsVoiceTaskContext.setSentences(sentences);
            } else {
                log.warn("火山大模型语音合成-任务失败[{}]: {}", sectionId, resp);
            }
        } catch (Exception e) {
            log.error("火山大模型语音合成-任务异常[{}]: ", sectionId, e);
        }
        return ttsVoiceTaskContext;
    }

    private boolean isAiVoice(String voice) {
        return voice.endsWith("_tob") || voice.endsWith("_bigtts");
    }


    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId,
                                               String text, String voice, boolean async) {
        TtsVoiceTaskRequest request = new TtsVoiceTaskRequest();
        request.setCourseId(courseId);
        request.setSectionId(sectionId);
        request.setText(text);
        request.setVoice(voice);
        request.setAsync(async);
        return createAudioTask(request);
    }

    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request) {
        String text = request.getText();
        String courseId = request.getCourseId();
        String sectionId = request.getSectionId();
        String voice = request.getVoice();

        String ttsApi = configProperties.getNlsBaseUrl() + configProperties.getTtsLongApi();
        TtsVoiceTaskContext ttsVoiceTaskContext = new TtsVoiceTaskContext();
        String err = "";
        if (StringUtils.isBlank(text)) {
            log.info("语音合成[{}]-任务-语音内容不能为空！:{}", courseId, voice);
            return ttsVoiceTaskContext;
        }
        //text = TrainCommonUtil.removeXMLTags(text).trim();
        JSONObject tts = new JSONObject();
        String requestId = NiccCommonUtil.createGuestUID(true);
        tts.put("reqid", requestId);
        tts.put("appid", configProperties.getAppId());
        tts.put("text", text);
        tts.put("voice_type", voice);
        tts.put("enable_subtitle", 1);
        tts.put("format", "wav");
        if (StringUtils.isNotBlank(request.getLanguage())) {
            if (langMap.containsKey(request.getLanguage())) {
                tts.put("language", langMap.get(request.getLanguage()));
            } else {
                tts.put("language", request.getLanguage());
            }
        }
        Integer volume = request.getVolume();
        Integer speech_rate = request.getSpeechRate();
        Integer pitch_rate = request.getPitchRate();

        //音量，范围0.1～3，默认为1
        float volumeRange = NlsHelperApi.convertRange(volume, 1, 0, 100, 0.1f, 2f);
        tts.put("volume", volumeRange);
        //语速，范围0.2～3，默认为1
        float speechRange = NlsHelperApi.convertRange(speech_rate, 1, -500, 500, 0.2f, 2f);
        tts.put("speed", speechRange);
        //语调，范围0.1～3，默认为1
        float pitchRange = NlsHelperApi.convertRange(pitch_rate, 1, -500, 500, 0.1f, 2f);
        tts.put("pitch", pitchRange);

        if (isAiVoice(voice)) {
            //火山大模型语音合成
            return createAiAudioTask(request,speechRange);
        }

        log.info("语音合成-任务开始[{}]: {}", sectionId, tts.toJSONString());

        /*if (text.getBytes().length < 1024) {
            //TODO 后续可改为实时接口
            tts = getRequestParam(volumeRange,speechRange,pitchRange,voice,text);
            log.info("语音合成-实时合成开始[{}]: {}", sectionId, tts.toJSONString());
            return getSyncAudioTask(tts,sectionId);
        }*/
        // 发起请求
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), tts.toJSONString());
        Request tts_request = new Request.Builder().url(ttsApi).
                header("Resource-Id", "volc.tts_async.default").
                header("Authorization", "Bearer; " + configProperties.getAccessKey()).
                header("Content-Type", "application/json")
                .post(reqBody)
                .build();
        try {
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(tts_request).execute();
            // 获取结果，并根据返回进一步进行处理
            String result = response.body().string();
            response.close();
            log.info("语音合成-任务结果[{}]: {}", sectionId, result);
            JSONObject resultJson = JSON.parseObject(result);
            if (resultJson.containsKey("task_id")) {
                String taskId = resultJson.getString("task_id");
                log.info("语音合成-获取任务信息[{}]: task_id = {}", sectionId, taskId);
                if (request.isAsync()) {
                    //开始轮询
                    new Thread(() -> getAudioResult(courseId, sectionId, taskId, requestId)).start();
                } else {
                    ttsVoiceTaskContext = getAudioResult(courseId, sectionId, taskId, requestId);
                }
            } else {
                String error = resultJson.getString("message");
                log.error("语音合成-任务异常[{}]: {}", sectionId, resultJson.toJSONString());
                final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(error).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            }
        } catch (Exception e) {
            final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(e.getMessage()).build();
            Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            log.error("语音合成-任务异常[{}]: ", sectionId, e);
        }
        ttsVoiceTaskContext.setCourseId(courseId);
        ttsVoiceTaskContext.setSectionId(sectionId);
        return ttsVoiceTaskContext;
    }


    public TtsVoiceTaskContext getSyncAudioTask(JSONObject ttsParam,String sectionId){
        TtsVoiceTaskContext ttsVoiceTaskContext = new TtsVoiceTaskContext();
        // 发起请求
        String ttsApi = configProperties.getNlsBaseUrl() + configProperties.getTtsApi();
        System.out.println(ttsApi);
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), ttsParam.toJSONString());
        Request tts_request = new Request.Builder().url(ttsApi).
                header("Authorization", "Bearer;" + configProperties.getAccessKey())
                .post(reqBody)
                .build();
        try {
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(tts_request).execute();
            // 获取结果，并根据返回进一步进行处理
            String result = response.body().string();
            response.close();
            log.info("语音合成-任务结果[{}]: {}", sectionId, result);
            JSONObject resultJson = JSON.parseObject(result);
            if (resultJson != null && resultJson.containsKey("data")){
                String base64 = resultJson.getString("data");
                //转换为音频bytes
                byte[] bytes = base64ToBytes(base64);
                CourseVoiceOnSuccess success = CourseVoiceOnSuccess.builder().sectionId(sectionId).file(bytes).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener -> ttsVoiceTaskContext.setAudio_address(listener.onSuccess(success)));
            } else {
                String error = "";
                if (resultJson != null) {
                    error = resultJson.toJSONString();
                }
                final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(error).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            }
        } catch (Exception e) {
            final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(e.getMessage()).build();
            Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            log.error("语音合成-任务异常[{}]: ", sectionId, e);
        }
        return ttsVoiceTaskContext;
    }

    private static byte[] base64ToBytes(String base64){
        return Base64.getDecoder().decode(base64);
    }


    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, boolean async) {
        return createAudioTask(courseId, sectionId, text, configProperties.getVoice(), async);
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async) {
        Long requestTime = System.currentTimeMillis();
        String taskId = IdWorker.nextAutoIdStr();
        String asrApi = configProperties.getNlsBaseUrl() + configProperties.getAsrApi();
        AsrVoiceTaskContext context = new AsrVoiceTaskContext();
        String err = "";
        try {
            JSONObject taskObject = new JSONObject();
            taskObject.put("audio_in", filepath);
            taskObject.put("enable_sentences", true);
            log.info("录音文件识别[{}]-任务-开始:{}", courseId, taskObject);
            String result = HttpClientHelper.doPost(asrApi, new HashMap<>(), taskObject, 60000);
            log.info("录音文件识别[{}]-任务-完成:{}", courseId, result);
            if (JSONObject.isValid(result)) {
                AsrResponse response = JSONObject.parseObject(result, AsrResponse.class);
                if (response.success()) {
                    context.setTaskId(taskId);
                    context.setRequestId(taskId);
                    context.setCourseId(courseId);
                    context.setRequestTime(requestTime);
                    context.setStatusCode(200);
                    context.setSentences(response.getData().getSentences());
                    CourseVoiceAsrOnSuccess onSuccess = CourseVoiceAsrOnSuccess.builder().
                            courseId(courseId).
                            fileId(fileId).
                            taskContext(context).
                            taskId(taskId).build();
                    Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onSuccess(onSuccess));
                    return context;
                }
            }
            err = result;
        } catch (Exception e) {
            log.error("录音文件识别[{}]-异常:", courseId, e);
            err = e.getMessage();
        }
        final CourseVoiceOnError onError = CourseVoiceOnError.builder().courseId(courseId).fileId(fileId).error(err).build();
        Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onError(onError));
        return context;
    }


    @Override
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        return createAsrTask(request.getCourseId(),
                request.getFileId(),
                request.getFilePath(), request.isAsync());
    }


    /**
     * 获取录音结果
     *
     * @param sectionId
     * @param taskId
     * @param requestId
     * @return
     */
    private TtsVoiceTaskContext getAudioResult(String courseId, String sectionId, String taskId, String requestId) {
        String appId = configProperties.getAppId();
        String address = "";
        String urlBuilder = configProperties.getTtsResultApi().replace("{appId}", appId).replace("{taskId}", taskId);
        String apiUrl = configProperties.getNlsBaseUrl() + urlBuilder;
        log.info("语音合成-获取录音[{}]: {}|{}", sectionId,taskId,apiUrl);
        TtsVoiceTaskContext context = new TtsVoiceTaskContext();
        int count = 0;
        //异步获取结果，不确定时效性
        while (++count < 500) {
            try {
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                Request request = new Request.Builder().url(apiUrl).
                        header("Resource-Id", "volc.tts_async.default").
                        header("Authorization", "Bearer; " + configProperties.getAccessKey()).
                        get().build();
                OkHttpClient client = new OkHttpClient();
                //添加请求头
                Response response = client.newCall(request).execute();
                String result = null;
                if (response.body() != null) {
                    result = response.body().string();
                }
                response.close();
                log.info("语音合成-获取录音-结果[{}]: {}", sectionId, result);
                JSONObject resultJson = mapper.readValue(result, JSONObject.class);
                if (resultJson == null) {
                    continue;
                }
                if (resultJson.containsKey("code")) {
                    log.error("语音合成-获取录音-失败[{}]: {}", sectionId, resultJson);
                    break;
                }
                if (resultJson.containsKey("task_id")
                        && resultJson.getIntValue("task_status") == 1) {
                    context = resultJson.toJavaObject(TtsVoiceTaskContext.class);
                    Optional.ofNullable(context.getSentences()).ifPresent(sens -> {
                        for (TtsVoiceResult voiceResult : sens) {
                            //移除xml标记
                            String txt = TrainCommonUtil.removeXMLTags(voiceResult.getText()).trim();
                            voiceResult.setText(txt);
                        }
                    });
                    address = context.getAudio_address();
                    log.info("语音合成-获取录音-地址[{}]: {}", sectionId, address);
                    break;
                }
            } catch (Exception e) {
                log.error("语音合成-获取录音-异常[{}]: taskId = {}, requestId = {}", sectionId, taskId, requestId, e);
            }
        }
        if (StringUtils.isBlank(address)) {
            log.warn("语音合成-获取录音-失败[{}]: taskId = {}, requestId = {}", sectionId, taskId, requestId);
            final CourseVoiceOnError onError = CourseVoiceOnError.builder().
                    sectionId(sectionId).
                    taskId(taskId).error("语音合成-获取录音-失败").build();
            Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            return context;
        }
        //回调写入数据
        final CourseVoiceOnSuccess onSuccess = CourseVoiceOnSuccess.builder().
                courseId(courseId).sectionId(sectionId).taskId(taskId).address(address).build();
        final TtsVoiceTaskContext finalContext = context;
        Optional.ofNullable(courseVoiceListener).ifPresent(listener -> finalContext.setAudio_address(listener.onSuccess(onSuccess)));
        return finalContext;
    }


}
