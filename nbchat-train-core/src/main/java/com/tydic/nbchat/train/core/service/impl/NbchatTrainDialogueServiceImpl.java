package com.tydic.nbchat.train.core.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.NbchatTrainDialogueApi;
import com.tydic.nbchat.train.api.bo.dialogue.*;
import com.tydic.nbchat.train.api.bo.dialogue.manage.DialogueManageBO;
import com.tydic.nbchat.train.api.bo.eums.TrainCommonState;
import com.tydic.nbchat.train.api.bo.train.SceneDialogueContent;
import com.tydic.nbchat.train.core.service.impl.event.EventPublishFactory;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class NbchatTrainDialogueServiceImpl implements NbchatTrainDialogueApi {

    @Resource
    private NbchatTrainSceneDialogueMapper nbchatTrainSceneDialogueMapper;
    @Resource
    private NbchatTrainSceneDialogueRecordMapper nbchatTrainSceneDialogueRecordMapper;
    @Resource
    private NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    private NbchatTrainSceneDialogueManageMapper nbchatTrainSceneDialogueManageMapper;
    @Resource
    private NbchatTrainSceneDialogueSessionMapper nbchatTrainSceneDialogueSessionMapper;

    @MethodParamVerifyEnable
    @Override
    public RspList getCourseDialogues(DialogueReqBO reqBO) {
        log.info("查询场景对话：{}", reqBO);
        List<DialogueBO> list = Lists.newArrayList();
        NbchatTrainSceneDialogue record = new NbchatTrainSceneDialogue();
        BeanUtils.copyProperties(reqBO, record);
        Page<NbchatTrainSceneDialogue> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatTrainSceneDialogueMapper.selectByCondition(record);
        NiccCommonUtil.copyList(page.getResult(), list, DialogueBO.class);
        return BaseRspUtils.createSuccessRspList(list);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp getSessionInfo(DialogueSessionQueryReqBO request) {
        log.info("查询会话记录：{}", request);
        DialogueSessionRspBO rspBO = new DialogueSessionRspBO();
        NbchatTrainSceneDialogueSession session;
        if (StringUtils.isAllBlank(request.getCourseId(),request.getSessionId())) {
            return BaseRspUtils.createErrorRsp("参数异常: 课程id和会话id不得同时为空");
        }
        if (StringUtils.isNotBlank(request.getSessionId())) {
            session = nbchatTrainSceneDialogueSessionMapper.queryById(request.getSessionId());
        } else {
            session = nbchatTrainSceneDialogueSessionMapper.selectMaxScorePassSession(request.getCourseId(), request.getUserId());
        }
        if (session != null) {
            BeanUtils.copyProperties(session, rspBO);
            NbchatTrainSceneDialogueRecord record = new NbchatTrainSceneDialogueRecord();
            record.setDialogueSessionId(session.getSessionId());
            List<NbchatTrainSceneDialogueRecord> records = nbchatTrainSceneDialogueRecordMapper.selectAll(record);
            List<DialogueSessionRecordBO> dialogues = Lists.newArrayList();
            NiccCommonUtil.copyList(records, dialogues, DialogueSessionRecordBO.class);
            rspBO.setDialogues(dialogues);
            return BaseRspUtils.createSuccessRsp(rspBO);
        } else {
            return BaseRspUtils.createErrorRsp("未查询到对应的会话");
        }
    }


    @Deprecated
    @Override
    public Rsp save(DialogueBO request) {
        log.info("保存场景对话：{}", request);
        List<SceneDialogueContent> dialogueContents =
                JSONObject.parseArray(request.getContent(), SceneDialogueContent.class);
        for (SceneDialogueContent dialogueContent : dialogueContents) {
            NbchatTrainSceneDialogue dialogue = new NbchatTrainSceneDialogue();
            dialogue.setRole("user");
            dialogue.setContent(dialogueContent.getUser());
            dialogue.setDateTime(new Date());
            dialogue.setCourseId(request.getCourseId());
            dialogue.setTenantCode(request.getTenantCode());
            nbchatTrainSceneDialogueMapper.insertSelective(dialogue);

            NbchatTrainSceneDialogue dialogue1 = new NbchatTrainSceneDialogue();
            dialogue1.setRole("assistant");
            dialogue1.setContent(dialogueContent.getAssistant());
            dialogue1.setDateTime(new Date());
            dialogue1.setCourseId(request.getCourseId());
            dialogue1.setTenantCode(request.getTenantCode());
            nbchatTrainSceneDialogueMapper.insertSelective(dialogue1);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    public Rsp saveSession(DialogueSessionSaveReqBO request) {
        log.info("保存场景实践记录:{}", request);
        String sessionId = request.getSessionId();
        if (StringUtils.isBlank(request.getSessionId())) {
            //生成会话id,初始化会话
            sessionId = IdWorker.nextAutoIdStr();
            NbchatTrainSceneDialogueSession session = new NbchatTrainSceneDialogueSession();
            session.setSessionId(sessionId);
            session.setCourseId(request.getCourseId());
            session.setTenantCode(request.getTenantCode());
            session.setUserId(request.getUserId());
            session.setStartTime(new Date());
            session.setSessionState(TrainCommonState.NOT_PASS.getCode());
            session.setSceneState(TrainCommonState.NOT_PASS.getCode());
            session.setDialogueType(request.getDialogueType());
            NbchatTrainSceneDialogueManage manage = nbchatTrainSceneDialogueManageMapper.
                    queryByCourseId(request.getCourseId(), request.getTenantCode());
            if (manage != null) {
                session.setPassScore(manage.getScore());
            }
            nbchatTrainSceneDialogueSessionMapper.insertSelective(session);
        } else {
            if (request.getDialogues() != null && !request.getDialogues().isEmpty()) {
                nbchatTrainSceneDialogueRecordMapper.delete(sessionId, request.getTenantCode());
                for (DialogueSessionRecordBO recordBO: request.getDialogues()) {
                    NbchatTrainSceneDialogueRecord record = new NbchatTrainSceneDialogueRecord();
                    record.setDialogueSessionId(sessionId);
                    BeanUtils.copyProperties(request, record);
                    BeanUtils.copyProperties(recordBO, record);
                    if (record.getCreateTime() == null) {
                        record.setCreateTime(new Date());
                    }
                    nbchatTrainSceneDialogueRecordMapper.insertSelective(record);
                }
            }
            if (request.getUserScore() != null) {
                NbchatTrainSceneDialogueSession session = nbchatTrainSceneDialogueSessionMapper.queryById(sessionId);
                if (session != null) {
                    //更新培训任务状态
                    NbchatTrainRecord nbchatTrainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(request.getUserId(), request.getCourseId());
                    if (nbchatTrainRecord == null) {
                        return BaseRspUtils.createErrorRsp("未查询到对应的培训任务");
                    }
                    nbchatTrainRecord.setSceneState(TrainCommonState.PASS.getCode());
                    session.setUserScore(request.getUserScore());
                    session.setAnalysis(request.getAnalysis());
                    session.setSessionState(EntityValidType.NORMAL.getCode());
                    session.setSceneState(EntityValidType.NORMAL.getCode());
                    if (StringUtils.isNotEmpty(request.getDialogueType()) && request.getDialogueType().equals("3")) {
                        if (request.getUserScore() >= session.getPassScore()) {
                            nbchatTrainRecord.setLastTime(new Date());
                            nbchatTrainRecord.setScenePassState(TrainCommonState.PASS.getCode());
                        }
                    }
                    session.setEndTime(new Date());
                    nbchatTrainSceneDialogueSessionMapper.update(session);
                    //更新培训任务状态
                    nbchatTrainRecordMapper.updateByPrimaryKeySelective(nbchatTrainRecord);
                    if (TrainCommonState.PASS.getCode().equals(nbchatTrainRecord.getScenePassState())) {
                        //发布培训任务完成检测事件
                        EventPublishFactory.publishUserCompleteTaskEvent(request.getTenantCode(), request.getUserId());
                    }
                } else {
                    return BaseRspUtils.createErrorRsp("未查询到对应的会话");
                }
            }
        }
        return BaseRspUtils.createSuccessRsp(sessionId);
    }


    @Deprecated
    public Rsp updateScore(DialogueManageBO request) {
        log.info("更新场景评分:{}", request);
        NbchatTrainSceneDialogueManage dialogueManage = nbchatTrainSceneDialogueManageMapper.queryByCourseId(request.getCourseId(), request.getTenantCode());
        if (ObjectUtils.isEmpty(dialogueManage)) {
            return BaseRspUtils.createErrorRsp("未查询到对应的课程");
        }
        NbchatTrainRecord nbchatTrainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(request.getUserId(), request.getCourseId());
        if (ObjectUtils.isNotEmpty(nbchatTrainRecord) && "0".equals(nbchatTrainRecord.getSceneState())) {
            if (request.getScore() >= dialogueManage.getScore()) {
                nbchatTrainRecord.setSceneState("1");
                nbchatTrainRecordMapper.updateByPrimaryKeySelective(nbchatTrainRecord);
            }
        }
        return BaseRspUtils.createSuccessRsp("更新结束");
    }
}
