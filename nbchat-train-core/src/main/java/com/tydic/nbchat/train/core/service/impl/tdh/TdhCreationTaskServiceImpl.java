package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.ShareType;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskReqBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationTaskRspBo;
import com.tydic.nbchat.train.api.tdh.TdhCreationTaskApi;
import com.tydic.nbchat.train.core.busi.TdhQueueCountBusiService;
import com.tydic.nbchat.train.mapper.TdhCreationRecordMapper;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhCreationRecord;
import com.tydic.nbchat.train.mapper.po.TdhCreationTask;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description 我的作品
 * @Date 2023/8/29 17:55
 * @Created by dong_pc
 */
@Slf4j
@Service
public class TdhCreationTaskServiceImpl implements TdhCreationTaskApi {

    @Resource
    TdhCreationTaskMapper tdhCreationTaskMapper;
    @Resource
    TdhCreationRecordMapper tdhCreationRecordMapper;
    private final TdhQueueCountBusiService tdhQueueCountBusiService;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi userApi;

    public TdhCreationTaskServiceImpl(TdhQueueCountBusiService tdhQueueCountBusiService) {
        this.tdhQueueCountBusiService = tdhQueueCountBusiService;
    }

    @Override
    public RspList<TdhCreationTaskRspBo> getCreationList(TdhCreationTaskReqBo request) {
        TdhCreationTask cond = new TdhCreationTask();
        BeanUtils.copyProperties(request,cond);
        if (ShareType.DEFAULT_SHARE.getCode().equals(request.getIsShare())) {
            //查询我的作品 0
            cond.setIsShare(null);
        }
        if (ShareType.IS_SHARE.getCode().equals(request.getIsShare())) {
            //查询分享作品 1
            cond.setUserId(null);
        }
        if (ShareType.ID_SHARE.getCode().equals(request.getIsShare())) {
            //查询单个作品（通过ID）2
            cond.setUserId(null);
            cond.setIsShare(null);
        }
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        //获取排队任务
        Map<String,Integer> queueMap = tdhQueueCountBusiService.getTaskQueueMap();
        if (StringUtils.isNoneBlank(request.getTargetTenant(),request.getTargetUid())) {
            cond.setUserId(request.getTargetUid());
            cond.setTenantCode(request.getTargetTenant());
        }
        Page<TdhCreationTask> info = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhCreationTaskMapper.selectAll(cond);
        List<TdhCreationTask> result = info.getResult();
        ArrayList<TdhCreationTaskRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result,rspList,TdhCreationTaskRspBo.class);
        for (TdhCreationTaskRspBo bo : rspList) {
            if (ObjectUtils.allNotNull(bo.getStartTime(),bo.getEndTime())) {
                bo.setDuration((bo.getEndTime().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (ObjectUtils.isEmpty(bo.getEndTime())) {
                bo.setDuration((new Date().getTime() - bo.getStartTime().getTime()) / 1000);
            }
            if (queueMap.containsKey(bo.getTaskId())) {
                bo.setQueueNum(queueMap.get(bo.getTaskId()));
            }
        }
        log.debug("数字人面板-我的作品：{}",rspList);
        rspList.forEach(bo->{
            if (StringUtils.isNotEmpty(bo.getUserId())) {
                Rsp rsp = userApi.getUserInfo(bo.getUserId());
                if (rsp.isSuccess()) {
                    NbchatUserInfo nbchatUserInfo = (NbchatUserInfo) rsp.getData();
                    bo.setName(nbchatUserInfo.getName());
                }
                TdhCreationRecord obj = tdhCreationRecordMapper.querySampleById(bo.getCreationId(),"0");
                if (ObjectUtils.isNotEmpty(obj)) {
                    bo.setPreviewUrl(obj.getPreviewUrl());
                    bo.setCreationSource(obj.getCreationSource());
                }
            }
            if (StringUtils.isBlank(request.getTaskId())) {
                bo.setCreationContent(null);
            }
        });
        return BaseRspUtils.createSuccessRspList(rspList,info.getTotal());
    }

    @Override
    public Rsp<TdhCreationTaskRspBo> getCreationInfo(TdhCreationTaskReqBo request) {
        TdhCreationTask task = tdhCreationTaskMapper.queryById(request.getTaskId());
        if (ObjectUtils.isNotEmpty(task)) {
            TdhCreationTaskRspBo rspBo = new TdhCreationTaskRspBo();
            BeanUtils.copyProperties(task,rspBo);
            return BaseRspUtils.createSuccessRsp(rspBo);
        }
        return BaseRspUtils.createErrorRsp("查询失败：任务不存在");
    }

    @Override
    public Rsp delete(TdhCreationTaskReqBo request) {
        log.info("数字人面板-删除作品:{}",request);
        if (StringUtils.isEmpty(request.getTaskId())) {
            return BaseRspUtils.createErrorRsp("作品id不得为空");
        }
        int i = tdhCreationTaskMapper.deleteById(request.getTaskId());
        return BaseRspUtils.createSuccessRsp(i);
    }

    @Override
    public Rsp update(TdhCreationTaskReqBo request) {
        log.info("数字人面板-更新作品:{}",request);
        if (StringUtils.isEmpty(request.getTaskId())) {
            return BaseRspUtils.createErrorRsp("作品id不得为空");
        }
        TdhCreationTask tdhCreationTask = new TdhCreationTask();
        BeanUtils.copyProperties(request,tdhCreationTask);
        tdhCreationTask.setUserId(null);
        int i = tdhCreationTaskMapper.update(tdhCreationTask);
        log.info("数字人面板-更新作品结果:{}",i);
        return BaseRspUtils.createSuccessRsp(i,"更新成功");
    }
}
