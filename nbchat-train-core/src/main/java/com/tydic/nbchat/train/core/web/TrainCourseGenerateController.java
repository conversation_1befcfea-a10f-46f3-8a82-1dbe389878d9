package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatCourseAiToolApi;
import com.tydic.nbchat.train.api.bo.generate.TranCourseGenerateRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/course")
public class TrainCourseGenerateController {
    private final NbchatCourseAiToolApi nbchatTrainCourseApi;

    public TrainCourseGenerateController(NbchatCourseAiToolApi nbchatTrainCourseApi) {
        this.nbchatTrainCourseApi = nbchatTrainCourseApi;
    }

    /**
     * 查询列表
     * @param request
     * @return
     */
    @PostMapping("/generate")
    public Rsp generate(@RequestBody TranCourseGenerateRequest request) {
        return nbchatTrainCourseApi.generate(request);
    }

}
