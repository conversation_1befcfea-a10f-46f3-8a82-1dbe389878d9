package com.tydic.nbchat.train.core.service.impl.tdh;

import com.alibaba.nacos.common.utils.StringUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.CustomizeStatusEnum;
import com.tydic.nbchat.train.api.bo.eums.CustomizeType;
import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.TdhCustomizeRecordOperationsApi;
import com.tydic.nbchat.train.core.busi.TdhAnchorBusiService;
import com.tydic.nbchat.train.core.busi.TdhHumanBusiService;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualAnchorMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecordCondition;
import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.NoticeApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.UserTypeEnum;
import com.tydic.nbchat.user.api.bo.notice.NoticeBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
              group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class TdhCustomizeRecordOperationsServiceImpl implements TdhCustomizeRecordOperationsApi {

    private final TdhCustomizeRecordMapper tdhCustomizeRecordMapper;
    private final TdhAnchorBusiService tdhAnchorBusiService;
    private final TdhHumanBusiService tdhHumanBusiService;
    private final NameMapper nameMapper;
    private final TdhVirtualAnchorMapper tdhVirtualAnchorMapper;
    private final TdhVirtualHumanMapper tdhVirtualHumanMapper;
    private final TrainEventSender trainEventSender;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi nbchatUserApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NoticeApi noticeApi;

    public TdhCustomizeRecordOperationsServiceImpl(TdhCustomizeRecordMapper tdhCustomizeRecordMapper,
                                                   TdhAnchorBusiService tdhAnchorBusiService,
                                                   TdhHumanBusiService tdhHumanBusiService,
                                                   NameMapper nameMapper,
                                                   TdhVirtualAnchorMapper tdhVirtualAnchorMapper,
                                                   TdhVirtualHumanMapper tdhVirtualHumanMapper,
                                                   TrainEventSender trainEventSender) {
        this.tdhCustomizeRecordMapper = tdhCustomizeRecordMapper;
        this.tdhAnchorBusiService = tdhAnchorBusiService;
        this.tdhHumanBusiService = tdhHumanBusiService;
        this.nameMapper = nameMapper;
        this.tdhVirtualHumanMapper = tdhVirtualHumanMapper;
        this.tdhVirtualAnchorMapper = tdhVirtualAnchorMapper;
        this.trainEventSender = trainEventSender;
    }

    @Override
    public RspList list(TdhCustomizeRecordQueryReqBO reqBO) {
        reqBO.setUserId(null);
        reqBO.setTenantCode(null);
        List<TdhCustomizeRecordQueryRspBO> result = new ArrayList<>();
        //有手机号查询条件时，先查询用户信息
        if (StringUtils.isNotBlank(reqBO.getPhone())) {
            Rsp<NbchatUserInfo> rsp = nbchatUserApi.getUserByPhone(reqBO.getPhone());
            if (!rsp.isSuccess()) {
                log.error("运营平台-形象定制-手机号查询-查询用户信息失败，phone:{}|{}", reqBO.getPhone(), rsp.getRspDesc());
                return BaseRspUtils.createSuccessRspList(result, 0);
            }
            NbchatUserInfo nbchatUserInfo = rsp.getData();
            if (nbchatUserInfo == null) {
                log.info("运营平台-形象定制-手机号查询-未查询到用户信息，phone:{}", reqBO.getPhone());
                return BaseRspUtils.createSuccessRspList(result, 0);
            }
            reqBO.setUserId(nbchatUserInfo.getUserId());
        }

        TdhCustomizeRecordCondition condition = new TdhCustomizeRecordCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhCustomizeRecordCondition> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhCustomizeRecordMapper.list(condition);

        if (CollectionUtils.isEmpty(page)) {
            return BaseRspUtils.createSuccessRspList(result, page.getTotal());
        }
        NiccCommonUtil.copyList(page.getResult(), result, TdhCustomizeRecordQueryRspBO.class);

        List<String> userList = result.stream()
                .map(TdhCustomizeRecordQueryRspBO::getUserId)
                .collect(Collectors.toList());
        RspList<NbchatUserInfo> rsp = nbchatUserApi.getUserInfoList(userList);
        if (!rsp.isSuccess()) {
            log.error("运营平台-形象定制-查询用户信息失败，userList:{}|{}", userList, rsp.getRspDesc());
            return BaseRspUtils.createSuccessRspList(result, page.getTotal());
        }
        List<NbchatUserInfo> userInfoList = rsp.getRows();
        // 将 userId 和 phone 映射到 Map
        Map<String, String> userIdToPhoneMap = userInfoList.stream()
                .collect(Collectors.toMap(NbchatUserInfo::getUserId, NbchatUserInfo::getPhone));

        Map<String, String> userIdToNameMap = userInfoList.stream()
                .collect(Collectors.toMap(NbchatUserInfo::getUserId, NbchatUserInfo::getName));

        List<TdhCustomizeRecordQueryRspBO> updatedList = result.stream()
                .peek(record -> {
                    String phone = userIdToPhoneMap.get(record.getUserId());
                    if (phone != null) {
                        record.setPhone(phone);
                    }
                    String name = userIdToNameMap.get(record.getUserId());
                    if (name != null) {
                        record.setName(name);
                    }
                    if (UserAttributeConstants.DEFAULT_TENANT_CODE.equals(record.getTenantCode())) {
                        record.setUserType(UserTypeEnum.COMMON_USER.getName());
                    } else {
                        record.setUserType(UserTypeEnum.COMPANY_USER.getName());
                    }
                })
                .collect(Collectors.toList());
        log.info("运营平台-形象定制-查询成功，reqBO:{}|{}", reqBO, page.getTotal());
        return BaseRspUtils.createSuccessRspList(updatedList, page.getTotal());
    }

    @Override
    public Rsp info(TdhCustomizeRecordQueryReqBO reqBO) {
        TdhCustomizeRecord customizeRecord = tdhCustomizeRecordMapper.selectByPrimaryKey(reqBO.getId());
        if (customizeRecord == null) {
            log.error("运营平台-形象定制-明细查询-未查询到记录，id:{}", reqBO.getId());
            return BaseRspUtils.createErrorRsp("未查询到记录");
        }
        Rsp rsp = nbchatUserApi.getUserInfo(customizeRecord.getUserId());
        if (!rsp.isSuccess() || rsp.getData() == null) {
            log.error("运营平台-形象定制-明细查询-查询用户信息失败，userId:{}|{}", customizeRecord.getUserId(), rsp.getRspDesc());
            return BaseRspUtils.createErrorRsp("查询用户信息失败");
        }
        NbchatUserInfo userInfo = (NbchatUserInfo) rsp.getData();
        TdhCustomizeRecordQueryRspBO rspBO = new TdhCustomizeRecordQueryRspBO();
        BeanUtils.copyProperties(customizeRecord, rspBO);
        rspBO.setPhone(userInfo.getPhone());
        rspBO.setName(userInfo.getName());
        rspBO.setCompanyName(nameMapper.queryTenantNameByCode(customizeRecord.getTenantCode()));
        if (UserAttributeConstants.DEFAULT_TENANT_CODE.equals(customizeRecord.getTenantCode())) {
            rspBO.setUserType(UserTypeEnum.COMMON_USER.getName());
        } else {
            rspBO.setUserType(UserTypeEnum.COMPANY_USER.getName());
        }
        log.info("运营平台-形象定制-明细查询成功，reqBO:{}|{}", reqBO, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    public Rsp priceChange(TdhCustomizeRecordPriceChangeReqBO request) {
        if (request.getOrderPrice() <= 0) {
            log.warn("运营平台-形象定制-订单改价失败，价格不合法，request:{}", request);
            return BaseRspUtils.createErrorRsp("价格不合法");
        }
        TdhCustomizeRecord record = tdhCustomizeRecordMapper.findByOrderNo(request.getOrderNo());
        TdhCustomizeRecord updated = new TdhCustomizeRecord();
        BeanUtils.copyProperties(request, updated);
        String orderNo = IdWorker.nextAutoIdStr();
        updated.setOrderNo(orderNo);
        int update = tdhCustomizeRecordMapper.updateByOrderNo(updated, request.getOrderNo());
        if (update <= 0) {
            log.error("运营平台-形象定制-订单改价失败，request:{}|{}", request, updated);
            return BaseRspUtils.createErrorRsp("订单改价失败");
        }
        if (CustomizeType.AUDIO.getCode().equals(record.getCustomizeType())) {
            //更改声音的订单ID
            TdhVirtualAnchor tdhVirtualAnchor = tdhVirtualAnchorMapper.findByOrderNoAndUserId(request.getOrderNo(), record.getUserId());
            TdhVirtualAnchor reqBO = new TdhVirtualAnchor();
            reqBO.setOrderNo(orderNo);
            reqBO.setAnchorId(tdhVirtualAnchor.getAnchorId());
            tdhVirtualAnchorMapper.update(reqBO);
        } else {
            //更改形象的订单ID
            TdhVirtualHuman tdhVirtualHuman = tdhVirtualHumanMapper.findByOrderNoAndUserId(request.getOrderNo(), record.getUserId());
            TdhVirtualHuman reqBO = new TdhVirtualHuman();
            reqBO.setOrderNo(orderNo);
            reqBO.setTdhId(tdhVirtualHuman.getTdhId());
            tdhVirtualHumanMapper.update(reqBO);
        }
        log.info("运营平台-形象定制-订单改价成功，request:{}|{}", request, updated);
        TdhCustomizeRecord customizeRecord = tdhCustomizeRecordMapper.findByOrderNo(orderNo);
        // 发送用户维度报表消息
        trainEventSender.sendUserRpEventByTdhCust(customizeRecord, request.getOrderPrice());
        TdhCustomizeRecordQueryRspBO rspBO = new TdhCustomizeRecordQueryRspBO();
        BeanUtils.copyProperties(customizeRecord, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO, "订单改价成功");
    }

    @Override
    @MethodParamVerifyEnable
    public Rsp modifyStatus(TdhCustomizeRecordModifyStatusReqBO request) {
        log.info("运营平台-形象/声音定制-修改状态，request:{}", request);
        long start = System.currentTimeMillis();
        if (!CustomizeType.isExistCode(request.getCustomizeType()) || !CustomizeStatusEnum.isExistCode(request.getCustomizeStatus())) {
            log.error("运营平台-形象/声音定制-修改状态失败，request: {}", request);
            return BaseRspUtils.createErrorRsp("状态或类型不合法");
        }
        TdhCustomizeRecord customizeRecord = new TdhCustomizeRecord();
        BeanUtils.copyProperties(request, customizeRecord);
        int update = tdhCustomizeRecordMapper.updateByOrderNo(customizeRecord, request.getOrderNo());
        if (update < 1) {
            log.error("运营平台-形象/声音定制-修改状态失败，request:{}|{}", request, customizeRecord);
            return BaseRspUtils.createErrorRsp("修改状态失败");
        }

        TdhCustomizeRecord record = tdhCustomizeRecordMapper.findByOrderNo(request.getOrderNo());
        TdhCustomizeRecordQueryRspBO rspBO = new TdhCustomizeRecordQueryRspBO();
        BeanUtils.copyProperties(record, rspBO);
        NoticeBO noticeBO = new NoticeBO();
        noticeBO.setTenantCode(record.getTenantCode());
        if (CustomizeType.AUDIO.getCode().equals(customizeRecord.getCustomizeType())) {
            TdhVirtualAnchorQueryReqBO reqBO = new TdhVirtualAnchorQueryReqBO();
            reqBO.setOrderNo(request.getOrderNo());
            reqBO.setCustomizeStatus(request.getCustomizeStatus());
            if (CustomizeStatusEnum.REVIEW_COMPLETED.getCode().equals(request.getCustomizeStatus())) {
                // 声音定制处理（运营平台保存声音）
                reqBO.setVolcId(request.getVolcId());
                reqBO.setDemoUrl(request.getVoiceDemo());
            } else if (CustomizeStatusEnum.CANCEL.getCode().equals(request.getCustomizeStatus())) {
                // 退款成功后，将语音文件删除
                reqBO.setIsValid(EntityValidType.DELETE.getCode());
                //发送通知
                noticeBO.setOrderId(record.getOrderNo());
                noticeBO.setUserId(record.getUserId());
                noticeBO.setType(NoticeBO.Type.AUDIO);
                noticeBO.setAction(NoticeBO.Action.CANCEL);
                try {
                    noticeApi.notice(noticeBO);
                } catch (Exception e) {
                    log.error("运营平台-声音定制-修改状态成功-发送通知失败，reqBO:{}|{}", request, e.getMessage());
                }
            } else if (CustomizeStatusEnum.CUSTOMIZE_COMPLETED.getCode().equals(request.getCustomizeStatus())) {
                //发送通知
                noticeBO.setOrderId(record.getOrderNo());
                noticeBO.setUserId(record.getUserId());
                noticeBO.setType(NoticeBO.Type.AUDIO);
                noticeBO.setAction(NoticeBO.Action.CUSTOM);
            }
            tdhAnchorBusiService.updateAnchor(reqBO);
        } else {
            TdhVirtualHumanReqBO reqBO = new TdhVirtualHumanReqBO();
            reqBO.setOrderNo(request.getOrderNo());
            reqBO.setCustomizeStatus(request.getCustomizeStatus());
            reqBO.setVipFlag(request.getVipFlag());
            if (CustomizeStatusEnum.REVIEW_COMPLETED.getCode().equals(request.getCustomizeStatus())) {
                // 数字人定制处理（运营平台保存形象）
                reqBO.setTdhImgThu(request.getTdhImg());
                reqBO.setTdhImg(request.getTdhImg());
            } else if (CustomizeStatusEnum.CANCEL.getCode().equals(request.getCustomizeStatus())) {
                // 退款成功后，将语音文件删除
                reqBO.setIsValid(EntityValidType.DELETE.getCode());
                //发送通知
                noticeBO.setOrderId(record.getOrderNo());
                noticeBO.setUserId(record.getUserId());
                noticeBO.setType(NoticeBO.Type.TDH);
                noticeBO.setAction(NoticeBO.Action.CANCEL);
            } else if (CustomizeStatusEnum.CUSTOMIZE_COMPLETED.getCode().equals(request.getCustomizeStatus())) {
                //定制完成，发送通知
                noticeBO.setOrderId(record.getOrderNo());
                noticeBO.setUserId(record.getUserId());
                noticeBO.setType(NoticeBO.Type.TDH);
                noticeBO.setAction(NoticeBO.Action.CUSTOM);
                // 数字人定制处理（运营平台保存形象）
                reqBO.setTdhImgThu(record.getTdhImg());
                reqBO.setTdhImg(record.getTdhImg());
            }
            tdhHumanBusiService.updateHuman(reqBO);
        }
        if (ObjectUtils.isNotEmpty(noticeBO)) {
            try {
                noticeApi.notice(noticeBO);
            } catch (Exception e) {
                log.error("运营平台-声音定制-通知失败，noticeBO:{}", noticeBO, e);
            }
        }
        // 发送用户维度报表消息
        trainEventSender.sendUserRpEventByTdhCust(record, null);
        return BaseRspUtils.createSuccessRsp(rspBO, "修改状态成功");
    }
}
