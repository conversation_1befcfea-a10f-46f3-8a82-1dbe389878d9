package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.tydic.nbchat.train.api.CourseVoiceAsrListener;
import com.tydic.nbchat.train.api.CourseVoiceListener;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceAsrOnSuccess;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;
import com.tydic.nbchat.train.core.config.AliTtsConfigProperties;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class AliAsrTtsHelper implements NlsHelperApi {

    private final static String ASR_FILETRANS_DOMAN = "filetrans.cn-shanghai.aliyuncs.com";
    private final static String ASR_FILETRANS_PRODUCT = "nls-filetrans";

    private final AliAccessTokenHelper aliAccessTokenHelper;
    private final AliTtsConfigProperties aliTtsConfigProperties;
    private final CourseVoiceListener courseVoiceListener;
    private final CourseVoiceAsrListener courseVoiceAsrListener;
    private final NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;

    public AliAsrTtsHelper(AliAccessTokenHelper aliAccessTokenHelper,
                           AliTtsConfigProperties aliTtsConfigProperties,
                           @Autowired(required = false) CourseVoiceListener courseVoiceListener,
                           @Autowired(required = false) CourseVoiceAsrListener courseVoiceAsrListener,
                           NbchatTrainSectionApi nbchatTrainSectionApi) {
        this.aliAccessTokenHelper = aliAccessTokenHelper;
        this.aliTtsConfigProperties = aliTtsConfigProperties;
        this.courseVoiceListener = courseVoiceListener;
        this.courseVoiceAsrListener = courseVoiceAsrListener;
        this.nbchatTrainSectionApi = nbchatTrainSectionApi;
        log.info("初始化tts配置:{}", aliTtsConfigProperties);
    }


    @Override
    public String anchorConfig() {
        return AnchorType.ALI.getCode();
    }

    public void createAudioTask(String courseId, String sectionId, boolean courseAll) {
        if (!courseAll && StringUtils.isNotEmpty(courseId)) {
            NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(courseId);
            createAudioTask(courseId, null, course.getCourseDesc(), true);
        }
        if (StringUtils.isNotEmpty(sectionId)) {
            Rsp<String> rsp = nbchatTrainSectionApi.querySection(sectionId);
            createAudioTask(null, sectionId, rsp.getData(), true);
        }
        if (courseAll && StringUtils.isNotEmpty(courseId)) {
            List<NbchatTrainSections> nbchatTrainSections = nbchatTrainSectionsMapper.selectBySectionIds(courseId, null);
            for (NbchatTrainSections section : nbchatTrainSections) {
                Rsp<String> rsp = nbchatTrainSectionApi.querySection(section.getSectionId());
                createAudioTask(null, section.getSectionId(), rsp.getData(), true);
            }
        }
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        return createAsrTask(request.getCourseId(), request.getFileId(), request.getFilePath(), false);
    }

    /**
     * 创建asr任务
     *
     * @param courseId
     * @param filepath
     * @param async    是否异步查询任务
     */
    public AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async) {
        /**
         * 创建CommonRequest 设置请求参数
         */
        CommonRequest postRequest = new CommonRequest();
        postRequest.setDomain(ASR_FILETRANS_DOMAN); // 设置域名，固定值。
        postRequest.setVersion("2018-08-17");         // 设置中国站的版本号。
        // postRequest.setVersion("2019-08-23");         // 设置国际站的版本号，国际站用户请设置此值。
        postRequest.setAction("SubmitTask");          // 设置action，固定值。
        postRequest.setProduct(ASR_FILETRANS_PRODUCT);      // 设置产品名称，固定值。
        postRequest.setHttpContentType(FormatType.JSON);
        JSONObject taskObject = new JSONObject();
        // 项目的Appkey，获取Appkey请前往控制台：https://nls-portal.console.aliyun.com/applist
        taskObject.put("appkey", aliTtsConfigProperties.getAppKey());
        taskObject.put("file_link", filepath);  // 设置录音文件的链接
        taskObject.put("version", "4.0");
        String task = taskObject.toJSONString();
        postRequest.putBodyParameter("Task", task);
        postRequest.setMethod(MethodType.POST);
        String err = "任务执行异常:";
        AsrVoiceTaskContext context = new AsrVoiceTaskContext();
        context.setCourseId(courseId);
        try {
            /**
             * 提交录音文件识别请求
             */
            log.info("录音文件识别[{}]-任务创建-开始:{}", courseId, filepath);
            CommonResponse postResponse = aliAccessTokenHelper.getiAcsClient().getCommonResponse(postRequest);
            log.info("录音文件识别[{}]-任务创建-完成:{}", courseId, postResponse.getData());
            if (postResponse.getHttpStatus() == 200) {
                JSONObject result = JSONObject.parseObject(postResponse.getData());
                String statusText = result.getString("StatusText");
                if ("SUCCESS".equals(statusText)) {
                    final String taskId = result.getString("TaskId");
                    log.info("录音文件识别[{}]-拉取任务-开始:{}", courseId, taskId);
                    if (async) {
                        new Thread(() -> getAsrTaskResult(courseId, fileId, taskId)).start();
                        context.setTaskId(taskId);
                        return context;
                    } else {
                        return getAsrTaskResult(courseId, fileId, taskId);
                    }
                }
            } else {
                err += postResponse.getHttpStatus();
            }
        } catch (Exception e) {
            log.info("录音文件识别[{}]-异常:", courseId, e);
            err = e.getMessage();
        }
        final CourseVoiceOnError onError = CourseVoiceOnError.builder().courseId(courseId).fileId(fileId).error(err).build();
        Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onError(onError));
        return context;
    }

    /**
     * 创建语音合成任务
     *
     * @param taskRequest
     * @return
     */
    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest taskRequest) {
        String text = taskRequest.getText();
        String voice = taskRequest.getVoice();
        String sectionId = taskRequest.getSectionId();
        String courseId = taskRequest.getCourseId();
        boolean async = taskRequest.isAsync();
        Integer volume = taskRequest.getVolume();
        Integer speech_rate = taskRequest.getSpeechRate();
        Integer pitch_rate = taskRequest.getPitchRate();
        TtsVoiceTaskContext ttsVoiceTaskContext = new TtsVoiceTaskContext();
        String appKey = aliTtsConfigProperties.getAppKey();
        String token = aliAccessTokenHelper.getAccessToken();
        String url = aliTtsConfigProperties.getTtsApi();
        // 拼接Http Post请求的消息体内容
        JSONObject context = new JSONObject();
        // device_id设置，可以设置为自定义字符串或者设备信息id
        context.put("device_id", "dyc_nbchat_train");
        JSONObject header = new JSONObject();
        // 必选：设置你的appkey
        header.put("appkey", appKey);
        // 必选：设置你的token
        header.put("token", token);
        // voice 发音人，可选，默认是xiaoyun
        // volume 音量，范围是0~100，可选，默认50
        // speech_rate 语速，范围是-500~500，可选，默认是0
        // pitch_rate 语调，范围是-500~500，可选，默认是0
        JSONObject tts = new JSONObject();
        text = text.replace("&", "");
        tts.put("text",TrainCommonUtil.trimXmlTagName(text, "emotion"));
        // 设置发音人
        tts.put("voice", voice);
        if (volume != null) {
            tts.put("volume", volume);
        }
        if (speech_rate != null) {
            tts.put("speech_rate", speech_rate);
        }
        if (pitch_rate != null) {
            tts.put("pitch_rate", pitch_rate);
        }
        // 设置编码格式
        tts.put("format", aliTtsConfigProperties.getFormat());
        // 设置采样率
        tts.put("sample_rate", aliTtsConfigProperties.getSampleRate());
        tts.put("enable_subtitle", true);
        // 设置声音大小，可选
        //tts.put("volume", 100);
        // 设置语速，可选
        //tts.put("speech_rate", 200);
        JSONObject payload = new JSONObject();
        // 可选，是否设置回调(enable_notify为true且notify_url有效)，如果设置，则服务端在完成长文本语音合成之后回调用用户此处设置的回调接口，将请求状态推送给用户侧
        payload.put("enable_notify", false);
        payload.put("notify_url", aliTtsConfigProperties.getNotifyUrl());
        payload.put("tts_request", tts);
        JSONObject json = new JSONObject();
        json.put("context", context);
        json.put("header", header);
        json.put("payload", payload);
        String bodyContent = json.toJSONString();
        log.info("语音合成-任务开始[{}]: {}", sectionId, bodyContent);
        // 发起请求
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), bodyContent);
        Request request = new Request.Builder().url(url).
                header("Content-Type", "application/json")
                .post(reqBody)
                .build();
        try {
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            // 获取结果，并根据返回进一步进行处理
            String result = response.body().string();
            response.close();
            log.info("语音合成-任务结果[{}]: {}", sectionId, result);
            JSONObject resultJson = JSON.parseObject(result);
            if (resultJson.containsKey("error_code") && resultJson.getIntValue("error_code") == 20000000) {
                String taskId = resultJson.getJSONObject("data").getString("task_id");
                String requestId = resultJson.getString("request_id");
                log.info("语音合成-获取任务信息[{}]: task_id = {},request_id = {}", sectionId, taskId, requestId);
                if (async) {
                    //开始轮询
                    new Thread(() -> getAudioResult(courseId, sectionId, taskId, requestId)).start();
                } else {
                    ttsVoiceTaskContext = getAudioResult(courseId, sectionId, taskId, requestId);
                }
            } else {
                String error = resultJson.getString("error_message");
                log.error("语音合成-任务异常[{}]: status={}, error_code={}, error_message={}", sectionId,
                        resultJson.getIntValue("status"),
                        resultJson.getIntValue("error_code"),
                        error);
                final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(error).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            }
        } catch (Exception e) {
            final CourseVoiceOnError onError = CourseVoiceOnError.builder().sectionId(sectionId).error(e.getMessage()).build();
            Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            log.error("语音合成-任务异常[{}]: ", sectionId, e);
        }
        ttsVoiceTaskContext.setCourseId(courseId);
        ttsVoiceTaskContext.setSectionId(sectionId);
        return ttsVoiceTaskContext;
    }

    /**
     * 创建语音合成任务
     *
     * @param courseId
     * @param sectionId
     * @param text
     * @param async
     * @return
     */
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, boolean async) {
        return createAudioTask(courseId, sectionId, text, aliTtsConfigProperties.getVoice(), async);
    }

    /**
     * 创建语音合成任务
     *
     * @param sectionId 章节id
     * @param text      输入内容
     */
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async) {
        TtsVoiceTaskRequest taskRequest = new TtsVoiceTaskRequest();
        taskRequest.setCourseId(courseId);
        taskRequest.setSectionId(sectionId);
        taskRequest.setText(text);
        taskRequest.setVoice(voice);
        taskRequest.setAsync(async);
        return createAudioTask(taskRequest);
    }

    /**
     * 获取录音结果
     *
     * @param sectionId
     * @param taskId
     * @param requestId
     * @return
     */
    private TtsVoiceTaskContext getAudioResult(String courseId, String sectionId, String taskId, String requestId) {
        String appKey = aliTtsConfigProperties.getAppKey();
        String token = aliAccessTokenHelper.getAccessToken();
        String fullUrl = aliTtsConfigProperties.getTtsApi() +
                "?appkey=" + appKey +
                "&task_id=" + taskId +
                "&token=" + token +
                "&request_id=" + requestId;
        String address = "";
        log.info("语音合成-获取录音[{}]: {}", sectionId, fullUrl);
        TtsVoiceTaskContext context = new TtsVoiceTaskContext();
        while (true) {
            Request request = new Request.Builder().url(fullUrl).get().build();
            try {
                OkHttpClient client = new OkHttpClient();
                Response response = client.newCall(request).execute();
                String result = response.body().string();
                response.close();
                log.info("语音合成-获取录音-结果[{}]: {}", sectionId, result);
                JSONObject resultJson = JSON.parseObject(result);
                if (resultJson.containsKey("error_code")
                        && resultJson.getIntValue("error_code") == 40000000
                        && resultJson.containsKey("data")
                        && resultJson.getJSONObject("data") == null) {
                    log.warn("语音合成-失败: {}", resultJson);
                    break;
                }
                if (resultJson.containsKey("error_code")
                        && resultJson.getIntValue("error_code") == 20000000
                        && resultJson.containsKey("data")
                        && resultJson.getJSONObject("data").getString("audio_address") != null) {
                    //address = resultJson.getJSONObject("data").getString("audio_address");
                    context = resultJson.getObject("data", TtsVoiceTaskContext.class);
                    Optional.ofNullable(context.getSentences()).ifPresent(sens -> {
                        for (TtsVoiceResult voiceResult : sens) {
                            //移除xml标记
                            String txt = TrainCommonUtil.removeXMLTags(voiceResult.getText()).trim();
                            voiceResult.setText(txt);
                        }
                    });
                    address = context.getAudio_address();
                    log.info("语音合成-获取录音-地址[{}]: {}", sectionId, address);
                    break;
                }
                // 每隔3秒钟轮询一次状态
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("语音合成-获取录音-异常[{}]: taskId = {}, requestId = {}", sectionId, taskId, requestId, e);
                final CourseVoiceOnError onError = CourseVoiceOnError.builder().
                        sectionId(sectionId).
                        taskId(taskId).error(e.getMessage()).build();
                Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
            }
        }
        //回调写入数据
        final CourseVoiceOnSuccess onSuccess = CourseVoiceOnSuccess.builder().
                courseId(courseId).sectionId(sectionId).taskId(taskId).address(address).build();
        final TtsVoiceTaskContext finalContext = context;
        Optional.ofNullable(courseVoiceListener).ifPresent(listener -> finalContext.setAudio_address(listener.onSuccess(onSuccess)));
        return finalContext;
    }


    /**
     * 获取录音转文本任务
     *
     * @param courseId
     * @param taskId
     * @return
     */
    public AsrVoiceTaskContext getAsrTaskResult(String courseId, String fileId, String taskId) {
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain(ASR_FILETRANS_DOMAN);   // 设置域名，固定值。
        getRequest.setVersion("2018-08-17");         // 设置中国站的版本号。
        getRequest.setAction("GetTaskResult");           // 设置action，固定值。
        getRequest.setProduct(ASR_FILETRANS_PRODUCT);          // 设置产品名称，固定值。
        getRequest.putQueryParameter("TaskId", taskId);  // 设置任务ID为查询参数。
        getRequest.setMethod(MethodType.GET);            // 设置为GET方式的请求。
        /**
         * 提交录音文件识别结果查询请求
         * 以轮询的方式进行识别结果的查询，直到服务端返回的状态描述为“SUCCESS”、“SUCCESS_WITH_NO_VALID_FRAGMENT”，或者为错误描述，则结束轮询。
         */
        AsrVoiceTaskContext context = new AsrVoiceTaskContext();
        try {
            /**
             * {"TaskId":"53b432abd5944acfa7f3fe1a12dfc459","RequestId":"37299948-4CB6-5B20-BEFF-60E24C7CCAB7",
             * "StatusText":"SUCCESS","BizDuration":3101,"SolveTime":1688023363204,"RequestTime":1688023360767,"StatusCode":21050000,"Result":
             * {"Sentences":[{"EndTime":2510,"SilenceDuration":0,"BeginTime":880,"Text":"北京的天气。","ChannelId":0,
             * "SpeechRate":184,"EmotionValue":6.7}]}}
             */
            String statusText = "";
            context.setCourseId(courseId);
            while (true) {
                Thread.sleep(1000);
                log.info("录音文件识别[{}]-任务查询:{}", courseId, taskId);
                CommonResponse getResponse = aliAccessTokenHelper.getiAcsClient().getCommonResponse(getRequest);
                if (getResponse.getHttpStatus() != 200) {
                    log.info("录音文件识别[{}]-任务失败:{}", courseId, getResponse.getData());
                    break;
                }
                JSONObject result = JSONObject.parseObject(getResponse.getData());
                statusText = result.getString("StatusText");
                if ("RUNNING".equals(statusText) || "QUEUEING".equals(statusText)) {
                    log.info("录音文件识别[{}]-任务状态:{}", courseId, statusText);
                } else {
                    log.info("录音文件识别[{}]-任务完成:{}", courseId, result.toJSONString());
                    context = result.toJavaObject(AsrVoiceTaskContext.class);
                    List<AsrVoiceResult> sentences = result.getJSONObject("Result").
                            getJSONArray("Sentences").toJavaList(AsrVoiceResult.class);
                    context.setSentences(sentences);
                    context.setCourseId(courseId);
                    CourseVoiceAsrOnSuccess onSuccess = CourseVoiceAsrOnSuccess.builder().
                            courseId(courseId).
                            fileId(fileId).
                            taskContext(context).
                            taskId(taskId).build();
                    Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onSuccess(onSuccess));
                    break;
                }
            }
        } catch (Exception e) {
            log.error("录音文件识别[{}]-任务异常:", courseId, e);
            final CourseVoiceOnError onError = CourseVoiceOnError.builder().taskId(taskId).
                    courseId(courseId).fileId(fileId).error(e.getMessage()).build();
            Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onError(onError));
        }
        return context;
    }


}
