package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainTaskWhiteListApi;
import com.tydic.nbchat.train.api.bo.task.NbchatTrainTaskWhiteListReqBO;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/task/white")
@AllArgsConstructor
public class TrainTaskWhiteListController {
    private final NbchatTrainTaskWhiteListApi nbchatTrainTaskWhiteListApi;

    /**
     * 添加白名单列表
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.opUser})
    @PostMapping("/add")
    public Rsp addWhiteList(@RequestBody NbchatTrainTaskWhiteListReqBO reqBO) {
        return nbchatTrainTaskWhiteListApi.addWhiteList(reqBO);
    }

    /**
     * 查询白名单列表
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.opUser})
    @PostMapping("/list")
    public RspList listWhiteList(@RequestBody NbchatTrainTaskWhiteListReqBO reqBO) {
        return nbchatTrainTaskWhiteListApi.queryWhiteList(reqBO);
    }

    /**
     * 移除白名单列表
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.opUser})
    @PostMapping("/delete")
    public Rsp deleteWhiteList(@RequestBody NbchatTrainTaskWhiteListReqBO reqBO) {
        return nbchatTrainTaskWhiteListApi.deleteWhiteList(reqBO);
    }
}
