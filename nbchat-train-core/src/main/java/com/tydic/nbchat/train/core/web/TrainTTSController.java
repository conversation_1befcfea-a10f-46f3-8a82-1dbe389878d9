package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/course/tts")
public class TrainTTSController {
    private final NlsStrategyInvokeFactory nlsStrategyInvokeFactory;

    public TrainTTSController(NlsStrategyInvokeFactory nlsStrategyInvokeFactory) {
        this.nlsStrategyInvokeFactory = nlsStrategyInvokeFactory;
    }

    @PostMapping("/task/create")
    public Rsp<AsrVoiceTaskContext> createTtsTask(@RequestBody TtsVoiceTaskRequest request){
        String text = TrainCommonUtil.removeXMLTags(request.getText()).trim();
        if (StringUtils.isBlank(text.trim())) {
            return BaseRspUtils.createErrorRsp("请输入文本内容!");
        }
        TtsVoiceTaskContext context = nlsStrategyInvokeFactory.createAudioTask(request);
        if(context.isSuccess()){
            return BaseRspUtils.createSuccessRsp(context);
        } else {
            return BaseRspUtils.createErrorRsp(context,"语音合成异常!");
        }
    }

    @PostMapping("new")
    public TtsVoiceTaskContext createTts(@RequestBody TtsReqBO reqBO){
        log.info("tts开始 :{}",reqBO);
        return nlsStrategyInvokeFactory.getApi().createAudioTask(reqBO.getCourseId(),reqBO.getSectionId(),reqBO.getTxt(),true);
    }

    @Deprecated
    @PostMapping("course")
    public void createCourseTts(@RequestBody TtsReqBO reqBO){
        log.info("tts开始 :{}",reqBO);
        //nlsStrategyInvokeFactory.getApi().
        //        createAudioTask(reqBO.getCourseId(),reqBO.getSectionId(), reqBO.isCourseAll());
    }
}
