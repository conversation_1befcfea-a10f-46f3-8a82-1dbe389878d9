package com.tydic.nbchat.train.core.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;



import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
public class TrainCommonUtil {

    private final static ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) {
        String text = "<speak><emotion style=''>大家好。我是参展人小画家。非常荣幸能与大家分享我的绘画世界。今天。我要向大家介绍的第一幅作品是《海天一色》。这幅画是我对自然之美的赞歌。它不仅展现了我对艺术的热爱。更是我内心世界的反映。在这片广袤的大海上。天空与海洋仿佛无缝衔接。分不清哪里是天。哪里是海。我用明亮的色彩和细腻的笔触。尽力捕捉这宁静而壮丽的景象。创作这幅画的过程中。我深受大自然的力量和和谐所启发。我希望观者能在这幅作品中感受到我对世界的敬畏以及我对美好生活的向往。在接下来的日子里。我还将分享更多我心中的色彩和故事。敬请期待。\\n</emotion></speak>";
        System.out.println(trimXmlTagName(text, "emotion"));
    }


    public static void writeWavFromBase64(String base64, String filePath) {
        // base64字符串转byte[]，写入文件
        byte[] bytes = Base64.getDecoder().decode(base64);
        writeBytesToFile(bytes, filePath);
    }

    public static void writeBytesToFile(byte[] bytes, String filePath) {
        // 写入文件
        try {
            Files.write(Paths.get(filePath), bytes);
        } catch (IOException e) {
            log.error("Failed to write bytes to file", e);
        }
    }

    public static String hmacSha256AndBase64Encode(String text, String secret){
        //加密字符串
        try {
            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKey);
            byte[] hashedBytes = sha256HMAC.doFinal(text.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashedBytes);
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate HMAC-SHA256", e);
        }
    }



    /**
     * 移除xml标签
     *
     * @param text
     * @return
     */
    public static String removeXMLTags(String text) {
        // 正则表达式用于匹配XML标签及其属性
        String regex = "<[^>]+>";
        Pattern pattern = Pattern.compile(regex);
        // 使用空字符串替换匹配到的XML标签及其属性
        String result = pattern.matcher(text).replaceAll("");
        return result;
    }


    /***
     * 去除指定标签，保留标签里的内容
     * @param content
     * @param tagName
     * @return
     */
    public static String trimXmlTagName(String content, String tagName) {
        String regex = "<" + tagName + ".*?>|</" + tagName + ">";
        return content.replaceAll(regex, "");
    }

    /**
     * 从文本提取json字符串
     *
     * @param text
     * @return
     */
    public static String extractJson(String text) {
        log.info("提取json数组：{}", text);
        if (StringUtils.isBlank(text)) {
            return text;
        }
        // 提取JSON串
        int index1 = text.indexOf("[");
        int index2 = text.indexOf("{");
        String jsonString = "";
        if (index1 == -1 && index2 == -1) {
            log.info("提取json数组-提取来源中不包含json串");
            return text;
        }
        try {
            if (index1 < index2) {
                int startIndex = text.indexOf("[");
                int endIndex = text.lastIndexOf("]");
                jsonString = text.substring(startIndex, endIndex + 1);
            } else {
                int startIndex = text.indexOf("{");
                int endIndex = text.lastIndexOf("}");
                jsonString = text.substring(startIndex, endIndex + 1);
            }
            // 解析JSON
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            // 输出JSON结构
            log.debug("Extracted JSON:{}", jsonNode.toString());
            return jsonNode.toString();
        } catch (Exception e) {
            log.warn("提取json数组-异常:text = {},err = {}", text, e.getMessage());
        }
        return text;
    }
}
