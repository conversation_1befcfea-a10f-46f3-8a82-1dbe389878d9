package com.tydic.nbchat.train.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.NbchatTrainTaskWhiteListApi;
import com.tydic.nbchat.train.api.bo.task.NbchatTrainTaskWhiteListReqBO;
import com.tydic.nbchat.train.api.bo.task.NbchatTrainTaskWhiteListRspBO;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskWhiteListMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskWhiteList;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskWhiteListCondition;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class NbchatTrainTaskWhiteListServiceImpl implements NbchatTrainTaskWhiteListApi {
    private final NbchatTrainTaskWhiteListMapper nbchatTrainTaskWhiteListMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MethodParamVerifyEnable
    public Rsp addWhiteList(NbchatTrainTaskWhiteListReqBO reqBO) {
        List<NbchatTrainTaskWhiteList> list = reqBO.getNbchatTrainTaskWhiteList();
        int num = nbchatTrainTaskWhiteListMapper.insertList(list);
        return BaseRspUtils.createSuccessRsp(num,"添加成功");
    }

    @Override
    public RspList queryWhiteList(NbchatTrainTaskWhiteListReqBO reqBO) {
        List<NbchatTrainTaskWhiteListRspBO> list = new ArrayList<>();
        NbchatTrainTaskWhiteListCondition condition = new NbchatTrainTaskWhiteListCondition();
        BeanUtils.copyProperties(reqBO,condition);
        Page page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        condition.setUserId(null);
        condition.setTenantCode(reqBO.getTargetTenantCode());
        nbchatTrainTaskWhiteListMapper.queryWhiteList(condition);
        if (CollectionUtils.isEmpty(page.getResult())){
            log.info("查询白名单-查询结果为空：{}",reqBO);
            return BaseRspUtils.createSuccessRspList(list, list.size());
        }
        NiccCommonUtil.copyList(page.getResult(),list,NbchatTrainTaskWhiteListRspBO.class);
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp deleteWhiteList(NbchatTrainTaskWhiteListReqBO reqBO) {
        List<Integer> list = reqBO.getNbchatTrainTaskWhiteList();
        int num = nbchatTrainTaskWhiteListMapper.deleteList(list);
        return BaseRspUtils.createSuccessRsp(num,"删除成功");
    }
}
