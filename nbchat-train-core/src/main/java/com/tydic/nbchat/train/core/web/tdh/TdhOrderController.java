package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.CustomizeOrderApi;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.user.api.bo.vip.NbchatUserVipRightsBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/tdh_customize")
public class TdhOrderController {

    private final CustomizeOrderApi customizeOrderApi;

    @PostMapping("/create_order")
    public Rsp create(@RequestBody TdhCustomizeRecordQueryReqBO request) {
        return customizeOrderApi.createOrder(request);
    }

    @PostMapping("/confirm_order")
    public Rsp confirmOrder(@RequestBody NbchatUserVipRightsBO request) {
        return customizeOrderApi.updateRights(request);
    }

    /**
     * 专业会员免费定制
     * @param request
     * @return
     */
    @PostMapping("profess/free")
    public Rsp freeCustomize(@RequestBody NbchatUserVipRightsBO request) {
        return customizeOrderApi.freeCustomize(request);
    }

    /**
     * 首次免费定制
     * @param request
     * @return
     */
    @PostMapping("first/free")
    public Rsp firstFree(@RequestBody NbchatUserVipRightsBO request) {
        request.setIsFreeExperience(true);
        request.setStartTime(new Date());
        request.setEndTime(DateTimeUtil.DateAddHour(24));
        return customizeOrderApi.freeCustomize(request);
    }

}
