package com.tydic.nbchat.train.core.busi;

import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.common.nbchat.msg.UserRpEventContext;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@AllArgsConstructor
public class TrainEventSender {

    private final KKMqProducerHelper kkMqProducerHelper;
    private final TdhCustomizeRecordMapper tdhCustomizeRecordMapper;

    /**
     * 发送用户报表事件消息，基于提供的定制记录数据。
     *
     * @param data        定制记录数据
     * @param oldPayPrice 旧的支付价格
     */
    public void sendUserRpEventByTdhCust(TdhCustomizeRecord data, Integer oldPayPrice) {
        try {
            if (StringUtils.isBlank(data.getCustomizeType())) {
                data = tdhCustomizeRecordMapper.findByOrderNo(data.getOrderNo());
            }
            String tdhType = getTdhType(data.getCustomizeType());
            if (tdhType == null) {
                return;
            }
            sendUserRpEvent(UserRpEventContext.buildForTdhCust(data.getTenantCode(), data.getUserId(), new Date(), tdhType, data.getCustomizeStatus(), data.getPayPrice(), oldPayPrice));
        } catch (Exception e) {
            log.error("发送用户维度报表事件消息-异常:", e);
        }
    }

    /**
     * 将用户报告事件消息发送到消息队列中的指定主题。
     * 该方法构造用户报告事件上下文并尝试发送它。
     * 如果在发送过程中发生异常，则会将其捕获并记录为错误。
     *
     * @param tenantCode 事件所针对的租户的代码
     * @param userId     与事件关联的用户的标识符
     * @param pptType    PPT类型 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
     */
    public void sendUserRpEventByPptMake(String tenantCode, String userId, String pptType) {
        if (StringUtils.isBlank(pptType)) {
            return;
        }
        sendUserRpEvent(UserRpEventContext.buildForPptMake(tenantCode, userId, new Date(), pptType));
    }

    /**
     * 将用户报告事件消息发送到消息队列中的指定主题，基于视频制作。
     * 该方法构造用户报告事件上下文并尝试发送它。
     * 如果在发送过程中发生异常，则会将其捕获并记录为错误。
     *
     * @param tenantCode 事件所针对的租户的代码
     * @param userId     与事件关联的用户的标识符
     */
    public void sendUserRpEventByVideoMake(String tenantCode, String userId) {
        sendUserRpEvent(UserRpEventContext.buildForVideoMake(tenantCode, userId, new Date()));
    }

    private void sendUserRpEvent(UserRpEventContext context) {
        try {
            log.info("发送用户维度报表事件消息: {}", context);
            kkMqProducerHelper.sendMsg(NbchatTopicsConstants.NBCHAT_USER_RP_EVENT_TOPIC, context);
        } catch (Exception e) {
            log.error("发送用户维度报表事件消息-异常:{}", context, e);
        }
    }

    /**
     * 根据提供的定制类型获取对应的TDH类型。
     *
     * @param customizeType 定制类型字符串，可以是 "2d_mtk", "2.5d_mtk" 或 "audio"
     * @return 对应的TDH类型字符串，如果输入的定制类型不在预定义的范围内，则返回 null
     */
    private static String getTdhType(String customizeType) {
        switch (customizeType) {
            case "2d_mtk":
                return "0";
            case "2.5d_mtk":
                return "1";
            case "audio":
                return "2";
            default:
                log.info("当前数字人非定制数字人");
                return null;
        }
    }
}
