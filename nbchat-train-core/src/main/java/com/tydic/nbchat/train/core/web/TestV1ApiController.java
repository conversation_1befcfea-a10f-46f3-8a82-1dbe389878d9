package com.tydic.nbchat.train.core.web;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/v1/")
public class TestV1ApiController {
    private final static SseEmitter emitter = new SseEmitter();
    ;

    @PostMapping(value = "/chat/completions")
    public void chatProcessEvent1(@RequestBody RobotMessageRequest request, HttpServletResponse res) throws Exception{
        res.setContentType("text/event-stream;charset=UTF-8");
        //替换为组装的答案
        String result = "我是百度公司开发的人工智能语言模型，我的中文名是文心一言，英文名是ERNIE Bot，可以协助您完成范围广泛的任务并提供有关各种主题的信息，比如回答问题，提供定义和解释及建议。如果您有任何问题，请随时向我提问。";
        int start = 0;
        int step = 5; //每次5个字符
        String content = "";
        boolean finish = false;
        while (true) {
            JSONObject context = null;
            if (start + step >= result.length()) {
                content = result.substring(start, result.length());
                step = result.length();
                context = buildContext("stop", content);
                finish = true;
            } else {
                content = result.substring(start, start + step);
                start += 5;
                context = buildContext("", content);
            }
            log.info("推送数据:{}", context);
            try {
                res.getWriter().write("data: " + context.toJSONString() + "\n\n");
                res.getWriter().flush();
            } catch (Exception e) {
                log.error("推送数据-异常:{}", context, e);
            }
            if (finish) {
                break;
            }
        }
        // 用[done]来标识结束
        res.getWriter().write("data: [done]\n\n");
        res.getWriter().flush();
    }

    @PostMapping(value = "/chat/completions1")
    public SseEmitter chatProcessEvent(@RequestBody RobotMessageRequest request) {
        //替换为组装的答案
        String result = "我是百度公司开发的人工智能语言模型，我的中文名是文心一言，英文名是ERNIE Bot，可以协助您完成范围广泛的任务并提供有关各种主题的信息，比如回答问题，提供定义和解释及建议。如果您有任何问题，请随时向我提问。";
        int start = 0;
        int step = 5; //每次5个字符
        String content = "";
        boolean finish = false;
        while (true) {
            JSONObject context = null;
            if (start + step >= result.length()) {
                content = result.substring(start, result.length());
                step = result.length();
                context = buildContext("stop", content);
                finish = true;
            } else {
                content = result.substring(start, start + step);
                start += 5;
                context = buildContext("", content);
            }
            log.info("推送数据:{}", context);
            try {
                TimeUnit.MILLISECONDS.sleep(1000);
                emitter.send(context);
            } catch (Exception e) {
                log.error("推送数据-异常:{}", context, e);
            }
            if (finish) {
                break;
            }
        }
        log.info("推送数据:结束");
        emitter.complete();
        return emitter;
    }


    public static JSONObject buildContext(String finish, String text) {
        String autoId = IdWorker.nextAutoIdStr();
        String template = "{\n" +
                "  \"id\": \"" + autoId + "\",\n" +
                "  \"object\": \"chat.completion\",\n" +
                "  \"created\": " + System.currentTimeMillis() + ",\n" +
                "  \"model\": \"dic-knoledge\",\n" +
                "  \"choices\": [{\n" +
                "    \"index\": 0, \n" +
                "    \"message\": {\n" +
                "      \"role\": \"assistant\", \n" +
                "      \"content\": \"" + text + "\" \n" +
                "    },\n" +
                "    \"finish_reason\": \"" + finish + "\" \n" +
                "  }],\n" +
                "  \"usage\": {\n" +
                "    \"prompt_tokens\": 0,\n" +
                "    \"total_tokens\": 0,\n" +
                "    \"completion_tokens\": 0 \n" +
                "  }\n" +
                "}";
        return JSONObject.parseObject(template);
    }


}
