package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.TdhMaterialPackageApi;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageQueryBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageRspBo;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 图库素材包
 */
@Slf4j
@RestController
@RequestMapping("/train/tdh_material")
public class TdhMaterialPackageController {
    private final TdhMaterialPackageApi tdhMaterialPackageApi;

    public TdhMaterialPackageController(TdhMaterialPackageApi tdhMaterialPackageApi) {
        this.tdhMaterialPackageApi = tdhMaterialPackageApi;
    }

    /**
     * 根据条件查询图库素材包列表
     */
    @PostMapping("/package/list")
    public RspList<TdhMaterialPackageRspBo> getMaterialPackageList(@RequestBody TdhMaterialPackageQueryBO request) {
        return tdhMaterialPackageApi.getMaterialPackageList(request);
    }
    /**
     * 更新、新增素材包
     */
    @PostMapping("/package/save")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp<TdhMaterialPackageRspBo> saveMaterialPackage(@RequestBody TdhMaterialPackageQueryBO request) {
        return tdhMaterialPackageApi.saveMaterialPackage(request);

    }
    /**
     * 删除素材包并且批量删除图片素材
     */
    @PostMapping("/package/delete")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp deleteMaterialPackage(@RequestBody TdhMaterialPackageQueryBO request) {
        return tdhMaterialPackageApi.deleteMaterialPackage(request.getPkgId(), request.getIsValid());

    }
}
