package com.tydic.nbchat.train.core.service.impl.trainTask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.CourseType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.task.CourseFinishInfo;
import com.tydic.nbchat.train.api.bo.task.NbchatTaskRecordBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTaskInfoBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskConfigBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskCourseRelBO;
import com.tydic.nbchat.train.api.trainTask.TrainTaskApi;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TrainTaskServiceImpl implements TrainTaskApi {

    @Resource
    private NbchatTrainTaskMapper nbchatTrainTaskMapper;
    @Resource
    private NbchatTrainTaskCourseRelMapper nbchatTrainTaskCourseRelMapper;
    @Resource
    private NameMapper nameMapper;
    @Resource
    private NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    private NbchatTrainTaskWhiteListMapper nbchatTrainTaskWhiteListMapper;
    @Resource
    private NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    private NbchatTrainTaskDeptRelMapper nbchatTrainTaskDeptRelMapper;
    @Resource
    private NbchatExamTestRecordMapper nbchatExamTestRecordMapper;
    @Resource
    private NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;
    @Resource
    private NbchatTrainTaskUserRelMapper nbchatTrainTaskUserRelMapper;

    @Override
    @Transactional
    public Rsp save(NbchatTrainTaskConfigBO request) {
        log.info("保存学习任务信息：{}", request);
        NbchatTrainTaskBO taskConfig = request.getTaskConfig();
        Integer taskId = request.getTaskConfig().getId();

        if (ObjectUtils.isEmpty(taskId)) {
            NbchatTrainTask trainTask = new NbchatTrainTask();
            BeanUtils.copyProperties(taskConfig, trainTask);
            trainTask.setCreateUser(request.getUserId());
            trainTask.setTenantCode(request.getTenantCode());
            nbchatTrainTaskMapper.insertSelective(trainTask);
            taskId = trainTask.getId();
            if (CollectionUtils.isNotEmpty(taskConfig.getDeptIds())) {
                nbchatTrainTaskDeptRelMapper.insertBatch(taskId, taskConfig.getDeptIds());
            }
            if (CollectionUtils.isNotEmpty(taskConfig.getTargetUserIds())) {
                nbchatTrainTaskUserRelMapper.insertBatch(taskId, taskConfig.getTargetUserIds());
            }
            List<NbchatTrainTaskCourseRelBO> courseRelList = request.getCourseRelList();
            if (CollectionUtils.isNotEmpty(courseRelList)) {
                for (NbchatTrainTaskCourseRelBO relBO : courseRelList) {
                    NbchatTrainTaskCourseRel po = new NbchatTrainTaskCourseRel();
                    BeanUtils.copyProperties(relBO, po);
                    po.setTaskId(String.valueOf(trainTask.getId()));
                    nbchatTrainTaskCourseRelMapper.insertSelective(po);
                }
            }
        } else {
            //更新任务
            NbchatTrainTask trainTask = new NbchatTrainTask();
            BeanUtils.copyProperties(taskConfig, trainTask);
            trainTask.setUpdateUser(request.getUserId());
            List<String> deptIds = taskConfig.getDeptIds();
            List<String> userIds = taskConfig.getTargetUserIds();
            //更新用户关联
            if (CollectionUtils.isEmpty(deptIds) && CollectionUtils.isNotEmpty(userIds)) {
                nbchatTrainTaskDeptRelMapper.deleteByTaskId(taskId);
                nbchatTrainTaskUserRelMapper.deleteByTaskId(taskId);
                nbchatTrainTaskUserRelMapper.insertBatch(taskId, userIds);
                trainTask.setPostId("");
            }
            //更新部门、岗位关联
            if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isNotEmpty(deptIds)) {
                nbchatTrainTaskUserRelMapper.deleteByTaskId(taskId);
                nbchatTrainTaskDeptRelMapper.deleteByTaskId(taskId);
                nbchatTrainTaskDeptRelMapper.insertBatch(taskId, deptIds);
            }
            nbchatTrainTaskMapper.update(trainTask);
            //更新课程关联
            if (CollectionUtils.isNotEmpty(request.getCourseRelList())) {
                nbchatTrainTaskCourseRelMapper.deleteByTaskId(String.valueOf(trainTask.getId()));
                List<NbchatTrainTaskCourseRelBO> courseRelList = request.getCourseRelList();
                if (CollectionUtils.isNotEmpty(courseRelList)) {
                    for (NbchatTrainTaskCourseRelBO relBO : courseRelList) {
                        NbchatTrainTaskCourseRel po = new NbchatTrainTaskCourseRel();
                        BeanUtils.copyProperties(relBO, po);
                        po.setTaskId(String.valueOf(trainTask.getId()));
                        nbchatTrainTaskCourseRelMapper.insertSelective(po);
                    }
                }
            }
        }

        return BaseRspUtils.createSuccessRsp(taskId);
    }


    @Override
    public RspList list(NbchatTrainTaskBO request) {
        log.info("查询学习任务列表：{}", request);
        NbchatTrainTask cond = new NbchatTrainTask();
        BeanUtils.copyProperties(request, cond);
        Page<NbchatTrainTask> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatTrainTaskMapper.selectList(cond);
        List<NbchatTrainTaskBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), res, NbchatTrainTaskBO.class);
        for (NbchatTrainTaskBO re : res) {
            String deptNames = this.getDeptNamesAndSetUsers(re.getId(), new HashSet<>(), "0", null, request.getTenantCode());
            re.setDeptName(deptNames);

            String postNameStr = Arrays.stream(re.getPostId().split(",")).map(nameMapper::queryPostName)
                    .collect(Collectors.joining(","));
            re.setPostName(postNameStr);

            int taskCount = nameMapper.queryTaskCount(re.getId());
            re.setTaskCount(taskCount);
            String userName = nameMapper.queryUserName(re.getCreateUser(), re.getTenantCode());
            re.setCreateUserName(userName);
            String updateUserName = nameMapper.queryUserName(re.getUpdateUser(), re.getTenantCode());
            re.setUpdateUserName(updateUserName);

            List<JSONObject> taskUserRels = nbchatTrainTaskUserRelMapper.queryUserName(re.getId(), request.getTenantCode());
            if (CollectionUtils.isNotEmpty(taskUserRels)) {
                re.setTargetUserInfoList(taskUserRels);
            }
        }

        return BaseRspUtils.createSuccessRspList(res, page.getTotal());
    }

    @Override
    public Rsp info(NbchatTrainTaskBO request) {
        log.info("查询学习任务明细：{}", request);
        NbchatTrainTaskConfigBO info = new NbchatTrainTaskConfigBO();
        NbchatTrainTask trainTask = nbchatTrainTaskMapper.queryById(request.getId());
        if (ObjectUtils.isEmpty(trainTask)) {
            return BaseRspUtils.createSuccessRsp("未查询到学习任务信息");
        }
        NbchatTrainTaskBO taskConfig = new NbchatTrainTaskBO();
        BeanUtils.copyProperties(trainTask, taskConfig);
        info.setTaskConfig(taskConfig);

        NbchatTrainTaskDeptRel po = NbchatTrainTaskDeptRel.builder().taskId(request.getId()).build();
        List<NbchatTrainTaskDeptRel> taskDeptRels = nbchatTrainTaskDeptRelMapper.selectAll(po);
        if (CollectionUtils.isNotEmpty(taskDeptRels)) {
            List<String> deptIds = taskDeptRels.stream().map(NbchatTrainTaskDeptRel::getDeptId).collect(Collectors.toList());
            info.getTaskConfig().setDeptIds(deptIds);
        }
        List<JSONObject> taskUserRels = nbchatTrainTaskUserRelMapper.queryUserName(request.getId(), request.getTenantCode());
        if (CollectionUtils.isNotEmpty(taskUserRels)) {
            info.getTaskConfig().setTargetUserInfoList(taskUserRels);
        }

        List<NbchatTrainTaskCourseRel> rels = nbchatTrainTaskCourseRelMapper.selectByTaskId(String.valueOf(request.getId()));
        if (CollectionUtils.isNotEmpty(rels)) {
            List<NbchatTrainTaskCourseRelBO> courseRelList = new ArrayList<>();

            //校验课程是否是上架中
            for (NbchatTrainTaskCourseRel rel : rels) {
                NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(rel.getCourseId());
                if (StateEnum.COURSE.ON.getCode().equals(course.getCourseState())) {
                    NbchatTrainTaskCourseRelBO bo = new NbchatTrainTaskCourseRelBO();
                    BeanUtils.copyProperties(rel, bo);
                    courseRelList.add(bo);
                    bo.setCourseState(course.getCourseState());
                    bo.setTestPaperState(course.getTestPaperState());
                    bo.setDialogueState(course.getSceneState());
                    int i = nbchatTrainRecordMapper.countVideo(course.getCourseId());
                    bo.setVideoCount(i);
                } else {
                    log.warn("课程未上架：{}", course);
                }
            }
            info.setCourseRelList(courseRelList);
        }

        return BaseRspUtils.createSuccessRsp(info);
    }

    @Override
    public RspList queryTask(NbchatTrainTaskBO request) {
        log.info("查询学习任务：{}", request);
        String userId = request.getUserId();
        String tenantCode = request.getTenantCode();
        if (inWhiteList(tenantCode, userId)) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }
        List<NbchatTaskRecordBO> res = new ArrayList<>();

        String deptId = nameMapper.queryDeptId(userId, tenantCode);
        List<String> postIds = nameMapper.queryPostId(userId, tenantCode);
        if (StringUtils.isEmpty(deptId) || CollectionUtils.isEmpty(postIds)) {
            log.info("未查询到用户关联的部门、岗位信息:{}|{}", userId, tenantCode);
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }

        for (String postId : postIds) {
            NbchatTrainTask cond = NbchatTrainTask.builder().startStatus(request.getStartStatus()).id(request.getId())
                    .deptId(deptId).postId(postId).isDegree(request.getIsDegree()).userId(userId).build();
            List<NbchatTrainTask> trainTasks = nbchatTrainTaskMapper.selectAllBySubDept(cond);
            if (CollectionUtils.isEmpty(trainTasks)) {
                continue;
            }
            for (NbchatTrainTask task : trainTasks) {
                List<Integer> taskIds = res.stream().map(NbchatTaskRecordBO::getTaskId).collect(Collectors.toList());
                if (taskIds.contains(task.getId())) {
                    continue;
                }

                NbchatTaskRecordBO bo = new NbchatTaskRecordBO();
                bo.setIsDegree(task.getIsDegree());
                bo.setTaskName(task.getTaskName());
                bo.setTaskId(task.getId());
                bo.setDegreeName(task.getDegreeName());
                bo.setDegreeDesc(task.getDegreeDesc());
                bo.setStartStatus(task.getStartStatus());
                bo.setUpdateTime(task.getUpdateTime());

                this.handleStatus(task.getId(), userId, bo);
                if (ObjectUtils.isNotEmpty(task.getEndTime()) && task.getEndTime().before(new Date())) {
                    bo.setStatus(TRAIN_TASK_EXPIRED);
                }
                NbchatTrainTaskDegree degree = nbchatTrainTaskDegreeMapper.queryDegree(String.valueOf(task.getId()), userId);
                bo.setHasDegree(ObjectUtils.isNotEmpty(degree) ? "1" : "0");
                res.add(bo);
            }
        }

        res = res.stream().sorted(Comparator.comparing(NbchatTaskRecordBO::getUpdateTime).reversed()).collect(Collectors.toList());
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public RspList queryTaskCourse(NbchatTrainTaskBO request) {
        log.info("查询学习任务-不同类型课程：{}", request);
        RspList rspList = this.queryTask(request);
        if (!rspList.isSuccess() || CollectionUtils.isEmpty(rspList.getRows())) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }
        List<CourseFinishInfo> list = new ArrayList<>();
        List<NbchatTaskRecordBO> res = (List<NbchatTaskRecordBO>) rspList.getRows();
        if (CourseType.normal.getCode().equals(request.getCourseType())) {
            list = res.stream().flatMap(record -> record.getInfoList().stream())
                    .filter(v -> v.getIsStudyVideo().equals("1")).distinct().collect(Collectors.toList());
        }
        if (CourseType.exam.getCode().equals(request.getCourseType())) {
            list = res.stream().flatMap(record -> record.getInfoList().stream())
                    .filter(v -> v.getIsAfterTest().equals("1")).distinct().collect(Collectors.toList());
        }
        int total = list.size();
        list = list.stream().skip((request.getPage() - 1) * request.getLimit()).limit(request.getLimit()).collect(Collectors.toList());
        return BaseRspUtils.createSuccessRspList(list, total);
    }


    @Override
    public RspList analysis(NbchatTrainTaskBO request) {
        log.info("查询学习任务分析：{}", request);
        List<String> deptIds= new ArrayList<>();
        deptIds.addAll(request.getDeptIds());
        NbchatTrainTask cond = new NbchatTrainTask();
        BeanUtils.copyProperties(request, cond);
        if (cond.getAtDeptIds()==null|| cond.getAtDeptIds().size()==0){
           if (cond.getTargetIds()!=null && cond.getTargetIds().size() > 0){
               cond.getTargetIds().removeAll(cond.getDeptIds());
           }
        }else {
            cond.getTargetIds().retainAll(cond.getAtDeptIds());
            cond.getDeptIds().retainAll(cond.getAtDeptIds());
            if (cond.getTargetIds().isEmpty()){
                cond.getTargetIds().add("");
            }
            if (cond.getDeptIds().isEmpty()){
                cond.getDeptIds().add("");
            }
        }
        Page<NbchatTrainTask> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatTrainTaskMapper.selectAll(cond);
        if (CollectionUtils.isEmpty(page.getResult())) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }
        List<NbchatTaskInfoBO> res = new ArrayList<>();
        for (NbchatTrainTask trainTask : page.getResult()) {
            NbchatTaskInfoBO bo = new NbchatTaskInfoBO();
            BeanUtils.copyProperties(trainTask, bo);
            String postNameStr = Arrays.stream(trainTask.getPostId().split(",")).map(nameMapper::queryPostName)
                    .collect(Collectors.joining(","));
            bo.setPostName(postNameStr);
            //查询部门名称 和 部门下的用户
            HashSet<String> userIds = new HashSet<>();
            String deptNames = this.getDeptNamesAndSetUsers(trainTask.getId(), userIds, trainTask.getSupportSubDept(),
                    trainTask.getPostId(), request.getTenantCode(),deptIds);
            bo.setDeptName(deptNames);
            bo.setTargetUserCount(userIds.size());

            int finishUserCount = 0;
            for (String userId : userIds) {
                NbchatTaskRecordBO taskBO = new NbchatTaskRecordBO();
                this.handleStatus(trainTask.getId(), userId, taskBO);
                if (TRAIN_TASK_FINISH.equals(taskBO.getStatus())) {
                    finishUserCount++;
                }
            }
            bo.setFinishUserCount(finishUserCount);
            if (bo.getTargetUserCount() != 0) {
                String rate = String.format("%.2f%%", ((float) finishUserCount / bo.getTargetUserCount()) * 100);
                bo.setFinishRate(rate);
            }
            res.add(bo);
        }

        return BaseRspUtils.createSuccessRspList(res, page.getTotal());
    }


    @Override
    public RspList queryTaskCourseList(NbchatTrainTaskBO request) {
        log.info("查询学习任务课程列表：{}", request);
        NbchatTaskRecordBO bo = new NbchatTaskRecordBO();
        this.handleStatus(request.getId(), request.getUserId(), bo, true);
        List<CourseFinishInfo> infoList = bo.getInfoList();
        return BaseRspUtils.createSuccessRspList(infoList);
    }

    public String getDeptNamesAndSetUsers(Integer taskId, Set<String> userIds, String supportSubDept, String postId, String tenantCode) {
        StringBuilder names = new StringBuilder();

        NbchatTrainTaskDeptRel po = new NbchatTrainTaskDeptRel();
        po.setTaskId(taskId);
        List<NbchatTrainTaskDeptRel> taskDeptRels = nbchatTrainTaskDeptRelMapper.selectAll(po);
        if (CollectionUtils.isEmpty(taskDeptRels)) {
            return names.toString();
        }
        for (NbchatTrainTaskDeptRel taskDeptRel : taskDeptRels) {
            String deptName = nameMapper.queryOrganizeName(taskDeptRel.getDeptId());
            if (StringUtils.isNotEmpty(deptName)) {
                names.append(deptName).append(",");
            }
            if (StringUtils.isNotEmpty(postId)) {
                Set<String> users = nameMapper.queryDeptPostUser(taskDeptRel.getDeptId(), supportSubDept, postId, tenantCode);
                userIds.addAll(users);
            }
        }
        if (names.lastIndexOf(",") > 0) {
            return names.deleteCharAt(names.lastIndexOf(",")).toString();
        }
        return "";
    }
    //方法重构
    public String getDeptNamesAndSetUsers(Integer taskId, Set<String> userIds, String supportSubDept, String postId, String tenantCode,List<String> deptIds) {
        StringBuilder names = new StringBuilder();

        NbchatTrainTaskDeptRel po = new NbchatTrainTaskDeptRel();
        po.setTaskId(taskId);
        List<NbchatTrainTaskDeptRel> taskDeptRels = nbchatTrainTaskDeptRelMapper.selectAll(po);
        if (CollectionUtils.isEmpty(taskDeptRels)) {
            return names.toString();
        }
        for (NbchatTrainTaskDeptRel taskDeptRel : taskDeptRels) {
            String deptName = nameMapper.queryOrganizeName(taskDeptRel.getDeptId());
            if (StringUtils.isNotEmpty(deptName)) {
                names.append(deptName).append(",");
            }
            if (StringUtils.isNotEmpty(postId)) {
                boolean b = deptIds.stream().anyMatch(deptId -> deptId.equals(taskDeptRel.getDeptId()));
                if(b){
                    taskDeptRel.setDeptId(deptIds.get(0));
                }
                for (String pid : postId.split(",")) {
                    Set<String> users = nameMapper.queryDeptPostUser(taskDeptRel.getDeptId(), supportSubDept, pid, tenantCode);
                    userIds.addAll(users);
                }
            }
        }
        if (names.lastIndexOf(",") > 0) {
            return names.deleteCharAt(names.lastIndexOf(",")).toString();
        }
        return "";
    }
    public void handleStatus(Integer taskId, String userId, NbchatTaskRecordBO bo) {
        this.handleStatus(taskId, userId, bo, true);
    }

    public void handleStatus(Integer taskId, String userId, NbchatTaskRecordBO bo, Boolean countScore) {
        List<CourseFinishInfo> infoList = new ArrayList<>();
        //查询配置状态
        NbchatTrainTaskCourseRel cond = NbchatTrainTaskCourseRel.builder().taskId(String.valueOf(taskId)).build();
        List<NbchatTrainTaskCourseRel> rels = nbchatTrainTaskCourseRelMapper.selectAll(cond);
        if (CollectionUtils.isEmpty(rels)) {
            return;
        }

        int finishCourse = 0;
        int courseOnCount = rels.size();
        for (NbchatTrainTaskCourseRel rel : rels) {
            //校验课程是否是上架中
            NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(rel.getCourseId());
            if (!StateEnum.COURSE.ON.getCode().equals(course.getCourseState())) {
                log.warn("课程未上架：{}|{}", taskId, course.getCourseId());
                courseOnCount--;
                continue;
            }
            CourseFinishInfo finishInfo = new CourseFinishInfo();
            BeanUtils.copyProperties(rel, finishInfo);
            finishInfo.setCourseName(course.getCourseName());
            finishInfo.setCourseId(course.getCourseId());
            finishInfo.setImgAvatar(course.getImgAvatar());
            finishInfo.setCourseType(course.getCourseType());
            finishInfo.setCourseState(course.getCourseState());
            finishInfo.setTestPaperState(course.getTestPaperState());
            finishInfo.setDialogueState(course.getSceneState());
            int i = nbchatTrainRecordMapper.countVideo(course.getCourseId());
            finishInfo.setVideoCount(i);

            NbchatTrainRecord trainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(userId, rel.getCourseId());
            if (ObjectUtils.isNotEmpty(trainRecord)) {
                BeanUtils.copyProperties(trainRecord, finishInfo);
                bo.setStatus(TRAIN_TASK_LEARNING);
                finishInfo.setTestState(trainRecord.getTestPassState());
                finishInfo.setSceneState(trainRecord.getScenePassState());
                if (countScore) {
                    Integer score = nbchatExamTestRecordMapper.queryScore(userId, rel.getCourseId());
                    if (ObjectUtils.isNotEmpty(score)) {
                        finishInfo.setTestScore(score);
                    }
                }
            }
            infoList.add(finishInfo);
            if (this.checkIsFinish(finishInfo)) {
                finishCourse++;
            }
        }

        //检查是否任务完成
        if (finishCourse == courseOnCount) {
            bo.setStatus(TRAIN_TASK_FINISH);
            bo.setFinishStatus("1");
        }
        bo.setInfoList(infoList);
    }

    public Boolean inWhiteList(String tenantCode, String userId) {
        NbchatTrainTaskWhiteList cond = new NbchatTrainTaskWhiteList();
        cond.setTenantCode(tenantCode);
        cond.setUserId(userId);
        List<NbchatTrainTaskWhiteList> whiteLists = nbchatTrainTaskWhiteListMapper.selectAll(cond);
        if (CollectionUtils.isNotEmpty(whiteLists)) {
            log.info("匹配到白名单：{}", JSON.toJSONString(whiteLists));
            return true;
        }
        return false;
    }

    public boolean checkIsFinish(CourseFinishInfo finishInfo) {
        boolean finish1 = true;
        boolean finish2 = true;
        boolean finish3 = true;
        if ("1".equals(finishInfo.getIsStudyVideo())) {
            finish1 = EntityValidType.NORMAL.getCode().equals(finishInfo.getTrainState());
        }
        if ("1".equals(finishInfo.getIsAfterTest())) {
            finish2 = EntityValidType.NORMAL.getCode().equals(finishInfo.getTestState());
        }
        if ("1".equals(finishInfo.getIsHumanMachine())) {
            finish3 = EntityValidType.NORMAL.getCode().equals(finishInfo.getSceneState());
        }

        return finish1 && finish2 && finish3;
    }


}
