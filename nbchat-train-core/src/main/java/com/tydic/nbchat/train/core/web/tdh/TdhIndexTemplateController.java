package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhIndexHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateSortReqBO;
import com.tydic.nbchat.train.api.tdh.TdhIndexTemplateApi;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/tdh/index")
public class TdhIndexTemplateController {

    private final TdhIndexTemplateApi tdhIndexTemplateApi;

    @PostMapping("/templates")
    public RspList<TdhIndexTemplateQueryRspBO> list(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.list(request);
    }

    @PostMapping("/template/info")
    public Rsp info(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.info(reqBO);
    }

    /**
     * 管理员查询首页模板列表
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/templates")
    public RspList<TdhIndexTemplateQueryRspBO> adminList(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.adminList(request);
    }

    /**
     * 新增首页模板（管理员）
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/template/add")
    public Rsp addTemplate(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.addTemplate(reqBO);
    }

    /**
     * 更新首页模板（管理员）
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/template/update")
    public Rsp updateTemplate(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.updateTemplate(reqBO);
    }

    /**
     * 删除首页模板（管理员）
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/template/delete")
    public Rsp deleteTemplate(@RequestBody Map<String, String> request) {
        String tplId = request.get("tplId");
        return tdhIndexTemplateApi.deleteTemplate(tplId);
    }

    /**
     * 首页模板排序（管理员）
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/template/sort")
    public Rsp sortTemplates(@RequestBody TdhIndexTemplateSortReqBO reqBO) {
        return tdhIndexTemplateApi.sortTemplates(reqBO);
    }

    /**
     * 首页模板上下架（管理员）
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/admin/template/status")
    public Rsp updateTemplateStatus(@RequestBody Map<String, String> request) {
        String tplId = request.get("tplId");
        String status = request.get("status");
        return tdhIndexTemplateApi.updateTemplateStatus(tplId, status);
    }

    /**
     * 查询首页数字人列表
     */
    @PostMapping("/humans")
    public RspList<TdhIndexHumanQueryRspBO> getIndexHumans(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.getIndexHumans(request);
    }
}
