package com.tydic.nbchat.train.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.TrainCourseVideosApi;
import com.tydic.nbchat.train.api.bo.course.TrainVideosQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TrainVideosRspBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper;
import com.tydic.nbchat.train.mapper.TrainCourseVideosMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import com.tydic.nbchat.train.mapper.po.TrainCourseVideosPO;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/9/2 14:55
 * @description:
 */
@Slf4j
@Service
public class TrainCourseVideosImpl implements TrainCourseVideosApi {
    @Resource
    private TrainCourseVideosMapper trainCourseVideosMapper;
    @Resource
    private NbchatTrainCatalogMapper trainCatalogMapper;

    @Override
    public RspList getTrainCourseVideos(TrainVideosQueryReqBO request) {
        log.info("查询视频文件请求参数：{}", request);
        Page<TrainCourseVideosPO> page = PageHelper.startPage(request.getPage(), request.getLimit());
        trainCourseVideosMapper.selectAll(request.getCourseName(), request.getTenantCode());

        List<TrainVideosRspBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, TrainVideosRspBO.class);
        list.forEach(item -> {
            if (item.getVideoUrl()!= null&&item.getVideoUrl().contains("/files/")) {
                item.setPlayUrl(item.getVideoUrl().replace("/files/", "/files/player.html?src="));
            }
            List<String> strings = trainCatalogMapper.selectBySectionIdStr(item.getSectionId());
            item.setCatalogTitle(strings.get(0));
        });

        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }
}
