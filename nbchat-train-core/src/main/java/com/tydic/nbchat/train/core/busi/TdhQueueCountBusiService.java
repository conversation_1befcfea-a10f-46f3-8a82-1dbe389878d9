package com.tydic.nbchat.train.core.busi;

import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhQueueInfo;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TdhQueueCountBusiService {
    @Resource
    private TdhCreationTaskMapper tdhCreationTaskMapper;

    /**
     * 获取每个任务排队编号
     * @return Map<String, Integer>
     */
    public  Map<String, Integer> getTaskQueueMap(){
        //查询 queueTime > 最近 2 天创建的任务
        Date startTime = DateTimeUtil.DateAddDayOfYear(-2);
        List<TdhQueueInfo> queueTasks = tdhCreationTaskMapper.selectQueueTasks(startTime);
        //按照 TdhQueueInfo.idleQueue区分闲时队列和排队队列，每个队列单独计数
        Map<String, Integer> queueMap = new HashMap<>();
        //按照 TdhQueueInfo.idleQueue区分闲时队列和排队队列，每个队列单独计数
        Map<Integer, List<TdhQueueInfo>> groupedTasks = queueTasks.stream()
                .collect(Collectors.groupingBy(TdhQueueInfo::getIdleQueue));
        // 对每个组单独计数
        for (Map.Entry<Integer, List<TdhQueueInfo>> entry : groupedTasks.entrySet()) {
            List<TdhQueueInfo> tasksInQueue = entry.getValue();
            for (int i = 0; i < tasksInQueue.size(); i++) {
                queueMap.put(tasksInQueue.get(i).getTaskId(), i + 1);
            }
        }
        return queueMap;
    }
}
