package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.volcengine-config")
public class VolcengineConfigProperties {
    private String accessKey = "";
    private String secretKey = "";
    private String appId = "";
    private String nlsBaseUrl = "https://openspeech.bytedance.com";
    //长文本异步合成
    private String ttsLongApi = "/api/v1/tts_async/submit";
    //语音合成-实时
    private String ttsApi = "/api/v1/tts";
    private String ttsResultApi = "/api/v1/tts_async/query?appid={appId}&task_id={taskId}";
    private String asrApi = "/nls/asr";
    private String voice = "";

}
