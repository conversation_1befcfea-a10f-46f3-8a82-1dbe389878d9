package com.tydic.nbchat.train.core.service.impl.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EventPublishFactory {

    private static ApplicationEventPublisher applicationEventPublisher;

    public EventPublishFactory(ApplicationEventPublisher applicationEventPublisher) {
        EventPublishFactory.applicationEventPublisher = applicationEventPublisher;
    }

    public static void publish(Object event) {
        applicationEventPublisher.publishEvent(event);
    }

    public static void publishUserCompleteTaskEvent(String tenantCode, String userId) {
        Object source = new Object();
        UserCompleteTaskEvent<Object> event = new UserCompleteTaskEvent<>(source, tenantCode, userId);
        log.info("发送证书完成事件：{}|{}", tenantCode, userId);
        applicationEventPublisher.publishEvent(event);
    }

    public static void publishModifyTaskEvent(String tenantCode, String taskId) {
        Object source = new Object();
        ModifyTaskEvent<Object> event = new ModifyTaskEvent<>(source, tenantCode, taskId);
        log.info("发送任务变更事件：{}|{}", tenantCode, taskId);
        applicationEventPublisher.publishEvent(event);
    }
}
