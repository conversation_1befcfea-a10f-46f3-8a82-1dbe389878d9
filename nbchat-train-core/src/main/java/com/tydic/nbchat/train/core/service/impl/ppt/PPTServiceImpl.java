package com.tydic.nbchat.train.core.service.impl.ppt;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUserStarApi;
import com.tydic.nbchat.admin.api.bo.eum.StarType;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.admin.api.star.UserStarRequest;
import com.tydic.nbchat.train.api.bo.eums.PPTSortType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.eums.TemplateSourceType;
import com.tydic.nbchat.train.api.bo.eums.VipFlagType;
import com.tydic.nbchat.train.api.bo.ppt.*;
import com.tydic.nbchat.train.api.ppt.PPTApi;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.core.util.PptThemeMatcherProcessor;
import com.tydic.nbchat.train.mapper.PptCreationRecordMapper;
import com.tydic.nbchat.train.mapper.PptLayoutMapper;
import com.tydic.nbchat.train.mapper.PptThemeMapper;
import com.tydic.nbchat.train.mapper.TdhTemplateMapper;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

@Slf4j
@Service
public class PPTServiceImpl implements PPTApi {

    @Resource
    PptCreationRecordMapper pptCreationRecordMapper;

    @Resource
    PptLayoutMapper pptLayoutMapper;

    @Resource
    PptThemeMapper pptThemeMapper;
    @Resource
    private TrainEventSender trainEventSender;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private SysUserStarApi sysUserStarApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private FileManageService fileManageService;

    @Resource
    private TdhTemplateMapper tdhTemplateMapper;

    @Override
    public Rsp save(PPTCreationRecordBO request) {
        PptCreationRecord po = new PptCreationRecord();
        BeanUtils.copyProperties(request, po);
        if (StringUtils.isEmpty(po.getPptId())) {
            po.setPptId(IdWorker.nextAutoIdStr());
            po.setCreateTime(new Date());
            po.setUpdateTime(new Date());
            if (StringUtils.isEmpty(request.getLayout())) {
                po.setLayout("{}");
            }
            int i = pptCreationRecordMapper.insertSelective(po);
            // 发送用户维度报表消息
            trainEventSender.sendUserRpEventByPptMake(request.getTenantCode(), request.getUserId(), request.getCreationType());
        } else {
            PptCreationRecord ppt = pptCreationRecordMapper.queryById(po.getPptId());
            if (ObjectUtils.isNotEmpty(ppt)) {
                String userId = ppt.getUserId();
                if (request.getUserId().equals(userId)) {
                    po.setUpdateTime(new Date());
                    int i = pptCreationRecordMapper.update(po);
                } else {
                    return BaseRspUtils.createErrorRsp("非本人创作记录，无权限修改");
                }
            }
        }
        return BaseRspUtils.createSuccessRsp(po.getPptId());
    }

    @Override
    public Rsp query(PPTCreationRecordBO request) {
        log.info("查询ppt内容-开始：{}", request);
        if (StringUtils.isEmpty(request.getPptId())) {
            return BaseRspUtils.createErrorRsp("pptId不得为空");
        }

        PptCreationRecord pptRecord = pptCreationRecordMapper.queryById(request.getPptId());
        if (ObjectUtils.isEmpty(pptRecord)) {
            return BaseRspUtils.createSuccessRsp("无此ppt信息");
        }
        PPTCreationRecordBO rspBO = new PPTCreationRecordBO();
        BeanUtils.copyProperties(pptRecord, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp layoutQuery(PPTLayoutBO request) {
        log.info("布局查询-开始:{}", request);

        PptTheme pptTheme = pptThemeMapper.queryById(request.getThemeId());
        if (ObjectUtils.isEmpty(pptTheme)) {
            return BaseRspUtils.createSuccessRsp("未查询到该主题");
        }
        PptLayoutSelectCondition cond = new PptLayoutSelectCondition();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        List<PptLayout> pptLayouts = pptLayoutMapper.selectAll(cond);
        List<PPTLayoutBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(pptLayouts, res, PPTLayoutBO.class);

        PPTLayoutWrapper rsp = new PPTLayoutWrapper();
        rsp.setConfig(pptTheme.getConfig());
        rsp.setLayoutList(res);
        return BaseRspUtils.createSuccessRsp(rsp);
    }

    @Override
    public RspList themeQuery(PPTThemeBO request) {
        log.info("主题查询-开始:{}", request);
        PptTheme cond = new PptTheme();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setThemeState(StateEnum.STATE.AVAILABLE.getCode());
        Page<PptTheme> page = PageHelper.startPage(request.getPage(), request.getLimit());
        pptThemeMapper.selectAll(cond);
        if (page.getTotal() > 0) {
            log.info("主题查询-查询到数据：{}", page.getTotal());
            List<PptTheme> result = page.getResult();
            List<PPTThemeBO> res = new ArrayList<>();
            NiccCommonUtil.copyList(result, res, PPTThemeBO.class);
            res.forEach(rsp -> rsp.setIsStar(isStar(request, rsp.getThemeId())));
            return BaseRspUtils.createSuccessRspList(res, page.getTotal());
        }
        log.info("主题查询-未查询到数据");
        return BaseRspUtils.createSuccessRspList(new ArrayList<PPTThemeBO>(), 0L);
    }

    @Override
    public RspList themeAdminList(PPTThemeBO request) {
        log.info("主题查询-开始:{}", request);
        PptTheme cond = new PptTheme();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTenantCode(null);
        cond.setTargetTenant(request.getTargetTenant());
        Page<PptTheme> page = PageHelper.startPage(request.getPage(), request.getLimit());
        pptThemeMapper.selectAll(cond);
        if (page.getTotal() > 0) {
            log.info("主题查询-查询到数据：{}", page.getTotal());
            List<PptTheme> result = page.getResult();
            List<PPTThemeBO> res = new ArrayList<>();
            NiccCommonUtil.copyList(result, res, PPTThemeBO.class);
            return BaseRspUtils.createSuccessRspList(res, page.getTotal());
        }
        log.info("主题查询-未查询到数据");
        return BaseRspUtils.createSuccessRspList(new ArrayList<PPTThemeBO>(), 0L);
    }

    @Override
    public Rsp themeInfo(PPTThemeBO request) {
        log.info("主题查询-开始:{}", request);
        PptTheme cond = pptThemeMapper.queryById(request.getThemeId());
        if (ObjectUtils.isEmpty(cond)) {
            return BaseRspUtils.createErrorRsp("未查询到该主题");
        }
        PPTThemeBO res = new PPTThemeBO();
        BeanUtils.copyProperties(cond, res);
        return BaseRspUtils.createSuccessRsp(res, "查询成功");
    }

    @Override
    public RspList themeStarList(PPTThemeBO request) {
        log.info("主题查询-开始:{}", request);
        PptTheme cond = new PptTheme();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setThemeState(StateEnum.STATE.AVAILABLE.getCode());
        Page<PptTheme> page = PageHelper.startPage(request.getPage(), request.getLimit());
        pptThemeMapper.selectStarThemeByCondition(cond);
        if (page.getTotal() > 0) {
            log.info("主题查询-查询到数据：{}", page.getTotal());
            List<PptTheme> result = page.getResult();
            List<PPTThemeBO> res = new ArrayList<>();
            NiccCommonUtil.copyList(result, res, PPTThemeBO.class);
            return BaseRspUtils.createSuccessRspList(res, page.getTotal());
        }
        log.info("主题查询-未查询到数据");
        return BaseRspUtils.createSuccessRspList(new ArrayList<PPTThemeBO>(), 0L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp themeSave(PPTThemeReqBO request) {
        log.debug("保存PPT模板：{}", request);
        String themeId = Optional.ofNullable(request.getThemeInfo().getThemeId()).orElse(IdWorker.nextAutoIdStr());
        List<String> layoutIdList = new ArrayList<>();
        PPTThemeBO themeInfo = request.getThemeInfo();
        List<PPTLayoutBO> layoutBOList = request.getLayoutBOList();

        createOrUpdateTheme(themeInfo, themeId, request);
        saveOrUpdateLayouts(layoutBOList, themeId, request, layoutIdList);

        compared(layoutIdList, themeId);
        log.info("成功保存PPT模板");

        PPTThemeRspBO response = new PPTThemeRspBO();
        response.setThemId(themeId);
        response.setLayoutId(layoutIdList);
        return BaseRspUtils.createSuccessRsp(response, "保存成功");
    }

    private PptTheme createOrUpdateTheme(PPTThemeBO themeInfo, String themeId, PPTThemeReqBO request) {
        PptTheme pptTheme = new PptTheme();
        BeanUtils.copyProperties(themeInfo, pptTheme);
        String themeTargetTenant = getTargetTenant(themeInfo.getThemeSource(), themeInfo.getTargetTenant(), request.getTenantCode());
        String themeTargetUserId = getTargetUserId(themeInfo.getThemeSource(), themeInfo.getTargetTenant(), request.getUserId());
        pptTheme.setUserId(themeTargetUserId);
        pptTheme.setTenantCode(themeTargetTenant);
        pptTheme.setThemeId(themeId);
        pptTheme.setOrderIndex(request.getOrderIndex());
        pptTheme.setIsValid(EntityValidType.NORMAL.getCode());
        pptTheme.setUpdateTime(new Date());

        if (StringUtils.isBlank(themeInfo.getThemeId())) {
            pptTheme.setCreateTime(new Date());
            pptTheme.setVipFlag(Optional.ofNullable(pptTheme.getVipFlag()).orElse(VipFlagType.COMMON_USER.getCode()));
            pptThemeMapper.insertSelective(pptTheme);
            log.info("保存ppt 模板-新增主题成功", pptTheme);
        } else {
            pptThemeMapper.update(pptTheme);
            log.info("保存ppt 模板-更新主题成功", pptTheme);
        }
        return pptTheme;
    }

    private void saveOrUpdateLayouts(List<PPTLayoutBO> layoutBOList, String themeId, PPTThemeReqBO request, List<String> layoutIdList) {
        if (CollectionUtils.isNotEmpty(layoutBOList)) {
            for (int i = 0; i < layoutBOList.size(); i++) {
                PPTLayoutBO layoutBO = layoutBOList.get(i);
                String layoutTargetTenant = getTargetTenant(request.getThemeInfo().getThemeSource(), request.getThemeInfo().getTargetTenant(), request.getTenantCode());
                String layoutTargetUserId = getTargetUserId(request.getThemeInfo().getThemeSource(), request.getThemeInfo().getTargetTenant(), request.getUserId());
                PptLayout pptLayout = new PptLayout();
                BeanUtils.copyProperties(layoutBO, pptLayout);
                pptLayout.setUserId(layoutTargetUserId);
                pptLayout.setTenantCode(layoutTargetTenant);
                pptLayout.setThemeId(themeId);
                pptLayout.setComposeType(request.getThemeInfo().getComposeType());
                pptLayout.setSort(i);
                pptLayout.setUpdateTime(new Date());

                if (StringUtils.isBlank(layoutBO.getLayoutId())) {
                    pptLayout.setLayoutId(IdWorker.nextAutoIdStr());
                    pptLayout.setCreateTime(new Date());
                    pptLayout.setVipFlag(Optional.ofNullable(pptLayout.getVipFlag()).orElse(VipFlagType.COMMON_USER.getCode()));
                    pptLayout.setIsValid(EntityValidType.NORMAL.getCode());
                    pptLayoutMapper.insertSelective(pptLayout);
                    log.info("保存ppt 模板-新增布局成功", pptLayout.getLayoutName());
                } else {
                    pptLayoutMapper.update(pptLayout);
                    log.info("保存ppt 模板-更新布局成功", pptLayout.getLayoutName());
                }
                layoutIdList.add(pptLayout.getLayoutId());
            }
        }
    }

    private String getTargetTenant(String sourceType, String targetTenant, String defaultTenant) {
        if (TemplateSourceType.SYSTEM_BUILT_IN.getCode().equals(sourceType)) {
            return StateEnum.SOURCE.PUBLIC.getCode().equals(targetTenant)
                   ? UserAttributeConstants.DEFAULT_TENANT_CODE
                   : Optional.ofNullable(targetTenant).filter(StringUtils::isNotBlank).orElse(defaultTenant);
        }
        return defaultTenant;
    }

    private String getTargetUserId(String sourceType, String targetTenant, String defaultUserId) {
        if (TemplateSourceType.SYSTEM_BUILT_IN.getCode().equals(sourceType)) {
            return StateEnum.SOURCE.PUBLIC.getCode().equals(targetTenant)
                   ? StateEnum.SOURCE.PUBLIC.getCode()
                   : Optional.ofNullable(targetTenant).filter(StringUtils::isNotBlank).orElse(defaultUserId);
        }
        return defaultUserId;
    }

    @Override
    public RspList imageConversion(ImageConversionReqBO request) {
        log.info("图片转换-开始:{}", request);
        String tenantCode = request.getTenantCode();
        String userId = request.getUserId();
        List<ImageConversionRspBO> rspBOList = new ArrayList<>();
        Optional.ofNullable(request.getImages()).ifPresent(images -> {
            images.forEach(image -> {
                ImageConversionRspBO rspBO = new ImageConversionRspBO();
                String newImage = imageDownload(image, tenantCode, userId);
                rspBO.setOldImage(image);
                rspBO.setNewImage(newImage);
                rspBOList.add(rspBO);
            });
        });
        return BaseRspUtils.createSuccessRspList(rspBOList);
    }

    /**
     * ppt创作记录查询
     *
     * @param request
     * @return
     */
    @Override
    public RspList queryCreateHistory(PPTCreationRecordBO request) {
        log.info("ppt创作记录查询-开始:{}", request);
        PptCreationRecord record = new PptCreationRecord();
        record.setTenantCode(request.getTenantCode());
        record.setUserId(request.getUserId());
        record.setPptType(request.getPptType());
        record.setIsValid(EntityValidType.NORMAL.getCode());
        if (StringUtils.isNoneBlank(request.getTargetTenant(), request.getTargetUid())) {
            record.setUserId(request.getTargetUid());
            record.setTenantCode(request.getTargetTenant());
        }
        List<PPTCreationRecordBO> list = new ArrayList<>();
        Page<PptCreationRecord> page = PageHelper.startPage(request.getPage(), request.getLimit());
        pptCreationRecordMapper.selectAll(record);
        log.info("ppt创作记录查询-查询到数据：{}", page.getTotal());
        NiccCommonUtil.copyList(page.getResult(), list, PPTCreationRecordBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    /**
     * 模版的上下架
     *
     * @param request
     * @return
     */
    @Override
    public Rsp releaseTemplate(PPTThemeReqBO request) {
        PptTheme pptTheme = new PptTheme();
        pptTheme.setThemeId(request.getThemeInfo().getThemeId());
        pptTheme.setThemeState(request.getThemeInfo().getThemeState());
        pptTheme.setUpdateTime(new Date());
        pptTheme.setIsValid(request.getThemeInfo().getIsValid());
        pptThemeMapper.update(pptTheme);
        PptLayoutSelectCondition pptLayout = new PptLayoutSelectCondition();
        pptLayout.setThemeId(request.getThemeInfo().getThemeId());
        pptLayout.setIsValid(EntityValidType.NORMAL.getCode());
        pptLayoutMapper.selectAll(pptLayout).forEach(layout -> {
            layout.setThemeState(request.getThemeInfo().getThemeState());
            layout.setUpdateTime(new Date());
            layout.setIsValid(request.getThemeInfo().getIsValid());
            pptLayoutMapper.update(layout);
        });
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 复制历史记录
     *
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp copyHistory(PPTCreationRecordBO request) {
        //查询历史记录
        PptCreationRecord pptCreationRecord = pptCreationRecordMapper.queryById(request.getPptId());
        if (ObjectUtils.isEmpty(pptCreationRecord)) {
            return BaseRspUtils.createErrorRsp("未查询到该记录");
        }
        PptCreationRecord newRecord = new PptCreationRecord();
        BeanUtils.copyProperties(pptCreationRecord, newRecord);
        newRecord.setPptId(IdWorker.nextAutoIdStr());
        newRecord.setUserId(request.getUserId());
        newRecord.setTenantCode(request.getTenantCode());
        newRecord.setCreateTime(new Date());
        newRecord.setUpdateTime(new Date());
        pptCreationRecordMapper.insertSelective(newRecord);
        // 发送用户维度报表消息
        trainEventSender.sendUserRpEventByPptMake(request.getTenantCode(), request.getUserId(), request.getCreationType());
        return BaseRspUtils.createSuccessRsp(newRecord, "复制成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp delete(PPTCreationRecordBO request) {
        PptTheme pptTheme = new PptTheme();
        pptTheme.setThemeId(request.getThemeId());
        pptTheme.setIsValid(EntityValidType.DELETE.getCode());
        int theme = pptThemeMapper.update(pptTheme);
        log.info("删除主题成功-{}|{}", theme, request.getThemeId());
        int layout = pptLayoutMapper.updateByThemeId(request.getThemeId());
        log.info("删除布局成功-{}|{}", layout, request.getThemeId());
        return BaseRspUtils.createSuccessRsp("删除成功");
    }

    @Override
    public Rsp copyTheme(PPTCreationRecordBO request) {
        PptTheme pptTheme = pptThemeMapper.queryById(request.getThemeId());
        if (ObjectUtils.isEmpty(pptTheme)) {
            return BaseRspUtils.createErrorRsp("未查询到该主题");
        }
        PptTheme newTheme = new PptTheme();
        BeanUtils.copyProperties(pptTheme, newTheme);
        newTheme.setThemeId(IdWorker.nextAutoIdStr());
        newTheme.setUserId(request.getUserId());
        newTheme.setTenantCode(request.getTenantCode());
        newTheme.setCreateTime(new Date());
        newTheme.setUpdateTime(new Date());
        pptThemeMapper.insertSelective(newTheme);
        PptLayoutSelectCondition cond = new PptLayoutSelectCondition();
        cond.setThemeId(request.getThemeId());
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        List<PptLayout> pptLayouts = pptLayoutMapper.selectAll(cond);
        if (CollectionUtils.isNotEmpty(pptLayouts)) {
            pptLayouts.forEach(pptLayout -> {
                PptLayout newLayout = new PptLayout();
                BeanUtils.copyProperties(pptLayout, newLayout);
                newLayout.setLayoutId(IdWorker.nextAutoIdStr());
                newLayout.setThemeId(newTheme.getThemeId());
                newLayout.setCreateTime(new Date());
                newLayout.setUpdateTime(new Date());
                pptLayoutMapper.insertSelective(newLayout);
            });
        }
        return BaseRspUtils.createSuccessRsp(newTheme, "复制成功");
    }

    @Override
    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    public Rsp sort(PPTSortReqBO request) {
        if (!PPTSortType.isExist(request.getSortType())) {
            return BaseRspUtils.createErrorRsp("排序类型不存在");
        }
        List<String> idList = request.getPptIds();
        if (PPTSortType.PPT_THEME.getCode().equals(request.getSortType())) {
            //主题模版排序
            List<PptTheme> pptThemes = new ArrayList<>();
            IntStream.range(0, idList.size()).forEach(i -> {
                String id = idList.get(i);
                PptTheme pptTheme = new PptTheme();
                pptTheme.setThemeId(id);
                pptTheme.setOrderIndex(i);
                pptThemes.add(pptTheme);
            });
            pptThemeMapper.updateList(pptThemes);
        } else if (PPTSortType.PPT_LAYOUT.getCode().equals(request.getSortType())) {
            //布局模版排序
            List<PptLayout> pptLayouts = new ArrayList<>();
            IntStream.range(0, idList.size()).forEach(i -> {
                String id = idList.get(i);
                PptLayout pptLayout = new PptLayout();
                pptLayout.setLayoutId(id);
                pptLayout.setSort(i);
                pptLayouts.add(pptLayout);
            });
            pptLayoutMapper.updateList(pptLayouts);
        } else if (PPTSortType.PPT_VIDEO.getCode().equals(request.getSortType()) ||
                   PPTSortType.PPT_ORAL.getCode().equals(request.getSortType())) {
            tdhTemplateSort(idList);
        }
        return BaseRspUtils.createSuccessRsp("排序成功");
    }

    @Override
    public Rsp matchTheme(PPTThemeMatchReqBO request) {
        List<PptTheme> pptThemeList = pptThemeMapper.findByScene(request.getScene());
        if (CollectionUtils.isEmpty(pptThemeList)) {
            log.warn("{}用户查询的{}场景下没有主题", request.getUserId(), request.getScene());
            return BaseRspUtils.createErrorRsp("该场景下没有主题,请联系运营人员!");
        }
        Optional<PptTheme> optionalPptTheme = PptThemeMatcherProcessor.findBestMatch(pptThemeList, request.getStyle(), request.getColor());
        PPTThemeBO rspBO = new PPTThemeBO();
        optionalPptTheme.ifPresent(theme -> {
            BeanUtils.copyProperties(theme, rspBO);
        });
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    private String imageDownload(String imageUrl, String tenantCode, String userId) {
        try {
            String fileName = "a.jpg";
            URL url = new URL(imageUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setInstanceFollowRedirects(false);
            String redirectUrl = conn.getHeaderField("Location");
            URL redirectURL = new URL(redirectUrl);
            HttpURLConnection redirectConn = (HttpURLConnection) redirectURL.openConnection();
            InputStream in = new BufferedInputStream(redirectConn.getInputStream());
            System.out.println(redirectUrl);
            byte[] buffer = new byte[in.available()];
            FileUploadRequest uploadRequest = new FileUploadRequest();
            uploadRequest.setTenantCode(tenantCode);
            uploadRequest.setUploadUser(userId);
            uploadRequest.setFileName(fileName);
            uploadRequest.setFile(buffer);
            in.close();
            RspList<FileManageSaveBO> fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
            log.info("文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
            if (fileManageSaveBOS.isSuccess()) {
                return fileManageSaveBOS.getRows().get(0).getAccessUrl();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Transactional(rollbackFor = Exception.class)
    public void compared(List<String> list, String themeId) {
        PptLayoutSelectCondition cond = new PptLayoutSelectCondition();
        cond.setThemeId(themeId);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        List<PptLayout> pptLayouts = pptLayoutMapper.selectAll(cond);
        log.info("上架/下架布局-查询到的布局|传入布局：{}|{}", pptLayouts.size(), list.size());
        if (CollectionUtils.isNotEmpty(pptLayouts) && CollectionUtils.isNotEmpty(list)) {
            pptLayouts.forEach(pptLayout -> {
                if (!list.contains(pptLayout.getLayoutId())) {
                    log.info("删除不存在的布局：{}", pptLayout.getLayoutName());
                    pptLayout.setIsValid(EntityValidType.DELETE.getCode());
                    pptLayoutMapper.update(pptLayout);
                }
            });
        }
    }

    public void tdhTemplateSort(List<String> idList) {
        //PPT视频模版排序
        List<TdhTemplate> tdhTemplates = new ArrayList<>();
        IntStream.range(0, idList.size()).forEach(i -> {
            String id = idList.get(i);
            TdhTemplate tdhTemplate = new TdhTemplate();
            tdhTemplate.setTpId(id);
            tdhTemplate.setOrderIndex(i);
            tdhTemplates.add(tdhTemplate);
        });
        tdhTemplateMapper.updateList(tdhTemplates);
    }

    public String isStar(PPTThemeBO reqBO, String busiId) {
        UserStarRequest request = new UserStarRequest();
        request.setBusiId(busiId);
        request.setUserId(reqBO.getUserId());
        request.setTenantCode(reqBO.getTenantCode());
        Rsp rsp = null;
        try {
            rsp = sysUserStarApi.getStar(request);
        } catch (Exception e) {
            log.warn("数字人面板-获取用户模板-查询收藏状态异常：{}", e.getMessage());
        }
        return rsp != null && rsp.isSuccess() ? StarType.FAVORITED.getCode() : StarType.UNFAVORITED.getCode();
    }

}
