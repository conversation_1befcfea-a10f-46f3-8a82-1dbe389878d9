package com.tydic.nbchat.train.core.web.tdh;


import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordModifyStatusReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordPriceChangeReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordUpdateInfoReqBO;
import com.tydic.nbchat.train.api.tdh.TdhCustomizeRecordApi;
import com.tydic.nbchat.train.api.tdh.TdhCustomizeRecordOperationsApi;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/tdh_customize")
public class TdhCustomizeRecordController {
    private final TdhCustomizeRecordOperationsApi tdhCustomizeRecordOperationsApi;
    private final TdhCustomizeRecordApi tdhCustomizeRecordApi;

    @PostMapping("/admin_list")
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    public RspList list(@RequestBody TdhCustomizeRecordQueryReqBO reqBO) {
        return tdhCustomizeRecordOperationsApi.list(reqBO);
    }

    @PostMapping("/info")
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    public Rsp info(@RequestBody TdhCustomizeRecordQueryReqBO reqBO) {
        return tdhCustomizeRecordOperationsApi.info(reqBO);
    }

    @PostMapping("/order_list")
    public RspList orderList(@RequestBody TdhCustomizeRecordQueryReqBO reqBO) {
        return tdhCustomizeRecordApi.orderList(reqBO);
    }

    @PostMapping("/order_info")
    public Rsp orderInfo(@RequestBody TdhCustomizeRecordQueryReqBO reqBO) {
        return tdhCustomizeRecordApi.orderInfo(reqBO);
    }



    @PostMapping("/price_change")
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    public Rsp priceChange(@RequestBody TdhCustomizeRecordPriceChangeReqBO request) {
        return tdhCustomizeRecordOperationsApi.priceChange(request);
    }

    @PostMapping("/admin_update")
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    public Rsp modifyStatus(@RequestBody TdhCustomizeRecordModifyStatusReqBO request) {
        return tdhCustomizeRecordOperationsApi.modifyStatus(request);
    }

    @PostMapping("/update")
    public Rsp updateInfo(@RequestBody TdhCustomizeRecordUpdateInfoReqBO request) {
        return tdhCustomizeRecordApi.updateInfo(request);
    }

    @PostMapping("/update/view_time")
    public Rsp updateFirstViewTime(@RequestBody TdhCustomizeRecordQueryReqBO request) {
        return tdhCustomizeRecordApi.updateFirstViewTime(request);
    }
}
