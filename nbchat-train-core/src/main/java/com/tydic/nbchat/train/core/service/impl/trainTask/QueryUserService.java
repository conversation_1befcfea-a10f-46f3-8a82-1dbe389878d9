package com.tydic.nbchat.train.core.service.impl.trainTask;

import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskDeptRelMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskUserRelMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDeptRel;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskUserRel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QueryUserService {

    @Resource
    private NbchatTrainTaskDeptRelMapper nbchatTrainTaskDeptRelMapper;
    @Resource
    private NbchatTrainTaskUserRelMapper nbchatTrainTaskUserRelMapper;
    @Resource
    private NameMapper nameMapper;

    /**
     * 获取部门、岗位下所有用户
     *
     * @return
     */
    public List<String> getDeptPostUsers(Integer taskId, String supportSubDept, String postId, String tenantCode) {
        List<String> userIds = new ArrayList<>();
        NbchatTrainTaskDeptRel po = new NbchatTrainTaskDeptRel();
        po.setTaskId(taskId);
        List<NbchatTrainTaskDeptRel> taskDeptRels = nbchatTrainTaskDeptRelMapper.selectAll(po);
        if (CollectionUtils.isEmpty(taskDeptRels)) {
            return userIds;
        }
        for (NbchatTrainTaskDeptRel taskDeptRel : taskDeptRels) {
            List<String> postIds = Arrays.asList(postId.split(","));
            Set<String> users = nameMapper.queryDeptPostListUser(taskDeptRel.getDeptId(), supportSubDept, postIds, tenantCode);
            userIds.addAll(users);
        }
        log.info("获取部门、岗位下所有用户：{}|{}|{}|{}|{}", taskId, supportSubDept, postId, tenantCode, userIds.size());
        return userIds;
    }

    /**
     * 获取任务直接关联的用户
     *
     * @return
     */
    public List<String> getUsers(Integer taskId) {
        NbchatTrainTaskUserRel po = new NbchatTrainTaskUserRel();
        po.setTaskId(taskId);
        List<NbchatTrainTaskUserRel> userRels = nbchatTrainTaskUserRelMapper.queryAll(po);
        if (CollectionUtils.isEmpty(userRels)) {
            return new ArrayList<>();
        }
        return userRels.stream().map(NbchatTrainTaskUserRel::getUserId).collect(Collectors.toList());
    }
}
