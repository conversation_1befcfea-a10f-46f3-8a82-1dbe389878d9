package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigAdminSetReqBO;
import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigQueryReqBO;
import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigUserSetReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 切换机器人
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("/train/robot/config")
public class TrainRobotConfigController {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private SysRobotConfigApi sysRobotConfigApi;

    /**
     * 管理员配置机器人
     * @param setReqBO
     * @return
     */
    @PostMapping("/admin/set")
    public Rsp adminSet(@RequestBody SysRobotConfigAdminSetReqBO setReqBO){
        return sysRobotConfigApi.setRobotConfigByAdmin(setReqBO);
    }

    /**
     * 普通用户切换机器人
     * @param setReqBO
     * @return
     */
    @PostMapping("/set")
    public Rsp set(@RequestBody SysRobotConfigUserSetReqBO setReqBO){
        return sysRobotConfigApi.setRobotConfig(setReqBO);
    }

    /**
     * 用户查询机器人列表
     * @param reqBO
     * @return
     */
    @PostMapping("/list")
    public Rsp list(@RequestBody SysRobotConfigQueryReqBO reqBO){
        return sysRobotConfigApi.getRobotConfigList(reqBO);
    }
}
