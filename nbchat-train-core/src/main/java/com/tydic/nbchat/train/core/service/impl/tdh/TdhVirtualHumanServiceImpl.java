package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.TdhHumanType;
import com.tydic.nbchat.train.api.bo.eums.TdhVirtualHumanType;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanSortReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanUpdateReqBO;
import com.tydic.nbchat.train.api.tdh.TdhObjectShareApi;
import com.tydic.nbchat.train.api.tdh.TdhVirtualHumanApi;
import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 数字人：虚拟人形象管理相关接口
 */
@Slf4j
@Service
public class TdhVirtualHumanServiceImpl implements TdhVirtualHumanApi {

    @Resource
    TdhVirtualHumanMapper tdhVirtualHumanMapper;
    @Resource
    TdhCustomizeRecordMapper tdhCustomizeRecordMapper;
    @Resource
    TdhObjectShareApi tdhObjectShareApi;

    @Override
    public RspList<TdhVirtualHumanQueryRspBO> getVirtualHumanList(TdhVirtualHumanQueryReqBO reqBO) {
        TdhVirtualHuman cond = new TdhVirtualHuman();
        BeanUtils.copyProperties(reqBO,cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhVirtualHuman> page = PageHelper.startPage(reqBO.getPage(),reqBO.getLimit());
        tdhVirtualHumanMapper.selectAll(cond);
        ArrayList<TdhVirtualHumanQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(page,rspList,TdhVirtualHumanQueryRspBO.class);
        for (TdhVirtualHumanQueryRspBO bo : rspList) {
            if (bo.getTdhSource().equals("1")) {
                //私有的（泛化+训练+卡通）都在一起
                bo.setTdhGroup("定制形象");
            } else {
                if (TdhHumanType.GIF.getName().equals(bo.getTdhType())) {
                    //卡通形象
                    bo.setTdhGroup("卡通形象");
                } else if (TdhHumanType.CUSTOM.getName().equals(bo.getTdhType()) ||
                        TdhHumanType.RAIN.getName().equals(bo.getTdhType())) {
                    //写实形象(泛化+训练)
                    bo.setTdhGroup("写实形象");
                } else if (TdhHumanType.CUSTOM_PHOTO.getName().equals(bo.getTdhType())) {
                    //卡通形象
                    bo.setTdhGroup("照片形象");
                } else {
                    //其他形象
                    bo.setTdhGroup("其他形象");
                }
            }
            if (StringUtils.isNotEmpty(bo.getOrderNo())) {
                TdhCustomizeRecord record = tdhCustomizeRecordMapper.findByOrderNo(bo.getOrderNo());
                if (ObjectUtils.isNotEmpty(record)) {
                    bo.setCustomizeStatus(record.getCustomizeStatus());
                    bo.setOrderStatus(record.getOrderStatus());
                    if (ObjectUtils.isNotEmpty(record.getEndTime())) {
                        bo.setIsExpire(record.getEndTime().before(new Date()) ? "1" : "0");
                    }
                }
            }
        }
        return BaseRspUtils.createSuccessRspList(rspList,page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveOrUpdateVirtualHuman(TdhVirtualHumanUpdateReqBO request) {
        // 非GIF类型必须传入ID
        if (!TdhHumanType.GIF.getName().equals(request.getTdhType())) {
            if (StringUtils.isBlank(request.getTdhId())) {
                return BaseRspUtils.createErrorRsp("非GIF类型数字人必须传入ID");
            }
        }
        TdhVirtualHuman tdhVirtualHuman = new TdhVirtualHuman();
        BeanUtils.copyProperties(request,tdhVirtualHuman);
        // 如果 tdhType 是 2d_gif，则设置 tdhSource 为 2
        if (TdhHumanType.GIF.getName().equals(request.getTdhType())) {
            tdhVirtualHuman.setTdhSource("2");
        }
        if (TdhVirtualHumanType.CUSTOM.getCode().equals(request.getTdhSource())) {
            if (StringUtils.isBlank(request.getSpecifyTenantCode())) {
               request.setSpecifyTenantCode(request.getTenantCode());
            }
            //指定租户
            tdhVirtualHuman.setUserId(request.getSpecifyTenantCode());
            tdhVirtualHuman.setTenantCode(request.getSpecifyTenantCode());
        }else {
            //全局
            tdhVirtualHuman.setUserId(UserAttributeConstants.DEFAULT_TENANT_CODE);
            tdhVirtualHuman.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
        }
        TdhVirtualHuman human = tdhVirtualHumanMapper.queryById(request.getTdhId());
        if (ObjectUtils.isEmpty(human)) {
            // 仅GIF类型自动生成ID
            if (TdhHumanType.GIF.getName().equals(request.getTdhType())) {
                tdhVirtualHuman.setTdhId(IdWorker.nextAutoIdStr());
            }
            tdhVirtualHuman.setIsValid(EntityValidType.DELETE.getCode());
            tdhVirtualHuman.setCreateTime(new Date());
            tdhVirtualHumanMapper.insertSelective(tdhVirtualHuman);
        } else {
            tdhVirtualHuman.setOrderIndex(null);
            tdhVirtualHumanMapper.update(tdhVirtualHuman);
        }
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp sort(TdhVirtualHumanSortReqBO request) {
        List<TdhVirtualHuman> humanList = new ArrayList<>();
        IntStream.range(0,request.getVirtualHumanIds().size()).forEach(i -> {
            TdhVirtualHuman human = new TdhVirtualHuman();
            human.setTdhId(request.getVirtualHumanIds().get(i));
            human.setOrderIndex(i);
            humanList.add(human);
        });
        tdhVirtualHumanMapper.updateBatchSelective(humanList);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 数字人上下架
     */
    @Override
    public Rsp updateStatus(TdhVirtualHumanUpdateReqBO request) {
        TdhVirtualHuman tdhVirtualHuman = new TdhVirtualHuman();
        tdhVirtualHuman.setTdhId(request.getTdhId());
        tdhVirtualHuman.setIsValid(request.getIsValid());
        tdhVirtualHumanMapper.update(tdhVirtualHuman);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 删除虚拟人
     */
    @Override
    public Rsp delete(TdhVirtualHumanUpdateReqBO request) {
        TdhVirtualHuman tdhVirtualHuman = new TdhVirtualHuman();
        tdhVirtualHuman.setTdhId(request.getTdhId());
        tdhVirtualHumanMapper.deleteById(request.getTdhId());
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 运营平台数字人列表查询
     * @param request
     * @return
     */

    @Override
    public RspList<TdhVirtualHumanQueryRspBO> getQueryVirtualHumanList(TdhVirtualHumanQueryReqBO request) {
        TdhVirtualHuman queryRspBO = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, queryRspBO);
        if (StringUtils.isNotEmpty(request.getSpecifyTenantCode())) {
            queryRspBO.setTenantCode(request.getSpecifyTenantCode());
            queryRspBO.setUserId(request.getSpecifyTenantCode());
        } else {
            queryRspBO.setTenantCode(null);
            queryRspBO.setUserId(null);
        }
        Page<TdhVirtualHumanQueryRspBO> page = PageHelper.startPage(request.getPage(), request.getLimit());
        List<TdhVirtualHuman> resultList = tdhVirtualHumanMapper.selectByConditions(queryRspBO);
        List<TdhVirtualHumanQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(resultList, rspList, TdhVirtualHumanQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }

}
