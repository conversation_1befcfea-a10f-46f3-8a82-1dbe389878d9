package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.modelscope-config")
public class ModelscopeConfigProperties {
    private String privateTtsApi = "http://172.168.1.249:8098/v1/tts_gateway/generate";
    private String privateAsrApi = "http://172.168.1.246:10097/asr/offline/doAsr";
    private String privateAsrSign = "xx";
    private String voice = "";
}
