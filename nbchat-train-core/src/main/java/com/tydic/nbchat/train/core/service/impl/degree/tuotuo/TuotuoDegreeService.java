package com.tydic.nbchat.train.core.service.impl.degree.tuotuo;

import com.tydic.nbchat.train.api.bo.eums.ProjectType;
import com.tydic.nbchat.train.core.service.impl.degree.DegreeApi;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskDegreeMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;

@Slf4j
@Service
public class TuotuoDegreeService implements DegreeApi {

    @Resource
    NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;


    @Override
    public String projectType() {
        return ProjectType.Tuotuo.getCode();
    }

    @Override
    public String createDegree(String degreeId) throws Exception {
        NbchatTrainTaskDegree taskDegree = nbchatTrainTaskDegreeMapper.queryById(degreeId);
        log.info("证书信息：{}", taskDegree);

        LocalDate localDate = taskDegree.getIssueDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        String issueDate = localDate.getYear() + "年" + localDate.getMonthValue() + "月" + localDate.getDayOfMonth() + "日";

        return new ImageProcessor()
                .userName(taskDegree.getUserName())
                .projectName("《" +taskDegree.getDegreeName() + "》课程")
                .issueTime(issueDate)
                .build();
    }

}
