package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogQueryReqBO;
import com.tydic.nbchat.train.api.bo.train.section.SectionsSaveRequest;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionQueryRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/section")
public class TrainCourseSectionController {
    private final NbchatTrainSectionApi nbchatTrainSectionApi;

    public TrainCourseSectionController(NbchatTrainSectionApi nbchatTrainSectionApi) {
        this.nbchatTrainSectionApi = nbchatTrainSectionApi;
    }


    @PostMapping("/train/incr")
    public Rsp addTrainCount(@RequestBody TrainCatalogQueryReqBO request) {
        return nbchatTrainSectionApi.addTrainCount(request);
    }

    /**
     * 查询列表
     * @param request
     * @return
     */
    @PostMapping("/catalogs")
    public RspList getSectionCatalogs(@RequestBody TrainCatalogQueryReqBO request) {
        return nbchatTrainSectionApi.getSectionCatalogs(request);
    }

    /**
     * 查询章节列表
     * @param request
     * @return
     */
    @PostMapping("/list")
    public Rsp<TrainSectionQueryRspBO> getSectionContents(@RequestBody TrainCatalogQueryReqBO request) {
        return nbchatTrainSectionApi.getSectionContents(request);
    }

    @PostMapping("/save")
    public Rsp saveSection(@RequestBody SectionsSaveRequest request) {
        return nbchatTrainSectionApi.saveSection(request);
    }


}
