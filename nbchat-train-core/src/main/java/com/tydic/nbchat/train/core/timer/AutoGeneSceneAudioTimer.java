package com.tydic.nbchat.train.core.timer;

import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskRequest;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.eums.FileUploadDirEnum;
import com.tydic.nbchat.train.core.config.NbchatTrainConfigProperties;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import com.tydic.nbchat.train.mapper.NbchatTrainSceneDialogueMapper;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSceneDialogue;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@ConditionalOnProperty(
        value = "nbchat-train.config.scene-audio-enable",
        havingValue = "true")
@EnableScheduling
@Component
public class AutoGeneSceneAudioTimer {

    private final NlsStrategyInvokeFactory nlsStrategyInvokeFactory;
    private final NbchatTrainConfigProperties nbchatTrainConfigProperties;

    @Resource
    private NbchatTrainSceneDialogueMapper nbchatTrainSceneDialogueMapper;

    public AutoGeneSceneAudioTimer(NlsStrategyInvokeFactory nlsStrategyInvokeFactory,
                                   NbchatTrainConfigProperties nbchatTrainConfigProperties) {
        this.nlsStrategyInvokeFactory = nlsStrategyInvokeFactory;
        this.nbchatTrainConfigProperties = nbchatTrainConfigProperties;
    }

    //每5s查询一次任务，自动生成场景音频
    @Scheduled(fixedRate = 5 * 1000)
    public void run() {
        /**
         * TODO
         * 1. 查询待生成音频的场景对话
         * 2. 调用NlsStrategyInvokeFactory生成音频
         * {
         *     "courseId": "0",
         *     "voice": "BV407_V2_streaming",
         *     "anchorType": "volcengine",
         *     "text": "<speak><emotion style=''>你好\n</emotion></speak>",
         *     "speechRate": 0,
         *     "pitchRate": 0,
         *     "volume": 50
         * }
         *
         */
        NbchatTrainSceneDialogue record = new NbchatTrainSceneDialogue();
        record.setAudioState("0");
        record.setStartTime(DateTimeUtil.DateAddHour(-2));
        record.setEndTime(new Date());
        List<NbchatTrainSceneDialogue> dialogues = nbchatTrainSceneDialogueMapper.selectByCondition(record);
        log.info("查询到待生成音频的场景对话数量：{}", dialogues.size());
        for (NbchatTrainSceneDialogue dialogue : dialogues) {
            //生成音频
            TtsVoiceTaskRequest request = new TtsVoiceTaskRequest();
            request.setAsync(false);
            request.setCourseId("0");
            request.setSectionId(FileUploadDirEnum.TTS_DIR.getCode());
            request.setAnchorType(nbchatTrainConfigProperties.getTtsType());
            request.setVoice(AnchorType.getDefaultVoiceByCode(request.getAnchorType()));
            request.setText(dialogue.getContent());
            TtsVoiceTaskContext context = nlsStrategyInvokeFactory.getApi().createAudioTask(request);
            //更新音频
            if (context != null && StringUtils.isNotBlank(context.getAudio_address())) {
                dialogue.setAudioState("1");
                dialogue.setAudioUrl(context.getAudio_address());
                dialogue.setId(dialogue.getId());
                nbchatTrainSceneDialogueMapper.updateByPrimaryKeySelective(dialogue);
            }
        }
    }
}
