package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.tydic.nbchat.train.core.config.AliAkskConfigProperties;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AliAccessTokenHelper {

    private final AliAkskConfigProperties aliConfigProperties;
    private final RedisHelper redisHelper;
    // 您的地域ID
    private static final String REGIONID = "cn-shanghai";
    // 获取Token服务域名
    private static final String DOMAIN = "nls-meta.cn-shanghai.aliyuncs.com";
    // API 版本
    private static final String API_VERSION = "2019-02-28";
    // API名称
    private static final String REQUEST_ACTION = "CreateToken";
    // 响应参数
    private static final String KEY_TOKEN = "Token";
    private static final String KEY_ID = "Id";
    private static final String KEY_EXPIRETIME = "ExpireTime";
    private static final String NBCHAT_NLS_TOKEN = "NBCHAT_NLS_TOKEN";
    private IAcsClient iAcsClient;
    private DefaultProfile profile;

    public IAcsClient getiAcsClient() {
        return iAcsClient;
    }

    public AliAccessTokenHelper(AliAkskConfigProperties aliConfigProperties, RedisHelper redisHelper) {
        this.aliConfigProperties = aliConfigProperties;
        this.redisHelper = redisHelper;
        profile = DefaultProfile.getProfile(
                REGIONID,
                aliConfigProperties.getAccessKey(),
                aliConfigProperties.getSecretKey());
        iAcsClient = new DefaultAcsClient(profile);
    }


    /**
     * 获取token
     * @return
     */
    public String getAccessToken(){
        String token = (String) redisHelper.get(NBCHAT_NLS_TOKEN);
        if(StringUtils.isBlank(token)){
            log.info("刷新token - 开始...");
            token = flashAccessToken();
        }
        return token;
    }

    /**
     * 刷新并返回token
     * @return
     */
    public String flashAccessToken() {
        //token 80101f5ad0c844a6b8e7749c1cfafb37
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setDomain(DOMAIN);
        request.setVersion(API_VERSION);
        request.setAction(REQUEST_ACTION);
        request.setMethod(MethodType.POST);
        request.setProtocol(ProtocolType.HTTPS);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            log.warn("刷新token - 异常:{}",request,e);
            try {
                response = client.getCommonResponse(request);
            } catch (ClientException ex) {
                log.error("刷新token - 重试异常:{}",request,ex);
            }
        }
        String token = "";
        //System.out.println(response.getData());
        if (response.getHttpStatus() == 200) {
            JSONObject result = JSON.parseObject(response.getData());
            token = result.getJSONObject(KEY_TOKEN).getString(KEY_ID);
            long expireTime = result.getJSONObject(KEY_TOKEN).getLongValue(KEY_EXPIRETIME) * 1000;
            // 将10位数的时间戳转换为北京时间
            String expireDate = DateTimeUtil.getTimeShortString(new Date(expireTime),DateTimeUtil.TIME_FORMAT_NORMAL);
            log.info("刷新token - 获取到的Token: {}，expireDate: {}" ,token, expireDate);
            long expSec = (expireTime - System.currentTimeMillis()) / 1000;
            redisHelper.set(NBCHAT_NLS_TOKEN,token,expSec - 60);
        } else {
            try {
                TimeUnit.SECONDS.sleep(1L);
            } catch (InterruptedException e) {
            }
            flashAccessToken();
        }
        return token;
    }
}
