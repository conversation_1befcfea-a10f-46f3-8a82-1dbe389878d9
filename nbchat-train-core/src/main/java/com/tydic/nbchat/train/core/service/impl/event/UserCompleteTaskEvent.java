package com.tydic.nbchat.train.core.service.impl.event;

import lombok.*;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class UserCompleteTaskEvent<T> extends ApplicationEvent {

    private String tenantCode;
    private String userId;

    public UserCompleteTaskEvent(T source, String tenantCode, String userId) {
        super(source);
        this.tenantCode = tenantCode;
        this.userId = userId;
    }
}
