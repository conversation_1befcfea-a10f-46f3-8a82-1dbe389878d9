package com.tydic.nbchat.train.core.service.impl.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhObjectShareBO;
import com.tydic.nbchat.train.api.tdh.TdhObjectShareApi;
import com.tydic.nbchat.train.mapper.TdhObjectShareMapper;
import com.tydic.nbchat.train.mapper.TdhTemplateMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualAnchorMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper;
import com.tydic.nbchat.train.mapper.po.TdhObjectShare;
import com.tydic.nbchat.train.mapper.po.TdhTemplate;
import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TdhObjectShareServiceImpl implements TdhObjectShareApi {

    @Resource
    TdhObjectShareMapper tdhObjectShareMapper;
    @Resource
    TdhVirtualHumanMapper tdhVirtualHumanMapper;
    @Resource
    TdhVirtualAnchorMapper tdhVirtualAnchorMapper;
    @Resource
    TdhTemplateMapper tdhTemplateMapper;

    @Override
    public RspList queryShareList(TdhObjectShareBO request) {
        TdhObjectShare cond = new TdhObjectShare();
        cond.setObjectType(request.getObjectType());
        cond.setShareTo(request.getTenantCode());
        List<TdhObjectShare> tenantShares = tdhObjectShareMapper.selectAll(cond);

        cond.setShareTo(request.getUserId());
        List<TdhObjectShare> userShares = tdhObjectShareMapper.selectAll(cond);

        tenantShares.addAll(userShares);

        List<?> data = new ArrayList<>();
        List<String> collect = tenantShares.stream().map(TdhObjectShare::getObjectId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            if (human.equals(request.getObjectType())) {
                data = tdhVirtualHumanMapper.selectByIds(collect);
            }
            if (anchor.equals(request.getObjectType())) {
                data = tdhVirtualAnchorMapper.selectByIds(collect);
            }
            if (template.equals(request.getObjectType())) {
                data = tdhTemplateMapper.selectByIds(collect);
            }
        }
        log.info("共享列表查询结果:{}/条",collect.size());
        return BaseRspUtils.createSuccessRspList(data);
    }
}
