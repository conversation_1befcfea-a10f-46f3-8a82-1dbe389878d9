package com.tydic.nbchat.train.core.timer;

import com.tydic.nbchat.train.mapper.NbchatTrainTaskDegreeMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

@Slf4j
@ConditionalOnProperty(
        value = "nbchat-train.config.degree-timer-enable",
        havingValue = "true")
@EnableScheduling
@Component
public class DegreeCheckTimer {

    @Resource
    NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;

    //每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    public void run() {
        NbchatTrainTaskDegree po = new NbchatTrainTaskDegree();
        po.setStatus("1");
        List<NbchatTrainTaskDegree> degrees = nbchatTrainTaskDegreeMapper.selectAll(po);
        if (CollectionUtils.isEmpty(degrees)) {
            return;
        }
        log.info("证书有效期检查，共{}条数据", degrees.size());
        for (NbchatTrainTaskDegree degree : degrees) {
            LocalDate localDate = degree.getIssueDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            localDate = localDate.plusMonths(degree.getValidityPeriod());
            if (localDate.isBefore(LocalDate.now())) {
                degree.setStatus("2");
                nbchatTrainTaskDegreeMapper.update(degree);
            }
        }
    }
}
