package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainCourseApi;
import com.tydic.nbchat.train.api.bo.course.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/course")
public class TrainCourseController {
    private final NbchatTrainCourseApi nbchatTrainCourseApi;

    public TrainCourseController(NbchatTrainCourseApi nbchatTrainCourseApi) {
        this.nbchatTrainCourseApi = nbchatTrainCourseApi;
    }


    /**
     * 查询列表
     * @param request
     * @return
     */
    @PostMapping("/list")
    public RspList getCourses(@RequestBody TranCourseQueryReqBO request) {
        return nbchatTrainCourseApi.getCourses(request);
    }

    /**
     * 查询对象
     * @param request
     * @return
     */
    @PostMapping("/get")
    public Rsp getCourse(@RequestBody TranCourseQueryReqBO request) {
        return nbchatTrainCourseApi.getCourse(request);
    }

    /**
     * 删除
     * @param request
     * @return
     */
    @PostMapping("/delete")
    public Rsp deleteCourse(@RequestBody TranCourseQueryReqBO request) {
        return nbchatTrainCourseApi.deleteCourse(request);
    }

    /**
     * 保存对象
     * @param request
     * @return
     */
    @PostMapping("/save")
    public Rsp saveCourse(@RequestBody TranCourseSaveReqBO request) {
        return nbchatTrainCourseApi.saveCourse(request);
    }

    @PostMapping("/newType")
    public Rsp newCourse(@RequestBody TranCourseSaveReqBO request) {
        return nbchatTrainCourseApi.newCourse(request);
    }

    /**
     *
     * @param request
     * @return
     */
    @PostMapping("/topHub")
    public RspList getTopHubCourse(@RequestBody TrainCourseHotHubBO request) {
        return nbchatTrainCourseApi.getTopHubCourse(request);
    }

    @PostMapping("/content")
    public Rsp getCourseContent(@RequestBody NbchatTrainCourseTextBO request) {
        return nbchatTrainCourseApi.getCourseContent(request);
    }

    @PostMapping("/content/save")
    public Rsp saveCourseContent(@RequestBody NbchatTrainCourseTextBO request) {
        return nbchatTrainCourseApi.saveCourseContent(request);
    }

    @PostMapping("/sections/save")
    public Rsp saveCourseSections(@RequestBody TrainCourseSectionsQueryReqBO request) {
        return nbchatTrainCourseApi.saveCourseSections(request);

    }

    @PostMapping("/sections/query")
    public Rsp queryCourseSections(@RequestBody TrainCourseSectionsQueryReqBO request){
        return nbchatTrainCourseApi.queryCourseSections(request);
    }
}
