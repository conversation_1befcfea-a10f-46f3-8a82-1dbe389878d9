package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.TrainSectionVideoApi;
import com.tydic.nbchat.train.api.bo.video.SectionVideoStstReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/course/video")
public class TrainSectionVideoController {

    @Resource
    TrainSectionVideoApi trainSectionVideoApi;

    @PostMapping("click")
    public Rsp click(@RequestBody SectionVideoStstReqBO request){
        return trainSectionVideoApi.handleVideoStst(request);
    }

    @PostMapping("index")
    public Rsp queryIndex(@RequestBody SectionVideoStstReqBO request){
        return trainSectionVideoApi.queryVideoIndex(request);
    }
}
