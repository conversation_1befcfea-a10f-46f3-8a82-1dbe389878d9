package com.tydic.nbchat.session.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionMsgReqBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionMsgRspBO;
import com.tydic.nbchat.session.api.enums.StatusEnum;
import com.tydic.nbchat.session.api.SessionMsgService;
import com.tydic.nbchat.session.mapper.NbchatSessionMapper;
import com.tydic.nbchat.session.mapper.NbchatSessionMsgMapper;
import com.tydic.nbchat.session.mapper.po.NbchatSessionMsgPO;
import com.tydic.nbchat.session.mapper.po.NbchatSessionMsgSelectCondition;
import com.tydic.nbchat.session.mapper.po.NbchatSessionPO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SessionMsgServiceImpl implements SessionMsgService {

    @Resource
    NbchatSessionMsgMapper nbchatSessionMsgMapper;
    @Resource
    NbchatSessionMapper nbchatSessionMapper;

    @Override
    public RspList queryHistory(NbchatSessionMsgReqBO reqBO) {
        log.info("查询历史会话:{}", JSON.toJSONString(reqBO));
        NbchatSessionMsgSelectCondition condition = new NbchatSessionMsgSelectCondition();
        BeanUtils.copyProperties(reqBO,condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        List<NbchatSessionMsgPO> res = nbchatSessionMsgMapper.selectByCondition(condition);
        List<NbchatSessionMsgRspBO> msgRspBOS = Lists.newArrayList();
        NiccCommonUtil.copyList(res,msgRspBOS,NbchatSessionMsgRspBO.class);
        return BaseRspUtils.createSuccessRspList(msgRspBOS);
    }

    @Override
    public RspList getMsgList(NbchatSessionMsgReqBO reqBO) {
        log.info("查询历史会话:{}", JSON.toJSONString(reqBO));
        NbchatSessionMsgSelectCondition condition = new NbchatSessionMsgSelectCondition();
        BeanUtils.copyProperties(reqBO,condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setOrderTimeDesc(true);
        Page<NbchatSessionMsgPO> page = PageHelper.startPage(reqBO.getPage(),reqBO.getLimit());
        nbchatSessionMsgMapper.selectByCondition(condition);
        //处理试题专业版消息展示
        if (StringUtils.isNotEmpty(reqBO.getRefer()) && reqBO.getRefer().equals("0")) {
            for (NbchatSessionMsgPO po : page.getResult()) {
                po.setPrompt(po.getConversationOptions());
            }
        }
        List<NbchatSessionMsgRspBO> msgRspBOS = Lists.newArrayList();
        NiccCommonUtil.copyList(page.getResult(),msgRspBOS,NbchatSessionMsgRspBO.class);
        return BaseRspUtils.createSuccessRspList(msgRspBOS);
    }

    @Override
    public Rsp deleteMsg(NbchatSessionMsgReqBO reqBO) {
        log.info("删除消息:{}",JSON.toJSONString(reqBO));
        NbchatSessionMsgPO po = new NbchatSessionMsgPO();
        po.setRequestId(reqBO.getRequestId());
        po.setIsValid(StatusEnum.DELETE.getValue());
        po.setSessionId(reqBO.getSessionId());
        int i = nbchatSessionMsgMapper.update(po);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(null);
        }
        return BaseRspUtils.createErrorRsp("删除失败");
    }



    @Override
    public Rsp clearMsg(NbchatSessionMsgReqBO reqBO) {
        log.info("清除会话记录:{}",JSON.toJSONString(reqBO));
        NbchatSessionMsgPO po = new NbchatSessionMsgPO();
        po.setSessionId(reqBO.getSessionId());
        po.setIsValid(StatusEnum.DELETE.getValue());
        int i = nbchatSessionMsgMapper.updateBySessionId(po);
        if (i > 0) {
            log.info("清空会话[{}]成功",reqBO.getSessionId());
        }
        return BaseRspUtils.createSuccessRsp(null);
    }


    @Override
    public RspList getAllSession(NbchatSessionMsgReqBO request) {
        if (StringUtils.isEmpty(request.getAppId())) {
            return BaseRspUtils.createErrorRspList("appId不得为空");
        }
        log.info("查询会话消息:{}", JSON.toJSONString(request));
        NbchatSessionMsgSelectCondition po = new NbchatSessionMsgSelectCondition();
        po.setAppId(request.getAppId());
        po.setStartTime(request.getStartTime());
        po.setEndTime(request.getEndTime());
        po.setKeyword(request.getKeyword());
        Page<NbchatSessionMsgPO> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatSessionMsgMapper.queryAppSessionMsg(po);
        List<NbchatSessionMsgRspBO> msgRspBOS = Lists.newArrayList();
        NiccCommonUtil.copyList(page.getResult(),msgRspBOS,NbchatSessionMsgRspBO.class);
        if (CollectionUtils.isNotEmpty(msgRspBOS)) {
            List<String> sessionIds = msgRspBOS.stream().map(NbchatSessionMsgRspBO::getSessionId).collect(Collectors.toList());
            List<NbchatSessionPO> nbchatSessionPOS = nbchatSessionMapper.querySessionName(sessionIds);
            Map<String, String> nameMap = nbchatSessionPOS.stream().collect(Collectors.toMap(NbchatSessionPO::getSessionId, NbchatSessionPO::getSessionName));
            msgRspBOS.forEach(msg -> {
                msg.setSessionName(nameMap.get(msg.getSessionId()));
            });
        }
        return BaseRspUtils.createSuccessRspList(msgRspBOS,page.getTotal());
    }
}
