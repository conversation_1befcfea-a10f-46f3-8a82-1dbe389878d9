package com.tydic.nbchat.session.core.service.impl;

import com.tydic.nbchat.session.api.bo.ReportQueryBO;
import com.tydic.nbchat.session.mapper.ReportMapper;
import com.tydic.nbchat.session.mapper.po.ReportResultPO;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ReportService {

    @Resource
    ReportMapper reportMapper;

    /* 统计会话指标
     */
    public RspList countMsg(ReportQueryBO request) {
        if (StringUtils.isEmpty(request.getAppId())) {
            return BaseRspUtils.createErrorRspList("appId不能为空");
        }
        if (StringUtils.isEmpty(request.getStartTime())) {
            Date date = DateTimeUtil.DateAddDayOfYear(-30);
            request.setStartTime(DateTimeUtil.getTimeShortString(date, DateTimeUtil.TIME_FORMAT_NORMAL));
        }
        if (StringUtils.isEmpty(request.getEndTime())) {
            request.setEndTime(DateTimeUtil.getTimeShortString(new Date(), DateTimeUtil.TIME_FORMAT_NORMAL));
        }
        List<ReportResultPO> res = reportMapper.countSessionIndex(request.getStartTime(), request.getEndTime(), request.getAppId());
        return BaseRspUtils.createSuccessRspList(res);
    }

}
