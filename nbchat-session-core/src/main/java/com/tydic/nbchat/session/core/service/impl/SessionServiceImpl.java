package com.tydic.nbchat.session.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.session.api.bo.*;
import com.tydic.nbchat.session.api.enums.StatusEnum;
import com.tydic.nbchat.session.core.config.SessionConfig;
import com.tydic.nbchat.session.core.busi.ActionService;
import com.tydic.nbchat.session.api.SessionMsgService;
import com.tydic.nbchat.session.api.SessionService;
import com.tydic.nbchat.session.core.utils.DateUtil;
import com.tydic.nbchat.session.core.utils.RandomStringGenerator;
import com.tydic.nbchat.session.mapper.NbchatSessionMapper;
import com.tydic.nbchat.session.mapper.NbchatSessionMsgMapper;
import com.tydic.nbchat.session.mapper.NbchatSessionShareMapper;
import com.tydic.nbchat.session.mapper.po.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class SessionServiceImpl implements SessionService {

    @Resource
    NbchatSessionMapper nbchatSessionMapper;
    @Resource
    NbchatSessionShareMapper nbchatSessionShareMapper;
    @Resource
    NbchatSessionMsgMapper nbchatSessionMsgMapper;

    private final ActionService actionService;
    private final SessionMsgService sessionMsgService;
    private final SessionConfig sessionConfig;

    public SessionServiceImpl(ActionService actionService, SessionMsgService sessionMsgService, SessionConfig sessionConfig) {
        this.actionService = actionService;
        this.sessionMsgService = sessionMsgService;
        this.sessionConfig = sessionConfig;
    }

    @Override
    public Rsp getFromShareSavedSession(NbchatSessionShareReqBO reqBO) {
        if(StringUtils.isAllBlank(reqBO.getUserId(),reqBO.getSessionId())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        NbchatSessionPO sessionPO = nbchatSessionMapper.selectLastByFromSession(reqBO.getUserId(),reqBO.getSessionId());
        String sessionId = "";
        if(sessionPO != null){
            sessionId = sessionPO.getSessionId();
        }
        return BaseRspUtils.createSuccessRsp(sessionId);
    }

    @Override
    public RspList<NbchatSessionRspBO> getAllSessions(NbchatSessionReqBO reqBO) {
        log.info("查询会话列表：{}", JSON.toJSONString(reqBO));
        NbchatSessionSelectCondition condition = new NbchatSessionSelectCondition();
        condition.setIsValid(StatusEnum.NORMAL.getValue());
        condition.setUserId(reqBO.getUserId());
        condition.setSessionName(reqBO.getSessionName());
        condition.setSessionId(reqBO.getSessionId());
        condition.setSessionType(reqBO.getSessionType());
        condition.setAppId(reqBO.getAppId());
        List<NbchatSessionRspBO> sessionRspBOS = Lists.newArrayList();
        List<NbchatSessionPO> res = nbchatSessionMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(res,sessionRspBOS,NbchatSessionRspBO.class);
        log.info("历史会话查询结果:{}",sessionRspBOS);
        return BaseRspUtils.createSuccessRspList(sessionRspBOS);
    }

    @Override
    public RspList<NbchatSessionRspBO> getSessions(NbchatSessionReqBO reqBO) {
        log.info("查询会话列表：{}", JSON.toJSONString(reqBO));
        NbchatSessionSelectCondition condition = new NbchatSessionSelectCondition();
        condition.setIsValid(StatusEnum.NORMAL.getValue());
        condition.setUserId(reqBO.getUserId());
        condition.setSessionName(reqBO.getSessionName());
        condition.setSessionId(reqBO.getSessionId());
        condition.setSessionType(reqBO.getSessionType());
        condition.setAppId(reqBO.getAppId());
        Page<NbchatSessionPO> page = PageHelper.startPage(reqBO.getPage(),reqBO.getLimit());
        List<NbchatSessionRspBO> sessionRspBOS = Lists.newArrayList();
        nbchatSessionMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(),sessionRspBOS,NbchatSessionRspBO.class);
        log.info("历史会话查询结果:{}",sessionRspBOS);
        return BaseRspUtils.createSuccessRspList(sessionRspBOS);
    }

    @Override
    public Rsp session(NbchatSessionReqBO reqBO) {
        log.info("添加、更新、收藏会话：{}", JSON.toJSONString(reqBO));
        NbchatSessionPO po = new NbchatSessionPO();
        BeanUtils.copyProperties(reqBO,po);
        //没有会话id就是新增
        if (StringUtils.isEmpty(reqBO.getSessionId())){
            po.setCreatedAt(new Date());
            po.setLastTime(new Date());
            po.setRobotType(sessionConfig.getRobotType());
            po.setSessionId(NiccCommonUtil.createImUserId(true));
            int i = nbchatSessionMapper.insertSelective(po);
            if (i > 0) {
                log.info("会话新增成功！");
                NbchatSessionPO sessionPO = nbchatSessionMapper.selectById(po.getSessionId());
                return BaseRspUtils.createSuccessRsp(sessionPO);
            }
        } else {
            //有会话id就更新
            int i = nbchatSessionMapper.update(po);
            if (i > 0) {
                log.info("会话更新成功！");
                return BaseRspUtils.createSuccessRsp(null);
            }
        }
        log.error("更新保存会话失败：{}",reqBO);
        return BaseRspUtils.createErrorRsp("更新保存会话失败");
    }

    @Override
    public Rsp shareSession(NbchatSessionShareReqBO reqBO) {
        log.info("分享会话-开始:{}",JSON.toJSONString(reqBO));
        NbchatSessionPO sessionPO = nbchatSessionMapper.selectById(reqBO.getSessionId());
        if (Objects.isNull(sessionPO)) {
            return BaseRspUtils.createSuccessRsp("该会话不存在或已被删除");
        }
        if (reqBO.isNeedKey() && StringUtils.isEmpty(reqBO.getShareKey())) {
            reqBO.setShareKey(RandomStringGenerator.generateRandomString(sessionConfig.getShareKeyLength()));
        }
        NbchatSessionSharePO sharePO = new NbchatSessionSharePO();
        BeanUtils.copyProperties(reqBO,sharePO);
        sharePO.setSessionShareId(NiccCommonUtil.createImUserId(true));
        sharePO.setShareUrl(sessionConfig.getShareUrlPrefix() + sharePO.getSessionShareId());
        if (reqBO.getExpiredDay() != -1) {
            sharePO.setExpiredDate(DateTimeUtil.DateAddDayOfYear(reqBO.getExpiredDay()));
        } else {
            sharePO.setExpiredDate(DateTimeUtil.convertAsDate("9999-01-01 00:00:00"));
        }
        sharePO.setCreatedAt(new Date());
        nbchatSessionShareMapper.insertSelective(sharePO);
        NbchatSessionSharePO res = nbchatSessionShareMapper.queryById(sharePO.getSessionShareId());
        log.info("分享记录：{},分享id:{}",res,sharePO.getSessionShareId());
        return BaseRspUtils.createSuccessRsp(res);
    }

    @Override
    public Rsp checkShareKey(NbchatSessionShareReqBO reqBO) {
        log.info("检查分享码-开始:{}",JSON.toJSONString(reqBO));
        NbchatSessionSharePO sharePO = nbchatSessionShareMapper.queryById(reqBO.getSessionShareId());
        if (Objects.isNull(sharePO)) {
            log.info("该条分享已过期或会话已被删除 :{}",sharePO);
            return BaseRspUtils.createErrorRsp("会话已过期或已被删除");
        }
        return BaseRspUtils.createSuccessRsp(sharePO);
    }


    @Override
    public Rsp queryShare(NbchatSessionShareReqBO reqBO) {
        log.info("查看分享的会话,分享会话:{}",reqBO);
        NbchatSessionSharePO shareCondi = new NbchatSessionSharePO();
        shareCondi.setSessionShareId(reqBO.getSessionShareId());
        NbchatSessionSharePO sharePO = nbchatSessionShareMapper.queryByCondition(shareCondi);
        if (Objects.isNull(sharePO)) {
            log.info("该条分享不存在或已过期:{}",reqBO.getSessionShareId());
            return BaseRspUtils.createErrorRsp("该条分享不存在或已过期");
        }
        NbchatSessionActionLogPO logPORes = new NbchatSessionActionLogPO();
        logPORes.setShareId(reqBO.getSessionShareId());
        logPORes.setUserId(reqBO.getUserId());
        NbchatSessionActionLogPO record = actionService.queryRecord(logPORes);
        //如果已经输入过分享码，就不需要重复输入
        if (Objects.isNull(record)) {
            log.info("用户[{}]首次查看 ,入参：{}",reqBO.getUserId(),reqBO);
            if (StringUtils.isEmpty(reqBO.getShareKey()) && StringUtils.isNotEmpty(sharePO.getShareKey())) {
                return BaseRspUtils.createSuccessRsp("请输入分享码");
            }
            shareCondi.setShareKey(reqBO.getShareKey());
            sharePO = nbchatSessionShareMapper.queryByCondition(shareCondi);
            if (Objects.isNull(sharePO)) {
                log.info("该条分享已过期或分享码错误:{}",reqBO.getSessionShareId());
                return BaseRspUtils.createErrorRsp("该条分享已过期或分享码错误");
            }
        }
        NbchatSessionShareRspBO rspBO = new NbchatSessionShareRspBO();
        NbchatSessionReqBO sessionReqBO = new NbchatSessionReqBO();
        sessionReqBO.setSessionId(sharePO.getSessionId());
        RspList<NbchatSessionRspBO> session = getAllSessions(sessionReqBO);
        List<NbchatSessionRspBO> rows = session.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return BaseRspUtils.createErrorRsp("会话不存在或已被删除");
        }
        NbchatSessionMsgReqBO msgReqBO = new NbchatSessionMsgReqBO();
        msgReqBO.setSessionId(sharePO.getSessionId());
        msgReqBO.setDateTime(sharePO.getCreatedAt());
        //查询分享时间之前的历史记录
        RspList<NbchatSessionMsgRspBO> rspList = sessionMsgService.queryHistory(msgReqBO);
        NbchatSessionRspBO sessionRspBO = rows.get(0);
        NbchatSessionShare share = new NbchatSessionShare();
        BeanUtils.copyProperties(sessionRspBO,share);
        Date expiredDate = sharePO.getExpiredDate();
        long differDay = DateUtil.getDaysBetween(new Date(), expiredDate);
        //默认超过一年都是永久，后续根据具体需求调整
        share.setExpiredDay(Math.max(differDay, 0) > 366 ? -1 : (int) Math.max(differDay, 0));
        share.setCreatedAt(sharePO.getCreatedAt());
        rspBO.setSession(share);
        rspBO.setMsgList(rspList.getRows());
        //判断是否保存会话
        NbchatSessionPO po = new NbchatSessionPO();
        po.setFromSessionId(sharePO.getSessionId());
        po.setUserId(reqBO.getUserId());
        NbchatSessionPO nbchatSessionPO = nbchatSessionMapper.selectSession(po);
        if (!Objects.isNull(nbchatSessionPO)) {
            rspBO.setSaved(StatusEnum.SAVE.getValue());
        }
        NbchatSessionActionLogPO logPO  = NbchatSessionActionLogPO.builder()
                .tenantCode(reqBO.getTenantCode())
                .shareId(reqBO.getSessionShareId())
                .userId(reqBO.getUserId())
                .actionType(StatusEnum.SHARE_QUERY.getValue())
                .actionTime(new Date())
                .build();
        actionService.recording(logPO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }




    @Override
    public Rsp saveShare(NbchatSessionReqBO reqBO) {
        log.info("保存分享的会话到我的会话列表：{}",JSON.toJSONString(reqBO));
        NbchatSessionSharePO sharePO = nbchatSessionShareMapper.queryById(reqBO.getSessionShareId());
        String sessionId = sharePO.getSessionId();
        NbchatSessionPO session = nbchatSessionMapper.selectById(sessionId);
        if (ObjectUtils.anyNull(sharePO,session)) {
            log.info("该条分享已过期或会话已被删除 :{}",sharePO);
            return BaseRspUtils.createErrorRsp("会话已过期或已被删除");
        }
        NbchatSessionPO cond = new NbchatSessionPO();
        cond.setFromSessionId(sessionId);
        cond.setUserId(reqBO.getUserId());
        NbchatSessionPO checkDupli = nbchatSessionMapper.selectSession(cond);
        if (!Objects.isNull(checkDupli)) {
            return BaseRspUtils.createErrorRsp("该会话已被收藏，请不要重复收藏");
        }
        //复制会话-使用新会话id
        session.setSessionId(NiccCommonUtil.createImUserId(true));
        session.setUserId(reqBO.getUserId());
        session.setTenantCode(reqBO.getTenantCode());
        session.setIsStar(StatusEnum.UNCOLLECT.getValue());
        session.setFromSessionId(sessionId);
        session.setSource(StatusEnum.SHARE.getValue());
        int i = nbchatSessionMapper.insertSelective(session);
        //复制会话明细
        NbchatSessionMsgSelectCondition condition = new NbchatSessionMsgSelectCondition();
        condition.setSessionId(sessionId);
        condition.setDateTime(sharePO.getCreatedAt());
        List<NbchatSessionMsgPO> msgList = nbchatSessionMsgMapper.selectByCondition(condition);
        for (NbchatSessionMsgPO msgPO : msgList) {
            msgPO.setRequestId(NiccCommonUtil.createImUserId(true));
            msgPO.setSessionId(session.getSessionId());
            msgPO.setUserId(reqBO.getUserId());
            msgPO.setTenantCode(reqBO.getTenantCode());
            nbchatSessionMsgMapper.insertSelective(msgPO);
        }
        //TODO 保存分享记录
        NbchatSessionActionLogPO logPO = NbchatSessionActionLogPO.builder()
                .tenantCode(reqBO.getTenantCode())
                .shareId(reqBO.getSessionShareId())
                .userId(reqBO.getUserId())
                .actionType(StatusEnum.SHARE_SAVE.getValue())
                .actionTime(new Date())
                .build();
        actionService.recording(logPO);
        return BaseRspUtils.createSuccessRsp(null);
    }
}
