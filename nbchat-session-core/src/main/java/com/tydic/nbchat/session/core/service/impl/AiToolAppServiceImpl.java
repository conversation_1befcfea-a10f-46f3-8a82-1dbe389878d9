package com.tydic.nbchat.session.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.session.api.AiToolAppApi;
import com.tydic.nbchat.session.api.bo.AiToolAppBO;
import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.mapper.AiToolAppMapper;
import com.tydic.nbchat.session.mapper.po.AiToolApp;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class AiToolAppServiceImpl implements AiToolAppApi {

    @Resource
    AiToolAppMapper aiToolAppMapper;

    @Override
    public Rsp createApp(AiToolAppBO request) {
        log.info("创建app：{}",request);
        AiToolApp po = new AiToolApp();
        BeanUtils.copyProperties(request,po);
        if (StringUtils.isNotEmpty(request.getAppId())) {
            aiToolAppMapper.update(po);
        } else {
            po.setAppId(IdWorker.nextAutoIdStr());
            aiToolAppMapper.insertSelective(po);
        }
        return BaseRspUtils.createSuccessRsp(po.getAppId());
    }

    @Override
    public RspList getAppRecord(AiToolAppBO request) {
        log.info("查询app记录列表：{}",request);
        AiToolApp po = new AiToolApp();
        BeanUtils.copyProperties(request,po);
        Page<AiToolApp> res = PageHelper.startPage(request.getPage(), request.getLimit());
        aiToolAppMapper.selectAll(po);
        return BaseRspUtils.createSuccessRspList(res.getResult(),res.getTotal());
    }

    @Override
    public Rsp getAppInfo(AiToolAppBO request) {
        log.info("查询app记录：{}",request);
        AiToolApp record = aiToolAppMapper.queryById(request.getAppId());
        if(ObjectUtils.isEmpty(record)) {
            return BaseRspUtils.createErrorRsp("app配置不存在");
        }
        AiToolAppBO bo = new AiToolAppBO();
        BeanUtils.copyProperties(record,bo);
        return BaseRspUtils.createSuccessRsp(bo);
    }

}
