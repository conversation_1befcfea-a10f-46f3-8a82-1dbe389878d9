package com.tydic.nicc.dc.base.bo;

import com.tydic.nicc.dc.base.bo.constants.DcBaseConstants;
import java.io.Serializable;


/**
 *  封装服务返回结果
 */
public class Rsp<T> implements Serializable {

    //业务返回编码
    private String rspCode;

    //业务返回描述
    private String rspDesc;

    //服务返回数据
    private T data = null;

    /**
     * 判断是否成功
     * @return
     */
    public boolean isSuccess(){
        if(DcBaseConstants.RSP_SUCCESS.equals(this.rspCode)){
            return true;
        }
        return false;
    }

    public Rsp() {
    }

    public Rsp(String rspCode, String rspDesc) {
        this.rspCode = DcBaseConstants.RSP_SUCCESS;
        this.rspDesc = rspDesc;
    }

    public String getRspCode() {
        return this.rspCode;
    }

    public void setRspCode(String rspCode) {
        this.rspCode = rspCode;
    }

    public String getRspDesc() {
        return this.rspDesc;
    }

    public void setRspDesc(String rspDesc) {
        this.rspDesc = rspDesc;
    }


    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Rsp{rspCode='" + this.rspCode + '\'' + ", rspDesc='" + this.rspDesc + '\''  + ", data=" + this.data + '}';
    }
}