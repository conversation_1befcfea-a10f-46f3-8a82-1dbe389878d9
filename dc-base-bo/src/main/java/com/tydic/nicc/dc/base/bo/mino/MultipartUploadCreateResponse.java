package com.tydic.nicc.dc.base.bo.mino;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MultipartUploadCreateResponse implements Serializable {
    private List<UploadCreateItem> chunks;
    private String uploadId;
    private String accessUrlPrefix;
    private String fileId;
    private String objectName;
    private String fileType;
    private String fileName;
    private String accessUrl;
    @Data
    public static class UploadCreateItem {
        private Integer partNumber;
        private String uploadUrl;
    }
}
