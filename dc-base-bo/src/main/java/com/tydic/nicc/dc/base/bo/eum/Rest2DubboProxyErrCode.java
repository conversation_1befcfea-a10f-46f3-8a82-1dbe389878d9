package com.tydic.nicc.dc.base.bo.eum;

/**
 * @Classname Rest2DubboProxyErrCode
 * @Description Rest2DubboProxyErrCode
 * @Date 2021/5/14 4:29 下午
 * @Created by kangkang
 */
public enum Rest2DubboProxyErrCode {

    PARAM_ERROR(100, "请求参数异常"),
    REQUEST_METHOD_ERROR(101, "请求方法不支持"),
    UNKNOW_DUBBO_SERVICE(102, "找不到dubbo服务"),
    RPC_ERROR(102, "找不到dubbo服务");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private Rest2DubboProxyErrCode(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (Rest2DubboProxyErrCode field : Rest2DubboProxyErrCode.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
