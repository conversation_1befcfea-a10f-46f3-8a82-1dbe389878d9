package com.tydic.nicc.dc.base.bo.eum;

public enum JobExecStatusEnum {


    SUCCESS(1, "执行成功"), ERROR(0, "执行失败");

    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    private JobExecStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(String value){
        for (JobExecStatusEnum field : JobExecStatusEnum.values()){
            if(field.value.equals(value)){
                return field.desc;
            }
        }
        return "";
    }

}
