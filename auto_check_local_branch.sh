#!/bin/bash

## 自动切换本地分支

WORK_HOME=`pwd`

SOURCE=$1

echo "nicc-common 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-common/
git checkout ${SOURCE}
git pull

echo "nicc-im 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-im/
git checkout ${SOURCE}
git pull

echo "nicc-event-center 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-event-center/
git checkout ${SOURCE}
git pull

echo "nicc-user-center 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-user-center/
git checkout ${SOURCE}
git pull

echo "nicc-session 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-session/
git checkout ${SOURCE}
git pull

echo "nicc-csm 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-csm/
git checkout ${SOURCE}
git pull

echo "nicc-opdata 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-op-data/
git checkout ${SOURCE}
git pull


