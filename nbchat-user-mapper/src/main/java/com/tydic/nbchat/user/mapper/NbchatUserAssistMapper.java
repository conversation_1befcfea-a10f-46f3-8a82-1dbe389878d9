package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserAssist;

import java.util.List;

import com.tydic.nbchat.user.mapper.po.NbchatUserAssistResult;
import org.apache.ibatis.annotations.Param;

public interface NbchatUserAssistMapper {
    /**
     * 插入助力信息
     * @param record
     * @return
     */
    int insert(NbchatUserAssist record);
    /**
     * 查询助力信息
     */
    List<NbchatUserAssistResult> selectUserAssistInfo(@Param("userId") String userId,
                                                      @Param("tenantCode") String tenantCode);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(NbchatUserAssist record);

    /**
     * 查询助力id
     * @param assistId
     * @return
     */
    NbchatUserAssist selectByPrimaryKey(String assistId);
    /**
     * 根据助力id，用户id，租户编码查询助力信息
     */
    NbchatUserAssist selectById(@Param("assistId") String assistId,
                              @Param("userId") String userId,
                              @Param("tenantCode") String tenantCode);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(NbchatUserAssist record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(NbchatUserAssist record);

    List<NbchatUserAssist> selectAll();

    int updateBatchSelective(@Param("list") List<NbchatUserAssist> list);

    int batchInsert(@Param("list") List<NbchatUserAssist> list);

    int deleteByPrimaryKeyIn(List<String> list);
}