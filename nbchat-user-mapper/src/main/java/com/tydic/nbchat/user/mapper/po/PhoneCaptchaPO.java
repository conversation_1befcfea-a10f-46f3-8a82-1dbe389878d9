package com.tydic.nbchat.user.mapper.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 手机验证码表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 16:01:21
 */
@Data
public class PhoneCaptchaPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 编号ID
	 */
	private String id;
	/**
	 * 手机号
	 */
	private String phone;
	/**
	 * 验证码
	 */
	private String code;
	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
	private Date createdTime;
	/**
	 * 过期时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
	private Date expiredTime;
	/**
	 * 是否删除;1表示删除0表示未删除
	 */
	private Boolean isDeleted;

}
