package com.tydic.nbchat.user.mapper.po;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 16:01:22
 */
@Data
public class WxUserPO implements Serializable {
	private static final long serialVersionUID = 1L;

	private String channel; //微信渠道 1迪问 2课件帮

	/**
	 * 用户ID
	 */
	private String userId;
	/**
	 * 租户代码
	 */
	private String tenantCode;
	/**
	 * 微信openid
	 */
	private String openId;
	/**
	 * 用户统一标识
	 */
	private String unionId;
	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 手机号
	 */
	private String phone;
	/**
	 * 微信昵称
	 */
	private String nickName;
	/**
	 * 微信头像链接
	 */
	private String avatar;
	/**
	 * 性别
	 */
	private String gender;
	/**
	 * 状态;1表示正常0表示注销2表示封号
	 */
	private Integer status;
	/**
	 * 创建人
	 */
	private String createdBy;
	/**
	 * 创建时间
	 */
	private Date createdTime;
	/**
	 * 更新人
	 */
	private String updatedBy;
	/**
	 * 更新时间
	 */
	private Date updatedTime;
	/**
	 * 是否删除;1表示删除0表示未删除
	 */
	private Boolean isDeleted;

}
