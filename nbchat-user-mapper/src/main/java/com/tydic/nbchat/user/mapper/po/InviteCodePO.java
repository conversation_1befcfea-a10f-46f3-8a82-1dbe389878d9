package com.tydic.nbchat.user.mapper.po;


import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/03/30
 * @email <EMAIL>
 * @description 邀请码
 */
@Data
public class InviteCodePO {
    /**
     * 用户ID
     */
    private String id;
    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedTime;
    /**
     * 是否删除;1表示删除0表示未删除
     */
    private Boolean isDeleted;

    private Date expTime;
}
