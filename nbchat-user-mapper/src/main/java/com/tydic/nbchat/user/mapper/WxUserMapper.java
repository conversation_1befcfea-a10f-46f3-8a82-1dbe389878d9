package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nbchat.user.mapper.po.WxUserPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信用户表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 16:01:22
 */
@Mapper
public interface WxUserMapper{

    /**
     * 微信用户注册
     * @param @param wxUserPO wx用户订单
     * @return @return int
     */
    int insertSelective(WxUserPO wxUserPO);

    /**
     * 通过openId获取用户信息
     * @param @param openId 开放id
     * @return @return {@link UserPO }
     */
    WxUserPO selectByOpenid(String openId);

    /**
     * 通过userId获取公众号用户openId
     * @param userId
     * @return
     */
    WxUserPO queryOAByUserId(String userId);
    int updateByOpenid(WxUserPO wxUserPO);

    /**
     * 更新微信用户信息
     * @param @param wxUserPO wx用户订单
     * @return
     */
    int update(WxUserPO wxUserPO);

    /**
     * 通过unionId获取用户信息
     * @param @param unionId 联盟id
     * @return @return {@link WxUserPO }
     */
    List<WxUserPO> selectByUnionId(String unionId);

}
