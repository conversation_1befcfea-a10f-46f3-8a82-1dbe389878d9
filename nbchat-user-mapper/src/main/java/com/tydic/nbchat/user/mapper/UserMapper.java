package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nbchat.user.mapper.po.UserSelectCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 16:01:22
 */
@Mapper
public interface UserMapper {
    /**
     * 删除用户
     * @param userId
     * @return
     */
    int deleteById(String userId);

    /**
     * 通过手机号查询用户信息
     * @param @param phone 电话
     * @return @return {@link UserPO }
     */
    UserPO findUserByMobile(@Param("phone") String phone);

    /**
     * 统计用户登录次数,不包括当天注册的用户
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    int selectLoginCount(@Param("userId") String userId,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);
    /**
     * 用户注册
     * @param @param userPO 用户订单
     * @return @return boolean
     */
    int registerUser(UserPO userPO);

    /**
     * 通过用户ID获取用户信息
     * @param @param userId 用户id
     * @return @return {@link UserPO }
     */
    UserPO findUserByUserId(@Param("userId") String userId);

    /**
     * 查询所有用户
     * @param
     * @return
     */
    List<UserPO> selectByCondition(UserSelectCondition condition);

    /**
     * 更新用户信息
     * @param @param userPO 用户订单
     * @return @return int
     */
    int updateById(UserPO userPO);

    /**
     * 通过用户名密码查询用户信息
     * @param @param   userName 用户名
     * @param password 密码
     * @return @return {@link UserPO }
     */
    UserPO findUserByUserNamePassword(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户名查询用户信息
     * @param @param userName 用户名
     * @return @return {@link UserPO }
     */
    UserPO findUserByUserName(@Param("userName") String userName);

    /**
     * 合并账号
     * @param @param             primaryAccountId 主要帐户id
     * @param secondaryAccountId 辅助帐户id
     * @param tenantCode         租户代码
     * @return
     */
    void mergeAccounts(@Param("vUId") String primaryAccountId,@Param("pUId") String secondaryAccountId,@Param("pTenantCode") String tenantCode);

    /**
     * 通过用户id查询租户代码
     * @param @param userId 用户id
     * @return @return {@link List }<{@link String }>
     */
    List<String> selectTenantCodeByUserId(String userId);

    List<String> getTenantCodeList(String userId);

    UserPO selectByUnionId(String unionId);

    List<UserPO> selectByUserIdList(@Param("userIdList") List<String> userIdList);
}
