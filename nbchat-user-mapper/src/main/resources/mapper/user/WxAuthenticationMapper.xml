<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.WxAuthenticationMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.tydic.nbchat.user.mapper.po.WxAuthenticationPO" id="wxAuth">
        <result property="openId" column="open_id"/>
        <result property="accessToken" column="access_token"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="expiredIn" column="expired_in"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <insert id="saveAccessToken" parameterType="com.tydic.nbchat.user.mapper.po.WxAuthenticationPO">
        INSERT INTO nbchat_wx_authentication
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="accessToken != null">access_token,</if>
            <if test="refreshToken != null">refresh_token,</if>
            <if test="expiredIn != null">expired_in,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="isDeleted != null">is_deleted</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="accessToken != null">#{accessToken},</if>
            <if test="refreshToken != null">#{refreshToken},</if>
            <if test="expiredIn != null">#{expiredIn},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="isDeleted != null">#{isDeleted}</if>
        </trim>
    </insert>


</mapper>