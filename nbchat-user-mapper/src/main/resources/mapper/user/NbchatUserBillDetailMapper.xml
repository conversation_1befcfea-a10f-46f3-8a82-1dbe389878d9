<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserBillDetailMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="trade_id" property="tradeId" jdbcType="VARCHAR" />
    <result column="account_id" property="accountId" jdbcType="BIGINT" />
    <result column="score" property="score" jdbcType="INTEGER" />
    <result column="bill_desc" property="billDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, tenant_code, user_id, trade_id, account_id, score, bill_desc, create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from nbchat_user_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from nbchat_user_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail" >
    insert into nbchat_user_bill_detail (id, tenant_code, user_id, 
      trade_id, account_id, score, 
      bill_desc, create_time)
    values (#{id,jdbcType=BIGINT}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{tradeId,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, #{score,jdbcType=INTEGER}, 
      #{billDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail" >
    insert into nbchat_user_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tenantCode != null" >
        tenant_code,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="tradeId != null" >
        trade_id,
      </if>
      <if test="accountId != null" >
        account_id,
      </if>
      <if test="score != null" >
        score,
      </if>
      <if test="billDesc != null" >
        bill_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null" >
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null" >
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="score != null" >
        #{score,jdbcType=INTEGER},
      </if>
      <if test="billDesc != null" >
        #{billDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail" >
    update nbchat_user_bill_detail
    <set >
      <if test="tenantCode != null" >
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null" >
        trade_id = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null" >
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="score != null" >
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="billDesc != null" >
        bill_desc = #{billDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail" >
    update nbchat_user_bill_detail
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      trade_id = #{tradeId,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=BIGINT},
      score = #{score,jdbcType=INTEGER},
      bill_desc = #{billDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail">
    insert into nbchat_user_bill_detail (id, tenant_code, user_id,
      trade_id, account_id, score,
      bill_desc, create_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
      #{item.tradeId,jdbcType=VARCHAR}, #{item.accountId,jdbcType=BIGINT}, #{item.score,jdbcType=INTEGER},
      #{item.billDesc,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectByTradeId" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_user_bill_detail
    where trade_id = #{tradeId,jdbcType=VARCHAR}
  </select>

</mapper>