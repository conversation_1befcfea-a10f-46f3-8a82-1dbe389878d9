<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserAssistMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
    <!--@mbg.generated-->
    <!--@Table nbchat_user_assist-->
    <id column="assist_id" jdbcType="VARCHAR" property="assistId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="assist_type" jdbcType="CHAR" property="assistType" />
    <result column="need_num" jdbcType="INTEGER" property="needNum" />
    <result column="busi_type" jdbcType="VARCHAR" property="busiType" />
    <result column="assist_status" jdbcType="CHAR" property="assistStatus" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    assist_id, tenant_code, user_id, assist_type, need_num, busi_type, assist_status, 
    start_time, end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from nbchat_user_assist
    where assist_id = #{assistId,jdbcType=VARCHAR}
  </select>
  <!--查询用户助力进度-->
  <select id="selectById" resultType="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
          select
          <include refid="Base_Column_List" />
          from nbchat_user_assist
          where tenant_code = #{tenantCode,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=VARCHAR}
          and assist_id = #{assistId,jdbcType=VARCHAR}
    </select>
  <!--查询助力信息-->
  <select id="selectUserAssistInfo" resultType="com.tydic.nbchat.user.mapper.po.NbchatUserAssistResult">
    SELECT
      a.user_id AS userId,
      c.user_reality_name AS userName,
      a.tenant_code AS tenantCode,
      d.assist_time AS assistTime,
      a.phone AS phone,
      c.avatar AS avatar
    FROM
      nbchat_user_assist_detail d
        JOIN
      nbchat_user a ON d.user_id = a.user_id
        LEFT JOIN
      nbchat_user_assist b ON d.assist_id = b.assist_id
        LEFT JOIN
      nbchat_sys_user_tenant c ON d.user_id = c.user_id
    WHERE
      b.user_id = #{userId,jdbcType=VARCHAR}
      AND b.tenant_code = #{tenantCode,jdbcType=VARCHAR}
    ORDER BY
      d.assist_time DESC
  </select>
  <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
    <!--@mbg.generated-->
    insert into nbchat_user_assist (assist_id, tenant_code, user_id, 
      assist_type, need_num, busi_type, 
      assist_status, start_time, end_time
      )
    values (#{assistId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{assistType,jdbcType=CHAR}, #{needNum,jdbcType=INTEGER}, #{busiType,jdbcType=VARCHAR}, 
      #{assistStatus,jdbcType=CHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
    <!--@mbg.generated-->
    insert into nbchat_user_assist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assistId != null">
        assist_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="assistType != null">
        assist_type,
      </if>
      <if test="needNum != null">
        need_num,
      </if>
      <if test="busiType != null">
        busi_type,
      </if>
      <if test="assistStatus != null">
        assist_status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assistId != null">
        #{assistId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="assistType != null">
        #{assistType,jdbcType=CHAR},
      </if>
      <if test="needNum != null">
        #{needNum,jdbcType=INTEGER},
      </if>
      <if test="busiType != null">
        #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="assistStatus != null">
        #{assistStatus,jdbcType=CHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
    <!--@mbg.generated-->
    update nbchat_user_assist
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="assistType != null">
        assist_type = #{assistType,jdbcType=CHAR},
      </if>
      <if test="needNum != null">
        need_num = #{needNum,jdbcType=INTEGER},
      </if>
      <if test="busiType != null">
        busi_type = #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="assistStatus != null">
        assist_status = #{assistStatus,jdbcType=CHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where assist_id = #{assistId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserAssist">
    <!--@mbg.generated-->
    update nbchat_user_assist
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      assist_type = #{assistType,jdbcType=CHAR},
      need_num = #{needNum,jdbcType=INTEGER},
      busi_type = #{busiType,jdbcType=VARCHAR},
      assist_status = #{assistStatus,jdbcType=CHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where assist_id = #{assistId,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from nbchat_user_assist
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update nbchat_user_assist
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="assist_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.assistType != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.assistType,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="need_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.needNum != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.needNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="busi_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busiType != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.busiType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="assist_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.assistStatus != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.assistStatus,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.startTime != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="end_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.endTime != null">
            when assist_id = #{item.assistId,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where assist_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.assistId,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into nbchat_user_assist
    (assist_id, tenant_code, user_id, assist_type, need_num, busi_type, assist_status, 
      start_time, end_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.assistId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.assistType,jdbcType=CHAR}, #{item.needNum,jdbcType=INTEGER}, #{item.busiType,jdbcType=VARCHAR}, 
        #{item.assistStatus,jdbcType=CHAR}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from nbchat_user_assist where assist_id in 
    <foreach close=")" collection="list" item="assistId" open="(" separator=", ">
      #{assistId,jdbcType=VARCHAR}
    </foreach>
  </delete>
</mapper>