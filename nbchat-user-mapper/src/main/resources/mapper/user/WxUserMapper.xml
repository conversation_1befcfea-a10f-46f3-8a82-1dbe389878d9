<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.WxUserMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.tydic.nbchat.user.mapper.po.WxUserPO" id="wxUserMap">
        <result property="userId" column="user_id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="appId" column="app_id"/>
        <result property="phone" column="phone"/>
        <result property="nickName" column="nick_name"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="channel" column="channel"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id
        ,tenant_code,open_id,union_id,phone,app_id,nick_name,avatar,gender,created_by,created_time,updated_by,
        updated_time,is_deleted,channel
    </sql>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.WxUserPO">
        INSERT INTO nbchat_wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="phone != null">phone,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="isDeleted != null">is_deleted</if>
            <if test="channel != null and channel != ''">
                channel
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="isDeleted != null">#{isDeleted}</if>
            <if test="channel != null and channel != ''">
                #{channel}
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.tydic.nbchat.user.mapper.po.WxUserPO">
        UPDATE nbchat_wx_user
        <set>
            <if test="tenantCode != null and tenantCode !='' ">tenant_code = #{tenantCode},</if>
            <if test="unionId != null and unionId !=''">union_id = #{unionId},</if>
            <if test="appId != null and appId !=''">app_id = #{appId},</if>
            <if test="phone != null and phone !=''">phone = #{phone},</if>
            <if test="nickName != null and nickName !='' ">nick_name = #{nickName},</if>
            <if test="avatar != null and avatar !=''">avatar = #{avatar},</if>
            <if test="gender != null and gender !=''">gender = #{gender},</if>
            <if test="status != null and status != '' ">status = #{status},</if>
            <if test="updatedBy != null and updatedBy !=''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="channel != null and channel != ''">channel = #{channel},</if>
        </set>
        WHERE user_id = #{userId}
    </update>

    <select id="selectByOpenid" resultMap="wxUserMap">
        select
            <include refid="Base_Column_List"/>
        from nbchat_wx_user
        where open_id = #{openId}
          AND is_deleted = 0
    </select>

    <select id="queryOAByUserId" resultMap="wxUserMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_wx_user
        where
            user_id = #{userId} and channel = '2' AND is_deleted = 0
        limit 1
    </select>

    <update id="updateByOpenid" parameterType="com.tydic.nbchat.user.mapper.po.WxUserPO">
        UPDATE nbchat_wx_user
        <set>
            <if test="tenantCode != null and tenantCode !='' ">tenant_code = #{tenantCode},</if>
            <if test="unionId != null and unionId !=''">union_id = #{unionId},</if>
            <if test="appId != null and appId !=''">app_id = #{appId},</if>
            <if test="phone != null and phone !=''">phone = #{phone},</if>
            <if test="nickName != null and nickName !='' ">nick_name = #{nickName},</if>
            <if test="avatar != null and avatar !=''">avatar = #{avatar},</if>
            <if test="gender != null and gender !=''">gender = #{gender},</if>
            <if test="status != null and status != '' ">status = #{status},</if>
            <if test="updatedBy != null and updatedBy !=''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="channel != null and channel != ''">channel = #{channel},</if>
        </set>
        WHERE open_id = #{openId}
    </update>

    <select id="selectByUnionId" resultMap="wxUserMap">
        select user_id,
               tenant_code,
               open_id,
               union_id,
               phone,
               app_id,
               nick_name,
               avatar,
               gender,
               created_by,
               created_time,
               updated_by,
               updated_time,
               is_deleted
        from nbchat_wx_user
        where union_id = #{unionId}
          AND is_deleted = 0
    </select>

</mapper>