<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.CommonMapper">


    <select id="queryCreationCount" resultType="Integer">
        select count(1)
        from tdh_creation_task
        where user_id = #{userId} and tenant_code = '00000000'
          and (task_state = '1' or task_state = '0')
    </select>

    <select id="queryCustomizeRecord" resultType="java.util.Map">
        select
            start_time,end_time
        from tdh_customize_record
        where order_no = #{orderNo} and is_valid = 1
    </select>

    <update id="updateOrderStatus" >
        update tdh_customize_record
        set order_status = #{orderStatus}, customize_status = #{customizeStatus}
        where order_no = #{orderNo}
    </update>
    
    <select id="queryPayType" resultType="String">
        select pay_type
        from nbchat_sys_tenant
        where tenant_code = #{tenantCode}
    </select>

</mapper>