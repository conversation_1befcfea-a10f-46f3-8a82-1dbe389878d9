<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserBalanceMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.user.mapper.po.NbchatUserBalance" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="score" property="score" jdbcType="INTEGER" />
    <result column="pp_score" property="ppScore" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, tenant_code, user_id, score, pp_score, create_time, update_time, status
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from nbchat_user_balance
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from nbchat_user_balance
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBalance" >
    insert into nbchat_user_balance (id, tenant_code, user_id, 
      score, pp_score, create_time, 
      update_time, status)
    values (#{id,jdbcType=INTEGER}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{score,jdbcType=INTEGER}, #{ppScore,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBalance" >
    insert into nbchat_user_balance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tenantCode != null" >
        tenant_code,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="score != null" >
        score,
      </if>
      <if test="ppScore != null" >
        pp_score,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="status != null" >
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="score != null" >
        #{score,jdbcType=INTEGER},
      </if>
      <if test="ppScore != null" >
        #{ppScore,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserBalance" >
    update nbchat_user_balance
    <set >
      <if test="score != null" >
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_user_balance
    where user_id = #{userId,jdbcType=VARCHAR}
      and tenant_code = #{tenantCode,jdbcType=VARCHAR} limit 1
  </select>

  <select id="selectByTenantCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_user_balance
    where tenant_code = #{tenantCode,jdbcType=VARCHAR}
      and user_id = #{tenantCode,jdbcType=VARCHAR} limit 1
  </select>

  <update id="updateBalance">
      update nbchat_user_balance
      set score = (select ifnull(sum(score), 0)
         from nbchat_user_score_detail
         where user_id = #{userId,jdbcType=VARCHAR}
           and tenant_code = #{tenantCode,jdbcType=VARCHAR}
           and score_status = '1'), update_time = NOW()
      where user_id = #{userId,jdbcType=VARCHAR}
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
  </update>
</mapper>