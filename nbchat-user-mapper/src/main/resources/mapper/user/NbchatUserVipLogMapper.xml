<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserVipLogMapper">

    <resultMap type="com.tydic.nbchat.user.mapper.po.NbchatUserVipLog" id="NbchatUserVipLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="vipType" column="vip_type" jdbcType="VARCHAR"/>
        <result property="validDays" column="valid_days" jdbcType="INTEGER"/>
        <result property="amount" column="amount" jdbcType="INTEGER"/>
        <result property="totalDou" column="total_dou" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isRefund" column="is_refund" jdbcType="VARCHAR"/>
        <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, tenant_code, user_id, vip_type, valid_days, amount, total_dou, start_time, end_time, create_time, is_refund, refund_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatUserVipLogMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_user_vip_log
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatUserVipLogMap" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVipLog">
        select
          <include refid="Base_Column_List" />
        from nbchat_user_vip_log
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="vipType != null and vipType != ''">
                and vip_type = #{vipType}
            </if>
            <if test="validDays != null">
                and valid_days = #{validDays}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="totalDou != null">
                and total_dou = #{totalDou}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="isRefund != null and isRefund != ''">
                and is_refund = #{isRefund}
            </if>
            <if test="refundTime != null">
                and refund_time = #{refundTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserVipLog">
        insert into nbchat_user_vip_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type,
            </if>
            <if test="validDays != null">
                valid_days,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="totalDou != null">
                total_dou,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isRefund != null and isRefund != ''">
                is_refund,
            </if>
            <if test="refundTime != null">
                refund_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="vipType != null and vipType != ''">
                #{vipType},
            </if>
            <if test="validDays != null">
                #{validDays},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="totalDou != null">
                #{totalDou},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="isRefund != null and isRefund != ''">
                #{isRefund},
            </if>
            <if test="refundTime != null">
                #{refundTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_user_vip_log(tenant_codeuser_idvip_typevalid_daysamounttotal_doustart_timeend_timecreate_timeis_refundrefund_time)
        values (#{tenantCode}#{userId}#{vipType}#{validDays}#{amount}#{totalDou}#{startTime}#{endTime}#{createTime}#{isRefund}#{refundTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_user_vip_log
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type = #{vipType},
            </if>
            <if test="validDays != null">
                valid_days = #{validDays},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="totalDou != null">
                total_dou = #{totalDou},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="isRefund != null and isRefund != ''">
                is_refund = #{isRefund},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_user_vip_log where id = #{id}
    </delete>

</mapper>

