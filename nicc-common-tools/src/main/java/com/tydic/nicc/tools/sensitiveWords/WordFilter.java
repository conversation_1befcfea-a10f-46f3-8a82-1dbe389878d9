package com.tydic.nicc.tools.sensitiveWords;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感词过滤器
 *
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
public class WordFilter {

    private static final Logger logger = LoggerFactory.getLogger(WordFilter.class);

    /**
     * 敏感词表
     */
    private final Map wordMap;
    private final Set<String> blackRegSet;

    /**
     * 构造函数
     */
    public WordFilter(WordContext context) {
        this.wordMap = context.getWordMap();
        this.blackRegSet = context.getBlackRegSet();
    }

    /**
     * 替换敏感词
     *
     * @param text 输入文本
     */
    public String replace(final String text) {
        return replace(text, 0, '*');
    }

    /**
     * 替换敏感词
     *
     * @param text   输入文本
     * @param symbol 替换符号
     */
    public String replace(final String text, final char symbol) {
        return replace(text, 0, symbol);
    }

    /**
     * 替换敏感词
     *
     * @param text   输入文本
     * @param skip   文本距离
     * @param symbol 替换符号
     */
    public String replace(final String text, final int skip, final char symbol) {
        char[] charset = text.toCharArray();
        for (int i = 0; i < charset.length; i++) {
            FlagIndex fi = getFlagIndex(charset, i, skip);
            if (fi.isFlag()) {
                if (!fi.isWhiteWord()) {
                    for (int j : fi.getIndex()) {
                        charset[j] = symbol;
                    }
                } else {
                    i += fi.getIndex().size() - 1;
                }
            }
        }
        String content = new String(charset);
        if (!blackRegSet.isEmpty()) {
            return replaceByReg(content, symbol);
        }
        return content;
    }

    /**
     * 替换敏感词
     *
     * @param text
     * @param symbol
     * @return
     */
    private String replaceByReg(final String text, final char symbol) {
        String content = text;
        try {
            for (String reg : blackRegSet) {
                Matcher matcher = Pattern.compile(reg, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(content);
                if (matcher.find()) {
                    content = matcher.replaceAll(String.valueOf(symbol) + symbol);
                }
            }
        } catch (Exception e){
            logger.error("正则匹配异常,请检查表达式:{}",blackRegSet,e);
        }
        return content;
    }


    /**
     * 是否包含敏感词
     *
     * @param text
     * @return
     */
    private boolean includeByReg(final String text) {
        String content = text;
        try {
            for (String reg : blackRegSet) {
                Matcher matcher = Pattern.compile(reg, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(content);
                if (matcher.find()) {
                    return true;
                }
            }
        } catch (Exception e){
            logger.error("正则匹配异常,请检查表达式:{}",blackRegSet,e);
        }
        return false;
    }

    /**
     * 计算匹配次数
     *
     * @param text
     * @return
     */
    private int countByReg(final String text) {
        int count = 0;
        try {
            for (String reg : blackRegSet) {
                Matcher matcher = Pattern.compile(reg, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(text);
                if (matcher.find()) {
                    count++;
                }
            }
        } catch (Exception e){
            logger.error("正则匹配异常,请检查表达式:{}",blackRegSet,e);
        }
        return count;
    }

    private List<String> listByReg(final String text) {
        List<String> groups = new ArrayList<>();
        try {
            for (String reg : blackRegSet) {
                Matcher matcher = Pattern.compile(reg, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE).matcher(text);
                if (matcher.find()) {
                    groups.add(matcher.group());
                }
            }
        } catch (Exception e){
            logger.error("正则匹配异常,请检查表达式:{}",blackRegSet,e);
        }
        return groups;
    }

    /**
     * 是否包含敏感词
     *
     * @param text 输入文本
     */
    public boolean include(final String text) {
        return include(text, 0);
    }

    /**
     * 是否包含敏感词
     *
     * @param text 输入文本
     * @param skip 文本距离
     */
    public boolean include(final String text, final int skip) {
        boolean include = false;
        char[] charset = text.toCharArray();
        for (int i = 0; i < charset.length; i++) {
            FlagIndex fi = getFlagIndex(charset, i, skip);
            if (fi.isFlag()) {
                if (fi.isWhiteWord()) {
                    i += fi.getIndex().size() - 1;
                } else {
                    include = true;
                    break;
                }
            }
        }
        if (!include && !blackRegSet.isEmpty()) {
            return includeByReg(text);
        }
        return include;
    }

    /**
     * 获取敏感词数量
     *
     * @param text 输入文本
     */
    public int wordCount(final String text) {
        return wordCount(text, 0);
    }

    /**
     * 获取敏感词数量
     *
     * @param text 输入文本
     * @param skip 文本距离
     */
    public int wordCount(final String text, final int skip) {
        int count = 0;
        char[] charset = text.toCharArray();
        for (int i = 0; i < charset.length; i++) {
            FlagIndex fi = getFlagIndex(charset, i, skip);
            if (fi.isFlag()) {
                if (fi.isWhiteWord()) {
                    i += fi.getIndex().size() - 1;
                } else {
                    count++;
                }
            }
        }
        if (count == 0 && !blackRegSet.isEmpty()) {
            return count + countByReg(text);
        }
        return count;
    }

    /**
     * 获取敏感词列表
     *
     * @param text 输入文本
     */
    public List<String> wordList(final String text) {
        return wordList(text, 0);
    }

    /**
     * 获取敏感词列表
     *
     * @param text 输入文本
     * @param skip 文本距离
     */
    public List<String> wordList(final String text, final int skip) {
        List<String> wordList = new ArrayList<>();
        char[] charset = text.toCharArray();
        for (int i = 0; i < charset.length; i++) {
            FlagIndex fi = getFlagIndex(charset, i, skip);
            if (fi.isFlag()) {
                if (fi.isWhiteWord()) {
                    i += fi.getIndex().size() - 1;
                } else {
                    StringBuilder builder = new StringBuilder();
                    for (int j : fi.getIndex()) {
                        char word = text.charAt(j);
                        builder.append(word);
                    }
                    wordList.add(builder.toString());
                }
            }
        }
        if (!blackRegSet.isEmpty()) {
            wordList.addAll(listByReg(text));
        }
        return wordList;
    }

    /**
     * 获取标记索引
     *
     * @param charset 输入文本
     * @param begin   检测起始
     * @param skip    文本距离
     */
    private FlagIndex getFlagIndex(final char[] charset, final int begin, final int skip) {
        FlagIndex fi = new FlagIndex();

        Map current = wordMap;
        boolean flag = false;
        int count = 0;
        List<Integer> index = new ArrayList<>();
        for (int i = begin; i < charset.length; i++) {
            char word = charset[i];
            Map mapTree = (Map) current.get(word);
            if (count > skip || (i == begin && Objects.isNull(mapTree))) {
                break;
            }
            if (Objects.nonNull(mapTree)) {
                current = mapTree;
                count = 0;
                index.add(i);
            } else {
                count++;
                if (flag && count > skip) {
                    break;
                }
            }
            if ("1".equals(current.get("isEnd"))) {
                flag = true;
            }
            if ("1".equals(current.get("isWhiteWord"))) {
                fi.setWhiteWord(true);
                break;
            }
        }

        fi.setFlag(flag);
        fi.setIndex(index);

        return fi;
    }
}
