package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.NbchatSessionSharePO;

import java.util.List;

/**
 * (NbchatSessionShare)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:38
 */
public interface NbchatSessionShareMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param  主键
     * @return 实例对象
     */
    NbchatSessionSharePO queryById( String sessionShareId);
    NbchatSessionSharePO queryByCondition(NbchatSessionSharePO po);

    List<NbchatSessionSharePO> selectAll(NbchatSessionSharePO nbchatSessionSharePO);

    /**
     * 新增数据
     *
     * @param nbchatSessionSharePO 实例对象
     * @return 影响行数
     */
    int insert(NbchatSessionSharePO nbchatSessionSharePO);


    int insertSelective(NbchatSessionSharePO nbchatSessionSharePO);

      /**
     * 修改数据
     *
     * @param nbchatSessionSharePO 实例对象
     * @return 影响行数
     */
    int update(NbchatSessionSharePO nbchatSessionSharePO);

    /**
     * 通过主键删除数据
     *
     * @param  主键
     * @return 影响行数
     */
    int deleteById( );

}

