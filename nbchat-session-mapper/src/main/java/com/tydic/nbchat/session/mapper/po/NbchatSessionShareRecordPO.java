package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSessionShareRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-03-30 15:11:48
 */
@Data
public class NbchatSessionShareRecordPO implements Serializable {
    private static final long serialVersionUID = 551635573518110041L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 分享id
     */
    private String sessionShareId;
    /**
     * 保存分享的用户id
     */
    private String userId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 保存时间
     */
    private Date dateTime;



}

