<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.AiToolAppResMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.AiToolAppRes" id="AiToolAppResMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, app_id, session_id, name, score, create_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="AiToolAppResMap">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app_res
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="AiToolAppResMap" parameterType="com.tydic.nbchat.session.mapper.po.AiToolAppRes">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app_res
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="appId != null and appId != ''">
                and app_id = #{appId}
            </if>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="score != null and score != ''">
                and score = #{score}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="selectAllSession" resultMap="AiToolAppResMap" parameterType="com.tydic.nbchat.session.mapper.po.AiToolAppRes">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app_res
        where app_id = #{appId}
        group by session_id
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.AiToolAppRes">
        insert into ai_tool_app_res
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="score != null and score != ''">
                score,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="appId != null and appId != ''">
                #{appId},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="score != null and score != ''">
                #{score},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ai_tool_app_res(app_idsession_idnamescorecreate_time)
        values (#{appId}#{sessionId}#{name}#{score}#{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ai_tool_app_res
        <set>
            <if test="appId != null and appId != ''">
                app_id = #{appId},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="score != null and score != ''">
                score = #{score},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ai_tool_app_res where id = #{id}
    </delete>

</mapper>

