<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.AiToolAppMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.AiToolApp" id="AiToolAppMap">
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="appDesc" column="app_desc" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="appType" column="app_type" jdbcType="VARCHAR"/>
        <result property="appConfig" column="app_config" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="robotType" column="robot_type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="shareLink" column="share_link" jdbcType="VARCHAR"/>
        <result property="shareState" column="share_state" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
app_id, app_name, app_desc, avatar, app_type, app_config, tenant_code, user_id, robot_type, create_time, update_time, share_link, share_state, is_valid,status</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="AiToolAppMap">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app
        where app_id = #{appId}
    </select>
    
    
    <select id="selectAll" resultMap="AiToolAppMap" parameterType="com.tydic.nbchat.session.mapper.po.AiToolApp">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app
         <where>
            <if test="appId != null and appId != ''">
                and app_id = #{appId}
            </if>
            <if test="appName != null and appName != ''">
                and app_name = #{appName}
            </if>
            <if test="appDesc != null and appDesc != ''">
                and app_desc = #{appDesc}
            </if>
            <if test="avatar != null and avatar != ''">
                and avatar = #{avatar}
            </if>
            <if test="appType != null and appType != ''">
                and app_type = #{appType}
            </if>
            <if test="appConfig != null and appConfig != ''">
                and app_config = #{appConfig}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="robotType != null and robotType != ''">
                and robot_type = #{robotType}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="shareLink != null and shareLink != ''">
                and share_link = #{shareLink}
            </if>
            <if test="shareState != null and shareState != ''">
                and share_state = #{shareState}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
             <if test="status != null and status != ''">
                 and status = #{status}
             </if>
        </where>
        order by create_time desc
    </select>


    <insert id="insertSelective" keyProperty="appId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.AiToolApp">
        insert into ai_tool_app
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="appName != null and appName != ''">
                app_name,
            </if>
            <if test="appDesc != null and appDesc != ''">
                app_desc,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="appType != null and appType != ''">
                app_type,
            </if>
            <if test="appConfig != null and appConfig != ''">
                app_config,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="shareLink != null and shareLink != ''">
                share_link,
            </if>
            <if test="shareState != null and shareState != ''">
                share_state,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="appId != null and appId != ''">
                #{appId},
            </if>
            <if test="appName != null and appName != ''">
                #{appName},
            </if>
            <if test="appDesc != null and appDesc != ''">
                #{appDesc},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar},
            </if>
            <if test="appType != null and appType != ''">
                #{appType},
            </if>
            <if test="appConfig != null and appConfig != ''">
                #{appConfig},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="robotType != null and robotType != ''">
                #{robotType},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="shareLink != null and shareLink != ''">
                #{shareLink},
            </if>
            <if test="shareState != null and shareState != ''">
                #{shareState},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="status != null and status != ''">
                #{status},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="appId" useGeneratedKeys="true">
        insert into ai_tool_app(app_nameapp_descavatarapp_typeapp_configtenant_codeuser_idrobot_typecreate_timeupdate_timeshare_linkshare_stateis_valid)
        values (#{appName}#{appDesc}#{avatar}#{appType}#{appConfig}#{tenantCode}#{userId}#{robotType}#{createTime}#{updateTime}#{shareLink}#{shareState}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ai_tool_app
        <set>
            <if test="appName != null and appName != ''">
                app_name = #{appName},
            </if>
            <if test="appDesc != null and appDesc != ''">
                app_desc = #{appDesc},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="appType != null and appType != ''">
                app_type = #{appType},
            </if>
            <if test="appConfig != null and appConfig != ''">
                app_config = #{appConfig},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type = #{robotType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="shareLink != null and shareLink != ''">
                share_link = #{shareLink},
            </if>
            <if test="shareState != null and shareState != ''">
                share_state = #{shareState},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
        </set>
        where app_id = #{appId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ai_tool_app where app_id = #{appId}
    </delete>

</mapper>

