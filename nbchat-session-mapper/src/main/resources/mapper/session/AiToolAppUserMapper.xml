<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.AiToolAppUserMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.AiToolAppUser" id="AiToolAppUserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, tenant_code, user_id, app_id, name, phone, email, create_time, session_id, is_valid</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="AiToolAppUserMap">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app_user
        where id = #{id}
    </select>

    <select id="selectBySessionId" resultMap="AiToolAppUserMap">
        select
            <include refid="Base_Column_List" />
        from ai_tool_app_user
        where session_id = #{sessionId} limit 1
    </select>
    
    <select id="selectAll" resultMap="AiToolAppUserMap" parameterType="com.tydic.nbchat.session.mapper.po.AiToolAppUser">
        select
          <include refid="Base_Column_List" />
        from ai_tool_app_user
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="appId != null and appId != ''">
                and app_id = #{appId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.AiToolAppUser">
        insert into ai_tool_app_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="appId != null and appId != ''">
                #{appId},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ai_tool_app_user(tenant_codeuser_idapp_idnamephoneemailcreate_timesession_idis_valid)
        values (#{tenantCode}#{userId}#{appId}#{name}#{phone}#{email}#{createTime}#{sessionId}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ai_tool_app_user
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="appId != null and appId != ''">
                app_id = #{appId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ai_tool_app_user where id = #{id}
    </delete>

</mapper>

