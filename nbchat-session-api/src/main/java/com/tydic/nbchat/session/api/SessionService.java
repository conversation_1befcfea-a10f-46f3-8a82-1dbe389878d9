package com.tydic.nbchat.session.api;

import com.tydic.nbchat.session.api.bo.NbchatSessionReqBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionRspBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionShareReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SessionService {

    /**
     * 查询是否已保存会话
     * @param reqBO
     * @return
     */
    Rsp getFromShareSavedSession(NbchatSessionShareReqBO reqBO);

    /**
     * 查询会话列表
     * @param reqBO
     * @return
     */
    RspList<NbchatSessionRspBO> getAllSessions(NbchatSessionReqBO reqBO);

    /**
     * 分页查询会话
     * @param reqBO
     * @return
     */
    RspList<NbchatSessionRspBO> getSessions(NbchatSessionReqBO reqBO);


    /**
     * 更新、新增、删除、收藏会话
     * @param reqBO
     * @return
     */
    Rsp session(NbchatSessionReqBO reqBO);

    /**
     * 分享会话
     * @param reqBO
     * @return
     */
    Rsp shareSession(NbchatSessionShareReqBO reqBO);

    /**
     * 检查是否需要分享码
     * @param reqBO
     * @return
     */
    Rsp checkShareKey(NbchatSessionShareReqBO reqBO);

    /**
     * 查询分享会话信息
     * @param reqBO
     * @return
     */
    Rsp queryShare(NbchatSessionShareReqBO reqBO);


    /**
     * 分享保存到我的
     * @param reqBO
     * @return
     */
    Rsp saveShare(NbchatSessionReqBO reqBO);



}
