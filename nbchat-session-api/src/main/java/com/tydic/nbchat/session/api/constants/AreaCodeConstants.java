package com.tydic.nbchat.session.api.constants;

/**
 * 区号常量类
 * 定义常用的国家和地区区号，避免使用魔法值
 */
public class AreaCodeConstants {
    
    /**
     * 中国大陆区号
     */
    public static final String CHINA_MAINLAND = "86";
    
    /**
     * 中国香港区号
     */
    public static final String CHINA_HONGKONG = "852";
    
    /**
     * 中国澳门区号
     */
    public static final String CHINA_MACAO = "853";
    
    /**
     * 中国台湾区号
     */
    public static final String CHINA_TAIWAN = "886";
    
    /**
     * 美国/加拿大区号
     */
    public static final String USA_CANADA = "1";
    
    /**
     * 英国区号
     */
    public static final String UK = "44";
    
    /**
     * 日本区号
     */
    public static final String JAPAN = "81";
    
    /**
     * 韩国区号
     */
    public static final String KOREA = "82";
    
    /**
     * 区号与国家/地区名称的映射
     */
    public static final java.util.Map<String, String> COUNTRY_NAME_MAP = new java.util.HashMap<>();
    
    static {
        COUNTRY_NAME_MAP.put(CHINA_MAINLAND, "中国大陆");
        COUNTRY_NAME_MAP.put(CHINA_HONGKONG, "中国香港");
        COUNTRY_NAME_MAP.put(CHINA_MACAO, "中国澳门");
        COUNTRY_NAME_MAP.put(CHINA_TAIWAN, "中国台湾");
        COUNTRY_NAME_MAP.put(USA_CANADA, "美国/加拿大");
        COUNTRY_NAME_MAP.put(UK, "英国");
        COUNTRY_NAME_MAP.put(JAPAN, "日本");
        COUNTRY_NAME_MAP.put(KOREA, "韩国");
    }
}
