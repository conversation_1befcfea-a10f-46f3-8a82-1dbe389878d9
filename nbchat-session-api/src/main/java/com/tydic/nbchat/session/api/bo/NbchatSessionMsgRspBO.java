package com.tydic.nbchat.session.api.bo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;


@Data
public class NbchatSessionMsgRspBO implements Serializable {
    private static final long serialVersionUID = 289587688205587833L;
    /**
     * 用户提问id
     */
    private String promptRequestId;
    /**
     * 请求id
     */
    private String requestId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 0 机器人 1 用户
     */
    private String userType;
    /**
     * 是否异常
     */
    private String error;
    /**
     * 机器人类型
     */
    private String robotType;
    /**
     * 会话文本
     */
    private String text;
    private String reasoning;

    private String prompt;
    private String requestOptions;
    private Date dateTime;

    private String appId;
    private Integer msgCount;
    private String sessionName;
}

