package com.tydic.nbchat.session.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatSessionMsg)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 19:49:38
 */
@Data
public class NbchatSessionMsgReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 289587688205587833L;

    private String startTime;
    private String endTime;
    private String appId;
    private String keyword;


    private String refer; //请求来源，0试题专业版

    /**
     * 请求id
     */
    private String requestId;
    /**
     * 最近一条会话id
     */
    private String lastId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 会话id
     */
    @ParamNotEmpty(message = "会话id不得为空")
    private String sessionId;
    /**
     * 0 机器人 1 用户
     */
    private String userType;
    /**
     * 0删除 1正常
     */
    private String isValid;

    private Date dateTime;
}

