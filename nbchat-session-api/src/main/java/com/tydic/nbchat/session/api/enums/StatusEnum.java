package com.tydic.nbchat.session.api.enums;

public enum StatusEnum {

    SHARE_QUERY("1","查看"),
    SHARE_SAVE("2","保存"),

    SAVE("1","保存"),
    UNSAVE("0","未保存"),

    COLLECT("1","收藏"),
    UNCOLLECT("0","取消收藏"),

    SHARE("2","共享"),
    SELF("1","自有"),

    DELETE("0","删除"),
    NORMAL("1","正常");

    private String value;
    private String desc;

    StatusEnum(String value,String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }
}