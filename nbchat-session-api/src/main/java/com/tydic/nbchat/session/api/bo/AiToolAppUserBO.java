package com.tydic.nbchat.session.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (AiToolAppUser)实体类
 *
 * <AUTHOR>
 * @since 2024-04-11 11:30:31
 */
@Data
public class AiToolAppUserBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 671611077712726806L;

    private Long id;
/**
     * 租户
     */
    private String tenantCode;
/**
     * 用户ID
     */
    private String userId;

    private String appId;
/**
     * 姓名
     */
    private String name;
/**
     * 手机号
     */
    private String phone;
/**
     * 邮箱
     */
    private String email;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 关联会话
     */
    private String sessionId;
/**
     * 0 删除 1 有效
     */
    private String isValid;

}

