package com.tydic.nbchat.gateway.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> <br>
 * @Description: HttpRequestUtil  <br>
 * @date 2021/10/13 3:47 下午  <br>
 * @Copyright tydic.com
 */
@Slf4j
public class HttpRequestUtil {

    /**
     * 编码query参数
     * @param request
     * @return
     */
    public static HttpServletRequest encodedQueryString(HttpServletRequest request) {
        String queryString = request.getQueryString();
        if (StringUtils.isBlank(queryString)) {
            return request;
        }
        log.info("encodedQueryString:{}", queryString);
        String encodedQueryString = URLEncoder.encode(queryString, StandardCharsets.UTF_8);
        String requestURL = request.getRequestURL().toString();
        //String newURL = requestURL + "?" + encodedQueryString;
        request = new HttpServletRequestWrapper(request) {
            @Override
            public StringBuffer getRequestURL() {
                return new StringBuffer(requestURL).append("?").append(encodedQueryString);
            }
        };
        return request;
    }

    /**
     * url追加参数
     * @param uri
     * @param name
     * @param param
     * @return
     */
    public static String urlAppendQueryParam(String uri,String name,String param){
        UriBuilder uriBuilder = UriComponentsBuilder.fromUriString(uri).queryParam(name,param);
        return uriBuilder.build().toString();
    }

    /**
     * 追加query参数
     * @param uri
     * @param queryString
     * @return
     */
    public static String urlAppendQuery(String uri,String queryString){
        UriBuilder uriBuilder = UriComponentsBuilder.fromUriString(uri).encode();
        if(StringUtils.isNotBlank(queryString)){
            uriBuilder.query(queryString);
        }
        return uriBuilder.build().toString();
    }

    /**
     * 编码参数
     * @param uri
     * @return
     */
    public static String urlEncodeQuery(String uri){
        UriBuilder uriBuilder = UriComponentsBuilder.fromUriString(uri).encode();
        return uriBuilder.build().toString();
    }


    /**
     * 编码
     * @param uri
     * @return
     */
    public static String urlEncode(String uri){
        String encode = "";
        encode = URLEncoder.encode(uri, StandardCharsets.UTF_8);
        return encode;
    }

    /**
     * 解码
     * @param uri
     * @return
     */
    public static String urlDecode(String uri){
        String decode = "";
        decode = URLDecoder.decode(uri, StandardCharsets.UTF_8);
        return decode;
    }


    public static String getBodyContent(HttpServletRequest request) throws IOException {
        request.setCharacterEncoding("UTF-8"); // 强制设置请求编码为 UTF-8
        StringBuilder sb = new StringBuilder();
        try (InputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    public static void authError(HttpServletResponse response, String error) throws IOException {
        authError(response, 401, error, "");
    }

    public static void authError(HttpServletResponse response, String error, String redirect) throws IOException {
        authError(response, 401, error, redirect);
    }


    public static void authError(HttpServletResponse response, int code, String error, String redirect) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        response.setStatus(code);
        if (StringUtils.isNotBlank(redirect)) {
            //设置跳转地址
            response.setHeader("redirectUrl", redirect);
            response.setHeader("enableRedirect", "true");
        }
        PrintWriter out = response.getWriter();
        JSONObject rspJson = new JSONObject();
        rspJson.put("code", "1");
        rspJson.put("message", error);
        String rspJsonStr = JSON.toJSONString(rspJson,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty);
        out.write(rspJsonStr);
        out.close();
    }

    public static void setResponse(HttpServletResponse response, Object obj) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(200);
        PrintWriter out = response.getWriter();
        String rspJsonStr = JSON.toJSONString(obj,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty);
        out.write(rspJsonStr);
        out.close();
    }


    //{"status":"UP","groups":["liveness","readiness"]}
    public static void healthCheck(HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        JSONObject rspJson = new JSONObject();
        rspJson.put("status", "UP");
        rspJson.put("groups", new String[]{"liveness", "readiness"});
        String rspJsonStr = JSON.toJSONString(rspJson,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty);
        out.write(rspJsonStr);
        out.close();
    }

}
