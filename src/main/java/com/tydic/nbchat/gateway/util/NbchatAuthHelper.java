package com.tydic.nbchat.gateway.util;

import com.alibaba.nacos.common.utils.MD5Utils;
import com.tydic.nbchat.gateway.api.bo.AuthUserRsp;
import com.tydic.nbchat.gateway.api.bo.FilterConstants;
import com.tydic.nbchat.gateway.auth.AuthRequestContext;
import com.tydic.nbchat.gateway.auth.service.UserAuthService;
import com.tydic.nbchat.gateway.config.NbChatGatewayConfigProperties;
import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.eums.AuthType;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class NbchatAuthHelper {

    private final UserAuthService userAuthService;
    private final RedisHelper redisHelper;
    private final NbChatGatewayConfigProperties nbChatGatewayConfigProperties;
    @Value("${server.servlet.context-path:}")
    private String contextPath = null;

    public NbchatAuthHelper(UserAuthService userAuthService,
                            RedisHelper redisHelper,
                            NbChatGatewayConfigProperties nbChatGatewayConfigProperties) {
        this.userAuthService = userAuthService;
        this.redisHelper = redisHelper;
        this.nbChatGatewayConfigProperties = nbChatGatewayConfigProperties;
    }

    public boolean isAttachment(String uri) {
        String attachmentTypes = nbChatGatewayConfigProperties.getAttachmentTypes();
        if (StringUtils.isNotEmpty(attachmentTypes)) {
            String[] types = attachmentTypes.split(",");
            for (String item : types) {
                if (uri.endsWith(item)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验白名单
     *
     * @param requestUri
     * @return
     */
    public boolean inWhiteList(String requestUri) {
        boolean isWhite = false;
        //白名单拦截过滤
        if (requestUri.equals("/") || requestUri.equals(contextPath)) {
            return true;
        }
        List<String> staticList = Arrays.asList("/static", "/assets");
        List<String> staticFiles = Arrays.asList(".js", ".png", ".css", ".jpg", ".jpeg", ".gif", ".ico", ".html");
        for (String s : staticList) {
            if (requestUri.startsWith(s)) {
                return true;
            }
        }
        for (String file : staticFiles) {
            if (requestUri.endsWith(file)) {
                return true;
            }
        }
        String whiteList = nbChatGatewayConfigProperties.getWhiteList();
        if (StringUtils.isNotBlank(whiteList)) {
            String[] whiteListArr = whiteList.split(",");
            for (String s : whiteListArr) {
                if (StringUtils.isNotBlank(contextPath)) {
                    s = contextPath + s;
                }
                if (RegexUtils.wildcardEquals(s, requestUri)) {
                    log.debug("authFilter 白名单路径:{}", requestUri);
                    isWhite = true;
                    break;
                }
            }
        }
        return isWhite;
    }

    public void loadUserContext(String platform, AuthRequestContext context) {
        /**
         * 查询用户信息
         */
        userAuthService.loadUserContext(platform, context);
    }

    /**
     * 微信登录
     *
     * @param request
     * @param response
     * @throws IOException
     */
    public void wchatLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // AuthType.WCHAT.getCode(), JoinTenantType.DI_YI_XUE.getName()
        String authType = "",loginClient = "";
        String requestUri = request.getRequestURI();
        if (FilterConstants.API_URI_WCHAT_LOGIN.equals(requestUri)) {
            authType = AuthType.WCHAT.getCode();
            loginClient = JoinTenantType.DI_YI_XUE.getName();
        } else if (FilterConstants.API_URI_WCHAT_LOGIN_PC.equals(requestUri)) {
            authType = AuthType.WCHAT_PC.getCode();
            loginClient = JoinTenantType.QI_YE_NEI_XUE.getCode();
        } else if (FilterConstants.API_URI_WCHAT_LOGIN_MP.equals(requestUri)) {
           //小程序
            authType = AuthType.WCHAT_MP_TDH.getCode();
            loginClient = JoinTenantType.TDH_MINI_APP.getName();
        } else {
            return;
        }
        String code = request.getParameter("code");
        String channel = request.getParameter("channel");
        String state = request.getParameter("state");
        if (StringUtils.isBlank(code)) {
            //认证失败
            HttpRequestUtil.authError(response, "Auth Error: code is null!");
            return;
        }
        if (StringUtils.isBlank(channel)) {
            channel = "wchat";
        }
        log.info("wchatLoginAuthFilter wchat认证[{}]-开始: {}|{}|{}", authType, code, channel, state);
        //String openid = wchatLoginHelper.getWorkChatUserId(code);
        try {
            AuthUserReqBO authUserReqBO = AuthUserReqBO.builder().
                    authType(authType).code(code).channel(channel).loginClient(loginClient).build();
            Rsp<AuthUserRsp> rsp = userAuthService.authUser(authUserReqBO, request, response);
            if (rsp.isSuccess()) {
                if (FilterConstants.API_URI_WCHAT_LOGIN_MP.equals(requestUri)){
                    HttpRequestUtil.setResponse(response,rsp);
                    return;
                } else {
                    AuthUserRsp authUserRsp = rsp.getData();
                    String redirectUri = authUserRsp.getRedirect();
                    redirectUri = HttpRequestUtil.urlAppendQuery(redirectUri, state);
                    log.info("wchatLoginAuthFilter wchat认证[{}]-重定向: {}|{}|{} -> {}", authType, code, channel, state, redirectUri);
                    //参数不支持中文，需要编码
                    response.sendRedirect(redirectUri);
                    return;
                }
            }
        } catch (Exception e) {
            log.error("wchatLoginAuthFilter wchat认证[{}]-异常: {}|{}|{}", authType, code, channel, state, e);
            HttpRequestUtil.authError(response, "Auth Error: Server error");
        }
        //认证失败
        HttpRequestUtil.authError(response, "Auth Error!");
    }

    /**
     * 是否失效
     * @param token
     * @return
     */
    public boolean isLogout(String token) {
        try {
            String tokenMd5 = MD5Utils.md5Hex(token.getBytes());
            String key = RedisConstants.USER_TOKEN_DISABLE_PREFIX_KEY + tokenMd5;
            return redisHelper.hasKey(key);
        } catch (NoSuchAlgorithmException e) {
            log.warn("token 计算md5异常:",e);
        }
        return false;
    }

    /**
     * token失效
     * @param token
     */
    public void logoutToken(String token){
        try {
            String tokenMd5 = MD5Utils.md5Hex(token.getBytes());
            String key = RedisConstants.USER_TOKEN_DISABLE_PREFIX_KEY + tokenMd5;
            long exp = TimeUnit.DAYS.toSeconds(3);
            log.info("用户退出登录-标记token:{}",key);
            redisHelper.set(key,System.currentTimeMillis(),exp);
        } catch (NoSuchAlgorithmException e) {
            log.warn("token 计算md5异常:",e);
        }
    }

    /**
     * 获取auth token
     *
     * @param request
     * @return
     */
    public static String getAuthToken(HttpServletRequest request) {
        log.info("获取token请求头={}",FilterConstants.HEADER_TOKEN_KEY);
        String token = request.getHeader(FilterConstants.HEADER_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            token = request.getParameter(FilterConstants.HEADER_TOKEN_KEY);
            if(StringUtils.isBlank(token)){
                Cookie[] cookies = request.getCookies();
                if (cookies != null) {
                    for (Cookie cookie : cookies) {
                        if (cookie.getName().equals(FilterConstants.HEADER_TOKEN_KEY)) {
                            token = cookie.getValue();
                            break;
                        }
                    }
                }
            }
        }
        return token;
    }

}
