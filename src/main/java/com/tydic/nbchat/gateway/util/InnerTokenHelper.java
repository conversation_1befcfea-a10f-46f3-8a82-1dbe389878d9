package com.tydic.nbchat.gateway.util;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.tydic.nbchat.admin.api.SysTokenConfigApi;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenConfigBO;
import com.tydic.nbchat.user.api.bo.auth.UserTokenInfo;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.JWTUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.tydic.nbchat.admin.api.SysTokenConfigApi.TOKEN_CONFIG_KEY;

@Slf4j
@Component
public class InnerTokenHelper {

    private final NbchatAuthHelper nbchatAuthHelper;
    private final RedisHelper redisHelper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private SysTokenConfigApi sysTokenConfigApi;

    public InnerTokenHelper(NbchatAuthHelper nbchatAuthHelper, RedisHelper redisHelper) {
        this.nbchatAuthHelper = nbchatAuthHelper;
        this.redisHelper = redisHelper;
    }

    private static UserTokenInfo newTokenInfo(String userId, String tenantCode, boolean apiAuth) {
        UserTokenInfo user = new UserTokenInfo();
        user.setUserId(userId);
        user.setTenantCode(tenantCode);
        user.setApiAuth(apiAuth);
        return user;
    }


    public UserTokenInfo authByInnerToken(String token, String requestUri) {
        String key = TOKEN_CONFIG_KEY + token;
        SysApiTokenConfigBO config = (SysApiTokenConfigBO) redisHelper.get(key);
        if (config == null) {
            Rsp<SysApiTokenConfigBO> rsp = sysTokenConfigApi.getApiTokenConfig(token);
            if (rsp.isSuccess()) {
                config = rsp.getData();
                if (config.getExpireTime() > 0) {
                    redisHelper.set(key, config, config.getExpireTime());
                } else {
                    redisHelper.set(key, config, 24 * 3600);
                }
            }
        }
        if (config != null) {
            if (config.getExpireTime() > 0) {
                if (config.getRefreshTime() == null) {
                    throw new JWTVerificationException("认证失败-配置异常: " + token);
                }
                if (System.currentTimeMillis() - config.getRefreshTime().getTime() > config.getExpireTime() * 1000) {
                    throw new JWTVerificationException("认证失败-token已过期: " + token);
                }
            }
            if (config.isDeny(requestUri)) {
                throw new JWTVerificationException("认证失败-无权限访问: " + requestUri);
            }
            if (config.isAllow(requestUri)) {
                String uid = config.getUserId();
                log.info("authFilter 内部token认证:{}:{}", token, uid);
                return newTokenInfo(uid, config.getTenantCode(), Boolean.TRUE);
            }
        }
        return null;
    }

    public UserTokenInfo authByInnerToken(Map<String, String> tokenUsers, String token) {
        String uid = tokenUsers.get(token);
        if (StringUtils.isNotBlank(uid)) {
            log.info("authFilter 内部token认证:{}:{}", token, uid);
            return newTokenInfo(uid, UserAttributeConstants.DEFAULT_TENANT_CODE, Boolean.TRUE);
        }
        return null;
    }

    public UserTokenInfo authByToken(String token) {
        if (nbchatAuthHelper.isLogout(token)) {
            throw new JWTVerificationException("token已失效请重新登录!");
        }
        String loginInfo = JWTUtils.getLoginInfo(token);
        return UserTokenInfo.parse(loginInfo);
    }
}
