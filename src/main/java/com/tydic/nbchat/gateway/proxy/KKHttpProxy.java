package com.tydic.nbchat.gateway.proxy;

import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.mitre.dsmiley.httpproxy.RefactorProxyServlet;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.HttpCookie;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: KKHttpProxy  <br>
 * @date 2021/9/22 5:04 下午  <br>
 * @Copyright tydic.com
 */
@Slf4j
public class KKHttpProxy extends RefactorProxyServlet {

    protected final static String P_ADD_QUERY_STRING = "P_ADD_QUERY_STRING";


    @Override
    protected Cookie createProxyCookie(HttpServletRequest servletRequest, HttpCookie cookie) {
        String proxyCookieName = this.getProxyCookieName(cookie);
        Cookie servletCookie = new Cookie(proxyCookieName, cookie.getValue());
        //根据用户实际设置的路径来设置cookie的路径
        servletCookie.setPath(cookie.getPath());
        //servletCookie.setPath(this.buildProxyCookiePath(servletRequest));
        servletCookie.setComment(cookie.getComment());
        servletCookie.setMaxAge((int)cookie.getMaxAge());
        servletCookie.setSecure(cookie.getSecure());
        servletCookie.setVersion(cookie.getVersion());
        servletCookie.setHttpOnly(cookie.isHttpOnly());
        return servletCookie;
    }


    @Override
    protected void service(HttpServletRequest servletRequest, HttpServletResponse servletResponse)
            throws ServletException, IOException {
        Date startTime = new Date();
        super.service(servletRequest, servletResponse);
        String uri = servletRequest.getRequestURL().toString();
        Date endTime = new Date();
        log.info("[{},{}]|{}|{}",
                DateTimeUtil.getTimeShortString(startTime,DateTimeUtil.TIME_FORMAT_T_FULL),
                DateTimeUtil.getTimeShortString(endTime,DateTimeUtil.TIME_FORMAT_T_FULL),
                uri,endTime.getTime()-startTime.getTime());
    }

    @Override
    protected void handleRequestException(HttpRequest proxyRequest, HttpResponse proxyResponse, Exception e) throws ServletException, IOException {
        String errMsg = e.getMessage();
        if (StringUtils.isBlank(errMsg)){
            super.handleRequestException(proxyRequest, proxyResponse,e);
            return;
        }
        if(errMsg.contains("reset by peer") || errMsg.toLowerCase().contains("broken pipe")){
            log.warn("{} | {}",proxyRequest.getRequestLine().getUri(),errMsg);
        } else {
            log.error("{} | {}",proxyRequest.getRequestLine().getUri(),errMsg);
            super.handleRequestException(proxyRequest, proxyResponse,e);
        }
    }


}
