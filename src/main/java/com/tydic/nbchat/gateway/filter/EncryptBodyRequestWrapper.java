package com.tydic.nbchat.gateway.filter;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.gateway.config.NbChatGatewayConfigProperties;
import com.tydic.nbchat.gateway.util.EncryptionUtil;
import com.tydic.nbchat.gateway.util.HttpRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Slf4j
public class EncryptBodyRequestWrapper extends HttpServletRequestWrapper {

    private String body = null;
    private final NbChatGatewayConfigProperties gatewayConfigProperties;

    public EncryptBodyRequestWrapper(HttpServletRequest request,
                                     NbChatGatewayConfigProperties gatewayConfigProperties) throws IOException {
        super(request);
        this.gatewayConfigProperties = gatewayConfigProperties;
        this.body = getBodyContent(request);
    }


    private String getBodyContent(final HttpServletRequest request) throws IOException {
        String reqUri = request.getRequestURI();
        String reqBody = HttpRequestUtil.getBodyContent(request);
        reqBody = EncryptionUtil.decryptBody(reqBody);
        JSONObject reqJson = JSONObject.parseObject(reqBody);
        //重写reqBody
        String newBody = reqJson.toString();
        log.info("authFilter 加密参数重写-重写请求参数: {}|{}|{}", reqUri, reqBody, newBody);
        return newBody;
    }


    public String getHeader(String name) {
        if (name.equals("Content-Length")) {
            return String.valueOf(body.getBytes(StandardCharsets.UTF_8).length);
        }
        return super.getHeader(name);
    }

    @Override
    public int getContentLength() {
        if (StringUtils.isNotEmpty(body)) {
            return body.getBytes(StandardCharsets.UTF_8).length;
        }
        return 0;
    }

    @Override
    public long getContentLengthLong() {
        return getContentLength();
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        log.debug("{}|{}", body, this.getRequest().getContentLength());
        final ByteArrayInputStream bais = new ByteArrayInputStream(body.getBytes(StandardCharsets.UTF_8));
        return new ServletInputStream() {
            @Override
            public int read() throws IOException {
                return bais.read();
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }
        };
    }
}
