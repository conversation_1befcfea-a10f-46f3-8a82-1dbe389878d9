package com.tydic.nbchat.gateway.filter;

import com.alibaba.fastjson.JSONException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.tydic.nbchat.gateway.api.bo.FilterConstants;
import com.tydic.nbchat.gateway.auth.AuthRequestContext;
import com.tydic.nbchat.gateway.auth.config.NbChatGatewayAuthProperties;
import com.tydic.nbchat.gateway.config.NbChatGatewayConfigProperties;
import com.tydic.nbchat.gateway.exception.DecryptException;
import com.tydic.nbchat.gateway.util.*;
import com.tydic.nbchat.user.api.bo.auth.UserTokenInfo;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@Slf4j
@Component
@WebFilter(filterName = "loginAuthFilter", urlPatterns = {"/*"})
@Order(1)
public class LoginAuthFilter implements Filter {

    private final NbChatGatewayConfigProperties nbChatGatewayConfigProperties;
    private final NbChatGatewayAuthProperties nbChatGatewayAuthProperties;
    private final InnerTokenHelper innerTokenHelper;
    private final NbchatAuthHelper nbchatAuthHelper;

    public LoginAuthFilter(NbChatGatewayConfigProperties nbChatGatewayConfigProperties,
                           NbChatGatewayAuthProperties nbChatGatewayAuthProperties,
                           InnerTokenHelper innerTokenHelper,
                           NbchatAuthHelper nbchatAuthHelper) {
        this.nbChatGatewayConfigProperties = nbChatGatewayConfigProperties;
        this.nbChatGatewayAuthProperties = nbChatGatewayAuthProperties;
        this.innerTokenHelper = innerTokenHelper;
        this.nbchatAuthHelper = nbchatAuthHelper;
    }

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("authFilter 初始化拦截器 --> nbChatGatewayConfigProperties:{}", nbChatGatewayConfigProperties);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String method = request.getMethod();
        String requestUri = request.getRequestURI();
        String referer = request.getHeader(FilterConstants.HEADER_REFRER);
        String userAgent = request.getHeader(FilterConstants.HEADER_UA);
        //获取query参数
        String queryString = request.getHeader(FilterConstants.HEADER_QUERY_STR);
        //request = HttpRequestUtil.encodedQueryString(request);
        if (healthCheck(request, response)) {
            return;
        }
        log.info("authFilter requestUri:{}, userAgent:{}, referer:{}, queryString:{}", requestUri, userAgent, referer, queryString);

        if (StringUtils.isNotBlank(requestUri) && requestUri.startsWith(FilterConstants.API_URI_WCHAT_LOGIN)) {
            //微信内打开认证
            nbchatAuthHelper.wchatLogin(request, response);
            return;
        }

        if (nbchatAuthHelper.isAttachment(requestUri)) {
            // 设置响应头，告诉浏览器这是一个压缩文件
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment");
        }
        //白名单校验
        if (nbchatAuthHelper.inWhiteList(requestUri)) {
            log.info("authFilter 白名单路径: {}", requestUri);
            if (HttpMethod.POST.matches(method) && nbChatGatewayConfigProperties.matchEncryptApis(requestUri)) {
                //加密接口
                try {
                    request = new EncryptBodyRequestWrapper(request, nbChatGatewayConfigProperties);
                } catch (DecryptException e) {
                    log.warn("authFilter 白名单路径[{}]-请求参数解密失败: {}", requestUri, e.getMessage());
                    HttpRequestUtil.authError(response, 400, "参数异常!", "");
                    return;
                }
            }
            filterChain.doFilter(request, response);
            return;
        }
        //认证开始
        if (nbChatGatewayConfigProperties.getAuthEnable()) {
            //获取token
            String token = NbchatAuthHelper.getAuthToken(request);
            log.info("authFilter 用户认证[{}]-获取token: {}", requestUri, token);
            //认证token
            try {
                if (StringUtils.isBlank(token)) {
                    throw new JWTVerificationException("token不得为空!");
                }
                UserTokenInfo userInfo = null;
                if (token.length() > 100) {
                    userInfo = innerTokenHelper.authByToken(token);
                } else {
                    userInfo = innerTokenHelper.authByInnerToken(nbChatGatewayConfigProperties.getTokenUsers(), token);
                    if (userInfo == null) {
                        userInfo = innerTokenHelper.authByInnerToken(token, requestUri);
                    }
                    if (userInfo == null) {
                        throw new JWTVerificationException("认证失败: 未查询到用户信息");
                    }
                }
                AuthRequestContext requestContext = AuthRequestContext.builder().
                        userTokenInfo(userInfo).gatewayConfigProperties(nbChatGatewayConfigProperties).build();
                log.info("authFilter 用户认证[{}]-解析token: {}|{}", requestUri, method, userInfo);
                if (HttpMethod.POST.matches(method)) {
                    //post 请求走拦截注入
                    doFilter(filterChain, request, response, requestContext);
                } else {
                    filterChain.doFilter(request, response);
                }
            } catch (JWTVerificationException e) {
                //e.printStackTrace();
                log.warn("authFilter 用户认证[{}]-token解析异常: {},{}", requestUri, e.getMessage(), nbChatGatewayAuthProperties.getLoginPage());
                //跳转到登录页面 MicroMessenger
                if (userAgent != null && userAgent.toLowerCase().contains("micromessenger")) {
                    // 是微信客户端
                    log.info("authFilter 用户认证[{}]-跳转到微信认证: {}|{}", requestUri, nbChatGatewayAuthProperties.getWchatLogin(), queryString);
                    if (StringUtils.isNotBlank(queryString)) {
                        String state = HttpRequestUtil.urlEncode(queryString);
                        String url = HttpRequestUtil.urlAppendQueryParam(nbChatGatewayAuthProperties.getWchatLogin(), "state", state);
                        log.info("authFilter 用户认证[{}]-跳转到微信认证-重写重定向地址: {}", requestUri, url);
                        HttpRequestUtil.authError(response, "Auth Error!", url);
                    } else {
                        //response.sendRedirect(nbChatGatewayAuthProperties.getWchatLogin());
                        HttpRequestUtil.authError(response, "Auth Error!", nbChatGatewayAuthProperties.getWchatLogin());
                    }
                } else {
                    log.info("authFilter 用户认证[{}]-跳转到登录页: {}", requestUri, nbChatGatewayAuthProperties.getLoginPage());
                    HttpRequestUtil.authError(response, "Auth Error!", nbChatGatewayAuthProperties.getLoginPage());
                }
            } catch (JSONException e) {
                String body = HttpRequestUtil.getBodyContent(request);
                log.warn("authFilter 用户认证[{}]-请求参数格式异常: {}", requestUri, body);
                HttpRequestUtil.authError(response, 400, "请求参数格式异常: " + body, "");
            } catch (DecryptException e) {
                String body = HttpRequestUtil.getBodyContent(request);
                log.warn("authFilter 用户认证[{}]-请求参数解密失败: {}", requestUri, body);
                HttpRequestUtil.authError(response, 400, "参数异常!", "");
            } catch (Exception e) {
                String msg = e.getMessage();
                if (StringUtils.isNotBlank(msg) && msg.contains("Broken pipe")) {
                    log.warn("authFilter 用户认证[{}]-客户端强制关闭数据流: {}", requestUri, e.getMessage());
                } else {
                    log.error("authFilter 用户认证[{}]-服务异常:", requestUri, e);
                    HttpRequestUtil.authError(response, 500, "服务器内部错误,请稍候重试!", "");
                }
                //authError(response, "Auth Error!", nbChatGatewayAuthProperties.getLoginPage());
            }
        } else {
            //走mock注入
            log.debug("authFilter 用户认证[{}]-拦截器未开启", requestUri);
            doFilter(filterChain, request, response, AuthRequestContext.builder().
                    gatewayConfigProperties(nbChatGatewayConfigProperties).build());
        }
    }


    private void doFilter(FilterChain chain, HttpServletRequest request,
                          HttpServletResponse response, AuthRequestContext context) throws ServletException, IOException {
        ServletRequest requestWrapper = null;
        String contentType = request.getContentType();
        String platform = request.getHeader(UserAttributeConstants.HEADER_PLATFORM);
        if (StringUtils.isNotBlank(contentType)) {
            //重新包装request
            if (contentType.contains(ContentType.APPLICATION_FORM_URLENCODED.getMimeType()) ||
                    contentType.contains(ContentType.MULTIPART_FORM_DATA.getMimeType())) {
                requestWrapper = new ParameterRequestWrapper(request, context);
            } else if (contentType.contains(ContentType.APPLICATION_JSON.getMimeType())) {
                Optional.ofNullable(context.getUserTokenInfo()).ifPresent(obj -> {
                    //加载用户信息
                    nbchatAuthHelper.loadUserContext(platform, context);
                });
                requestWrapper = new BodyRequestWrapper(request, context);
            }
            chain.doFilter(requestWrapper, response);
        } else {
            chain.doFilter(request, response);
        }
    }


    private boolean healthCheck(HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (FilterConstants.HEALTH_CHECK.equals(request.getRequestURI())){
            HttpRequestUtil.healthCheck(response);
            return true;
        }
        return false;
    }
}
