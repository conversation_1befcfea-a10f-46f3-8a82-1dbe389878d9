#!/bin/bash

## 批量切换到新分支

WORK_HOME=`pwd`

SOURCE=$1
TARGET=$2

if [[ "$SOURCE" == "" ]]
then
    SOURCE=""
fi

echo "nicc-common 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-common/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}

echo "nicc-im 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-im/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}


echo "nicc-event-center 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-event-center/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}

echo "nicc-user-center 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-user-center/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}


echo "nicc-session 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-session/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}

echo "nicc-csm 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-csm/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}


echo "nicc-opdata 切换分支开始 ..."
cd ${WORK_HOME}/../nicc-op-data/
git checkout -b ${SOURCE} ${TARGET}
git push --set-upstream origin ${TARGET}


