FROM kangkang223/alpine-jre11-arm64
ARG WORK_PATH="/app"
ADD nbchat-robot-service/target/nbchat-robot-service-*.jar $WORK_PATH/app.jar
WORKDIR $WORK_PATH
ENTRYPOINT ["/bin/bash","-c","exec java -server \
         ${JAVA_OPTS:--XX:+UseG1GC -Xmx1g -Xms512m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m} \
        -Dnacos.config.server-addr=${NACOS_SERVER_ADDR:-nacos-server:8848} \
        -Dnacos.config.username=${NACOS_USERNAME:-nacos} \
        -Dnacos.config.password=${NACOS_PASSWORD:-nacos} \
        -Dnacos.config.namespace=${NACOS_NAMESPACE:-name_space} \
        -Dspring.profiles.active=${PROFILES_ACTIVE:-nacos-dev} \
        -jar app.jar "]
