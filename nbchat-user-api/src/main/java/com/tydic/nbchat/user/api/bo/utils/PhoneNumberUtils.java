package com.tydic.nbchat.user.api.bo.utils;

import com.tydic.nbchat.user.api.bo.constants.AreaCodeConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/03/31
 * @email <EMAIL>
 * @description 手机号验证工具类
 */
public class PhoneNumberUtils {
    // 中国大陆手机号正则表达式
    private static final String CN_PHONE_REGEX = "^1[0-9]{10}$";
    // 香港手机号正则表达式 (主体是5/6/8/9开头的8位数字)
    private static final String CN_HK_PHONE_REGEX = "^[5689][0-9]{7}$";
    // 台湾手机号正则表达式 (主体是9开头的9位数字)
    private static final String CN_TW_PHONE_REGEX = "^9[0-9]{8}$";
    // 国际区号正则表达式，用于提取区号
    private static final Pattern INTERNATIONAL_PATTERN = Pattern.compile("^\\+?(\\d{1,4})[-\\s]?(.*)$");
    // 带前导零的区号正则表达式，如 0086
    private static final Pattern ZERO_PREFIX_PATTERN = Pattern.compile("^00?(\\d{1,4})(.*)$");

    // 各国家和地区的手机号正则表达式映射
    private static final Map<String, String> PHONE_REGEX_MAP = new HashMap<>();

    static {
        // 初始化各国家和地区的手机号正则表达式
        PHONE_REGEX_MAP.put(AreaCodeConstants.CHINA_MAINLAND, "^1[0-9]{10}$"); // 中国大陆
        PHONE_REGEX_MAP.put(AreaCodeConstants.CHINA_HONGKONG, "^[5689][0-9]{7}$"); // 香港
        PHONE_REGEX_MAP.put(AreaCodeConstants.CHINA_TAIWAN, "^9[0-9]{8}$"); // 台湾
        PHONE_REGEX_MAP.put(AreaCodeConstants.USA_CANADA, "^[2-9][0-9]{9}$"); // 美国/加拿大
        // 可以根据需要添加更多国家和地区的手机号正则表达式
    }


    /**
     * 标准化手机号格式
     * @param phoneNumber 原始手机号
     * @return 标准化后的手机号
     */
    public static String normalizePhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return "";
        }

        // 移除可能的空格和连字符
        String cleanNumber = phoneNumber.replaceAll("[\\s-]", "");

        return cleanNumber;
    }

    /**
     * 校验手机号
     * @param phoneNumber 电话号码
     * @return boolean
     */
    public static boolean validPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return false;
        }

        // 标准化手机号
        String cleanNumber = normalizePhoneNumber(phoneNumber);

        // 直接检查常见格式
        // 检查是否符合中国大陆手机号格式
        if (cleanNumber.matches(CN_PHONE_REGEX)) {
            return true;
        }
        // 检查是否符合香港手机号格式
        if (cleanNumber.matches(CN_HK_PHONE_REGEX)) {
            return true;
        }
        // 检查是否符合台湾手机号格式
        if (cleanNumber.matches(CN_TW_PHONE_REGEX)) {
            return true;
        }

        // 检查是否是带区号的手机号
        // 处理国际格式的手机号，如 +86123456789 或 +85291234567
        if (cleanNumber.startsWith("+")) {
            Matcher matcher = INTERNATIONAL_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                String areaCode = matcher.group(1);
                String number = matcher.group(2);
                // 去除前导零
                areaCode = areaCode.replaceFirst("^0+", "");
                return validateNumberWithAreaCode(areaCode, number);
            }
        }

        // 处理带前导零的区号格式，如 0086123456789 或 0085291234567
        if (cleanNumber.matches("^00?\\d{1,4}.*")) {
            Matcher matcher = ZERO_PREFIX_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                String areaCode = matcher.group(1);
                String number = matcher.group(2);
                // 去除前导零
                areaCode = areaCode.replaceFirst("^0+", "");
                return validateNumberWithAreaCode(areaCode, number);
            }
        }

        // 处理可能是直接带区号的格式，如 86123456789 或 85291234567
        if (cleanNumber.length() > 10) {
            // 尝试提取常见区号
            for (String knownAreaCode : PHONE_REGEX_MAP.keySet()) {
                if (cleanNumber.startsWith(knownAreaCode)) {
                    String number = cleanNumber.substring(knownAreaCode.length());
                    return validateNumberWithAreaCode(knownAreaCode, number);
                }
            }
        }

        // 如果上述所有检查都失败，则尝试将其作为纯数字手机号处理
        if (cleanNumber.matches("^\\d{7,15}$")) {
            return true;
        }

        return false;
    }

    /**
     * 根据区号验证手机号
     * @param areaCode 区号
     * @param number 手机号
     * @return 是否有效
     */
    private static boolean validateNumberWithAreaCode(String areaCode, String number) {
        // 特殊处理香港手机号
        if (AreaCodeConstants.CHINA_HONGKONG.equals(areaCode)) {
            // 香港手机号应该是8位数字，以 5/6/8/9 开头
            return number.matches(CN_HK_PHONE_REGEX);
        }

        // 特殊处理台湾手机号
        if (AreaCodeConstants.CHINA_TAIWAN.equals(areaCode)) {
            // 台湾手机号应该是9位数字，以 9 开头
            return number.matches(CN_TW_PHONE_REGEX);
        }

        // 特殊处理中国大陆手机号
        if (AreaCodeConstants.CHINA_MAINLAND.equals(areaCode)) {
            // 中国大陆手机号应该是11位数字，以 1 开头
            return number.matches(CN_PHONE_REGEX);
        }

        // 获取区号对应的手机号正则表达式
        String regex = PHONE_REGEX_MAP.get(areaCode);

        // 如果没有找到区号对应的正则表达式，则允许任何合理的手机号格式
        if (regex == null) {
            // 大多数国家的手机号长度在 7-15 位之间
            return number.matches("^\\d{7,15}$");
        }

        // 使用区号对应的正则表达式验证手机号
        return number.matches(regex);
    }


    /**
     * 替换手机号中间四位数
     * @param phoneNum 手机号
     * @return 隐藏中间部分的手机号
     */
    public static String maskPhoneNum(String phoneNum) {
        if (StringUtils.isBlank(phoneNum)) {
            return phoneNum;
        }

        // 标准化手机号
        String cleanNumber = normalizePhoneNumber(phoneNum);
        String areaCode = extractAreaCode(cleanNumber);
        String number = cleanNumber;

        // 处理国际格式的手机号，如 +86123456789
        if (cleanNumber.startsWith("+")) {
            Matcher matcher = INTERNATIONAL_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                number = matcher.group(2);
            }
        }
        // 处理带前导零的区号格式，如 0086123456789
        else if (cleanNumber.matches("^00?\\d{1,4}.*")) {
            Matcher matcher = ZERO_PREFIX_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                number = matcher.group(2);
            }
        }
        // 处理可能是直接带区号的格式，如 86123456789
        else if (cleanNumber.length() > 10) {
            for (String knownAreaCode : PHONE_REGEX_MAP.keySet()) {
                if (cleanNumber.startsWith(knownAreaCode)) {
                    number = cleanNumber.substring(knownAreaCode.length());
                    break;
                }
            }
        }
        // 处理纯手机号格式
        else {
            // 检查是否符合中国大陆手机号格式
            if (cleanNumber.matches(CN_PHONE_REGEX)) {
                number = cleanNumber;
            }
            // 检查是否符合香港手机号格式
            else if (cleanNumber.matches(CN_HK_PHONE_REGEX)) {
                number = cleanNumber;
            }
            // 检查是否符合台湾手机号格式
            else if (cleanNumber.matches(CN_TW_PHONE_REGEX)) {
                number = cleanNumber;
            }
        }

        // 保持原始格式的前缀
        String prefix = "";
        if (phoneNum.startsWith("+")) {
            prefix = "+" + areaCode + " ";
        } else if (phoneNum.startsWith("00")) {
            prefix = "00" + areaCode + " ";
        } else if (phoneNum.startsWith("0")) {
            prefix = "0" + areaCode + " ";
        } else if (!StringUtils.isBlank(areaCode) && !areaCode.equals(AreaCodeConstants.CHINA_MAINLAND)
                && !cleanNumber.equals(number)) { // 只有当原始号码包含区号时才添加区号前缀
            prefix = areaCode + " ";
        }

        // 如果是中国大陆手机号格式且没有前缀
        if (StringUtils.isBlank(prefix) && number.matches(CN_PHONE_REGEX)) {
            return number.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }

        // 如果是香港手机号格式且没有前缀
        if (StringUtils.isBlank(prefix) && number.matches(CN_HK_PHONE_REGEX)) {
            // 香港手机号是8位，隐藏中间四位
            return number.substring(0, 2) + "****" + number.substring(6);
        }

        // 如果是台湾手机号格式且没有前缀
        if (StringUtils.isBlank(prefix) && number.matches(CN_TW_PHONE_REGEX)) {
            // 台湾手机号是9位，隐藏中间四位
            return number.substring(0, 2) + "****" + number.substring(6);
        }

        // 其他格式的手机号，根据长度进行隐藏
        return prefix + maskNumberByLength(number);
    }

    /**
     * 根据手机号长度隐藏中间部分
     * @param number 手机号
     * @return 隐藏后的手机号
     */
    private static String maskNumberByLength(String number) {
        int length = number.length();
        if (length <= 4) {
            return number; // 太短的号码不隐藏
        } else if (length <= 7) {
            // 隐藏中间两位
            int prefixLength = (length - 2) / 2;
            int suffixLength = length - prefixLength - 2;
            return number.substring(0, prefixLength) + "**" + number.substring(prefixLength + 2);
        } else {
            // 隐藏中间四位
            int prefixLength = (length - 4) / 2;
            int suffixLength = length - prefixLength - 4;
            return number.substring(0, prefixLength) + "****" + number.substring(prefixLength + 4);
        }
    }

    /**
     * 从手机号中提取区号
     * @param phoneNumber 手机号
     * @return 区号，如果无法提取则返回默认区号"86"
     */
    public static String extractAreaCode(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return AreaCodeConstants.CHINA_MAINLAND; // 默认中国大陆区号
        }

        // 标准化手机号
        String cleanNumber = normalizePhoneNumber(phoneNumber);

        // 检查是否符合中国大陆手机号格式
        if (cleanNumber.matches(CN_PHONE_REGEX)) {
            return AreaCodeConstants.CHINA_MAINLAND;
        }

        // 检查是否符合香港手机号格式
        if (cleanNumber.matches(CN_HK_PHONE_REGEX)) {
            return AreaCodeConstants.CHINA_HONGKONG;
        }

        // 检查是否符合台湾手机号格式
        if (cleanNumber.matches(CN_TW_PHONE_REGEX)) {
            return AreaCodeConstants.CHINA_TAIWAN;
        }

        // 处理国际格式的手机号，如 +86123456789
        if (cleanNumber.startsWith("+")) {
            Matcher matcher = INTERNATIONAL_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                String areaCode = matcher.group(1);
                // 去除前导零
                areaCode = areaCode.replaceFirst("^0+", "");
                return areaCode;
            }
        }

        // 处理带前导零的区号格式，如 0086123456789
        if (cleanNumber.matches("^00?\\d{1,4}.*")) {
            Matcher matcher = ZERO_PREFIX_PATTERN.matcher(cleanNumber);
            if (matcher.matches()) {
                String areaCode = matcher.group(1);
                // 去除前导零
                areaCode = areaCode.replaceFirst("^0+", "");
                return areaCode;
            }
        }

        // 处理可能是直接带区号的格式，如 86123456789 或 85291234567
        if (cleanNumber.length() > 10) {
            // 尝试提取常见区号
            for (String knownAreaCode : PHONE_REGEX_MAP.keySet()) {
                if (cleanNumber.startsWith(knownAreaCode)) {
                    return knownAreaCode;
                }
            }
        }

        // 默认返回中国大陆区号
        return AreaCodeConstants.CHINA_MAINLAND;
    }

    /**
     * 获取区号对应的国家或地区名称
     * @param areaCode 区号
     * @return 国家或地区名称，如果不存在则返回"未知地区"
     */
    public static String getCountryNameByAreaCode(String areaCode) {
        if (StringUtils.isBlank(areaCode)) {
            return "未知地区";
        }

        return AreaCodeConstants.COUNTRY_NAME_MAP.getOrDefault(areaCode, "未知地区");
    }
}
