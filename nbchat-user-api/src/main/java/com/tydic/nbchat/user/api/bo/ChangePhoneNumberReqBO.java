package com.tydic.nbchat.user.api.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class ChangePhoneNumberReqBO implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 电话号码
     */
    @NotEmpty
    @Size(min = 11, max = 11, message = "请输入正确手机号！")
    private String phone;
    /**
     * 验证码
     */
    @NotEmpty
    @Size(min = 4, max = 6, message = "请输入正确验证码！")
    private String code;

}
