package com.tydic.nbchat.user.api.bo.trade;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserBalanceAccountReqBO implements Serializable {
    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    private Integer score;
}
