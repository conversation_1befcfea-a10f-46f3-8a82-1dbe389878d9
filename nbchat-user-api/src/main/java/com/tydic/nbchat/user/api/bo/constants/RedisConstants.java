package com.tydic.nbchat.user.api.bo.constants;

/**
 * <AUTHOR>
 * @date 2023/03/29
 * @email <EMAIL>
 * @description 常量
 */
public class RedisConstants {
    /**
     * 登录验证码后缀
     */
    public final static  String LOGIN_VERIFYCODE_PREFIX = "nbchat-user:verify_code:";
    public final static  String TENANT_APPLY_PREFIX = "nbchat-user:tenant_apply:";
    /**
     * 视频制作完成通知
     */
    public static final String NBCHAT_NOTICE_PREFIX = "nbchat-user:notice:";
    public final static  String NBCHAT_NOTICE_FREQUENCY_PREFIX = "nbchat-user:notice_frequency:";

    /**
     * 登录验证码频率前缀
     */
    public final static  String LOGIN_VERIFYCODE_FREQUENCY_PREFIX = "nbchat-user:verify_code_frequency:";
    public final static  String TENANT_APPLY_FREQUENCY_PREFIX = "nbchat-user:tenant_apply_frequency:";

    /**
     * 微信签名前缀
     */
    public final static  String WX_SIGN = "nbchat-user:wx_sign";
    /**
     * 图形验证码
     */
    public final static  String CAPTCHA_CODE_PREFIX = "nbchat-user:captcha:";

    //用户vip缓存 key:租户:用户ID
    public final static  String USER_VIP_INFO_PREFIX = "nbchat-user:vip:";
    //用户注册加锁
    public final static  String USER_REG_LOCK = "nbchat-user:reg-lock:";
    //用户积分扣减加锁
    public final static  String USER_SCORE_DEDUCT_ACCELERATE = "nbchat-user:score_deduct:";
    //用户积分充值加锁
    public final static  String USER_SCORE_RECHARGE_ACCELERATE = "nbchat-user:score_recharge:";
    //用户积分退款任务加锁
    public final static  String USER_SCORE_REFUND_ACCELERATE = "nbchat-user:score_refund:";
    //用户积分过期任务加锁
    public final static  String USER_SCORE_EXPIRED_TIMER_LOCK = "nbchat-user:score_expired:lock";
    //用户vip充值任务加锁
    public final static  String USER_SCORE_TASK_TIMER_LOCK = "nbchat-user:score_task:lock";
    //账户密码错误计数
    public final static  String USER_PWD_ERROR_COUNT_PREFIX = "nbchat-user:pwd_error_count:";
    //账户锁定
    public final static  String USER_ACCOUNT_LOCK_PREFIX = "nbchat-user:account_lock:";
    //用户权益过期任务加锁
    public final static  String USER_RIGHTS_EXPIRE_LOCK_PREFIX = "nbchat-user:expire_lock";
    //用户登录session
    public final static String USER_SESSION_KEY_PREFIX = "nbchat-user:session:";
    //记录已退出登录的token: + sessionKey
    public static final String USER_TOKEN_DISABLE_PREFIX_KEY = "nbchat-user:token:expired:";

    //用户信息缓存
    public final static String USER_INFO_KEY_PREFIX = "nbchat-user:info:";
    public final static String USER_SETTING_KEY_PREFIX = "nbchat-user:settings:";
    public final static String USER_SETTING_BINDCODE_PREFIX = "nbchat-user:settings:bindcode:";

    //用户通知灰度名单
    public final static String NBCHAT_USER_NOTICE_BLANK_LIST_KEY = "nbchat-user:notice:gray_list";

    //用户助力缓存
    public static final String USER_ASSIST_KEY_PREFIX = "nbchat-user:user_id";

    // 点击量缓存键前缀
    public static final String CLICK_COUNT_PREFIX = "nbchat-update-log:click:";
    // 浏览量缓存键前缀
    public static final String VIEW_COUNT_PREFIX = "nbchat-update-log:view:";

}
