package com.tydic.nbchat.user.api;

import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.UserQueryReqBO;
import com.tydic.nbchat.user.api.bo.UserQueryRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 16:01:22
 */
public interface UserService  {

    /**
     * 发送手机验证码
     * @param @param authUserReqBO 身份验证用户要求博
     * @return @return {@link Rsp }
     */
    Rsp sendSMS(AuthUserReqBO authUserReqBO);

    /**
     * 发送体验租户成功短信
     * @param authUserReqBO
     * @return
     */
    Rsp sendSMSApply(AuthUserReqBO authUserReqBO);

    Rsp sendSMSCommon(AuthUserReqBO request);

    default String getKey(String tmplCode,String phone){
        return tmplCode + ":" + phone;
    }

    /**
     * 通过手机号查询用户
     * @param @param phone 电话
     * @return @return {@link Rsp }
     */
    Rsp getUserByPhone(String phone);

    /**
     * 查询所有用户
     * @param @param basePageInfo 基本信息页
     * @return @return {@link Rsp }
     */
    RspList<UserQueryRspBO> queryPage(UserQueryReqBO reqBO);

    /**
     * 更新用户信息
     * @param @param userBO 用户博
     * @return @return {@link Rsp }
     */
    Rsp updateById(UserBO userBO);
}

