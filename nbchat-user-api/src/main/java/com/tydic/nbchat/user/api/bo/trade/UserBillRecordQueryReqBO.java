package com.tydic.nbchat.user.api.bo.trade;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;

@Data
public class UserBillRecordQueryReqBO extends BasePageInfo implements Serializable {
    private String userId;
    @ParamNotEmpty
    private String tenantCode;
    private String type;
    private String remark;
    private String bizId;
    private String bizCode;
    private String tradeId;
    private String payType;
}
