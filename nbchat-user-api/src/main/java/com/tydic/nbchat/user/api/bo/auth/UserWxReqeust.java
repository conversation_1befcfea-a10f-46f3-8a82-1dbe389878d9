package com.tydic.nbchat.user.api.bo.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class UserWxReqeust implements Serializable {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 租户代码
     */
    private String tenantCode;
    /**
     * 当前所属平台
     */
    @NotBlank
    private String authType;
    /**
     * 微信小程序code
     */
    @NotBlank
    @Size(min = 1, max = 100)
    private String code;
}
