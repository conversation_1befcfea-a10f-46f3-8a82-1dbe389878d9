package com.tydic.nbchat.user.api.trade;

import com.tydic.nbchat.user.api.bo.trade.*;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface TradeBalanceApi {

    /**
     * 获取用户交易余额
     *
     * @param tenantCode
     * @param userId
     * @return
     */
    Rsp<UserBalanceBO> getBalance(String tenantCode, String userId);

    /**
     * 扣减用户余额
     * @param deduct
     * @return
     */
    Rsp<UserTradeResult> deduct(UserBalanceDeductReqBO deduct);

    /**
     * 数字人/声音 复刻专项扣减
     * @param deduct
     * @return
     */
    Rsp<UserTradeResult> tdhTrainDeduct(UserBalanceDeductReqBO deduct);

    /**
     * 退款
     * @param refund
     * @return
     */
    Rsp<UserTradeResult> refund(UserBalanceRefundReqBO refund);

    /**
     * 校验余额
     * @param check
     * @return
     */
    Rsp<Boolean> deductCheck(UserBalanceRechargeReqBO check);

    /**
     * 充值
     * @param recharge
     * @return
     */
    Rsp<UserTradeResult> recharge(UserBalanceRechargeReqBO recharge);

    /**
     * 创建用户余额账户
     * @param accountReqBO
     * @return
     */
    Rsp createBalanceAccount(UserBalanceAccountReqBO accountReqBO);
}
