package com.tydic.nbchat.user.api.bo.trade;

import com.tydic.nbchat.user.api.bo.eums.TradePayType;
import com.tydic.nbchat.user.api.bo.eums.TradeType;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserBalanceDeductReqBO extends BaseInfo implements Serializable {
    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    private String deductTenantCode;
    private String deductUserId;
    //消费数量
    @ParamNotNull
    private Integer amount;
    //指定消费金额
    private Integer score;
    @ParamNotEmpty
    private String type = TradeType.DEDUCT.getCode();
    private String remark;
    @ParamNotEmpty
    private String bizId;
    @ParamNotEmpty
    private String bizCode;
    private String bizName;
    private String tradeId;
    private Date tradeTime;
    //支付类型
    private String payType = TradePayType.ENTERPRISE.getCode();
}
