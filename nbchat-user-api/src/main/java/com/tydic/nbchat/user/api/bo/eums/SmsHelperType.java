package com.tydic.nbchat.user.api.bo.eums;

/**
 * <AUTHOR> <br>
 * @Description: SmsHelperType  <br>
 * @date 2021/4/27 10:40 上午  <br>
 * @Copyright tydic.com
 */
public enum SmsHelperType {

    ALI("0", "阿里短信平台"),
    ETONG("1", "e通讯");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private SmsHelperType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (SmsHelperType field : SmsHelperType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
