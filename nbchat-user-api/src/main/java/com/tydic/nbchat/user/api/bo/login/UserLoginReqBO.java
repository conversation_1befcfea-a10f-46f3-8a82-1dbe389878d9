package com.tydic.nbchat.user.api.bo.login;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/06/15
 * @email <EMAIL>
 * @description 用户登录请求参数
 */
@Data
public class UserLoginReqBO implements Serializable {
    /**
     * 电话
     */
    private String phone;
    /**
     * 用户名
     */
    @ParamNotEmpty(message = "用户名不能为空")
    private String username;
    /**
     * 密码
     */
    @ParamNotEmpty(message = "密码不能为空")
    private String password;
    //图形验证码
    private String captchaCode;
    //短信验证码
    private String smsCode;
    /**
     * 登录客户端
     */
    private String loginClient;

    @Override
    public String toString() {
        return "UserLoginReqBO{" +
                "phone='" + phone + '\'' +
                ", username='" + username + '\'' +
                ", captchaCode='" + captchaCode + '\'' +
                ", smsCode='" + smsCode + '\'' +
                ", loginClient='" + loginClient + '\'' +
                '}';
    }
}
