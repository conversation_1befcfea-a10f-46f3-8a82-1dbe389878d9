package com.tydic.nbchat.user.api.bo.eums;

public enum UserVipType {

    // 0 加油包 1 体验会员 2 高级会员 3 专业会员 4专业会员Max
    SCORE_PACKAGE("0", "算力点加油包", "算力点加油包"),
    EXPERIENCE("1", "体验会员", "体验会员"),
    ADVANCED("2", "高级会员", "高级会员"),
    PROFESSIONAL("3", "专业会员", "专业会员"),
    PROFESSIONAL_MAX("4", "专业会员Max", "专业会员Max"),
    HOUTAI("h", "后台", "后台")
    ;

    private String code;
    private String name;
    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getNameByCode(String code) {
        for (UserVipType field : UserVipType.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }

    public static String getDescByCode(String code) {
        for (UserVipType field : UserVipType.values()) {
            if (field.code.equals(code)) {
                return field.desc;
            }
        }
        return "";
    }

    UserVipType(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
