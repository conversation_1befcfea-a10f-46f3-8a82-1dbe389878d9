package com.tydic.nbchat.user.api.bo.setting;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 绑定码
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CreateSettingBindCodeRspBO implements Serializable {
    private String code;
    private String url;
    private Map<String,Object> settings;
}
