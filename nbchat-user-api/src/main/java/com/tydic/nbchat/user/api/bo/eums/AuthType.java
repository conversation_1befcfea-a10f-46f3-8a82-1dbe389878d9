package com.tydic.nbchat.user.api.bo.eums;


/**
 * <AUTHOR> <br>
 * @Description: AuthType  <br>
 * @date 2021/4/27 10:40 上午  <br>
 * @Copyright tydic.com
 */
public enum AuthType {

    USER_PASS("0", "用户名密码"),
    PHONE("1", "手机号"),
    WCHAT("2", "微信认证"),
    WCHAT_PC("3", "微信pc扫码登录"),
    WCHAT_PC_OFFICIAL("4", "微信公众号pc扫码登录"),
    WCHAT_MP_TDH("5", "数字人小程序一键登录"),
    WCHAT_MP_TDH_PHONE("6", "数字人小程序手机号登录");

    private String code;
    private String name;

    public Short getShotCode() {
        return Short.valueOf(code);
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private AuthType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (AuthType field : AuthType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
