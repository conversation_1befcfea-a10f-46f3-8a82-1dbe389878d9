package com.tydic.nbchat.user.api.bo.setting;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 用户设置
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class UserSettingContext implements Serializable {

    private String userId;
    private Map<Object,Object> settings;
}
