package com.tydic.nbchat.user.api;

import com.tydic.nbchat.user.api.bo.SendSmsRequest;
import com.tydic.nbchat.user.api.bo.notice.NoticeBO;
import com.tydic.nbchat.user.api.bo.notice.NoticeContext;

public interface NoticeApi {

    void notice(NoticeBO request) throws Exception;

    /**
     * 公众号通知
     * 构建充值通知参数：
     * 单号、充值金额、充值项目
     *
     * 构建视频通知参数：
     * 产品名称、商品名称、完成时间
     */
    void officialNotice(NoticeContext context);

    /**
     * 短信通知
     * @param request
     */
    void smsNotice(SendSmsRequest request);
}
