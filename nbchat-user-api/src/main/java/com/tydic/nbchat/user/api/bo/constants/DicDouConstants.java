package com.tydic.nbchat.user.api.bo.constants;

public class DicDouConstants {

    //新用户赠送算力点
    public static final int NEW_PUBLIC_USER_GIVE_DOU = 500;
    //企业新用户赠送算力点
    public static final int NEW_TENANT_USER_GIVE_DOU = 0;
    //消费备注
    public static final String DEDUCT_DOU_REMARK = "算力点消费";
    //vip免费体验
    public static final String VIP_EXPERIENCE_REMARK = "会员限免";
    //企业支付
    public static final String DEDUCT_DOU_ENTERPRISE_REMARK = "算力点消费(企业支付)";
    //退款
    public static final String REFUND_DOU_REMARK = "算力点退费";
    //充值备注
    public static final String RECHARGE_DOU_REMARK = "算力点充值";
    //赠送备注 新用户赠送
    public static final String GIVE_DOU_REMARK = "新用户赠送";
    //过期描述
    public static final String EXPIRED_DOU_REMARK = "算力点过期";
    //会员赠送的豆，过期时间为31天
    public static final int GIVE_DOU_EXPIRE_DAY = 31;
    //默认期限 3年
    public static final int DEFAULT_EXPIRE_YEAR = 1;
    //vip算力有效期 90 天
    public static final int VIP_DOU_EXPIRE_DAY = 90;
    //算力充值有效期
    public static final int RECHARGE_DOU_EXPIRE_DAY = 3 * 365;

}
