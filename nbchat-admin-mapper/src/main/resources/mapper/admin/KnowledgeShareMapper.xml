<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.KnowledgeShareMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo">
    <id column="sentence_share_id" jdbcType="VARCHAR" property="sentenceShareId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="sentence_id" jdbcType="VARCHAR" property="sentenceId" />
    <result column="share_key" jdbcType="VARCHAR" property="shareKey" />
    <result column="expired_date" jdbcType="TIMESTAMP" property="expiredDate" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
  </resultMap>
  <sql id="Base_Column_List">
    sentence_share_id, tenant_code, user_id, sentence_id, share_key, expired_date, created_at, 
    share_url, is_valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from nbchat_knowledge_share
    where sentence_share_id = #{sentenceShareId,jdbcType=VARCHAR}
  </select>

  <select id="queryByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_knowledge_share
    where sentence_share_id = #{sentenceShareId} and expired_date > now()
    <if test="shareKey != null and shareKey != ''">
      and share_key = #{shareKey}
    </if>
  </select>

  <select id="queryById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_knowledge_share
    where sentence_share_id = #{sentenceShareId} and expired_date > now()
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from nbchat_knowledge_share
    where sentence_share_id = #{sentenceShareId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo">
    insert into nbchat_knowledge_share (sentence_share_id, tenant_code, user_id, 
      sentence_id, share_key, expired_date, 
      created_at, share_url, is_valid
      )
    values (#{sentenceShareId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{sentenceId,jdbcType=VARCHAR}, #{shareKey,jdbcType=VARCHAR}, #{expiredDate,jdbcType=TIMESTAMP}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{shareUrl,jdbcType=VARCHAR}, #{isValid,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo">
    insert into nbchat_knowledge_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sentenceShareId != null">
        sentence_share_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="sentenceId != null">
        sentence_id,
      </if>
      <if test="shareKey != null">
        share_key,
      </if>
      <if test="expiredDate != null">
        expired_date,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="shareUrl != null">
        share_url,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sentenceShareId != null">
        #{sentenceShareId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="sentenceId != null">
        #{sentenceId,jdbcType=VARCHAR},
      </if>
      <if test="shareKey != null">
        #{shareKey,jdbcType=VARCHAR},
      </if>
      <if test="expiredDate != null">
        #{expiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo">
    update nbchat_knowledge_share
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="sentenceId != null">
        sentence_id = #{sentenceId,jdbcType=VARCHAR},
      </if>
      <if test="shareKey != null">
        share_key = #{shareKey,jdbcType=VARCHAR},
      </if>
      <if test="expiredDate != null">
        expired_date = #{expiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="shareUrl != null">
        share_url = #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
    </set>
    where sentence_share_id = #{sentenceShareId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo">
    update nbchat_knowledge_share
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      sentence_id = #{sentenceId,jdbcType=VARCHAR},
      share_key = #{shareKey,jdbcType=VARCHAR},
      expired_date = #{expiredDate,jdbcType=TIMESTAMP},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=CHAR}
    where sentence_share_id = #{sentenceShareId,jdbcType=VARCHAR}
  </update>
</mapper>