<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysLoginLogMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysLoginLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="auth_type" property="authType" jdbcType="CHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
        <result column="login_type" property="loginType" jdbcType="CHAR"/>
        <result column="login_msg" property="loginMsg" jdbcType="VARCHAR"/>
        <result column="login_client" property="loginClient" jdbcType="VARCHAR"/>
        <result column="switch_tenant" property="switchTenant" jdbcType="VARCHAR"/>
        <result column="vip_type" property="vipType" jdbcType="CHAR"/>
        <result column="vip_status" property="vipStatus" jdbcType="CHAR"/>
        <result column="reg_time" property="regTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_code, user_id, user_name, auth_type, login_time, login_ip, login_type,
    login_msg, login_client, switch_tenant, vip_type, vip_status, reg_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_login_log
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysLoginLog">
        insert into sys_login_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="authType != null">
                auth_type,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="loginIp != null">
                login_ip,
            </if>
            <if test="loginType != null">
                login_type,
            </if>
            <if test="loginMsg != null">
                login_msg,
            </if>
            <if test="loginClient != null">
                login_client,
            </if>
            <if test="switchTenant != null">
                switch_tenant,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="vipStatus != null">
                vip_status,
            </if>
            <if test="regTime != null">
                reg_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="authType != null">
                #{authType,jdbcType=CHAR},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginIp != null">
                #{loginIp,jdbcType=VARCHAR},
            </if>
            <if test="loginType != null">
                #{loginType,jdbcType=CHAR},
            </if>
            <if test="loginMsg != null">
                #{loginMsg,jdbcType=VARCHAR},
            </if>
            <if test="loginClient != null">
                #{loginClient,jdbcType=VARCHAR},
            </if>
            <if test="switchTenant != null">
                #{switchTenant,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipStatus != null">
                #{vipStatus,jdbcType=CHAR},
            </if>
            <if test="regTime != null">
                #{regTime,jdbcType=TIMESTAMP},
            </if>
        </trim>

    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysLoginLog">
        update sys_login_log
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="authType != null">
                auth_type = #{authType,jdbcType=CHAR},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginIp != null">
                login_ip = #{loginIp,jdbcType=VARCHAR},
            </if>
            <if test="loginType != null">
                login_type = #{loginType,jdbcType=CHAR},
            </if>
            <if test="loginMsg != null">
                login_msg = #{loginMsg,jdbcType=VARCHAR},
            </if>
            <if test="loginClient != null">
                login_client = #{loginClient,jdbcType=VARCHAR},
            </if>
            <if test="switchTenant != null">
                switch_tenant = #{switchTenant,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipStatus != null">
                vip_status = #{vipStatus,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectTdhLoginCount" resultType="java.lang.Integer">
        select ifnull(count(1),0) from sys_login_log where tenant_code = #{param1,jdbcType=VARCHAR} and user_id = #{param2,jdbcType=VARCHAR} and login_client = #{param3}
    </select>
</mapper>