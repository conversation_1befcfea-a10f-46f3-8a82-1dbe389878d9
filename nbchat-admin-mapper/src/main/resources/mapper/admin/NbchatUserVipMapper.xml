<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatUserVipMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatUserVip">
        <!--@mbg.generated-->
        <!--@Table nbchat_user_vip-->
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="vip_type" jdbcType="CHAR" property="vipType"/>
        <result column="vip_status" jdbcType="CHAR" property="vipStatus"/>
        <result column="firstVipTime" jdbcType="TIMESTAMP" property="firstVipTime"/>
        <result column="lastVipTime" jdbcType="TIMESTAMP" property="lastVipTime"/>
        <result column="totalAmount" jdbcType="INTEGER" property="totalAmount"/>
        <result column="vipDays" jdbcType="INTEGER" property="vipDays"/>
        <result column="vipTimes" jdbcType="INTEGER" property="vipTimes"/>
    </resultMap>
    <select id="selectRpByUserId" resultMap="BaseResultMap">
        select nuv.tenant_code,
               nuv.user_id,
               nuv.vip_type,
               nuv.vip_status,
               ifnull(nuvl.vip_days, 0)     as vipDays,
               ifnull(nuvl.vip_times, 0)    as vipTimes,
               ifnull(nuvl.total_amount, 0) as totalAmount,
               nuv.vip_start                as firstVipTime,
               nuv.vip_end                  as lastVipTime
        from nbchat_user_vip nuv
                 left join
             (select tenant_code,
                     user_id,
                     vip_type,
                     sum(timestampdiff(day, start_time, end_time)) as vip_days,
                     count(0)                                      as vip_times,
                     sum(amount)                                   as total_amount
              from nbchat_user_vip_log
              where is_refund = '0'
              group by tenant_code, user_id, vip_type) nuvl
             on nuv.tenant_code = nuvl.tenant_code collate utf8mb4_general_ci
                 and nuv.user_id = nuvl.user_id collate utf8mb4_general_ci
                 and nuv.vip_type = nuvl.vip_type collate utf8mb4_general_ci
    </select>
</mapper>
