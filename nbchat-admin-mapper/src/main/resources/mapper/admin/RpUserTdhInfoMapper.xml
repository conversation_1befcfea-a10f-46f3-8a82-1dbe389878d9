<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserTdhInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserTdhInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_tdh_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="tdh_type" jdbcType="CHAR" property="tdhType"/>
        <result column="launch_times" jdbcType="INTEGER" property="launchTimes"/>
        <result column="actual_times" jdbcType="INTEGER" property="actualTimes"/>
        <result column="total_amount" jdbcType="INTEGER" property="totalAmount"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        tenant_code,
        user_id,
        tdh_type,
        launch_times,
        actual_times,
        total_amount,
        update_time
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.RpUserTdhInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_tdh_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            tenant_code,
            user_id,
            tdh_type,
            launch_times,
            <if test="actualTimes != null">
                actual_times,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{tenantCode,jdbcType=VARCHAR},
            #{userId,jdbcType=VARCHAR},
            #{tdhType,jdbcType=CHAR},
            #{launchTimes,jdbcType=INTEGER},
            <if test="actualTimes != null">
                #{actualTimes,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=INTEGER},
            </if>
            current_timestamp
        </trim>
    </insert>
    <update id="updateSelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserTdhInfo">
        <!--@mbg.generated-->
        update rp_user_tdh_info
        <set>
            <if test="launchTimes != null">
                launch_times = launch_times + #{launchTimes,jdbcType=INTEGER},
            </if>
            <if test="actualTimes != null">
                actual_times = actual_times + #{actualTimes,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                total_amount = total_amount + #{totalAmount,jdbcType=INTEGER},
            </if>
            update_time = current_timestamp,
        </set>
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        and tdh_type = #{tdhType,jdbcType=CHAR}
    </update>
    <select id="selectRpByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rp_user_tdh_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="findOneByTenantCodeAndUserIdAndTdhType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rp_user_tdh_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        and tdh_type = #{tdhType,jdbcType=VARCHAR}
        limit 1
    </select>
    <select id="selectAllTdhInfo" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               case
                   when customize_type = '2d_mtk' then '0'
                   when customize_type = '2.5d_mtk' then '1'
                   when customize_type = 'audio' then '2'
                   end                                                               as tdh_type,
               count(0)                                                              as launch_times,
               sum(if(customize_status = '4', 1, 0))                                 as actual_times,
               sum(if(customize_status in ('2', '3', '4'), ifnull(pay_price, 0), 0)) as total_amount
        from tdh_customize_record
        where customize_status != '5'
          and customize_type in ('2d_mtk', '2.5d_mtk', 'audio')
        group by tenant_code, user_id, customize_type
    </select>
    <select id="selectTdhInfoByUserId" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               case
                   when customize_type = '2d_mtk' then '0'
                   when customize_type = '2.5d_mtk' then '1'
                   when customize_type = 'audio' then '2'
                   end                                                               as tdh_type,
               count(0)                                                              as launch_times,
               sum(if(customize_status = '4', 1, 0))                                 as actual_times,
               sum(if(customize_status in ('2', '3', '4'), ifnull(pay_price, 0), 0)) as total_amount
        from tdh_customize_record
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=VARCHAR}
          and customize_status != '5'
          and customize_type in ('2d_mtk', '2.5d_mtk', 'audio')
        group by customize_type
    </select>
    <insert id="insertForAll">
        insert into rp_user_tdh_info (tenant_code, user_id, tdh_type, launch_times, actual_times, total_amount, update_time)
        select tenant_code,
               user_id,
               case
                   when customize_type = '2d_mtk' then '0'
                   when customize_type = '2.5d_mtk' then '1'
                   when customize_type = 'audio' then '2'
                   end,
               count(0),
               sum(if(customize_status = '4', 1, 0)),
               sum(if(customize_status in ('2', '3', '4'), ifnull(pay_price, 0), 0)),
               current_timestamp
        from tdh_customize_record
        where customize_status != '5'
          and customize_type in ('2d_mtk', '2.5d_mtk', 'audio')
        group by tenant_code, user_id, customize_type
    </insert>
    <delete id="deleteAll">
        delete
        from rp_user_tdh_info
    </delete>
</mapper>
