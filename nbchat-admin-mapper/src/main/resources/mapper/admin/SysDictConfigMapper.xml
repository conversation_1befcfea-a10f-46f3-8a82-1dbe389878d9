<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysDictConfigMapper">

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysDictConfig">
        <id column="dict_id" jdbcType="VARCHAR" property="dictId" />
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
        <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
        <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
        <result column="dict_desc" jdbcType="VARCHAR" property="dictDesc" />
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
        <result column="order_index" jdbcType="INTEGER" property="orderIndex"/>
        <result column="crt_time" jdbcType="TIMESTAMP" property="crtTime" />
        <result column="is_valid" jdbcType="VARCHAR" property="isValid" />
    </resultMap>

    <sql id="Base_Column_List">
        dict_id, dict_code, dict_name, dict_value, dict_desc, channel_code, tenant_code, order_index,crt_time,is_valid
    </sql>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from sys_dict_config
        where dict_code = #{dictCode,jdbcType=VARCHAR}
                and (tenant_code = #{tenantCode,jdbcType=VARCHAR}
                         or tenant_code = "" )
                and is_valid = '1' order by order_index,crt_time
    </select>

    <!--根据字典dict_code分组查询-->
    <select id="selectByGroup" resultType="com.tydic.nbchat.admin.mapper.po.SysDictConfigResult">
        SELECT
            dict_id as dictId,
            dict_code as dictCode,
            dict_name as dictName,
            dict_value as dictValue,
            dict_desc as dictDesc,
            COUNT(*) as dictCount
        FROM sys_dict_config
        WHERE is_valid = '1'
          <if test="dictName != null and dictName != ''">
          AND (dict_name LIKE CONCAT('%', #{dictName}, '%'))
          </if>
          <if test="dictCode != null and dictCode != ''">
          AND (dict_code LIKE CONCAT('%', #{dictCode}, '%'))
          </if>
          <if test="dictDesc != null and dictDesc != ''">
          AND (dict_desc LIKE CONCAT('%', #{dictDesc}, '%'))
          </if>
        GROUP BY dict_code
        ORDER BY order_index, crt_time
    </select>

    <!--删除指定dict_code的所有数据-->
    <delete id="deleteByDictCode">
        delete from sys_dict_config where dict_code = #{dictCode,jdbcType=VARCHAR}
    </delete>

    <!--根据dict_code进行批量更新数据-->
    <insert id="insertDictCode" parameterType="java.util.List">
        insert into sys_dict_config(dict_code, dict_name, dict_value, dict_desc, channel_code, tenant_code, order_index, crt_time, is_valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictCode, jdbcType=VARCHAR},
            #{item.dictName, jdbcType=VARCHAR},
            #{item.dictValue, jdbcType=VARCHAR},
            #{item.dictDesc, jdbcType=VARCHAR},
            #{item.channelCode, jdbcType=VARCHAR},
            #{item.tenantCode, jdbcType=VARCHAR},
            #{item.orderIndex, jdbcType=INTEGER},
            NOW(),
            '1')
        </foreach>
    </insert>

    <!--批量更新字典列表的状态-->
    <update id="updateDictStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE sys_dict_config
            SET is_valid = #{item.isValid, jdbcType=VARCHAR}
            WHERE dict_code = #{item.dictCode, jdbcType=VARCHAR}
        </foreach>
    </update>


</mapper>