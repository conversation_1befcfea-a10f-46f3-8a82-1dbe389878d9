<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysTenantSubsystemMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem">
    <!--@mbg.generated-->
    <!--@Table sys_tenant_subsystem-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="subsystem" jdbcType="VARCHAR" property="subsystem" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tpl_code" jdbcType="VARCHAR" property="tplCode" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_code, subsystem, remark, tpl_code, ext_info, create_time, update_time, 
    `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_tenant_subsystem
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByTenantCode" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from sys_tenant_subsystem
    where tenant_code = #{tenantCode,jdbcType=VARCHAR}
  </select>
  <!--根据租户编码和子系统查询-->
  <select id="selectByTenantCodeAndSubsystem" resultMap="BaseResultMap">
    SELECT * FROM sys_tenant_subsystem
    WHERE tenant_code = #{tenantCode}
    <if test="platformCode != null and platformCode != ''">
      AND subsystem = #{platformCode}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from sys_tenant_subsystem
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_tenant_subsystem (tenant_code, subsystem, remark, 
      tpl_code, ext_info, create_time, 
      update_time, `status`)
    values (#{tenantCode,jdbcType=VARCHAR}, #{subsystem,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{tplCode,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_tenant_subsystem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="subsystem != null">
        subsystem,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tplCode != null">
        tpl_code,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="subsystem != null">
        #{subsystem,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem">
    <!--@mbg.generated-->
    update sys_tenant_subsystem
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="subsystem != null">
        subsystem = #{subsystem,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        tpl_code = #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem">
    <!--@mbg.generated-->
    update sys_tenant_subsystem
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      subsystem = #{subsystem,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      tpl_code = #{tplCode,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_tenant_subsystem
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=INTEGER}
            </if>
            <if test="tenantCode != null">
                and tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="subsystem != null">
                and subsystem=#{subsystem,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and remark=#{remark,jdbcType=VARCHAR}
            </if>
            <if test="tplCode != null">
                and tpl_code=#{tplCode,jdbcType=VARCHAR}
            </if>
            <if test="extInfo != null">
                and ext_info=#{extInfo,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                and `status`=#{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_tenant_subsystem
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="subsystem = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.subsystem != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.subsystem,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplCode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tplCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ext_info = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extInfo != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.extInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_tenant_subsystem
    (tenant_code, subsystem, remark, tpl_code, ext_info, create_time, update_time, `status`
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantCode,jdbcType=VARCHAR}, #{item.subsystem,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.tplCode,jdbcType=VARCHAR}, #{item.extInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_tenant_subsystem where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
  <delete id="deleteByTenantCode">
    <!--@mbg.generated-->
    delete from sys_tenant_subsystem where tenant_code = #{tenantCode,jdbcType=VARCHAR}
  </delete>
</mapper>