<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.ScriptSQLMapper">

    <insert id="insertPost" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO" >
            INSERT INTO nbchat.sys_post ( tenant_code, post_name, post_desc, order_index, create_time)
        VALUES
            (#{tenantCode}, '默认岗位', '默认岗位描述', 0, now())

    </insert>

    <insert id="insertExamCategory" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        INSERT INTO nbchat.sys_tree_category (cate_id, parent_id, user_id, tenant_code, cate_name, cate_level, ancestors, cate_source, busi_type, create_time, create_by, update_time, update_by, is_valid, order_index)
        VALUES
            (#{id}, '-1', '1', #{tenantCode}, '入职须知', 1, #{id}, '0', 'exam_bank', now(), '', null, '', '1', 1),
            (#{id} + 1, '-1', '1', #{tenantCode}, '教育培训', 1, #{id} + 1, '0', 'exam_bank', now(), '', null, '', '1', 1),
            (#{id} + 2, '-1', '1', #{tenantCode}, '办公效率', 1, #{id} + 2, '0', 'exam_bank', now(), '', null, '', '1', 1),
            (#{id} + 3, '-1', '1', #{tenantCode}, '职业发展', 1, #{id} + 3, '0', 'exam_bank', now(), '', null, '', '1', 1),
            (#{id} + 4, '-1', '1', #{tenantCode}, '专业技能', 1, #{id} + 4, '0', 'exam_bank', now(), '', null, '', '1', 1),
            (#{id} + 5, '-1', '1', #{tenantCode}, '其他', 1, #{id} + 5, '0', 'exam_bank', now(), '', null, '', '1', 1)
    </insert>

    <insert id="insertCategory" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        <!-- 类目 -->
        INSERT INTO `nbchat`.`nbchat_train_course_category` (`cate_id`, `cate_name`, `cate_name_nick`, `parent_id`, `create_time`, `tenant_code`, `is_valid`, `cate_level`, `order_index`)
        VALUES
            (#{id},     '业务培训',  'YE WU PEI XUN',      '-1', now(), #{tenantCode}, '1', 1, 5),
            (#{id} + 1, '营销技能',  'YING XIAO JI NENG',   '-1', now(), #{tenantCode}, '1', 1, 3),
            (#{id} + 2, '其他',     'QI TA',               '-1', now(), #{tenantCode}, '1', 1, 3),
            (#{id} + 3, '业务培训',  'YE WU PEI XUN',       #{id}, now(), #{tenantCode}, '1', 2, 5),
            (#{id} + 4, '营销技能',  'YING XIAO JI NENG',   #{id} + 1, now(), #{tenantCode}, '1', 2, 3),
            (#{id} + 5, '其他',     'QI TA',               #{id} + 2, now(), #{tenantCode}, '1', 2, 3);
    </insert>
    <insert id="insertCourse" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        <!--  课程 -->
        INSERT INTO `nbchat`.`nbchat_train_course` (`course_id`, `course_name`, `course_desc`, `course_source`, `voice_url`, `labels`, `category`, `img_avatar`, `tenant_code`, `user_id`, `create_time`, `update_time`, `is_show`, `is_valid`, `order_index`, `ext_info`, `class_hour`, `video_url`, `video_img`, `category2`, `course_state`, `step_state`, `task_state`, `course_file_url`, `scene_state`, `test_paper_state`) VALUES (concat(#{tenantCode},'311606929786589184'), '迪易学产品介绍', '迪易学是由天源迪科打造的一款智能生成式企业培训产品。目前已经帮助百余个企业批量培养优秀人才，数千名用户实现了能力提升。迪易学通过AI技术有效解决企业培训中资料更新快，课程迭代频繁，培训成本高等问题。可广泛赋能通信行业，政府与公共事业，金融行业，互联网行业等开展企业内训。', 0, 'https://chatfiles.tydiczt.com/files/202309/925d853338fd414c0662994236f1c6fb.wav', '天源迪科智能培训产品介绍_V2.0.2_20230803.pdf', #{id}, 'https://chatfiles.tydiczt.com/files/202309/4ef3b5290a902caab2c0e1bdc60803dc.jpg', #{tenantCode}, '256179896886550528', now(), now(), '1', '1', 0, '', 7.0, 'https://chatfiles.tydiczt.com/files/202309/25059b9156bd515bcca340376ce9ad6c.mp4', 'https://chatfiles.tydiczt.com/files/202309/f61a6ffa9e02a3e45b7730b8cad846f1.jpg', #{id} + 3, '2', '5', NULL, 'https://chatfiles.tydiczt.com/files/202309/da2bcfbe89ea737dede9ab571a69a732.pdf', '2', '2');
        <!--目录 -->
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993911648256'), concat(#{tenantCode},'311606929786589184'), '', '', '', '10分钟极速生成课程', 1, 1, '-1', concat('[\"',#{tenantCode},'314506993903259648\"]'), now(), '1', 4);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993945202688'), concat(#{tenantCode},'311606929786589184'), '', '', '', '实战场景演练提升营销技能', 1, 5, '-1', concat('[\"',#{tenantCode},'314506993936814080\"]'), now(), '1', 4);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993982951424'), concat(#{tenantCode},'311606929786589184'), '', '', '', '数字人虚拟讲师授课', 1, 10, '-1', concat('[\"',#{tenantCode},'314506993978757120\"]'), now(), '1', 2);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994024894464'), concat(#{tenantCode},'311606929786589184'), '', '', '', '产品简介', 1, 15, '-1', concat('[\"',#{tenantCode},'314506994016505856\"]'), now(), '1', 1);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994041671680'), concat(#{tenantCode},'311606929786589184'), '', '', '', '大语言模型助力一站式课程生成', 1, 17, '-1', concat('[\"',#{tenantCode},'314506994037477376\"]'), now(), '1', 2);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994075226112'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多端灵活学习', 1, 21, '-1', concat('[\"',#{tenantCode},'314506994066837504\"]'), now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994104586240'), concat(#{tenantCode},'311606929786589184'), '', '', '', '支持信创及企业私有化部署', 1, 26, '-1', concat('[\"',#{tenantCode},'314506994100391936\"]'), now(), '1', 1);

    </insert>
    <insert id="insertCatalog" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993915842560'), concat(#{tenantCode},'311606929786589184'), '', '', '', '一键生成课程内容', 2, 2, concat(#{tenantCode},'314506993911648256'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993924231168'), concat(#{tenantCode},'311606929786589184'), '', '', '', '智能设计对练场景', 2, 3, concat(#{tenantCode},'314506993911648256'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993928425472'), concat(#{tenantCode},'311606929786589184'), '', '', '', '快速构建培训题库', 2, 4, concat(#{tenantCode},'314506993911648256'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993953591296'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多形式对话', 2, 6, concat(#{tenantCode},'314506993945202688'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993957785600'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多场景模拟', 2, 7, concat(#{tenantCode},'314506993945202688'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993966174208'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多维度打分', 2, 8, concat(#{tenantCode},'314506993945202688'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993970368512'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多方位指导', 2, 9, concat(#{tenantCode},'314506993945202688'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993991340032'), concat(#{tenantCode},'311606929786589184'), '', '', '', '企业形象定制', 2, 11, concat(#{tenantCode},'314506993982951424'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506993999728640'), concat(#{tenantCode},'311606929786589184'), '', '', '', '模版式视频生成', 2, 12, concat(#{tenantCode},'314506993982951424'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994003922944'), concat(#{tenantCode},'311606929786589184'), '', '', '', '数字人全程讲解无需剪辑', 2, 13, concat(#{tenantCode},'314506993982951424'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994012311552'), concat(#{tenantCode},'311606929786589184'), '', '', '', '视频分享灵活对接', 2, 14, concat(#{tenantCode},'314506993982951424'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994029088768'), concat(#{tenantCode},'311606929786589184'), '', '', '', '简介', 2, 16, concat(#{tenantCode},'314506994024894464'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994050060288'), concat(#{tenantCode},'311606929786589184'), '', '', '', '课纲自动生成', 2, 18, concat(#{tenantCode},'314506994041671680'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994054254592'), concat(#{tenantCode},'311606929786589184'), '', '', '', '课程内容自动生成', 2, 19, concat(#{tenantCode},'314506994041671680'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994062643200'), concat(#{tenantCode},'311606929786589184'), '', '', '', '试题自动生成', 2, 20, concat(#{tenantCode},'314506994041671680'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994079420416'), concat(#{tenantCode},'311606929786589184'), '', '', '', '多端学习', 2, 22, concat(#{tenantCode},'314506994075226112'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994083614720'), concat(#{tenantCode},'311606929786589184'), '', '', '', '碎片化学习', 2, 23, concat(#{tenantCode},'314506994075226112'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994092003328'), concat(#{tenantCode},'311606929786589184'), '', '', '', '灵活选择学习方式', 2, 24, concat(#{tenantCode},'314506994075226112'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994096197632'), concat(#{tenantCode},'311606929786589184'), '', '', '', '即学即考', 2, 25, concat(#{tenantCode},'314506994075226112'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994108780544'), concat(#{tenantCode},'311606929786589184'), '', '', '', '应用私有化', 2, 27, concat(#{tenantCode},'314506994104586240'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994112974848'), concat(#{tenantCode},'311606929786589184'), '', '', '', '数据不外漏', 2, 28, concat(#{tenantCode},'314506994104586240'), '', now(), '1', 0);
        INSERT INTO `nbchat`.`nbchat_train_catalog` (`catalog_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `catalog_title`, `catalog_level`, `catalog_index`, `parent_id`, `section_ids`, `create_time`, `is_valid`, `train_count`) VALUES (concat(#{tenantCode},'314506994121363456'), concat(#{tenantCode},'311606929786589184'), '', '', '', '底座国产化', 2, 29, concat(#{tenantCode},'314506994104586240'), '', now(), '1', 0);

    </insert>

    <insert id="insertSection" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        <!--章节-->
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506993903259648'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '【一键生成课程内容】迪易学通过用户上传的课程资料，大模型进行自动解析理解资料内容，智能生成课纲、课程简介、学习内容。\n【智能设计对练场景】迪易学基于AI大模型能力实现实战演练的一键生成，可以模拟客户以及真实业务场景。针对实战演练的回复进行实时对练综合评分，以及标准话术指导。\n【快速构建培训题库】AI大模型赋能快速构建培训题库，并且实时自动判卷，针对学员能力短板，还可推荐课程任务，实现个性化学习方案，降低培训成本。', 1, 'https://chatfiles.tydiczt.com/files/202309/cd7d56b11ccd37c7a8f8a80a18ba7f25.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/8415bbe1316cd5ef97a7e9b794ec6637.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506993936814080'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '\n【多形式对话】员工可以通过文字的方式或者是纯语音对话方式来进行场景演练，多种对话形式满足不同学员需求。\n【多场景模拟】实战演练采用一问一答的方式进行沟通模拟，增加了实战场景演练的真实性，让学员在实际操作中更好地理解和掌握所学知识。\n【多维度打分】大模型针对每一条学员回复的内容进行正确性、专业性、语气等多维度智能打分，可以让学员迅速了解自己对知识的掌握程度。\n【多方位指导】对于场景模拟中回复的内容，可以查看话术指导，帮助学员纠正知识遗漏点，提升营销能力。', 2, 'https://chatfiles.tydiczt.com/files/202309/904577f50598dd359bcc30a885b005a7.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/023c870a3951e0f3e877659856b7cc46.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506993978757120'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '【企业形象定制】企业可以定制专属的虚拟形象，更具企业个性化，提升品牌形象。\n【模版式视频生成】多模版样式生成教学视频，促进高质量视频制作，提升视频效果的专业性和规范性。\n【数字人全程讲解无需剪辑】全视频采用数字人讲解生成，无需人工进行剪辑拼接，减少人工成本。\n【视频分享灵活对接】教学视频成品可以直接对接到迪易学课程内容中的视频上传接口，或视频下载后灵活对接多平台。', 3, 'https://chatfiles.tydiczt.com/files/202309/6f473b43c17d1d6032726131fa3b9354.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/024cdf501667dc25d64d79d80be479a7.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506994016505856'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '{\"title\":\"\",\"content\":\"\",\"subtitle\":[{\"name\":\"简介\",\"content\":\"迪易学是由天源迪科打造的一款智能生成式企业培训产品。目前已经帮助百余个企业批量培养优秀人才，数千名用户实现了能力提升。迪易学通过AI技术有效解决企业培训中资料更新快，课程迭代频繁，培训成本高等问题。可广泛赋能通信行业，政府与公共事业，金融行业，互联网行业等开展企业内训。\"}]}', 4, 'https://chatfiles.tydiczt.com/files/202309/d5b4d3d1f31209b351112eb8c4b58409.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/a08f184633bef2b368f0fd4de84a3806.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506994037477376'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '【课纲自动生成】大语言模型通过对资料内容的提取、分析、理解，梳理出资料的内容逻辑，并自动生成课纲，减少人工梳理的过程，提升课程制作效率。\n【课程内容自动生成】大语言模型梳理课纲后，根据资料内容进行识别、提炼、总结，并按照课纲的逻辑自动生成课程内容。\n【试题自动生成】大语言模型根据资料内容提炼重要知识点，并根据知识点编写考题，自动生成具备问题、选项、正确答案的试题库。再也不用人工绞尽脑汁编写试题了。', 5, 'https://chatfiles.tydiczt.com/files/202309/b8800e1a6d08f8a71578e71c58b6f907.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/e94f0f9e235c04d4e8a7f22e2799ed5c.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506994066837504'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '【多端学习】迪易学支持多端登陆，无论学员使用小程序、H5、PC端还是APP，都可以轻松学习，随时随地开启学习之旅。 \n【碎片化学习】让学员的学习不再受到时间和空间的限制。您可以在任何时间、任何地点开始学习，灵活安排学习时间和进度，让学习变得更加自由和高效。 \n【灵活学习方式】迪易学为您提供多种学习方式，包括视频学习、资料学习、实战模拟等，学员可以根据自己的兴趣和学习需求进行选择。 \n【即学即考】让学员在完成学习后立即进入考试环节，帮助学员更好地了解自己的学习情况和薄弱环节，从而更好地调整学习计划和提升学习效果。', 6, 'https://chatfiles.tydiczt.com/files/202309/76afde230b59d8319a09ea749f684517.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/7ea704dbc3735cfa3da692d1c6c8be00.mp4', NULL, 0, 0, 0);
        INSERT INTO `nbchat`.`nbchat_train_sections` (`section_id`, `course_id`, `file_id`, `tenant_code`, `user_id`, `content`, `sections_index`, `voice_url`, `create_time`, `train_count`, `is_valid`, `video_url`, `video_img`, `view_num`, `star_num`, `zan_num`) VALUES (concat(#{tenantCode},'314506994100391936'), concat(#{tenantCode},'311606929786589184'), '', #{tenantCode}, NULL, '【应用私有化】通过将企业的核心业务数据和敏感信息保存在私有环境中，可以提高应用程序的可靠性和性能，同时更好地满足企业的个性化需求。\n【数据不外漏】避免数据在公共云端泄露的风险，更好地保护企业的敏感信息和核心业务数据，降低泄露风险，增强企业的信任度和声誉。\n【底座国产化】大语言模型与数字人模型私有化底座平台部署，国产化底座更加安全可靠。', 7, 'https://chatfiles.tydiczt.com/files/202309/b0d1a247a2f86f874148b689719a6b99.wav', now(), 0, '1', 'https://chatfiles.tydiczt.com/files/202309/cbb94d9a9b09fbbbaebca14f6dd9e402.mp4', NULL, 0, 0, 0);

    </insert>

    <insert id="insertConfig" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        <!--对话配置管理-->
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue_manage` ( `course_id`, `tenant_code`, `dialogue_num`, `score`, `role`, `create_time`, `create_user`, `update_time`) VALUES ( concat(#{tenantCode},'311606929786589184'), #{tenantCode}, 4, 90, '[\"user\",\"assistant\"]', now(), '254359302079324160', NULL);
        <!--考试配置管理-->
        INSERT INTO `nbchat`.`nbchat_exam_test_paper` ( `course_id`, `tenant_code`, `exam_state`, `test_num`, `passing_score`, `test_type`, `train_state`, `create_time`, `create_user`, `update_time`) VALUES ( concat(#{tenantCode},'311606929786589184'), #{tenantCode}, NULL, 5, '90', '2', '0', now(), '256179896886550528', now());

    </insert>

    <insert id="insertDialogue" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'user', now(), '1', '您好，请问您对我们的课程有什么了解吗？');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'assistant', now(), '1', '我们的课程是通过大模型技术自动生成的，能够快速提供高质量的教学内容。');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'user', now(), '1', '那这些课程都涵盖哪些方面的知识呢？');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'assistant', now(), '1', '我们的课程涵盖了多个方面的知识，包括但不限于技能培训、产品培训、岗前培训等。');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'user', now(), '1', '课程内容是如何生成的呢？');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'assistant', now(), '1', '我们通过上传课程资料，大模型会自动解析并生成课程相关内容，例如课程大纲、学习内容等。');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'user', now(), '1', '那对于学员来说，如何进行实战演练呢？');
        INSERT INTO `nbchat`.`nbchat_train_scene_dialogue` ( `tenant_code`, `course_id`, `role`, `date_time`, `is_valid`, `content`) VALUES ( #{tenantCode}, concat(#{tenantCode},'311606929786589184'), 'assistant', now(), '1', '学员可以通过我们提供的实战场景进行演练，在实际操作中巩固所学知识，提升培训效果。');

    </insert>

    <insert id="insertQuestion" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        INSERT INTO `nbchat`.`nbchat_exam_question` (`question_id`, `course_id`, `tenant_code`, `question_name`, `question_type`, `part_id`, `difficulty`, `create_time`, `is_valid`, `explan`) VALUES (concat(#{tenantCode},'315977020646408192'), concat(#{tenantCode},'311606929786589184'), #{tenantCode}, '迪易学是由哪家公司开发的？', '2001', '', NULL, now(), '1', '');
        INSERT INTO `nbchat`.`nbchat_exam_question` (`question_id`, `course_id`, `tenant_code`, `question_name`, `question_type`, `part_id`, `difficulty`, `create_time`, `is_valid`, `explan`) VALUES (concat(#{tenantCode},'315977021321691136'), concat(#{tenantCode},'311606929786589184'), #{tenantCode}, '迪易学的核心技术是什么？', '2001', '', NULL, now(), '1', '');
        INSERT INTO `nbchat`.`nbchat_exam_question` (`question_id`, `course_id`, `tenant_code`, `question_name`, `question_type`, `part_id`, `difficulty`, `create_time`, `is_valid`, `explan`) VALUES (concat(#{tenantCode},'315977021376217088'), concat(#{tenantCode},'311606929786589184'), #{tenantCode}, '迪易学的目标市场主要包括哪些企业？', '2001', '', NULL, now(), '1', '');
        INSERT INTO `nbchat`.`nbchat_exam_question` (`question_id`, `course_id`, `tenant_code`, `question_name`, `question_type`, `part_id`, `difficulty`, `create_time`, `is_valid`, `explan`) VALUES (concat(#{tenantCode},'315977021434937344'), concat(#{tenantCode},'311606929786589184'), #{tenantCode}, '迪易学产品的优势主要体现在哪些方面？', '2001', '', NULL, now(), '1', '');
        INSERT INTO `nbchat`.`nbchat_exam_question` (`question_id`, `course_id`, `tenant_code`, `question_name`, `question_type`, `part_id`, `difficulty`, `create_time`, `is_valid`, `explan`) VALUES (concat(#{tenantCode},'315977021476880384'), concat(#{tenantCode},'311606929786589184'), #{tenantCode}, '迪易学应用产品主要解决了什么问题？', '2001', '', NULL, now(), '1', '');

    </insert>

    <insert id="insertAnswer" parameterType="com.tydic.nbchat.admin.mapper.po.ScriptSQLPO">
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021267165184'),  concat(#{tenantCode},'315977020646408192'), '2001', '百度', 0, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021288136704'),  concat(#{tenantCode},'315977020646408192'), '2001', '天源迪科', 1, now(), '1', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021296525312'),  concat(#{tenantCode},'315977020646408192'), '2001', '阿里巴巴', 2, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021309108224'),  concat(#{tenantCode},'315977020646408192'), '2001', '微软', 3, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021330079744'),  concat(#{tenantCode},'315977021321691136'), '2001', '自动问答技术', 0, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021338468352'),  concat(#{tenantCode},'315977021321691136'), '2001', '知识图谱技术', 1, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021355245568'),  concat(#{tenantCode},'315977021321691136'), '2001', '大模型技术', 2, now(), '1', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021363634176'),  concat(#{tenantCode},'315977021321691136'), '2001', '机器学习技术', 3, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021388800000'),  concat(#{tenantCode},'315977021376217088'), '2001', '大型互联网公司、国企央企、中小企业、连锁店铺', 0, now(), '1', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021401382912'),  concat(#{tenantCode},'315977021376217088'), '2001', '医药制造公司、科技创新企业、农业公司、零售公司', 1, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021409771520'),  concat(#{tenantCode},'315977021376217088'), '2001', '金融机构、人力资源公司、房地产企业、学校教育机构', 2, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021422354432'),  concat(#{tenantCode},'315977021376217088'), '2001', '酒店旅游企业、媒体广告公司、艺术文化机构、运动健身机构', 3, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021443325952'),  concat(#{tenantCode},'315977021434937344'), '2001', '智能化题库提炼和自动化判卷', 0, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021451714560'),  concat(#{tenantCode},'315977021434937344'), '2001', '课程、实战和考题的自动化生成', 1, now(), '1', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021460103168'),  concat(#{tenantCode},'315977021434937344'), '2001', '智能学习效果评估和一键式课件生成', 2, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021468491776'),  concat(#{tenantCode},'315977021434937344'), '2001', '场景化实践演练和多模式学习功能', 3, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021485268992'),  concat(#{tenantCode},'315977021476880384'), '2001', '课件制作门槛高、传统考试效果差、企业培训成本高', 0, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021493657600'),  concat(#{tenantCode},'315977021476880384'), '2001', '课程内容理解困难、实战演练机会不足、学习效果难以评估', 1, now(), '0', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021502046208'),  concat(#{tenantCode},'315977021476880384'), '2001', '教材制作难、培训效果差、试题生成繁琐', 2, now(), '1', '1');
        INSERT INTO `nbchat`.`nbchat_exam_question_items` (`item_id`, `question_id`, `question_type`, `item_value`, `item_index`, `create_time`, `is_right`, `is_valid`) VALUES (concat(#{tenantCode},'315977021514629120'),  concat(#{tenantCode},'315977021476880384'), '2001', '学员管理困难、课程监控不便、学习资源匮乏', 3, now(), '0', '1');

    </insert>

</mapper>