<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysApiTokenConfigMapper">

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="access_key" jdbcType="VARCHAR" property="accessKey" />
        <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
        <result column="expire_time" jdbcType="INTEGER" property="expireTime" />
        <result column="subsystem" jdbcType="VARCHAR" property="subsystem" />
        <result column="api_permission" jdbcType="VARCHAR" property="apiPermission" />
        <result column="refresh_time" jdbcType="TIMESTAMP" property="refreshTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_valid" jdbcType="CHAR" property="isValid" />
    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, tenant_code, user_id, access_key, secret_key, expire_time,refresh_time,
          subsystem, api_permission, create_time, update_time, is_valid
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig">
        insert into sys_api_token_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appName != null">app_name,</if>
            <if test="tenantCode != null">tenant_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="accessKey != null">access_key,</if>
            <if test="secretKey != null">secret_key,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="subsystem != null">subsystem,</if>
            <if test="apiPermission != null">api_permission,</if>
            <if test="refreshTime != null">refresh_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isValid != null">is_valid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appName != null">#{appName},</if>
            <if test="tenantCode != null">#{tenantCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="accessKey != null">#{accessKey},</if>
            <if test="secretKey != null">#{secretKey},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="subsystem != null">#{subsystem},</if>
            <if test="apiPermission != null">#{apiPermission},</if>
            <if test="refreshTime != null">#{refreshTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isValid != null">#{isValid},</if>
        </trim>
    </insert>

    <update id="updateKeyById" parameterType="com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig">
        update sys_api_token_config
        <set>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="accessKey != null">access_key = #{accessKey},</if>
            <if test="refreshTime != null">refresh_time = #{refreshTime},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
        </set>
        where id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from sys_api_token_config
        where id = #{id}
    </select>

    <select id="selectByNameAndSk" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from sys_api_token_config
        where app_name = #{appName}
            and secret_key = #{secretKey}
            and is_valid = '1'
    </select>

    <select id="selectByAk" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from sys_api_token_config
        where access_key = #{accessKey}
            and is_valid = '1'
    </select>

    <delete id="deleteByPrimaryKey">
        update sys_api_token_config set is_valid = '0' where id = #{id}
    </delete>

</mapper>