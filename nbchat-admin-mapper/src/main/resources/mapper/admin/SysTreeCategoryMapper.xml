<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysTreeCategoryMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysTreeCategory" id="SysTreeCategoryMap">
        <result property="cateId" column="cate_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="cateName" column="cate_name" jdbcType="VARCHAR"/>
        <result property="cateLevel" column="cate_level" jdbcType="INTEGER"/>
        <result property="ancestors" column="ancestors" jdbcType="VARCHAR"/>
        <result property="cateSource" column="cate_source" jdbcType="VARCHAR"/>
        <result property="busiType" column="busi_type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        cate_id
        , parent_id, user_id, tenant_code, cate_name, cate_level, ancestors, cate_source, busi_type, create_time, create_by, update_time, update_by, is_valid, order_index</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysTreeCategoryMap">
        select
        <include refid="Base_Column_List"/>
        from sys_tree_category
        where cate_id = #{cateId}
    </select>


    <select id="selectAll" resultMap="SysTreeCategoryMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysTreeCategory">
        select
        <include refid="Base_Column_List"/>
        from sys_tree_category
        <where>
            <if test="cateId != null and cateId != ''">
                and cate_id = #{cateId}
            </if>
            <if test="parentId != null and parentId != ''">
                and parent_id = #{parentId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="cateName != null and cateName != ''">
                and cate_name = #{cateName}
            </if>
            <if test="cateLevel != null">
                and cate_level = #{cateLevel}
            </if>
            <if test="ancestors != null and ancestors != ''">
                and ancestors = #{ancestors}
            </if>
            <if test="cateSource != null and cateSource != ''">
                and cate_source = #{cateSource}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type = #{busiType}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
        </where>
        ORDER BY order_index,cate_level
    </select>


    <insert id="insertSelective" keyProperty="cateId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysTreeCategory">
        insert into sys_tree_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cateId != null and cateId != ''">
                cate_id,
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="cateName != null and cateName != ''">
                cate_name,
            </if>
            <if test="cateLevel != null">
                cate_level,
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors,
            </if>
            <if test="cateSource != null and cateSource != ''">
                cate_source,
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cateId != null and cateId != ''">
                #{cateId},
            </if>
            <if test="parentId != null and parentId != ''">
                #{parentId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="cateName != null and cateName != ''">
                #{cateName},
            </if>
            <if test="cateLevel != null">
                #{cateLevel},
            </if>
            <if test="ancestors != null and ancestors != ''">
                #{ancestors},
            </if>
            <if test="cateSource != null and cateSource != ''">
                #{cateSource},
            </if>
            <if test="busiType != null and busiType != ''">
                #{busiType},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="cateId" useGeneratedKeys="true">
        insert into sys_tree_category(parent_iduser_idtenant_codecate_namecate_levelancestorscate_sourcebusi_typecreate_timecreate_byupdate_timeupdate_byis_validorder_index)
        values (#{parentId}#{userId}#{tenantCode}#{cateName}#{cateLevel}#{ancestors}#{cateSource}#{busiType}#{createTime}#{createBy}#{updateTime}#{updateBy}#{isValid}#{orderIndex})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_tree_category
        <set>
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="cateName != null and cateName != ''">
                cate_name = #{cateName},
            </if>
            <if test="cateLevel != null">
                cate_level = #{cateLevel},
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors = #{ancestors},
            </if>
            <if test="cateSource != null and cateSource != ''">
                cate_source = #{cateSource},
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type = #{busiType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
        </set>
        where cate_id = #{cateId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from sys_tree_category
        where cate_id = #{cateId}
    </delete>

    <!--逻辑删除-->
    <update id="logicDeleteById">
        update sys_tree_category
        <set>
            is_valid = '0'
        </set>
        where FIND_IN_SET(#{cateId}, ancestors)
    </update>
</mapper>

