package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysHelpCenter;

import java.util.List;

import com.tydic.nbchat.admin.mapper.po.SysHelpCenterCondition;
import org.apache.ibatis.annotations.Param;

public interface SysHelpCenterMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SysHelpCenter record);

    /**
     * 管理帮助中心保存,新增或修改
     */
    int insertSelective(SysHelpCenter record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SysHelpCenter selectByPrimaryKey(Integer id);
    /**
     * 根据分类和标题模糊查询
     */
    List<SysHelpCenter> selectByCategoryAndTitle(SysHelpCenterCondition condition);

    /**
     * 用户查询配置帮助中心
     */
    List<SysHelpCenter> selectByCategory(SysHelpCenterCondition condition);

    /**
     * 根据id列表排序
     */
    int updateBatchSelective(@Param("list") List<SysHelpCenter> list);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysHelpCenter record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysHelpCenter record);

    List<SysHelpCenter> selectByAll(SysHelpCenter sysHelpCenter);

    int batchInsert(@Param("list") List<SysHelpCenter> list);

    int deleteByPrimaryKeyIn(List<Integer> list);
}