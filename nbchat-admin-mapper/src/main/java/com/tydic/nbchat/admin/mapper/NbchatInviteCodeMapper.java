package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatInviteCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 17:51
 * @description
 */

public interface NbchatInviteCodeMapper {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(NbchatInviteCode record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    NbchatInviteCode selectByPrimaryKey(String id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(NbchatInviteCode record);

    int updateBatchSelective(@Param("list") List<NbchatInviteCode> list);

    NbchatInviteCode selectOneByUserId(@Param("userId") String userId);

    NbchatInviteCode selectOneByInviteCode(@Param("inviteCode") String inviteCode);

    int updateUseCount(@Param("inviteCode") String inviteCode);
}
