package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUpdateLog;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.tydic.nbchat.admin.mapper.po.SysUpdateLogCondition;
import org.apache.ibatis.annotations.Param;
import org.joda.time.LocalDate;

public interface SysUpdateLogMapper {
    /**
     * 条件查询
     */
    List<SysUpdateLog> selectByCondition(SysUpdateLogCondition condition);
    /**
     * 用户端查询最新版本更新日志查询接口
     */
    List<SysUpdateLog> selectNewVersion(SysUpdateLogCondition condition);
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SysUpdateLog record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUpdateLog record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SysUpdateLog selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysUpdateLog record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysUpdateLog record);

    List<SysUpdateLog> selectByAll(SysUpdateLog sysUpdateLog);

    int updateBatchSelective(@Param("list") List<SysUpdateLog> list);

    int batchInsert(@Param("list") List<SysUpdateLog> list);

    int deleteByPrimaryKeyIn(List<Integer> list);

    List<String> selectVersionDatesLike(@Param("pattern") String pattern);

    List<String> selectPublishDatesLike(@Param("pattern") String pattern);

    List<Map<String, Object>> countPublishByDay(@Param("dates") Set<String> dates);



    List<String> selectAllPublishDates();

    List<Map<String, Object>> countVersionByDay(@Param("dates") Set<String> dates);

    List<String> selectAllVersionDates();


}