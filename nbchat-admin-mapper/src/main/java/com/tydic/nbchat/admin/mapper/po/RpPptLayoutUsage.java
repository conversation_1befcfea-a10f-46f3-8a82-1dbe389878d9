package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * PPT布局试用记录
 */
@Data
public class RpPptLayoutUsage {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * PPT ID
     */
    private String pptId;

    /**
     * 系统推荐模版ID
     */
    private String layoutId;

    /**
     * 当前页
     */
    private Short pageNum;

    /**
     * 实际使用模版ID
     */
    private String useLayoutId;

    /**
     * 是否更换布局: 0-否 1-是
     */
    private String isChanged;

    /**
     * 创建时间
     */
    private Date createTime;

    private Date updateTime;

    /**
     * 布局ID（与前端对应）
     */
    private String layid;
}