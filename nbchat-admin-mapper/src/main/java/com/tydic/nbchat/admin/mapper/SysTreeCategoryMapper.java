package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysTreeCategory;

import java.util.List;

/**
 * 系统通用树形类目表(SysTreeCategory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-16 14:40:02
 */
public interface SysTreeCategoryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param cateId 主键
     * @return 实例对象
     */
    SysTreeCategory queryById(String cateId);

    List<SysTreeCategory> selectAll(SysTreeCategory sysTreeCategory);

    /**
     * 新增数据
     *
     * @param sysTreeCategory 实例对象
     * @return 影响行数
     */
    int insert(SysTreeCategory sysTreeCategory);


    int insertSelective(SysTreeCategory sysTreeCategory);

    /**
     * 修改数据
     *
     * @param sysTreeCategory 实例对象
     * @return 影响行数
     */
    int update(SysTreeCategory sysTreeCategory);

    /**
     * 通过主键删除数据
     *
     * @param cateId 主键
     * @return 影响行数
     */
    int deleteById(String cateId);

    /**
     * 通过主键逻辑删除
     *
     * @param cateId
     * @return
     */
    int logicDeleteById(String cateId);

}

