package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/25 14:39
 * @description 该类用于表示用户详细信息，包括用户的基本信息、支付信息、会员信息以及平台使用情况等。
 * 它封装了与用户相关的各种属性，以便于在系统中进行数据传递和处理。
 * @since 2025/3/25 14:39
 */
@Data
public class UserRpDetailPO implements Serializable {
    private static final long serialVersionUID = 6559639800985516496L;

    /** 租户id */
    private String tenantCode;
    /** 用户id */
    private String userId;
    /** 用户名称 */
    private String userName;
    /** 手机号 */
    private String phone;
    /** 用户属性 0-内部用户/1- 外部用户 */
    private String userType;
    /** 企业名称 */
    private String companyName;
    /** 注册渠道 */
    private String regChannel;
    /** 注册时间 */
    private Date regTime;
    /** 是否绑定微信 0-否/1-是 */
    private String isBindWx;
    /** 推广渠道 */
    private String promChannel;
    /** 渠道id */
    private String promId;
    /** 渠道信息 */
    private String promKey;

    /** 累计拉新人数 */
    private Integer totalNum;
    /** 助力拉新人数 */
    private Integer assistNum;
    /** 裂变拉新人数 */
    private Integer fissionNum;

    /** 是否是付费用户 0-否/1-是 */
    private String isSubscriber;
    /** 是否进行过退款 0-否/1-是 */
    private String isRefund;
    /** 累计支付金额，单位：分 */
    private Integer totalPayAmount;
    /** 累计⽀付次数 */
    private Integer payNum;
    /** ⾸次⽀付时间 */
    private Date firstPayTime;
    /** 最近⽀付时间 */
    private Date lastPayTime;
    /** 首次开通会员日期 */
    private Date firstVipTime;
    /** 最终会员失效日期 */
    private Date lastVipTime;
    /** 会员累计付费金额，单位：分 */
    private Integer totalVipAmount;
    /** 累计充值算力点数量 */
    private Integer scoreRechargeTotal;
    /** 累计消耗算力点数量 */
    private Integer scoreConsumeTotal;
    /** 当前可用算力点数量 */
    private Integer scoreBalance;
    /** 算力点购买次数 */
    private Integer scoreTotalCount;
    /** 算力点累计支付金额，单位：分 */
    private Integer scoreTotalAmount;
    /** 算力点累计购买数量 */
    private Integer scoreTotalNum;
    /** 500算力点购买次数 */
    private Integer score500Count;
    /** 500算力点累计支付金额，单位：分 */
    private Integer score500Amount;
    /** 2000算力点购买次数 */
    private Integer score2000Count;
    /** 2000算力点累计支付金额，单位：分 */
    private Integer score2000Amount;
    /** 5000算力点购买次数 */
    private Integer score5000Count;
    /** 5000算力点累计支付金额，单位：分 */
    private Integer score5000Amount;

    /** 发票申请次数 */
    private Integer launchTimes;
    /** 累计开票金额，单位：分 */
    private Integer totalAmount;
    /** 发票抬头 */
    private String invoiceTitle;

    /** 平台访问次数 */
    private Integer accessTimes;
    /** 首次访问时间 */
    private Date firstAccessTime;
    /** 最近访问时间 */
    private Date lastAccessTime;
    /** PPT制作数量 */
    private Integer pptMakeTimes;
    /** PPT下载数量 */
    private Integer pptDownloadTimes;
    /** 视频制作数量 */
    private Integer videoMakeTimes;
    /** 视频生成数量 */
    private Integer videoBuildTimes;
    /** 视频总时长 */
    private Integer videoTotalDuration;
    /** 平均视频时长 */
    private Integer videoAvgDuration;
    /** 视频总大小:kb */
    private Integer videoTotalSize;
    /** 视频下载次数 */
    private Integer videoDownloadTimes;
    /** 【PPT播讲】视频数量 */
    private Integer videoPptCount;
    /** 【PPT播讲】【上传PPT】视频数量 */
    private Integer videoPptUploadCount;
    /** 【PPT播讲】【平台PPT】视频数量 */
    private Integer videoPptPlatformCount;
    /** 【Word播讲】视频数量 */
    private Integer videoWordSpeechCount;
    /** 【AI智能生成】视频数量 */
    private Integer videoAiCount;
    /** 【Word生成】视频数量 */
    private Integer videoWordGenerateCount;
    /** 【模版创作】视频数量 */
    private Integer videoTemplateCount;
    /** 【自定义创作】视频数量 */
    private Integer videoCustomCount;
    /** 卡通数字人-视频总量 */
    private Integer videoCartoonCount;
    /** 2.5d数字人-视频总量 */
    private Integer video25dCount;
    /** 2d数字人-视频总量 */
    private Integer video2dCount;
    /** 无数字人-视频总量 */
    private Integer videoNoneCount;
    /** 定制2.5d数字人-视频总量 */
    private Integer videoCustom25dCount;
    /** 定制2d数字人-视频总量 */
    private Integer videoCustom2dCount;
    /** 定制卡通数字人-视频总量 */
    private Integer videoCustomCartoonCount;
    /** 定制音频-视频总量 */
    private Integer videoAudioCount;

    /** 出题总数 */
    private Integer examMakeTimes;
    /** 出题次数 */
    private Integer examChickTimes;
    /** 导出次数 */
    private Integer examDownloadTimes;
}
