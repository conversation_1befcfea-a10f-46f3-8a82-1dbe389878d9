package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/18 09:55
 * @description
 */

/**
 * ppt|视频小时统计报表
 */
@Data
public class RpHourVideoCount {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 统计日期
     */
    private Date dayData;

    /**
     * 统计小时数
     */
    private String hourData;

    /**
     * PPT制作数量
     */
    private Integer pptMakeTimes = 0;

    /**
     * 【AI生成PPT】数量
     */
    private Integer pptAiCount = 0;

    /**
     * 【文档生成PPT】数量
     */
    private Integer pptDocCount = 0;

    /**
     * 【文本生成PPT】数量
     */
    private Integer pptTxtCount = 0;

    /**
     * 视频制作数量
     */
    private Integer videoMakeTimes = 0;

    /**
     * 视频生成数量
     */
    private Integer videoBuildTimes = 0;

    /**
     * 【PPT播讲】视频数量
     */
    private Integer videoPptCount = 0;

    /**
     * 【PPT播讲】【上传PPT】视频数量
     */
    private Integer videoPptUploadCount = 0;

    /**
     * 【PPT播讲】【平台PPT】视频数量
     */
    private Integer videoPptPlatformCount = 0;

    /**
     * 【Word播讲】视频数量
     */
    private Integer videoWordSpeechCount = 0;

    /**
     * 【AI智能生成】视频数量
     */
    private Integer videoAiCount = 0;

    /**
     * 【Word生成】视频数量
     */
    private Integer videoWordGenerateCount = 0;

    /**
     * 【模版创作】视频数量
     */
    private Integer videoTemplateCount = 0;

    /**
     * 【自定义创作】视频数量,
     */
    private Integer videoCustomCount = 0;

    /**
     * 卡通数字人-视频总量,
     */
    private Integer videoCartoonCount = 0;

    /**
     * 2.5d数字人-视频总量,
     */
    private Integer video25dCount = 0;

    /**
     * 2d数字人-视频总量,
     */
    private Integer video2dCount = 0;

    /**
     * 无数字人-视频总量,
     */
    private Integer videoNoneCount = 0;

    /**
     * 定制2.5d数字人-视频总量,
     */
    private Integer videoCustom25dCount = 0;

    /**
     * 定制2d数字人-视频总量,
     */
    private Integer videoCustom2dCount = 0;

    /**
     * 定制卡通数字人-视频总量,
     */
    private Integer videoCustomCartoonCount = 0;

    /**
     * 定制音频-视频总量
     */
    private Integer videoAudioCount = 0;

    /**
     * 创建时间
     */
    private Date createTime;
}
