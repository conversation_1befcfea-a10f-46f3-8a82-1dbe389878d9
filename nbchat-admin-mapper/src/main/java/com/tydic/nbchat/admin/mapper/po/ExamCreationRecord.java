package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/31 17:42
 * @description 试题创作记录
 */
@Data
public class ExamCreationRecord {
    /**
     * 创作记录ID
     */
    private String creationId;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创作名称
     */
    private String creationName;

    /**
     * 填空题数量
     */
    private Integer fillNum;

    /**
     * 判断题数量
     */
    private Integer judgeNum;

    /**
     * 多选题数量
     */
    private Integer choiceMNum;

    /**
     * 单选题数量
     */
    private Integer choiceSNum;

    /**
     * 问答题数量
     */
    private Integer questionNum;

    /**
     * 试题内容
     */
    private String questionContent;

    /**
     * 出题附件
     */
    private String fileUrl;

    /**
     * 文档内容
     */
    private String fileContent;

    /**
     * 资料内容
     */
    private String creationContent;

    /**
     * 出题方式 1-AI出题 2-上传文档出题 3-资料出题
     */
    private String creationType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否有效 1 有效 0 无效
     */
    private String isValid;

    private Integer examMakeTimes;
}
