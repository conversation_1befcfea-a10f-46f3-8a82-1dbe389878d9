package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class KnowledgeClassifyPo implements Serializable {
    private Long typeId;

    private String tenantCode;

    private Short typeGroup;

    private String custCode;

    private String typeName;

    private String typeDesc;

    private Integer sortId;

    private Date createTime;

    private String createUserId;

    private String createUserName;

    private Date updateTime;

    private String updateUserId;

    private String updateUserName;
    /** 父分类id*/
    private Long parentId;

    private String classes;

    private static final long serialVersionUID = 1L;


}
