package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * PPT模版使用记录
 */
@Data
public class RpPptUsage {
    /**
     * 主键
     */
    private Long id;

    /**
     * PPT ID
     */
    private String pptId;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否免费用户: 0-否 1-是
     */
    private String isFree;

    /**
     * 系统推荐模版ID
     */
    private String themeId;

    /**
     * 系统推荐场景（大模型返回）
     */
    private String aiScene;

    /**
     * 系统推荐风格（大模型返回）
     */
    private String aiStyle;

    /**
     * 系统推荐配色（大模型返回）
     */
    private String aiColor;

    /**
     * 实际使用模版ID
     */
    private String useThemeId;

    /**
     * 用户类型: 0-个人用户 1-企业用户
     */
    private String userType;

    /**
     * 是否更换模版: 0-否 1-是
     */
    private String isChanged;

    /**
     * 总页数
     */
    private Short totalPage;

    /**
     * 创建时间
     */
    private Date createTime;

    private Date updateTime;
}