package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig;
import org.apache.ibatis.annotations.Param;

public interface SysApiTokenConfigMapper {

    SysApiTokenConfig selectById(Integer id);

    SysApiTokenConfig selectByNameAndSk(@Param("appName") String appName,
                                        @Param("secretKey") String secretKey);

    int deleteByPrimaryKey(Integer id);

    SysApiTokenConfig selectByAk(String accessKey);

    int updateKeyById(SysApiTokenConfig record);

    int insert(SysApiTokenConfig record);
}