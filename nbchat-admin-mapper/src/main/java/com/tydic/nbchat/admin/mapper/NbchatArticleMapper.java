package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatArticle;
import com.tydic.nbchat.admin.mapper.po.NbchatArticleSelectCondition;

import java.util.List;

public interface NbchatArticleMapper {
    int deleteByPrimaryKey(String articleId);

    int insert(NbchatArticle record);

    int insertSelective(NbchatArticle record);

    NbchatArticle selectByPrimaryKey(String articleId);

    int updateByPrimaryKeySelective(NbchatArticle record);

    int updateByPrimaryKeyWithBLOBs(NbchatArticle record);

    int updateByPrimaryKey(NbchatArticle record);

    List<NbchatArticle> selectByCondition(NbchatArticleSelectCondition condition);
}