package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:15
 * @description ppt/视频制作信息
 */
@Data
public class RpUserPptVideoInfo {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * PPT制作数量
     */
    private Integer pptMakeTimes;

    /**
     * PPT下载数量
     */
    private Integer pptDownloadTimes;

    /**
     * 【AI生成PPT】数量
     */
    private Integer pptAiCount;

    /**
     * 【文档生成PPT】数量
     */
    private Integer pptDocCount;

    /**
     * 【文本生成PPT】数量
     */
    private Integer pptTxtCount;

    /**
     * 视频制作数量
     */
    private Integer videoMakeTimes;

    /**
     * 视频生成数量
     */
    private Integer videoBuildTimes;

    /**
     * 视频总时长
     */
    private Integer videoTotalDuration;

    /**
     * 平均视频时长
     */
    private Integer videoAvgDuration;

    /**
     * 视频总大小:kb
     */
    private Integer videoTotalSize;

    /**
     * 视频下载次数
     */
    private Integer videoDownloadTimes;

    /**
     * 【PPT播讲】视频数量
     */
    private Integer videoPptCount;

    /**
     * 【PPT播讲】【上传PPT】视频数量
     */
    private Integer videoPptUploadCount;

    /**
     * 【PPT播讲】【平台PPT】视频数量
     */
    private Integer videoPptPlatformCount;

    /**
     * 【Word播讲】视频数量
     */
    private Integer videoWordSpeechCount;

    /**
     * 【AI智能生成】视频数量
     */
    private Integer videoAiCount;

    /**
     * 【Word生成】视频数量
     */
    private Integer videoWordGenerateCount;

    /**
     * 【模版创作】视频数量
     */
    private Integer videoTemplateCount;

    /**
     * 【自定义创作】视频数量,
     */
    private Integer videoCustomCount;

    /**
     * 卡通数字人-视频总量,
     */
    private Integer videoCartoonCount;

    /**
     * 2.5d数字人-视频总量,
     */
    private Integer video25dCount;

    /**
     * 2d数字人-视频总量,
     */
    private Integer video2dCount;

    /**
     * 无数字人-视频总量,
     */
    private Integer videoNoneCount;

    /**
     * 定制2.5d数字人-视频总量,
     */
    private Integer videoCustom25dCount;

    /**
     * 定制2d数字人-视频总量,
     */
    private Integer videoCustom2dCount;

    /**
     * 定制卡通数字人-视频总量,
     */
    private Integer videoCustomCartoonCount;

    /**
     * 定制音频-视频总量
     */
    private Integer videoAudioCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Integer videoSuccessTimes;
}
