package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KnowledgeClassifyMapper {
    int deleteByPrimaryKey(Long typeId);

    int deleteByParentId(@Param("createUserId") String createUserId,@Param("parentId") Long parentId);

    int insert(KnowledgeClassifyPo record);

    int insertSelective(KnowledgeClassifyPo record);

    KnowledgeClassifyPo selectByPrimaryKey(Long typeId);

    int updateByPrimaryKeySelective(KnowledgeClassifyPo record);

    int updateByPrimaryKey(KnowledgeClassifyPo record);

    int selectCount(KnowledgeClassifyPo po);
    List<KnowledgeClassifyPo> selectByCondition(KnowledgeClassifyPo commSentenceTypePo);


    List<KnowledgeClassifyPo> selectByPo(KnowledgeClassifyPo commSentenceTypePo);

    Long selectId(String tenantCode, String typeName, Short typeGroup, String custCode);

    KnowledgeClassifyPo selectTypePo(String tenantCode, String typeName, Short typeGroup, String custCode);

    KnowledgeClassifyPo selectTypePo(KnowledgeClassifyPo commSentenceTypePo);

    List<KnowledgeClassifyPo> queryCommSentenceTypeByIds(@Param("tenantCode") String tenantCode, @Param("ids") List<Long> ids);

    List<KnowledgeClassifyPo> selectDefaultType(@Param("inner")String param);
}
