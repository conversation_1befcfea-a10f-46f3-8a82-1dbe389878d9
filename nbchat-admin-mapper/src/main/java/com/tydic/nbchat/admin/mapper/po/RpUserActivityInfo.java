package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description 活跃信息
 */
@Data
public class RpUserActivityInfo {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 平台访问次数
     */
    private Integer accessTimes;

    /**
     * 首次访问时间
     */
    private Date firstAccessTime;

    /**
     * 最近访问时间
     */
    private Date lastAccessTime;
}
