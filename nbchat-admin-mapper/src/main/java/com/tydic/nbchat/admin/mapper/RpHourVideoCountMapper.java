package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpHourVideoCount;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 09:55
 * @description
 */

public interface RpHourVideoCountMapper {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpHourVideoCount record);

    int updateBatchSelective(@Param("list") List<RpHourVideoCount> list);

    List<RpHourVideoCount> selectVideo();

    List<RpHourVideoCount> selectPpt();

    List<RpHourVideoCount> selectAllVideo();

    List<RpHourVideoCount> selectAllPpt();

    RpHourVideoCount selectOneByTenantCodeAndUserIdAndDayDataAndHourData(@Param("tenantCode") String tenantCode, @Param("userId") String userId, @Param("dayData") Date dayData, @Param("hourData") String hourData);

    int deleteAll();
}
