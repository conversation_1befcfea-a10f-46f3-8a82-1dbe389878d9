package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/27 17:51
 * @description
 */

/**
 * 邀请码表
 */
@Data
public class NbchatInviteCode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String id;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 创建人
     */
    private String createdBy;

    private String phone;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是否删除;1表示删除0表示未删除
     */
    private Boolean isDeleted;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 到期时间，如果设置了过期时间，验证码可多次使用
     */
    private Date expTime;

    private Integer maxCount;
}
