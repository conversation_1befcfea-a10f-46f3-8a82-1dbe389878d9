package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.UserPortraitBO;
import com.tydic.nbchat.admin.mapper.po.UserPortraitTimelineBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23 11:24
 * @description 用户画像Mapper
 */
public interface UserPortraitMapper {

    /**
     * 根据提供的租户代码和用户ID选择并检索用户肖像信息。
     *
     * @param tenantCode 与用户关联的租户的唯一标识符
     * @param userId     正在查询其肖像信息的用户的唯一标识符
     * @return UserPortraitBO的一个实例，其中包含指定用户的详细肖像信息
     */
    UserPortraitBO selectUserPortraitInfo(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 根据提供的租户代码和用户ID选择并检索用户的画像时间线信息。
     *
     * @param tenantCode 与用户关联的租户的唯一标识符
     * @param userId     正在查询其画像时间线信息的用户的唯一标识符
     * @return 包含指定用户画像时间线信息的UserPortraitTimelineBO对象列表
     */
    List<UserPortraitTimelineBO> selectUserPortraitTimelineInfo(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}
