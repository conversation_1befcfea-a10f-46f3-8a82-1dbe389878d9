package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysDept;
import com.tydic.nbchat.admin.mapper.po.SysDeptCondition;
import com.tydic.nbchat.admin.mapper.po.SysDeptUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (NbchatSysDept)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-20 17:34:48
 */
public interface SysDeptMapper {

    /**
     * 刷新祖级列表
     * @param tenantCode
     * @return
     */
    int updateAncestors(@Param("tenantCode") String tenantCode);

    /**
     * 通过ID查询单条数据
     *
     * @param deptId 主键
     * @return 实例对象
     */
    SysDept queryById(String deptId);
    String queryDeptFullPath(String deptId);

    /**
     * 条件查询
     * @param condition
     * @return
     */
    List<SysDept> selectByCondition(SysDeptCondition condition);


    /***
     * 查询部门树
     * @param condition
     * @return
     */
    List<SysDept> selectDeptTree(SysDeptCondition condition);


    /**
     * 查询全部数据
     * @param sysDept
     * @return
     */
    List<SysDept> selectAll(SysDept sysDept);

    /**
     * 新增数据
     * @param sysDept
     * @return
     */
    int insertSelective(SysDept sysDept);

      /**
     * 修改数据
     *
     * @param sysDept 实例对象
     * @return 影响行数
     */
    int updateSelective(SysDept sysDept);

    /**
     * 通过主键删除数据
     *
     * @param deptId 主键
     * @return 影响行数
     */
    int deleteById(String deptId);

    /**
     * 新增部门用户关系
     * @param deptUser
     * @return
     */
    int insertDeptUserRel(SysDeptUser deptUser);

    /**
     * 查询
     * @param deptId
     * @param userId
     * @return
     */
    Long selectDeptUserRid(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 更新部门关系
     * @param id
     * @param deptId
     * @return
     */
    int updateDeptUserRel(@Param("id") Long id, @Param("deptId") String deptId);

    /**
     * 查询用户部门
     * @param tenantCode
     * @param userId
     * @return
     */
    List<SysDept> selectByUserId(@Param("tenantCode") String tenantCode,
                                 @Param("userId") String userId);

    /**
     * 通过祖级列表查询所有子部门
     * @param deptId
     * @return
     */
    List<SysDept> selectDeptChildList(@Param("tenantCode") String tenantCode,
                                   @Param("deptId") String deptId);

    /**
     * 查询部门名称
     * @param deptId
     * @return
     */
    String queryOrganizeName(String deptId);

    /**
     * 删除部门用户关系
     * @param tenantCode
     * @param userId
     * @return
     */
    int deleteDeptUserRel(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}

