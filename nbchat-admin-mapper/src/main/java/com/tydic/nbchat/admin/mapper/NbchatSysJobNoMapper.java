package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatSysJobNo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatSysJobNoMapper {

    /**
     * 通过主键删除数据
     *
     * @param deptId 主键
     * @return 影响行数
     */
    int deleteById(String deptId);

    /**
     * 校验状态
     * @param jobNo
     * @param name
     * @param bindStatus
     * @return
     */
    NbchatSysJobNo selectJobNo(@Param("jobNo") String jobNo,
                               @Param("name") String name
                               );

    /**
     * @param id
     * @param userId
     * @return
     */
    int bindJobNo(@Param("id") Integer id,
                  @Param("userId") String userId);


    /**
     * 按用户查询
     * @param userId
     * @return
     */
    List<NbchatSysJobNo> selectByUserId(@Param("userId") String userId);
}

