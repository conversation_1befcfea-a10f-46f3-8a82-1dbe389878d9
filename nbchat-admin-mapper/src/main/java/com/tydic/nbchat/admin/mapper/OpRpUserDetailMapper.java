package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.OpRpUserDetail;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetailSelectCondition;
import com.tydic.nbchat.admin.mapper.po.UserRpDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:26
 * @description
 */

public interface OpRpUserDetailMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(OpRpUserDetail record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    OpRpUserDetail selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(OpRpUserDetail record);

    int updateUserScore(@Param("tenantCode") String tenantCode,
                        @Param("userId") String userId,
                        @Param("scoreBalance") Integer scoreBalance);

    List<OpRpUserDetail> selectByCondition(OpRpUserDetailSelectCondition condition);

    OpRpUserDetail selectByUserId(@Param("tenantCode") String tenantCode,
                                  @Param("userId") String userId);

    int countTdhTotal(@Param("tenantCode") String tenantCode,
                      @Param("userId") String userId);

    int countTdhSuccess(@Param("tenantCode") String tenantCode,
                        @Param("userId") String userId);

    List<Map<String, Object>> selectScoreConsumeList(List<String> userIdList);

    Integer selectScoreRecharge(String tenantCode, String userId);

    int updateJoinType(String targetUid, String targetTenant, String code);

    List<OpRpUserDetail> selectByPayTimeCondition(OpRpUserDetailSelectCondition condition);

    UserRpDetailPO selectRpByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}
