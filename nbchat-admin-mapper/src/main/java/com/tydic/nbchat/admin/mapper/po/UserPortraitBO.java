package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/23 11:27
 * @description
 */
@Data
public class UserPortraitBO implements Serializable {
    private static final long serialVersionUID = -1248825647402669182L;

    /**
     * 租户CODE
     */
    private String tenantCode;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 用户角色
     */
    private String userRole;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 注册时间
     */
    private Date regTime;
    /**
     * 高级会员状态
     * 0-已过期
     * 1-有效
     * 2-未开通
     */
    private String highVipStatus;
    /**
     * 高级会员累计购买月数
     */
    private Integer highVipMonths;
    /**
     * 高级会员剩余天湖
     */
    private Integer highVipDaysRemaining;
    /**
     * 专家会员状态
     * 0-已过期
     * 1-有效
     * 2-未开通
     */
    private String expertVipStatus;
    /**
     * 专家会员累计购买月数
     */
    private Integer expertVipMonths;
    /**
     * 专家会员剩余的天数
     */
    private Integer expertVipDaysRemaining;
    /**
     * 算力点点数余额
     */
    private Integer scoreBalance;
    /**
     * 累计支付金额
     */
    private Integer totalPayAmount;
    /**
     * 累计充值算力点点数
     */
    private Integer scoreRechargeTotal;
    /**
     * 累计视频制作数量
     */
    private Integer videoMakeTimes;
    /**
     * 累计视频制作平均时长
     */
    private Double videoAvgDuration;
    /**
     * 累计PPT制作数量
     */
    private Integer pptMakeTimes;
    /**
     * 形象定制数量
     */
    private Integer imageActualTimes;
    /**
     * 声音定制数量
     */
    private Integer audioActualTimes;
    /**
     * 累计活跃天数
     */
    private Integer totalActivityDays;
    /**
     * 近30天活跃天数
     */
    private Integer last30ActivityDays;
}
