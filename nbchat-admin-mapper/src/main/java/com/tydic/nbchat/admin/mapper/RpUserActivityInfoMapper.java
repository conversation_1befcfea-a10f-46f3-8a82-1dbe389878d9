package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserActivityInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description
 */

public interface RpUserActivityInfoMapper {

    int updateBatchSelective(@Param("list") List<RpUserActivityInfo> list);

    int batchInsert(@Param("list") List<RpUserActivityInfo> list);

    /**
     * Finds a user activity information record based on the provided tenant code and user ID.
     *
     * @param tenantCode the unique identifier for the tenant
     * @param userId     the unique identifier for the user
     * @return the RpUserActivityInfo object that matches the given tenant code and user ID, or null if no match is found
     */
    RpUserActivityInfo selectByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 每日更新统计数据
     *
     * @return update count
     */
    int insertForAll();

    int deleteAll();
}
