package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nbchat.user.api.bo.context.UserPayEventContext;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import com.tydic.nicc.mq.starter.entity.eum.ConsumeMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumeMode = ConsumeMode.ORDERLY,
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_PAY_EVENT_RP_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_PAY_EVENT_TOPIC)
@Component
public class UserPayEventConsumer implements KKMqConsumerListener<UserPayEventContext> {
    private final RpUserDetailService rpUserDetailService;

    public UserPayEventConsumer(RpUserDetailService rpUserDetailService) {
        this.rpUserDetailService = rpUserDetailService;
    }

    @Override
    public void onMessage(UserPayEventContext eventContext) {
        //用户支付事件
        try {
            rpUserDetailService.handlePayEvent(eventContext);
        } catch (Exception e) {
            log.error("用户支付事件消费-异常: {}", eventContext, e);
        }

    }
}
