package com.tydic.nbchat.admin.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.eum.SpuNameEnum;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.bo.rp.RpUserDetailBO;
import com.tydic.nbchat.admin.api.bo.rp.RpUserDetailExportBO;
import com.tydic.nbchat.admin.api.bo.rp.RpUserDetailQueryReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.constants.UserDetailConstants;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nbchat.admin.mapper.CommonMapper;
import com.tydic.nbchat.admin.mapper.OpRpUserDetailMapper;
import com.tydic.nbchat.admin.mapper.RpRevenueDataMapper;
import com.tydic.nbchat.admin.mapper.SysLoginLogMapper;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetail;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetailExportPO;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetailSelectCondition;
import com.tydic.nbchat.admin.mapper.po.SysLoginLog;
import com.tydic.nbchat.user.api.bo.context.UserPayEventContext;
import com.tydic.nbchat.user.api.bo.context.UserScoreChangeContext;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.nbchat.emus.MakeEventType;
import com.tydic.nicc.common.nbchat.emus.VipEventType;
import com.tydic.nicc.common.nbchat.msg.UserLoginEventContext;
import com.tydic.nicc.common.nbchat.msg.UserMakeEventContext;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.common.nbchat.msg.UserVipChangeContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_OPERATE_LOG_TOPIC;
import static com.tydic.nicc.common.nbchat.emus.UserOperationType.LOGIN;

@Slf4j
@Service
@AllArgsConstructor
public class RpUserDetailServiceImpl implements RpUserDetailService {

    private final CommonMapper commonMapper;
    private final SysLoginLogMapper sysLoginLogMapper;
    private final FileManageService fileManageService;
    private final KKMqProducerHelper kkMqProducerHelper;
    private final RpRevenueDataMapper rpRevenueDataMapper;
    private final OpRpUserDetailMapper opRpUserDetailMapper;

    @Override
    public RspList<RpUserDetailBO> getRpUserDetailList(RpUserDetailQueryReqBO reqBO) {
        log.info("查询用户明细报表-开始: {}", reqBO);
        OpRpUserDetailSelectCondition condition = new OpRpUserDetailSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setTenantCode(reqBO.getTargetTenant());
        condition.setUserId(reqBO.getTargetUid());
        Page<OpRpUserDetail> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        opRpUserDetailMapper.selectByCondition(condition);
        List<RpUserDetailBO> list = Lists.newArrayList();
        NiccCommonUtil.copyList(page.getResult(), list, RpUserDetailBO.class);
        for (RpUserDetailBO rpUserDetailBO : list) {
            //查询课件帮平台登录次数
            Integer tdhLoginCount = sysLoginLogMapper.selectTdhLoginCount(rpUserDetailBO.getTenantCode(),
                                                                          rpUserDetailBO.getUserId(),
                                                                          JoinTenantType.KE_JIAN_Bang.getCode());
            rpUserDetailBO.setTdhLoginCount(tdhLoginCount);
            if (StringUtils.isBlank(rpUserDetailBO.getLastGoodsName())) {
                rpUserDetailBO.setLastGoodsName(SpuNameEnum.FREE.getName());
            }
            Integer beautCount = commonMapper.queryBeautPPTCount(rpUserDetailBO.getUserId(), rpUserDetailBO.getTenantCode());
            rpUserDetailBO.setBeautCount(beautCount);
        }
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    @Override
    public Rsp exportRpUserDetailList(RpUserDetailQueryReqBO queryReqBO) {
        RpUserDetailExportBO rspBO = new RpUserDetailExportBO();
        queryReqBO.setLimit(999999999);
        RspList<RpUserDetailBO> rspList = getRpUserDetailList(queryReqBO);
        if (!rspList.isSuccess()) {
            return BaseRspUtils.createErrorRsp("查询用户明细报表失败");
        }
        List<RpUserDetailBO> data = rspList.getRows();
        List<OpRpUserDetailExportPO> list = new ArrayList<>();
        NiccCommonUtil.copyList(data, list, OpRpUserDetailExportPO.class);
        //转换数据
        list = list.stream().map(item -> {
            item.setUserType("0".equals(item.getUserType()) ? "个人用户" :
                             "1".equals(item.getUserType()) ? "企业用户" : "未知");

            item.setVipStatus("2".equals(item.getVipStatus()) ? "未开通" :
                              "0".equals(item.getVipStatus()) ? "已过期" :
                              "1".equals(item.getVipStatus()) ? "有效" : "未知");
            item.setRegChannel(JoinTenantType.getNameByCode(item.getRegChannel()));
            item.setVipType("0".equals(item.getVipType()) ? "免费" :
                            "1".equals(item.getVipType()) ? "体验会员" :
                            "2".equals(item.getVipType()) ? "高级会员" :
                            "3".equals(item.getVipType()) ? "专业会员" : "未知");

            item.setPaymentStatus(item.getTotalPayAmount() > 0 ? "已付费" : "未付费");
            if (item.getLastPayAmount() != null) {
                item.setLastPayPrice(String.format("%.2f", item.getLastPayAmount() / 100.0));
            }
            if (item.getTotalPayAmount() != null) {
                item.setTotalRevenue(String.format("%.2f", item.getTotalPayAmount() / 100.0));
            }
            return item;
        }).collect(Collectors.toList());

        String fileName = UserDetailConstants.EXCEL_NAME + new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒").format(new Date()) + ".xlsx";
        String tempPath = System.getProperty("java.io.tmpdir");
        File dirFile = new File(tempPath + "/" + fileName);
        EasyExcel.write(dirFile, OpRpUserDetailExportPO.class).sheet("用户明细报表").doWrite(list);
        log.info("试题导出:{}|{}", dirFile.exists(), dirFile.getAbsolutePath());
        MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(dirFile);
        FileUploadRequest uploadRequest = new FileUploadRequest();
        uploadRequest.setTenantCode(queryReqBO.getTenantCode());
        uploadRequest.setUploadUser(queryReqBO.getUserId());
        uploadRequest.setFileName(dirFile.getName());
        try {
            uploadRequest.setFile(multipartFile.getBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        uploadRequest.setUseOriginalName(true);
        RspList<FileManageSaveBO> fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
        log.info("文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
        if (!fileManageSaveBOS.isSuccess()) {
            return BaseRspUtils.createErrorRsp("上传文件失败");
        }
        rspBO.setFileManageSaveBO(fileManageSaveBOS.getRows());
        log.info("试题导出-导出成功");

        return BaseRspUtils.createSuccessRsp(rspBO, "导出成功");
    }

    @Override
    public void handleLoginEvent(UserLoginEventContext context) {
        //写入登录日志
        SysLoginLog record = new SysLoginLog();
        BeanUtils.copyProperties(context, record);
        sysLoginLogMapper.insertSelective(record);
        //更新用户登录次数
        List<OpRpUserDetail> details = getUserDetail(context.getTenantCode(), context.getUserId());
        if (!details.isEmpty()) {
            OpRpUserDetail orgDetail = details.get(0);
            OpRpUserDetail update = new OpRpUserDetail();
            update.setId(orgDetail.getId());
            update.setLoginTime(context.getLoginTime());
            update.setLoginCount(orgDetail.getLoginCount() + 1);
            update.setUpdateTime(new Date());
            opRpUserDetailMapper.updateByPrimaryKeySelective(update);
        }
        // 发送用户操作日志
        UserOperateLogContext userOperateLogContext = UserOperateLogContext.build(LOGIN, context.getTenantCode(), context.getUserId(), null, null, null);
        try {
            log.info("发送用户操作日志事件消息: {}", userOperateLogContext);
            kkMqProducerHelper.sendMsg(NBCHAT_USER_OPERATE_LOG_TOPIC, userOperateLogContext);
        } catch (Exception e) {
            log.error("发送用户操作日志事件消息-异常:{}", userOperateLogContext, e);
        }
    }

    @Override
    public void handleVipChangeEvent(UserVipChangeContext context) {
        List<OpRpUserDetail> details = getUserDetail(context.getTenantCode(), context.getUserId());
        if (!details.isEmpty()) {
            OpRpUserDetail orgDetail = details.get(0);
            OpRpUserDetail update = new OpRpUserDetail();
            update.setId(orgDetail.getId());
            update.setUpdateTime(new Date());
            update.setVipStatus(context.getVipStatus());
            update.setVipStartTime(context.getVipStart());
            update.setVipEndTime(context.getVipEnd());
            update.setVipType(context.getVipType());
            if (orgDetail.getVipBuyCount() == null) {
                orgDetail.setVipBuyCount(0);
            }
            if (orgDetail.getScoreBuyCount() == null) {
                orgDetail.setScoreBuyCount(0);
            }
            update.setVipBuyCount(orgDetail.getVipBuyCount() + 1);
            if (VipEventType.VIP_OPEN.getCode().equals(context.getEventType())) {
                update.setVipFirstTime(context.getCreateTime());
            }
            if (context.getDays() != null && context.getDays() > 0) {
                update.setBuyVersion(String.valueOf(context.getCycle() * context.getDays()));
            }
            if (VipEventType.DOU_RECHARGE.getCode().equals(context.getEventType())) {
                OpRpUserDetail updateBuyCount = new OpRpUserDetail();
                updateBuyCount.setId(orgDetail.getId());
                updateBuyCount.setUpdateTime(new Date());
                updateBuyCount.setVipBuyCount(update.getVipBuyCount());
                updateBuyCount.setScoreBuyCount(orgDetail.getScoreBuyCount() + 1);
                opRpUserDetailMapper.updateByPrimaryKeySelective(updateBuyCount);
            } else {
                opRpUserDetailMapper.updateByPrimaryKeySelective(update);
            }
        }
    }

    @Override
    public void handleMakeEventEvent(UserMakeEventContext context) {
        List<OpRpUserDetail> details = getUserDetail(context.getTenantCode(), context.getUserId());
        if (!details.isEmpty()) {
            OpRpUserDetail detail = details.get(0);
            OpRpUserDetail update = new OpRpUserDetail();
            update.setId(detail.getId());
            update.setUpdateTime(new Date());
            if (MakeEventType.MAKE_TDH.getCode().equals(context.getEventType())) {
                //数字人创作次数
                update.setVideoMakeCount(opRpUserDetailMapper.countTdhTotal(context.getTenantCode(), context.getUserId()));
                update.setVideoSuccessCount(opRpUserDetailMapper.countTdhSuccess(context.getTenantCode(), context.getUserId()));
            }
            if (MakeEventType.MAKE_EXAM.getCode().equals(context.getEventType())) {
                //更新试题创作次数
                update.setExamMakeCount(detail.getExamMakeCount() + context.getSuccessCount());
                update.setExamSuccessCount(detail.getVideoSuccessCount() + context.getSuccessCount());
            }
            if (MakeEventType.MAKE_PPT.getCode().equals(context.getEventType())) {
                //更新ppt制作次数
                update.setPptMakeCount(detail.getPptMakeCount() + context.getSuccessCount());
                //update.setPptSuccessCount(detail.getPptSuccessCount() + context.getSuccessCount());
            }
            opRpUserDetailMapper.updateByPrimaryKeySelective(update);
        }
    }

    @Override
    public void handleScoreChangeEvent(UserScoreChangeContext context) {
        log.info("更新用户报表-算力点消费事件: {}", context);
        opRpUserDetailMapper.updateUserScore(context.getTenantCode(), context.getUserId(), context.getScoreBalance());
    }

    @Override
    public void handlePayEvent(UserPayEventContext context) {
        List<OpRpUserDetail> details = getUserDetail(context.getTenantCode(), context.getUserId());
        if (!details.isEmpty()) {
            log.info("更新用户报表-支付事件: {}", context);
            //更新最近购买的商品名称
            String goodsName = rpRevenueDataMapper.selectSkuNameByOrderNo(context.getOrderNo());
            OpRpUserDetail orgDetail = details.get(0);
            OpRpUserDetail update = new OpRpUserDetail();
            update.setId(orgDetail.getId());
            update.setUpdateTime(new Date());
            update.setLastPayAmount(context.getPayAmount());
            update.setTotalPayAmount(orgDetail.getTotalPayAmount() + context.getPayAmount());
            update.setLastPayTime(context.getPayTime());
            update.setLastGoodsName(goodsName);
            opRpUserDetailMapper.updateByPrimaryKeySelective(update);
        }
    }

    private List<OpRpUserDetail> getUserDetail(String tenantCode, String userId) {
        OpRpUserDetailSelectCondition condition = new OpRpUserDetailSelectCondition();
        condition.setTenantCode(tenantCode);
        condition.setUserId(userId);
        return opRpUserDetailMapper.selectByCondition(condition);
    }

}
