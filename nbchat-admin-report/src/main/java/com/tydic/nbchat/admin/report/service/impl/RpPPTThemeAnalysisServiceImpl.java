package com.tydic.nbchat.admin.report.service.impl;

import com.tydic.nbchat.admin.api.bo.eum.SwitchLayoutEnum;
import com.tydic.nbchat.admin.api.bo.rp.RpPPTLayoutUsageBO;
import com.tydic.nbchat.admin.api.bo.rp.RpPPTUsageSaveReqBO;
import com.tydic.nbchat.admin.api.bo.rp.RpPPTUsageUpdateReqBO;
import com.tydic.nbchat.admin.api.bo.eum.ChangeStatusEnum;
import com.tydic.nbchat.admin.api.rp.RpPPTThemeAnalysisService;
import com.tydic.nbchat.admin.mapper.RpPptLayoutUsageMapper;
import com.tydic.nbchat.admin.mapper.RpPptUsageMapper;
import com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage;
import com.tydic.nbchat.admin.mapper.po.RpPptUsage;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RpPPTThemeAnalysisServiceImpl implements RpPPTThemeAnalysisService {
    private final RpPptUsageMapper rpPptUsageMapper;
    private final RpPptLayoutUsageMapper rpPptLayoutUsageMapper;

    public RpPPTThemeAnalysisServiceImpl(RpPptUsageMapper rpPptUsageMapper, RpPptLayoutUsageMapper rpPptLayoutUsageMapper) {
        this.rpPptUsageMapper = rpPptUsageMapper;
        this.rpPptLayoutUsageMapper = rpPptLayoutUsageMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveUserUsage(RpPPTUsageSaveReqBO reqBO) {
        try {
            RpPptUsage currentUsage = getLatestPptUsageByPptId(reqBO.getPptId());
            if (currentUsage != null) {
                RpPPTUsageUpdateReqBO updateReqBO = new RpPPTUsageUpdateReqBO();
                BeanUtils.copyProperties(reqBO, updateReqBO);
                return  updateUserUsage(updateReqBO);
            }

            // 1. 保存PPT使用信息
            RpPptUsage pptUsage = buildPptUsage(reqBO);
            rpPptUsageMapper.insertSelective(pptUsage);

            // 2. 保存布局使用信息
            savePptLayoutUsages(reqBO, pptUsage);

            return BaseRspUtils.createSuccessRsp("保存PPT使用信息成功");
        } catch (Exception e) {
            log.error("保存PPT使用信息失败", e);
            return BaseRspUtils.createSuccessRsp("保存PPT使用信息失败");
        }
    }


    public Rsp updateUserUsage(RpPPTUsageUpdateReqBO reqBO) {
        try {
            // 参数校验
            if (!StringUtils.hasText(reqBO.getPptId())) {
                return BaseRspUtils.createSuccessRsp("PPT ID不能为空");
            }

            // 查询当前PPT使用记录
            RpPptUsage currentUsage = getLatestPptUsageByPptId(reqBO.getPptId());
            if (currentUsage == null) {
                return BaseRspUtils.createSuccessRsp("未找到对应的PPT使用记录");
            }

            Date now = new Date();
            boolean updated = false;

            // 更新主题信息
            if (updateThemeUsage(currentUsage, reqBO.getUseThemeId(), now)) {
                updated = true;
            }

            // 更新布局信息
            if (updateLayoutUsages(reqBO, currentUsage, now)) {
                updated = true;
            }

            if (!updated) {
                return BaseRspUtils.createSuccessRsp("未提供任何需要更新的信息");
            }

            return BaseRspUtils.createSuccessRsp("更新PPT使用信息成功");
        } catch (Exception e) {
            log.error("更新PPT使用信息失败", e);
            return BaseRspUtils.createSuccessRsp("更新PPT使用信息失败");
        }
    }

    /**
     * 构建PPT使用信息对象
     */
    private RpPptUsage buildPptUsage(RpPPTUsageSaveReqBO reqBO) {
        RpPptUsage pptUsage = new RpPptUsage();
        pptUsage.setPptId(reqBO.getPptId());
        pptUsage.setThemeId(reqBO.getThemeId());
        pptUsage.setAiScene(reqBO.getAiScene());
        pptUsage.setAiStyle(reqBO.getAiStyle());
        pptUsage.setAiColor(reqBO.getAiColor());
        pptUsage.setUseThemeId(reqBO.getUseThemeId());
        pptUsage.setTotalPage(reqBO.getTotalPage() != null ? reqBO.getTotalPage().shortValue() : null);

        // 计算是否更换了主题
        String isChanged = ChangeStatusEnum.NOT_CHANGED.getCode();
        if (reqBO.getThemeId() != null && reqBO.getUseThemeId() != null
                && !reqBO.getThemeId().equals(reqBO.getUseThemeId())) {
            isChanged = ChangeStatusEnum.CHANGED.getCode();
        }
        pptUsage.setIsChanged(isChanged);

        // 设置租户和用户信息
        pptUsage.setTenantCode(reqBO.getTenantCode());
        pptUsage.setUserId(reqBO.get_userId());
        pptUsage.setIsFree(reqBO.getIsFree());
        pptUsage.setUserType(reqBO.getUserType());


        // 设置时间
        Date now = new Date();
        pptUsage.setCreateTime(now);
        pptUsage.setUpdateTime(now);

        return pptUsage;
    }

    /**
     * 保存PPT布局使用信息
     */
    private void savePptLayoutUsages(RpPPTUsageSaveReqBO reqBO, RpPptUsage pptUsage) {
        if (reqBO.getLayouts() == null || reqBO.getLayouts().isEmpty()) {
            return;
        }

        List<RpPptLayoutUsage> layoutUsages = new ArrayList<>();
        Date now = new Date();

        for (RpPPTLayoutUsageBO layoutBO : reqBO.getLayouts()) {
            RpPptLayoutUsage layoutUsage = new RpPptLayoutUsage();
            layoutUsage.setPptId(reqBO.getPptId());
            layoutUsage.setLayoutId(layoutBO.getLayoutId());
            layoutUsage.setPageNum(layoutBO.getPageNum() != null ? layoutBO.getPageNum().shortValue() : null);
            layoutUsage.setUseLayoutId(layoutBO.getUseLayoutId());
            layoutUsage.setLayid(layoutBO.getLayId());


            // 计算布局是否更换
            String layoutIsChanged = ChangeStatusEnum.NOT_CHANGED.getCode();
            if (layoutBO.getLayoutId() != null && layoutBO.getUseLayoutId() != null
                    && !layoutBO.getLayoutId().equals(layoutBO.getUseLayoutId())) {
                layoutIsChanged = ChangeStatusEnum.CHANGED.getCode();
            }
            layoutUsage.setIsChanged(layoutIsChanged);

            // 设置租户和用户信息
            layoutUsage.setTenantCode(pptUsage.getTenantCode());
            layoutUsage.setUserId(pptUsage.getUserId());

            // 设置时间
            layoutUsage.setCreateTime(now);
            layoutUsage.setUpdateTime(now);

            layoutUsages.add(layoutUsage);
        }

        // 批量插入布局使用记录
        if (!layoutUsages.isEmpty()) {
            rpPptLayoutUsageMapper.batchInsert(layoutUsages);
        }
    }

    /**
     * 获取最新的PPT使用记录
     */
    private RpPptUsage getLatestPptUsageByPptId(String pptId) {
        RpPptUsage query = new RpPptUsage();
        query.setPptId(pptId);
        List<RpPptUsage> existingUsages = rpPptUsageMapper.selectByAll(query);

        if (existingUsages == null || existingUsages.isEmpty()) {
            return null;
        }

        if (existingUsages.size() == 1) {
            return existingUsages.get(0);
        }

        return existingUsages.stream()
                .max(Comparator.comparing(RpPptUsage::getId))
                .orElse(existingUsages.get(0));
    }

    /**
     * 更新主题使用信息
     */
    private boolean updateThemeUsage(RpPptUsage currentUsage, String useThemeId, Date now) {
        if (useThemeId == null) {
            return false;
        }

        currentUsage.setUseThemeId(useThemeId);

        // 计算是否更改主题
        String isChanged = ChangeStatusEnum.NOT_CHANGED.getCode();
        if (currentUsage.getThemeId() != null && !currentUsage.getThemeId().equals(useThemeId)) {
            isChanged = ChangeStatusEnum.CHANGED.getCode();
        }
        currentUsage.setIsChanged(isChanged);
        currentUsage.setUpdateTime(now);

        rpPptUsageMapper.updateByPrimaryKeySelective(currentUsage);
        return true;
    }

    /**
     * 更新布局使用信息
     */
    private boolean updateLayoutUsages(RpPPTUsageUpdateReqBO reqBO, RpPptUsage currentUsage, Date now) {
        if (reqBO.getLayouts() == null || reqBO.getLayouts().isEmpty()) {
            return false;
        }

        boolean updated = false;

        for (RpPPTLayoutUsageBO layoutBO : reqBO.getLayouts()) {
            if (layoutBO.getLayId() == null || layoutBO.getUseLayoutId() == null) {
                continue;
            }

            // 查询当前布局
            RpPptLayoutUsage currentLayout = getLatestLayoutUsage(reqBO.getPptId(), layoutBO.getLayId());

            if (currentLayout != null) {
                if (SwitchLayoutEnum.DELETE.getCode().equals(layoutBO.getType())){
                    // 删除现有布局
                    rpPptLayoutUsageMapper.deleteByPrimaryKey(currentLayout.getId());
                }
                if (SwitchLayoutEnum.UPDATE.getCode().equals(layoutBO.getType())){
                    // 更新现有布局
                    updateExistingLayout(currentLayout, layoutBO, now);
                }
            } else {
                // 创建新的布局记录
                createNewLayoutUsage(reqBO.getPptId(), layoutBO, currentUsage, now);
            }

            updated = true;
        }

        return updated;
    }

    /**
     * 获取最新的布局使用记录
     */
    private RpPptLayoutUsage getLatestLayoutUsage(String pptId, String layId) {
        RpPptLayoutUsage layoutQuery = new RpPptLayoutUsage();
        layoutQuery.setPptId(pptId);
        layoutQuery.setLayid(layId);
        List<RpPptLayoutUsage> existingLayouts = rpPptLayoutUsageMapper.selectByAll(layoutQuery);

        if (existingLayouts == null || existingLayouts.isEmpty()) {
            return null;
        }

        if (existingLayouts.size() == 1) {
            return existingLayouts.get(0);
        }

        return existingLayouts.stream()
                .max(Comparator.comparing(RpPptLayoutUsage::getId))
                .orElse(existingLayouts.get(0));
    }

    /**
     * 更新现有布局
     */
    private void updateExistingLayout(RpPptLayoutUsage layout, RpPPTLayoutUsageBO layoutBO, Date now) {
        layout.setUseLayoutId(layoutBO.getUseLayoutId());
        layout.setPageNum(layoutBO.getPageNum() != null ? layoutBO.getPageNum().shortValue() : null);

        // 计算布局是否更改
        String isChanged = ChangeStatusEnum.NOT_CHANGED.getCode();
        if (layout.getLayoutId() != null && !layout.getLayoutId().equals(layoutBO.getUseLayoutId())) {
            isChanged = ChangeStatusEnum.CHANGED.getCode();
        }
        layout.setIsChanged(isChanged);
        layout.setUpdateTime(now);

        rpPptLayoutUsageMapper.updateByPrimaryKeySelective(layout);
    }

    /**
     * 创建新的布局使用记录
     */
    private void createNewLayoutUsage(String pptId, RpPPTLayoutUsageBO layoutBO, RpPptUsage pptUsage, Date now) {
        RpPptLayoutUsage newLayout = new RpPptLayoutUsage();
        newLayout.setPptId(pptId);
        newLayout.setPageNum(layoutBO.getPageNum()==null?null:layoutBO.getPageNum().shortValue());
        newLayout.setUseLayoutId(layoutBO.getUseLayoutId());
        newLayout.setLayoutId(layoutBO.getLayoutId()); // 如果有原始布局ID，则设置
        newLayout.setLayid(layoutBO.getLayId());

        // 计算是否变更
        String isChanged = ChangeStatusEnum.NOT_CHANGED.getCode();
        if (layoutBO.getLayoutId() != null && !Objects.equals(layoutBO.getLayoutId(), layoutBO.getUseLayoutId())) {
            isChanged = ChangeStatusEnum.CHANGED.getCode();
        }
        newLayout.setIsChanged(isChanged);

        newLayout.setTenantCode(pptUsage.getTenantCode());
        newLayout.setUserId(pptUsage.getUserId());
        newLayout.setCreateTime(now);
        newLayout.setUpdateTime(now);

        rpPptLayoutUsageMapper.insertSelective(newLayout);
    }
}