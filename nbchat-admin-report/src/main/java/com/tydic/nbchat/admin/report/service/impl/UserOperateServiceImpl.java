package com.tydic.nbchat.admin.report.service.impl;

import com.tydic.nbchat.admin.api.bo.rp.UserOperateInfoBO;
import com.tydic.nbchat.admin.api.rp.UserOperateApi;
import com.tydic.nbchat.admin.api.rp.UserRpApi;
import com.tydic.nbchat.admin.mapper.SysUserOperateLogMapper;
import com.tydic.nbchat.admin.mapper.po.SysUserOperateLog;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.tydic.nicc.common.nbchat.emus.UserOperationType.*;

/**
 * <AUTHOR>
 * @date 2025/3/20 19:20
 * @description 用户操作记录Service
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserOperateServiceImpl implements UserOperateApi {

    private UserRpApi userRpApi;
    private SysUserOperateLogMapper sysUserOperateLogMapper;

    /**
     * 保存用户操作记录。
     *
     * @param param 用户操作信息，包括租户ID、用户ID、操作类型、业务ID和操作内容
     * @return 操作结果响应对象，包含操作状态及相关消息
     */
    @Override
    public Rsp<?> save(UserOperateInfoBO param) {
        log.info("保存用户操作记录：{}", param);
        SysUserOperateLog record = new SysUserOperateLog();
        BeanUtils.copyProperties(param, record);
        sysUserOperateLogMapper.insertSelective(record);
        // 额外事件处理
        // LOGIN-登录 无额外事件处理
        // LOGOUT-登出 无额外事件处理
        // REGISTER-注册 已实现数据统计
        // PPT_DOWNLOAD-ppt下载 存在后需数据统计，需要另行处理
        // VIDEO_DOWNLOAD-视频下载 存在后需数据统计，需要另行处理
        // EXAM_GENE-试题生成 存在后需数据统计，需要另行处理
        // EXAM_EXPORT-试题导出 存在后需数据统计，需要另行处理
        if (List.of(PPT_DOWNLOAD.getType(), VIDEO_DOWNLOAD.getType(), EXAM_GENE.getType(), EXAM_EXPORT.getType()).contains(param.getType())) {
            userRpApi.rpAutoIncrement(param.getType(), param.getTenantCode(), param.getUserId());
        }
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 用户操作记录
     *
     * @param context 消息事件数据
     */
    @Override
    public void handleOperateEvent(UserOperateLogContext context) {
        // 存储用户操作记录
        UserOperateInfoBO param = new UserOperateInfoBO();
        if (!List.of(LOGIN.getType(), REGISTER.getType()).contains(context.getEventType())) {
            SysUserOperateLog operateLog = sysUserOperateLogMapper.selectLastByTenantCodeAndUserId(context.getTenantCode(), context.getUserId());
            if (operateLog != null) {
                param.setSessionId(operateLog.getSessionId());
            }
        }
        param.setTenantCode(context.getTenantCode());
        param.setUserId(context.getUserId());
        param.setType(context.getEventType());
        param.setBusiId(context.getBusiId());
        param.setContent(context.getContent());
        if (context.getExtParams() != null && context.getExtParams().containsKey("User-Agent")) {
            param.setClientUa((String) context.getExtParams().get("User-Agent"));
        }
        save(param);
    }
}
