package com.tydic.nbchat.admin.report.service.impl;

import com.tydic.nbchat.admin.api.bo.rp.RpUserGrowQueryRspBO;
import com.tydic.nbchat.admin.api.rp.RpUserGrowService;
import com.tydic.nbchat.admin.mapper.OpRpUserDetailMapper;
import com.tydic.nbchat.admin.mapper.RpUserGrowMapper;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetail;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetailSelectCondition;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class RpUserGrowServiceImpl implements RpUserGrowService {
    @Resource
    private OpRpUserDetailMapper opRpUserDetailMapper;
    private RpUserGrowMapper rpUserGrowMapper;
    private final static String INIT_DATE ="2001-01-01 00:00:00";

    @Override
    public Rsp userGrowUp(Date beginDate, Date endDate) {
        RpUserGrowQueryRspBO queryRspBO = new RpUserGrowQueryRspBO();
        String newRegistrationsUtilization = "0%";
        //查询新增免费用户
        int newVipUser = rpUserGrowMapper.queryVipUser(beginDate,endDate);
        queryRspBO.setNewVipUser(newVipUser);
        //查询累计注册人数
        Date date = null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = simpleDateFormat.parse(INIT_DATE);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        int totalUser = rpUserGrowMapper.queryTotalUser(date,endDate);
        queryRspBO.setTotalUser(totalUser);
        //查询新增免费用户
        int newFreeUser = rpUserGrowMapper.queryNewFreeUser(beginDate, endDate);
        queryRspBO.setNewFreeUser(newFreeUser);
        OpRpUserDetailSelectCondition condition = new OpRpUserDetailSelectCondition();
        condition.setRegStartTime(beginDate);
        condition.setRegEndTime(endDate);
        //查询注册用户数据
        List<OpRpUserDetail> regList = opRpUserDetailMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(regList)){
            log.info("没有查询到注册用户数据");
            return BaseRspUtils.createSuccessRsp(queryRspBO.toString(),"没有查询到注册用户数据");
        }
        //新注册使用人数
        int newUseUser = regList.stream()
                .filter(userDetail -> userDetail.getPptMakeCount() + userDetail.getVideoMakeCount() + userDetail.getExamMakeCount() > 0)
                .map(OpRpUserDetail::getUserId)
                .collect(Collectors.toSet()).size();
        //新注册人数
        int newUser = (int)regList.stream()
                .filter(userDetail -> UserAttributeConstants.DEFAULT_TENANT_CODE.equals(userDetail.getTenantCode()))
                .count();
        //新注册使用率
        if (newUser != 0) {
            double newRegistrationsUtilizationRatio = (double) newUseUser / newUser;
            newRegistrationsUtilization = String.format("%.2f%%", newRegistrationsUtilizationRatio * 100);
        }
        queryRspBO.setNewUser(newUser);
        queryRspBO.setNewUseUser(newUseUser);
        queryRspBO.setNewRegRate(newRegistrationsUtilization);
        log.info("查询成功-{}",queryRspBO.toString());
        return BaseRspUtils.createSuccessRsp(queryRspBO.toString(),"查询成功");
    }
}
