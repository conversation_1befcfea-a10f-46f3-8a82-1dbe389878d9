package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nbchat.user.api.bo.context.UserScoreChangeContext;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import com.tydic.nicc.mq.starter.entity.eum.ConsumeMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumeMode = ConsumeMode.ORDERLY,
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_SCORE_CHANGE_RP_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_SCORE_CHANGE_TOPIC)
@Component
public class UserScoreChangeEventConsumer implements KKMqConsumerListener<UserScoreChangeContext> {

    private final RpUserDetailService rpUserDetailService;

    public UserScoreChangeEventConsumer(RpUserDetailService rpUserDetailService) {
        this.rpUserDetailService = rpUserDetailService;
    }

    @Override
    public void onMessage(UserScoreChangeContext userScoreChangeContext) {
        //用户算力点积分消费
        try {
            rpUserDetailService.handleScoreChangeEvent(userScoreChangeContext);
        } catch (Exception e) {
            log.error("用户算力点积分消费-异常: {}", userScoreChangeContext, e);
        }
    }
}
