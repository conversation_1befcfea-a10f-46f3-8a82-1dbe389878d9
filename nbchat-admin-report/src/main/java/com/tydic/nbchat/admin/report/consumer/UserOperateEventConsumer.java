package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.UserOperateApi;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_OPERATE_LOG_CID;
import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_OPERATE_LOG_TOPIC;

/**
 * <AUTHOR>
 * @date 2025/3/20 18:58
 * @description 用户操作事件消费类
 */
@Slf4j
@Component
@AllArgsConstructor
@KKMqConsumer(topic = NBCHAT_USER_OPERATE_LOG_TOPIC, consumerGroup = NBCHAT_USER_OPERATE_LOG_CID)
public class UserOperateEventConsumer implements KKMqConsumerListener<UserOperateLogContext> {

    private final UserOperateApi userOperateApi;

    @Override
    public void onMessage(UserOperateLogContext context) {
        try {
            log.info("用户操作事件消费: {}", context);
            userOperateApi.handleOperateEvent(context);
        } catch (Exception e) {
            log.error("用户操作事件消费-异常: {}", context, e);
        }
    }
}
