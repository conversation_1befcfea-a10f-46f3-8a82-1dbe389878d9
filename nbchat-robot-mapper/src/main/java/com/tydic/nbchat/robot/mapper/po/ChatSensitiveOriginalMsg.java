package com.tydic.nbchat.robot.mapper.po;

import java.util.Date;

public class ChatSensitiveOriginalMsg {

    private String tenantCode;

    private String channelCode;

    private String sessionId;

    private String msgId;

    private String chatType;

    private String chatKey;

    private Date msgTime;

    private String msgContent;

    private String msgFilter;

    private String fromNo;

    private String toNo;

    private String msgType;

    private String msgForm;

    private Short msgStatus;

    private Date createTime;


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId == null ? null : msgId.trim();
    }

    public String getChatKey() {
        return chatKey;
    }

    public void setChatKey(String chatKey) {
        this.chatKey = chatKey == null ? null : chatKey.trim();
    }

    public String getChatType() {
        return chatType;
    }

    public void setChatType(String chatType) {
        this.chatType = chatType;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent == null ? null : msgContent.trim();
    }

    public String getFromNo() {
        return fromNo;
    }

    public void setFromNo(String fromNo) {
        this.fromNo = fromNo == null ? null : fromNo.trim();
    }

    public String getToNo() {
        return toNo;
    }

    public void setToNo(String toNo) {
        this.toNo = toNo == null ? null : toNo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getMsgForm() {
        return msgForm;
    }

    public void setMsgForm(String msgForm) {
        this.msgForm = msgForm;
    }

    public Short getMsgStatus() {
        return msgStatus;
    }

    public void setMsgStatus(Short msgStatus) {
        this.msgStatus = msgStatus;
    }

    public String getMsgFilter() {
        return msgFilter;
    }

    public void setMsgFilter(String msgFilter) {
        this.msgFilter = msgFilter;
    }
}