package com.tydic.nbchat.robot.mapper;

import com.tydic.nbchat.robot.mapper.po.NbchatResearchApp;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchAppSelectCondition;

import java.util.List;

public interface NbchatResearchAppMapper {
    int deleteByPrimaryKey(String appId);

    int insertSelective(NbchatResearchApp record);

    NbchatResearchApp selectByPrimaryKey(String appId);

    int updateByPrimaryKeySelective(NbchatResearchApp record);

    List<NbchatResearchApp> selectByCondition(NbchatResearchAppSelectCondition condition);
}