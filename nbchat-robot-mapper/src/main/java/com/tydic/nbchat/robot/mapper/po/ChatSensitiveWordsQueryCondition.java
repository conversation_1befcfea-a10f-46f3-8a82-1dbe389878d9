package com.tydic.nbchat.robot.mapper.po;

import java.io.Serializable;


public class ChatSensitiveWordsQueryCondition implements Serializable {

    private Integer wordId;
    private Integer sensitiveId;
    private String tenantCode;
    private String wordType;
    private String isValid;
    private String wordText;

    public Integer getWordId() {
        return wordId;
    }

    public void setWordId(Integer wordId) {
        this.wordId = wordId;
    }

    public Integer getSensitiveId() {
        return sensitiveId;
    }

    public void setSensitiveId(Integer sensitiveId) {
        this.sensitiveId = sensitiveId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getWordType() {
        return wordType;
    }

    public void setWordType(String wordType) {
        this.wordType = wordType;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getWordText() {
        return wordText;
    }

    public void setWordText(String wordText) {
        this.wordText = wordText;
    }

    @Override
    public String toString() {
        return "ChatSensitiveWordsQueryCondition{" +
                "wordId=" + wordId +
                ", sensitiveId=" + sensitiveId +
                ", tenantCode='" + tenantCode + '\'' +
                ", wordType='" + wordType + '\'' +
                ", isValid='" + isValid + '\'' +
                ", wordText='" + wordText + '\'' +
                '}';
    }
}
