package com.tydic.nbchat.robot.mapper.po;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSessionMsg)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 23:18:50
 */
public class NbchatSessionMsg implements Serializable {
    private static final long serialVersionUID = -50820551873256616L;
    /**
     * 请求id
     */
    private String requestId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 0 机器人 1 用户
     */
    private String userType;
    /**
     * 会话文本
     */
    private String text;
    // 思考过程
    private String reasoning;

    private String conversationOptions;

    private String prompt;
    private String promptRequestId;
    /**
     * 是否异常
     */
    private String error;
    
    private String requestOptions;

    private String robotType;
    /**
     * 消息时间
     */
    private Date dateTime;


    public String getRobotType() {
        return robotType;
    }

    public void setRobotType(String robotType) {
        this.robotType = robotType;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getConversationOptions() {
        return conversationOptions;
    }

    public void setConversationOptions(String conversationOptions) {
        this.conversationOptions = conversationOptions;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getRequestOptions() {
        return requestOptions;
    }

    public void setRequestOptions(String requestOptions) {
        this.requestOptions = requestOptions;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getPromptRequestId() {
        return promptRequestId;
    }

    public void setPromptRequestId(String promptRequestId) {
        this.promptRequestId = promptRequestId;
    }

    public String getReasoning() {
        return reasoning;
    }

    public void setReasoning(String reasoning) {
        this.reasoning = reasoning;
    }

    @Override
    public String toString() {
        return "NbchatSessionMsg{" +
                "requestId='" + requestId + '\'' +
                ", tenantCode='" + tenantCode + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", userId='" + userId + '\'' +
                ", userType='" + userType + '\'' +
                ", text='" + text + '\'' +
                ", reasoning='" + reasoning + '\'' +
                ", conversationOptions='" + conversationOptions + '\'' +
                ", prompt='" + prompt + '\'' +
                ", promptRequestId='" + promptRequestId + '\'' +
                ", error='" + error + '\'' +
                ", requestOptions='" + requestOptions + '\'' +
                ", dateTime=" + dateTime +
                '}';
    }
}

