package com.tydic.nbchat.robot.mapper;

import com.tydic.nbchat.robot.mapper.po.NbchatRobotToken;
import com.tydic.nbchat.robot.mapper.po.NbchatRobotTokenSelectCondition;

import java.util.List;

public interface NbchatRobotTokenMapper {

    NbchatRobotToken selectByPrimaryKey(Integer tokenId);

    List<NbchatRobotToken> selectByCondition(NbchatRobotTokenSelectCondition condition);

    void updateInValid(String token);

    int updateTokenKey(NbchatRobotToken token);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(NbchatRobotToken record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateSelective(NbchatRobotToken record);
}
