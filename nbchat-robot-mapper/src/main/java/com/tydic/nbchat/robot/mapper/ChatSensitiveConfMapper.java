package com.tydic.nbchat.robot.mapper;

import com.tydic.nbchat.robot.mapper.po.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname ChatSensitiveConfMapper
 * @Description 敏感词配置
 * @Date 2021/12/22 6:14 下午
 * @Created by kangkang
 */
public interface ChatSensitiveConfMapper {

    /**
     * 查询敏感词配置
     * @param condition
     * @return
     */
    List<ChatSensitiveWordsConf> selectWordsConfByCondition(ChatSensitiveConfQueryCondition condition);

    /**
     * 查询租户配置
     * @param condition
     * @return
     */
    List<ChatSensitiveConf> selectConfigsByCondition(ChatSensitiveConfQueryCondition condition);


    /**
     * 查询词库列表
     * @param condition
     * @return
     */
    List<ChatSensitiveWords> selectWordsListByCondition(ChatSensitiveWordsQueryCondition condition);

    /**
     * 插入词库
     * @param sensitiveWords
     * @return
     */
    int insertWords(ChatSensitiveWords sensitiveWords);

    /**
     * 更新词库
     * @param sensitiveWords
     * @return
     */
    int updateWords(ChatSensitiveWords sensitiveWords);

    /**
     * 是否已存在配置
     * @param tenantCode
     * @param wordText
     * @return
     */
    ChatSensitiveWords getOneWord(@Param("tenantCode") String tenantCode,
                 @Param("wordText") String wordText);
}
