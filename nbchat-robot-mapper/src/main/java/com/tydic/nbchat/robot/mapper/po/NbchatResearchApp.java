package com.tydic.nbchat.robot.mapper.po;

import lombok.Data;

import java.util.Date;
@Data
public class NbchatResearchApp {
    private String appId;

    private String appName;

    private String appDesc;

    private String appTag;

    private String appType;

    private String robotType;

    private String category;

    private String avatarImg;

    private String bizIds;

    private String tenantCode;

    private String userId;

    private String isValid;

    private String isShare;

    private String appSource;

    private Short orderIndex;

    private Date createTime;

    private String allowGuest;

    private String guestUrl;

    private String allowApi;

    private String apiUrl;

    private Date updateTime;

    private String appConfig;
    //发布状态
    private String publishState;
    //多回话
    private String multiSession;
    private Integer chatTurn;
    private String appStatus;

    private String queryRewrite; //查询改写1是 0否

}