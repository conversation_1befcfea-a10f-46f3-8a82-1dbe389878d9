<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.NbchatRobotSessionMsgMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg" id="NbchatSessionMsgMap">
        <result property="requestId" column="request_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="text" column="text" jdbcType="VARCHAR"/>
        <result property="reasoning" column="reasoning" jdbcType="VARCHAR"/>
        <result property="prompt" column="prompt" jdbcType="VARCHAR"/>
        <result property="promptRequestId" column="prompt_request_id" jdbcType="VARCHAR"/>
        <result property="conversationOptions" column="conversation_options" jdbcType="VARCHAR"/>
        <result property="error" column="error" jdbcType="VARCHAR"/>
        <result property="requestOptions" column="request_options" jdbcType="VARCHAR"/>
        <result property="dateTime" column="date_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        request_id, tenant_code, session_id, user_id, user_type,prompt,prompt_request_id,
            text, reasoning, conversation_options, error, request_options, date_time</sql>


    <select id="selectLastContents" resultMap="NbchatSessionMsgMap">
        select request_id, session_id, prompt, text, date_time, user_type
        from nbchat_session_msg WHERE session_id = #{sessionId} AND
                ( select date_time from nbchat_session_msg
                  where request_id = #{requestId} )
                >= date_time AND prompt is not null order by date_time desc limit ${limit}
    </select>

    <select id="selectMsgs" resultMap="NbchatSessionMsgMap">
        select *
        from nbchat_session_msg WHERE session_id = #{sessionId} AND
            ( select date_time from nbchat_session_msg
              where request_id = #{requestId} )
                >= date_time order by date_time desc limit ${limit}
    </select>


    <select id="selectByPromptRequestId" resultMap="NbchatSessionMsgMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_session_msg
        where prompt_request_id = #{promtReqId} limit 1
    </select>


    <update id="updateByRequestId" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg">
        update nbchat_session_msg
        <set>
            <if test="text != null and text != ''">
                 text = #{text},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options = #{conversationOptions},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options = #{requestOptions},
            </if>
            <if test="error != null and error != ''">
               error = #{error},
            </if>
        </set>
        where request_id = #{requestId}
    </update>


    <update id="updateByPromptRequestId" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg">
        update nbchat_session_msg set `text` = #{text}
            where prompt_request_id = #{promptRequestId}
    </update>

    <!--查询单个-->
    <select id="selectByRequestId" resultMap="NbchatSessionMsgMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_msg
        where request_id = #{requestId}
    </select>
    
    
    <select id="selectByCondition"
            resultMap="NbchatSessionMsgMap"
            parameterType="com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_msg
         <where>
            <if test="requestId != null and requestId != ''">
                and request_id = #{requestId}
            </if>
            <!--<if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>-->
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>
            <if test="text != null and text != ''">
                and text = #{text}
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                and conversation_options = #{conversationOptions}
            </if>
            <if test="error != null and error != ''">
                and error = #{error}
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                and request_options = #{requestOptions}
            </if>
            <if test="dateTime != null">
                and date_time = #{dateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="" useGeneratedKeys="true" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg">
        insert into nbchat_session_msg
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="requestId != null and requestId != ''">
                request_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="userType != null and userType != ''">
                user_type,
            </if>
            <if test="text != null and text != ''">
                text,
            </if>
            <if test="reasoning != null and reasoning != ''">
                reasoning,
            </if>
            <if test="prompt != null and prompt != ''">
                prompt,
            </if>
            <if test="promptRequestId != null and promptRequestId != ''">
                prompt_request_id,
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options,
            </if>
            <if test="error != null and error != ''">
                error,
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options,
            </if>
            <if test="dateTime != null">
                date_time,
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="requestId != null and requestId != ''">
                #{requestId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="userType != null and userType != ''">
                #{userType},
            </if>
            <if test="text != null and text != ''">
                #{text},
            </if>
            <if test="reasoning != null and reasoning != ''">
                #{reasoning},
            </if>
            <if test="prompt != null and prompt != ''">
                #{prompt},
            </if>
            <if test="promptRequestId != null and promptRequestId != ''">
                #{promptRequestId},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                #{conversationOptions},
            </if>
            <if test="error != null and error != ''">
                #{error},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                #{requestOptions},
            </if>
            <if test="dateTime != null">
                #{dateTime},
            </if>
            <if test="robotType != null and robotType != ''">
                #{robotType},
            </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateSelective">
        update nbchat_session_msg
        <set>
            <if test="requestId != null and requestId != ''">
                request_id = #{requestId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userType != null and userType != ''">
                user_type = #{userType},
            </if>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options = #{conversationOptions},
            </if>
            <if test="error != null and error != ''">
                error = #{error},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options = #{requestOptions},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
        </set>
        where request_id = #{requestId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_session_msg where request_id = #{requestId}
    </delete>

</mapper>

