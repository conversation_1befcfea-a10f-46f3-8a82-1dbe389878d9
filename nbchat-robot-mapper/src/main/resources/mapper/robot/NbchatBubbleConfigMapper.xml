<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.NbchatBubbleConfigMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.NbchatBubbleConfig" id="ChatBubbleConfigMap">
        <result property="bubbleId" column="bubble_id" jdbcType="INTEGER"/>
        <result property="showText" column="show_text" jdbcType="VARCHAR"/>
        <result property="sendText" column="send_text" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="data" column="data" jdbcType="VARCHAR"/>
        <result property="configLevel" column="config_level" jdbcType="VARCHAR"/>
        <result property="configLevelVal" column="config_level_val" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="crtTime" column="crt_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_column">
        bubble_id, show_text, send_text, `type`, url, target, `data`, config_level, config_level_val, tenant_code, order_index, crt_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="ChatBubbleConfigMap">
        select
        <include refid="base_column"></include>
        from nbchat_bubble_config
        where bubble_id = #{bubbleId}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.robot.mapper.po.NbchatBubbleConfigSelectCondition"
            resultMap="ChatBubbleConfigMap">
        select
            <include refid="base_column" />
        from nbchat_bubble_config
        <where>
            <if test="bubbleId != null">
                and bubble_id = #{bubbleId}
            </if>
            <if test="type != null and type != ''">
                and `type` = #{type}
            </if>
            <if test="configLevel != null and configLevel != ''">
                and config_level = #{configLevel}
            </if>
            <if test="configLevelVal != null and configLevelVal != ''">
                and config_level_val = #{configLevelVal}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
        </where>
               order by order_index asc,crt_time desc
    </select>

    <!--新增选择列-->
    <insert id="insertSelective" keyProperty="bubbleId" useGeneratedKeys="true">
        insert into nbchat_bubble_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="showText != null and showText != ''">
                show_text,
            </if>
            <if test="sendText != null and sendText != ''">
                send_text,
            </if>
            <if test="type != null and type != ''">
                `type`,
            </if>
            <if test="url != null and url != ''">
                url,
            </if>
            <if test="target != null and target != ''">
                target,
            </if>
            <if test="data != null and data != ''">
                `data`,
            </if>
            <if test="configLevel != null and configLevel != ''">
                config_level,
            </if>
            <if test="configLevelVal != null and configLevelVal != ''">
                config_level_val,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="crtTime != null">
                crt_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="showText != null and showText != ''">
                #{showText},
            </if>
            <if test="sendText != null and sendText != ''">
                #{sendText},
            </if>
            <if test="type != null and type != ''">
                #{type},
            </if>
            <if test="url != null and url != ''">
                #{url},
            </if>
            <if test="target != null and target != ''">
                #{target},
            </if>
            <if test="data != null and data != ''">
                #{data},
            </if>
            <if test="configLevel != null and configLevel != ''">
                #{configLevel},
            </if>
            <if test="configLevelVal != null and configLevelVal != ''">
                #{configLevelVal},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="crtTime != null">
                #{crtTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="bubbleId" useGeneratedKeys="true">
        insert into nbchat_bubble_config(show_text, send_text, type, url, target, data, config_level, config_level_val,
        tenant_code, order_index, crt_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.showText}, #{entity.sendText}, #{entity.type}, #{entity.url}, #{entity.target}, #{entity.data},
            #{entity.configLevel}, #{entity.configLevelVal}, #{entity.tenantCode}, #{entity.orderIndex},
            #{entity.crtTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="bubbleId" useGeneratedKeys="true">
        insert into nbchat_bubble_config(show_text, send_text, type, url, target, data, config_level, config_level_val,
        tenant_code, order_index, crt_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.showText}, #{entity.sendText}, #{entity.type}, #{entity.url}, #{entity.target}, #{entity.data},
            #{entity.configLevel}, #{entity.configLevelVal}, #{entity.tenantCode}, #{entity.orderIndex},
            #{entity.crtTime})
        </foreach>
        on duplicate key update
        show_text = values(show_text) , send_text = values(send_text) , type = values(type) , url = values(url) , target
        = values(target) , data = values(data) , config_level = values(config_level) , config_level_val =
        values(config_level_val) , tenant_code = values(tenant_code) , order_index = values(order_index) , crt_time =
        values(crt_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_bubble_config
        <set>
            <if test="showText != null and showText != ''">
                show_text = #{showText},
            </if>
            <if test="sendText != null and sendText != ''">
                send_text = #{sendText},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
            <if test="target != null and target != ''">
                target = #{target},
            </if>
            <if test="data != null and data != ''">
                data = #{data},
            </if>
            <if test="configLevel != null and configLevel != ''">
                config_level = #{configLevel},
            </if>
            <if test="configLevelVal != null and configLevelVal != ''">
                config_level_val = #{configLevelVal},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="crtTime != null">
                crt_time = #{crtTime},
            </if>
        </set>
        where bubble_id = #{bubbleId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_bubble_config where bubble_id = #{bubbleId}
    </delete>

</mapper>

