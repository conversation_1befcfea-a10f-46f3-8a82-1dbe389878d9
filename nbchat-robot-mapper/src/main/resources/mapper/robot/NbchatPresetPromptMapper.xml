<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt" id="BaseResultMap">
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="presetId" column="preset_id" jdbcType="VARCHAR"/>
        <result property="presetName" column="preset_name" jdbcType="VARCHAR"/>
        <result property="presetDesc" column="preset_desc" jdbcType="VARCHAR"/>
        <result property="presetImg" column="preset_img" jdbcType="VARCHAR"/>
        <result property="systemRole" column="system_role" jdbcType="VARCHAR"/>
        <result property="template" column="template" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="orderIndex" column="order_index" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="historyVersion" column="history_version" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
         tenant_code, preset_id, preset_name,preset_desc,preset_img,system_role,template,
            category,order_index, create_time, is_valid,version,history_version</sql>


    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from nbchat_preset_prompt where preset_id = #{presetId}
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from nbchat_preset_prompt
        <where>
            <if test="presetId != null">
                and preset_id = #{presetId}
            </if>
            <if test="category != null and category != ''">
                and category = #{category,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=VARCHAR}
            </if>
        </where>
        order by order_index asc,create_time desc
    </select>


    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_preset_prompt
        where preset_id = #{presetId}
    </select>


    <select id="selectAll" resultMap="BaseResultMap" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt">
        select
        <include refid="Base_Column_List" />
        from nbchat_preset_prompt
        <where>
            <if test="keyword != null and keyword != ''">
                and instr(CONCAT(preset_name,preset_desc,template,category),#{keyword}) > 0
            </if>
            <if test="presetId != null and presetId != ''">
                and preset_id = #{presetId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="systemRole != null and systemRole != ''">
                and system_role = #{systemRole}
            </if>
            <if test="template != null and template != ''">
                and template = #{template}
            </if>
            <if test="presetName != null and presetName != ''">
                and preset_name = #{presetName}
            </if>
            <if test="presetDesc != null and presetDesc != ''">
                and preset_desc = #{presetDesc}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="presetImg != null and presetImg != ''">
                and preset_img = #{presetImg}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
        </where>
        order by order_index asc,create_time desc
    </select>

    <select id="selectThemeDescs" resultType="string">
        SELECT CONCAT('(', theme_id, ':', theme_desc, ')') AS theme_descs
        FROM ppt_theme
        WHERE theme_state = '1' AND compose_type = '0' AND is_valid = '1';
    </select>


    <insert id="insertSelective" keyProperty="presetId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt">
        insert into nbchat_preset_prompt
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="presetId != null and presetId != '' ">
                preset_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="category != null and category != ''">
                category,
            </if>
            <if test="systemRole != null and systemRole != ''">
                system_role,
            </if>
            <if test="template != null and template != ''">
                template,
            </if>
            <if test="presetName != null and presetName != ''">
                preset_name,
            </if>
            <if test="presetDesc != null">
                preset_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="presetImg != null and presetImg != ''">
                preset_img,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="historyVersion != null and historyVersion != '' ">
                history_version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="presetId != null and presetId != '' ">
                #{presetId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="category != null and category != ''">
                #{category},
            </if>
            <if test="systemRole != null and systemRole != ''">
                #{systemRole},
            </if>
            <if test="template != null and template != ''">
                #{template},
            </if>
            <if test="presetName != null and presetName != ''">
                #{presetName},
            </if>
            <if test="presetDesc != null">
                #{presetDesc},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="presetImg != null and presetImg != ''">
                #{presetImg},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="historyVersion != null and historyVersion != '' ">
                #{historyVersion},
            </if>
        </trim>
    </insert>

    <!--新增所有列-->
    <insert id="insert" keyProperty="presetId" useGeneratedKeys="true">
        insert into nbchat_preset_prompt(tenant_codecategorysystem_roletemplatepreset_namepreset_desccreate_timepreset_imgorder_indexis_valid)
        values (#{tenantCode}#{category}#{systemRole}#{template}#{presetName}#{presetDesc}#{createTime}#{presetImg}#{orderIndex}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_preset_prompt
        <set>
            <if test="presetId != null and presetId != '' ">
                preset_id = #{presetId},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="systemRole != null">
                system_role = #{systemRole},
            </if>
            <if test="template != null">
                template = #{template},
            </if>
            <if test="presetName != null and presetName != ''">
                preset_name = #{presetName},
            </if>
            <if test="presetDesc != null">
                preset_desc = #{presetDesc},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="presetImg != null and presetImg != ''">
                preset_img = #{presetImg},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="historyVersion != null and historyVersion != '' ">
                history_version = #{historyVersion},
            </if>
        </set>
        where preset_id = #{presetId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_preset_prompt where preset_id = #{presetId}
    </delete>

</mapper>

