<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.NbchatRobotTokenMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.NbchatRobotToken" id="BaseResultMap">
        <result property="tokenId" column="token_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="tokenName" column="token_name" jdbcType="VARCHAR"/>
        <result property="tokenKey" column="token_key" jdbcType="VARCHAR"/>
        <result property="tokenIndex" column="token_index" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="TIMESTAMP"/>
        <result property="robotType" column="robot_type" jdbcType="VARCHAR"/>
        <result property="robotModel" column="robot_model" jdbcType="VARCHAR"/>
        <result property="tag" column="tag" jdbcType="VARCHAR"/>
        <result property="envType" column="env_type" jdbcType="VARCHAR"/>
        <result property="apiUrl" column="api_url" jdbcType="VARCHAR"/>
        <result property="accessToken" column="access_token" jdbcType="VARCHAR"/>
        <result property="maxTokens" column="max_tokens" jdbcType="VARCHAR"/>
        <result property="requestMaxTokens" column="request_max_tokens" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="maxChars" column="max_chars" jdbcType="VARCHAR"/>
        <result property="config" column="config" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        token_id, tenant_code, token_name, token_key,env_type,token_index,create_at,config,max_chars,
            robot_type,robot_model, tag, api_url,access_token,request_max_tokens,max_tokens, is_valid</sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from nbchat_robot_token where token_id = #{tokenId,jdbcType=INTEGER}
    </select>

    <update id="updateSelective" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatRobotToken">
        update nbchat_robot_token
        <set>
            <if test="tokenName != null and tokenName != ''">
                token_name = #{tokenName,jdbcType=VARCHAR},
            </if>
            <if test="tokenKey != null and tokenKey != ''">
                token_key = #{tokenKey,jdbcType=VARCHAR},
            </if>
            <if test="tokenIndex != null">
                token_index = #{tokenIndex},
            </if>
            <if test="createAt != null">
                create_at = #{createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type = #{robotType,jdbcType=VARCHAR},
            </if>
            <if test="robotModel != null and robotModel != ''">
                robot_model = #{robotModel,jdbcType=VARCHAR},
            </if>
            <if test="tag != null and tag != ''">
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="apiUrl != null and apiUrl != ''">
                api_url = #{apiUrl,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=VARCHAR},
            </if>
            <if test="maxChars != null">
                max_chars = #{maxChars},
            </if>
            <if test="config != null and config != ''">
                config = #{config,jdbcType=VARCHAR},
            </if>
        </set>
        where token_id = #{tokenId,jdbcType=INTEGER}
    </update>

    <insert id="insertSelective"
            useGeneratedKeys="true" keyProperty="tokenId" keyColumn="token_id"
            parameterType="com.tydic.nbchat.robot.mapper.po.NbchatRobotToken">
        insert into nbchat_robot_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tokenId != null">
                token_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="tokenName != null">
                token_name,
            </if>
            <if test="tokenKey != null">
                token_key,
            </if>
            <if test="tokenIndex != null">
                token_index,
            </if>
            <if test="createAt != null">
                create_at,
            </if>
            <if test="robotType != null">
                robot_type,
            </if>
            <if test="robotModel != null">
                robot_model,
            </if>
            <if test="tag != null">
                tag,
            </if>
            <if test="envType != null">
                env_type,
            </if>
            <if test="apiUrl != null">
                api_url,
            </if>
            <if test="accessToken != null">
                access_token,
            </if>
            <if test="maxTokens != null">
                max_tokens,
            </if>
            <if test="requestMaxTokens != null">
                request_max_tokens,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="maxChars != null">
                max_chars,
            </if>
            <if test="config != null">
                config,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tokenId != null">
                #{tokenId,jdbcType=INTEGER},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tokenName != null">
                #{tokenName,jdbcType=VARCHAR},
            </if>
            <if test="tokenKey != null">
                #{tokenKey,jdbcType=VARCHAR},
            </if>
            <if test="tokenIndex != null">
                #{tokenIndex,jdbcType=VARCHAR},
            </if>
            <if test="createAt != null">
                #{createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="robotType != null">
                #{robotType,jdbcType=VARCHAR},
            </if>
            <if test="robotModel != null">
                #{robotModel,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=VARCHAR},
            </if>
            <if test="envType != null">
                #{envType,jdbcType=VARCHAR},
            </if>
            <if test="apiUrl != null">
                #{apiUrl,jdbcType=VARCHAR},
            </if>
            <if test="accessToken != null">
                #{accessToken,jdbcType=VARCHAR},
            </if>
            <if test="maxTokens != null">
                #{maxTokens,jdbcType=VARCHAR},
            </if>
            <if test="requestMaxTokens != null">
                #{requestMaxTokens,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=VARCHAR},
            </if>
            <if test="maxChars != null">
                #{maxChars,jdbcType=VARCHAR},
            </if>
            <if test="config != null">
                #{config,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from nbchat_robot_token
        <where>
            <if test="tokenId != null">
                and token_id = #{tokenId,jdbcType=INTEGER}
            </if>
            <if test="tokenKey != null and tokenKey != ''">
                and token_key = #{tokenKey,jdbcType=VARCHAR}
            </if>
            <if test="envType != null and envType != ''">
                and env_type = #{envType,jdbcType=VARCHAR}
            </if>
            <if test="robotType != null and robotType != ''">
                and robot_type = #{robotType,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=VARCHAR}
            </if>
        </where>
        order by token_index
    </select>

    <select id="updateInValid">
        update nbchat_robot_token set is_valid = 0,create_at = NOW() where token_key = #{token}
    </select>

    <update id="updateTokenKey" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatRobotToken">
        update nbchat_robot_token set token_key = #{tokenKey,jdbcType=VARCHAR},create_at = NOW() where token_id = #{tokenId,jdbcType=INTEGER}
    </update>

</mapper>

