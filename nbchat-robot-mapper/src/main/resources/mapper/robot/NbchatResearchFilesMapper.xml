<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.robot.mapper.NbchatResearchFilesMapper">

    <resultMap type="com.tydic.nbchat.robot.mapper.po.NbchatResearchFiles" id="BaseResultMap">
        <result property="fileId" column="file_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="majorId" column="major_id"/>
        <result property="userId" column="user_id"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileType" column="file_type"/>
        <result property="accessUrl" column="access_url"/>
        <result property="partCount" column="part_count"/>
        <result property="pageCount" column="page_count"/>
        <result property="createTime" column="create_time"/>
        <result property="category" column="category"/>
        <result property="parseState" column="parse_state"/>
        <result property="qaState" column="qa_state"/>
        <result property="subState" column="sub_state"/>
        <result property="parseTime" column="parse_time"/>
        <result property="docSource" column="doc_source"/>
        <result property="docSub" column="doc_sub"/>
        <result property="isValid" column="is_valid"/>
    </resultMap>

    <sql id="sql_column">
        <trim suffixOverrides=",">
            `file_id`,
            `tenant_code`,
            `major_id`,
            `user_id`,
            `file_name`,
            `file_path`,
            `file_type`,
            `access_url`,
            `part_count`,
            `page_count`,
            `create_time`,
            `category`,
            `parse_state`,
            `qa_state`,
            `sub_state`,
            `parse_time`,
            `doc_source`,
            `doc_sub`,
            `is_valid`,
        </trim>
    </sql>

    <update id="deleteByCategorysWithUser">
        update nbchat_research_files
        set is_valid = '0'
        where user_id = #{userId} and category in
        <foreach collection="categoryList" item="category" index="index" separator="," open="(" close=")">
            #{category}
        </foreach>
    </update>


    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.robot.mapper.po.NbchatResearchFilesSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="sql_column"/>
        from nbchat_research_files
        <where>
            <!-- <if test="tenantCode != null and tenantCode!=''">
                 and `tenant_code` = #{tenantCode}
             </if>-->
            <if test="userId != null and userId!=''">
                and `user_id` = #{userId}
            </if>
            <if test="fileId != null and fileId!=''">
                and `file_id` = #{fileId}
            </if>
            <if test="docSource != null">
                and `doc_source` = #{docSource}
            </if>
            <if test="isValid != null and isValid!=''">
                and `is_valid` = #{isValid}
            </if>
            <if test="majorId != null and majorId!=''">
                and `major_id` = #{majorId}
            </if>
            <if test="stateList != null and stateList.size() > 0">
                and `parse_state` in
                <foreach collection="stateList" item="state" index="index" separator="," open="(" close=")">
                    #{state}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByCategoryWithUser" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatResearchFiles"
            resultMap="BaseResultMap">
        select *
        from nbchat_research_files
        where user_id = #{userId}
          and category = #{category}
    </select>


    <select id="selectPageByCondition" parameterType="com.tydic.nbchat.robot.mapper.po.NbchatResearchFiles"
            resultMap="BaseResultMap">
        select
        <include refid="sql_column"/>
        from nbchat_research_files
        where
        (doc_source = '0' or user_id = #{userId})
        <if test="category != null and category != ''">
            and category =#{category}
        </if>
        <if test="parseState != null and parseState != ''">
            and parse_state =#{parseState}
        </if>
        <if test="docSource != null">
            and doc_source = #{docSource}
        </if>
        and is_valid = '1'
        order by create_time desc
    </select>


    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="sql_column"/>
        from nbchat_research_files
        where file_id = #{fileId}
    </select>

    <delete id="deleteByFileId">
        delete
        nbchat_research_files from nbchat_research_files  where `file_id` =
        #{fileId}
    </delete>


    <update id="deleteByMajorId">
        delete
        nbchat_research_files from nbchat_research_files  where `major_id` =
        #{majorId}
        --update nbchat_research_files set is_valid = '0' where major_id = #{majorId}
    </update>

    <update id="updateSelectiveByMajorId">
        UPDATE `nbchat_research_files`
        SET
        <trim suffixOverrides=",">
            <if test="userId != null and userId!=''">
                `user_id` = #{userId},
            </if>
            <if test="fileName != null and fileName!=''">
                `file_name` = #{fileName},
            </if>
            <if test="partCount != null">
                `part_count` = #{partCount},
            </if>
            <if test="pageCount != null">
                `page_count` = #{pageCount},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="category != null and category!=''">
                `category` = #{category},
            </if>
            <if test="parseState != null and parseState!=''">
                `parse_state` = #{parseState},
            </if>
            <if test="qaState != null and qaState!=''">
                `qa_state` = #{qaState},
            </if>
            <if test="subState != null and subState!=''">
                `sub_state` = #{subState},
            </if>
            <if test="parseTime != null">
                `parse_time` = #{parseTime},
            </if>
            <if test="docSource != null">
                `doc_source` = #{docSource},
            </if>
            <if test="docSub != null and docSub!=''">
                `doc_sub` = #{docSub},
            </if>
            <if test="isValid != null and isValid!=''">
                `is_valid` = #{isValid},
            </if>
        </trim>
        <where>
            <if test="majorId != null and majorId!=''">
                and `major_id` = #{majorId}
            </if>
            <if test="majorId == null and fileId != null and fileId!='' ">
                and `file_id` = #{fileId}
            </if>
        </where>
    </update>

    <update id="updateSelectiveByFileId">
        UPDATE `nbchat_research_files`
        SET
        <trim suffixOverrides=",">
            <if test="userId != null and userId!=''">
                `user_id` = #{userId},
            </if>
            <if test="fileName != null and fileName!=''">
                `file_name` = #{fileName},
            </if>
            <if test="partCount != null">
                `part_count` = #{partCount},
            </if>
            <if test="pageCount != null">
                `page_count` = #{pageCount},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="category != null and category!=''">
                `category` = #{category},
            </if>
            <if test="parseState != null and parseState!=''">
                `parse_state` = #{parseState},
            </if>
            <if test="qaState != null and qaState!=''">
                `qa_state` = #{qaState},
            </if>
            <if test="subState != null and subState!=''">
                `sub_state` = #{subState},
            </if>
            <if test="parseTime != null">
                `parse_time` = #{parseTime},
            </if>
            <if test="docSource != null">
                `doc_source` = #{docSource},
            </if>
            <if test="docSub != null and docSub!=''">
                `doc_sub` = #{docSub},
            </if>
            <if test="isValid != null and isValid!=''">
                `is_valid` = #{isValid},
            </if>
        </trim>
        where `file_id` = #{fileId}
    </update>

    <insert id="insertSelective">
        INSERT INTO `nbchat_research_files`
        (
        <trim suffixOverrides=",">
            <if test="fileId!=null">
                `file_id`,
            </if>
            <if test="tenantCode!=null">
                `tenant_code`,
            </if>
            <if test="userId!=null">
                `user_id`,
            </if>
            <if test="fileName!=null">
                `file_name`,
            </if>
            <if test="filePath!=null">
                `file_path`,
            </if>
            <if test="fileType!=null">
                `file_type`,
            </if>
            <if test="accessUrl!=null">
                `access_url`,
            </if>
            <if test="partCount!=null">
                `part_count`,
            </if>
            <if test="pageCount!=null">
                `page_count`,
            </if>
            <if test="createTime!=null">
                `create_time`,
            </if>
            <if test="category!=null">
                `category`,
            </if>
            <if test="parseState!=null and parseState!=''">
                `parse_state`,
            </if>
            <if test="qaState != null and qaState!=''">
                `qa_state`,
            </if>
            <if test="subState != null and subState!=''">
                `sub_state`,
            </if>
            <if test="parseTime!=null">
                `parse_time`,
            </if>
            <if test="docSource!=null">
                `doc_source`,
            </if>
            <if test="isValid!=null">
                `is_valid`,
            </if>
            <if test="hashId!=null">
                `hash_id`,
            </if>
            <if test="majorId!=null">
                `major_id`,
            </if>
        </trim>
        )
        VALUES
        (
        <trim suffixOverrides=",">
            <if test="fileId!=null">
                #{fileId},
            </if>
            <if test="tenantCode!=null">
                #{tenantCode},
            </if>
            <if test="userId!=null">
                #{userId},
            </if>
            <if test="fileName!=null">
                #{fileName},
            </if>
            <if test="filePath!=null">
                #{filePath},
            </if>
            <if test="fileType!=null">
                #{fileType},
            </if>
            <if test="accessUrl!=null">
                #{accessUrl},
            </if>
            <if test="partCount!=null">
                #{partCount},
            </if>
            <if test="pageCount!=null">
                #{pageCount},
            </if>
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="category!=null">
                #{category},
            </if>
            <if test="parseState!=null">
                #{parseState},
            </if>
            <if test="parseTime!=null">
                #{parseTime},
            </if>
            <if test="docSource!=null">
                #{docSource},
            </if>
            <if test="isValid!=null">
                #{isValid},
            </if>
            <if test="hashId!=null">
                #{hashId},
            </if>
            <if test="majorId!=null">
                #{majorId},
            </if>
        </trim>
        )
    </insert>

    <select id="checkExistsFile" resultType="int">
        select count(*)
        from nbchat_research_files
        where user_id = #{userId}
          and hash_id = #{hashId}
          and is_valid = '1'
    </select>

    <select id="checkExistsMajorFile" resultType="int">
        select count(*)
        from nbchat_research_files
        where major_id = #{majorId}
          and hash_id = #{hashId}
          and is_valid = '1'
    </select>

    <select id="countUserFile" resultType="int">
        select count(*)
        from nbchat_research_files
        where user_id = #{userId}
    </select>

    <select id="countMajorFile" resultType="int">
        select count(*)
        from nbchat_research_files
        where major_id = #{majorId}
          and is_valid = '1'
    </select>

</mapper>