package com.tydic.nbchat.admin.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

@Slf4j
public class AdminCommonUtil {


    public static Date getBirthdayFromIdCard(String idCard) {
        //从身份证提取生日
        if (idCard != null && idCard.length() == 18) {
            String birthDateString = idCard.substring(6, 14);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            try {
                LocalDate birthDate = LocalDate.parse(birthDateString, formatter);
                return Date.from(birthDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } catch (DateTimeParseException e) {
                log.warn("身份证号码格式错误：{}", idCard);
            }
        }
        return null;
    }

    public static Integer getAgeFromIdCard(String idCard) {
        //从身份证提取年龄
        if (idCard != null && idCard.length() == 18) {
            String birthDateString = idCard.substring(6, 14);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            try {
                LocalDate birthDate = LocalDate.parse(birthDateString, formatter);
                LocalDate currentDate = LocalDate.now();
                return Period.between(birthDate, currentDate).getYears();
            } catch (DateTimeParseException e) {
                log.warn("身份证号码格式错误：{}", idCard);
            }
        }
        return null;
    }

    public static Integer getAgeFromBirthday(Date birthday) {
        //从生日提取年龄
        try {
            if (birthday != null) {
                LocalDate birthDate = birthday.toInstant().
                        atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate currentDate = LocalDate.now();
                int age = Period.between(birthDate, currentDate).getYears();
                if (age >= 0) {
                    return age;
                }
            }
        } catch (Exception e) {
            log.warn("生日格式错误：{}", birthday);
        }
        return null;
    }

}
