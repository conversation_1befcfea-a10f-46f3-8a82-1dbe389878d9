package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.bo.file.*;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.admin.mapper.FileUploadRecordMapper;
import com.tydic.nbchat.admin.mapper.po.FileUploadRecord;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.eums.FileUploadType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCompleteRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateRequest;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCreateResponse;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.exception.FileVerifyException;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR> <br>
 * @Description: FileManageServiceImpl  <br>
 * @date 2021/5/11 8:13 下午  <br>
 * @Copyright tydic.com
 */
@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class FileManageServiceImpl implements FileManageService {

    private final static int CONNECT_TIMEOUT = 1000;
    @Resource
    private FileUploadRecordMapper fileUploadRecordMapper;
    @Resource
    private FileManagerHelper fileManagerHelper;
    @Resource
    private RedisHelper redisHelper;
    private final static String MULTI_FILE_UPLOAD_PREFIX = "nbchat-admin:upload:";

    @MethodParamVerifyEnable
    @Override
    public Rsp completeMultipartUpload(ChunkUploadCompleteRequest request) {
        try {
            log.info("合并文件分片-开始: {}", request);
            /**
             * 1. 合并分片
             * 2. 保存上传记录
             * 3. 删除缓存记录
             */
            String key = MULTI_FILE_UPLOAD_PREFIX + request.getFileMd5();
            MultipartUploadCreateResponse response = (MultipartUploadCreateResponse) redisHelper.get(key);
            if (response == null) {
                return BaseRspUtils.createErrorRsp("分片任务不存在!");
            }
            MultipartUploadCompleteRequest completeRequest = MultipartUploadCompleteRequest.builder().
                    uploadId(request.getUploadId()).
                    objectName(response.getObjectName()).
                    contentType(request.getContentType()).build();
            boolean complete = fileManagerHelper.completeMultipartUpload(completeRequest);
            if (complete) {
                FileUploadRecord record = new FileUploadRecord();
                record.setUploadTime(new Date());
                record.setFileNo(response.getFileId());
                record.setFileName(response.getFileName());
                record.setFilePath(response.getObjectName());
                record.setAccessUrl(response.getAccessUrl());
                record.setFileType(response.getFileType());
                record.setUploadType(FileUploadType.MINIO.getCode());
                record.setUploadUser(request.getTenantCode());
                record.setUploadUser(request.getUserId());
                record.setFileMd5(request.getFileMd5());
                fileUploadRecordMapper.insertSelective(record);
                redisHelper.del(key);
                return BaseRspUtils.createSuccessRsp(response, "合并成功!");
            }
            return BaseRspUtils.createErrorRsp("合并文件分片失败!");
        } catch (Exception e) {
            log.error("合并文件分片-异常 {}", request, e);
            return BaseRspUtils.createErrorRsp("合并文件分片异常!");
        }
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp createMultipartUpload(ChunkUploadCreateRequest request) {
        log.info("创建分片上传任务-异常: {}", request);
        boolean check = fileManagerHelper.checkConfigSuffix(request.getFileName());
        if (!check) {
            return BaseRspUtils.createErrorRsp("创建分片上传失败：非法文件格式！");
        }
        String key = MULTI_FILE_UPLOAD_PREFIX + request.getFileMd5();
        try {
            if (request.getChunkCount() <= 0 || request.getChunkCount() > 1000) {
                return BaseRspUtils.createErrorRsp("创建分片上传失败：分片数异常!");
            }
            String fileId = NiccCommonUtil.createImUserId(true);
            String fileType = request.getFileName().toLowerCase().split("\\.")[1];
            String month = DateTimeUtil.getTimeShortString(new Date(), DateTimeUtil.MONTH_FORMAT);
            String objectName = month + File.separator + fileId + "." + fileType;
            MultipartUploadCreateRequest createRequest = MultipartUploadCreateRequest.builder().
                    chunkCount(request.getChunkCount()).
                    objectName(objectName).
                    contentType(request.getContentType()).build();
            MultipartUploadCreateResponse response = fileManagerHelper.createMultipartUpload(createRequest);
            String accessUrl = fileManagerHelper.getFileConfig().getAccessUrlPrefix() + objectName;
            response.setFileId(fileId);
            response.setObjectName(objectName);
            response.setFileType(fileType);
            response.setFileName(request.getFileName());
            response.setAccessUrl(accessUrl);
            redisHelper.set(key, response, TimeUnit.DAYS.toSeconds(1));
            return BaseRspUtils.createSuccessRsp(response);
        } catch (Exception e) {
            log.error("创建分片上传任务-异常: {}", request, e);
            return BaseRspUtils.createErrorRsp("创建分片上传任务异常!");
        }

    }

    private String createDir(String targetDir, boolean autoDir) {
        String dir = "";
        if (autoDir) {
            dir = DateTimeUtil.getTimeShortString(new Date(), DateTimeUtil.MONTH_FORMAT);
        }
        if (StringUtils.isNotBlank(targetDir)) {
            if (targetDir.startsWith("/")) {
                targetDir = targetDir.substring(1);
            }
            //拼接目标目录
            if (StringUtils.isNotBlank(dir)) {
                dir = dir + File.separator + targetDir;
            } else {
                dir = targetDir;
            }
        }
        if (StringUtils.isBlank(dir)) {
            dir = "tmp";
        }
        return dir;
    }


    @Override
    public RspList fileUpload(FileUploadReqBO fileUploadReqBO) {
        log.info("文件上传-保存文件/记录-开始:{}", fileUploadReqBO);
        if (ObjectUtils.anyNotNull(fileUploadReqBO.getFiles(), fileUploadReqBO.getSaveResults())) {
            MultipartFile[] files = fileUploadReqBO.getFiles();
            try {
                String dir = this.createDir(fileUploadReqBO.getTargetDir(), fileUploadReqBO.isAutoDir());
                List<FileManageSaveBO> saveBOS = fileUploadReqBO.getSaveResults();
                if (saveBOS == null) {
                    saveBOS = fileManagerHelper.saveFiles(files, dir,
                            fileUploadReqBO.isCompress(), fileUploadReqBO.isUseOriginalName());
                }
                if (!saveBOS.isEmpty()) {
                    for (FileManageSaveBO saveBO : saveBOS) {
                        FileUploadRecord record = new FileUploadRecord();
                        BeanUtils.copyProperties(saveBO, record);
                        BeanUtils.copyProperties(fileUploadReqBO, record);
                        record.setUploadTime(new Date());
                        fileUploadRecordMapper.insertSelective(record);
                    }
                }
                return BaseRspUtils.createSuccessRspList(saveBOS);
            } catch (FileVerifyException e1) {
                log.info("文件上传-异常:{}", e1.getMessage());
                return BaseRspUtils.createErrorRspList(e1.getErrorCode(), e1.getMessage());
            } catch (Exception e2) {
                log.error("文件上传-异常:", e2);
                return BaseRspUtils.createErrorRspList("文件上传失败:内部错误!");
            }
        }
        return BaseRspUtils.createErrorRspList("文件上传失败:参数异常!");
    }

    @MethodParamVerifyEnable
    @Override
    public RspList fileUploadRequest(FileUploadRequest request) {
        try {
            if (request.getFile() != null) {
                FileUploadReqBO reqBO = new FileUploadReqBO();
                BeanUtils.copyProperties(request, reqBO);
                MultipartFile[] files = {FileManagerHelper.parseToMultipartFile(request.getFileName(), request.getFile())};
                reqBO.setFiles(files);
                return fileUpload(reqBO);
            }
            return BaseRspUtils.createErrorRspList("文件上传失败:请选择文件!");
        } catch (Exception e) {
            log.error("文件上传-异常:", e);
            return BaseRspUtils.createErrorRspList("文件上传失败,请稍后重试!");
        }
    }

    @Override
    public Rsp fileDelete(FileDeleteRequest request) {
        log.info("文件删除-开始:{}", request);
        try {
            if (StringUtils.isNotBlank(request.getFileId())) {
                FileUploadRecord record = fileUploadRecordMapper.selectByFileNo(request.getFileId());
                if (record != null) {
                    fileManagerHelper.delete(record.getFilePath());
                    fileUploadRecordMapper.deleteByFileNo(request.getFileId());
                }
            } else if (StringUtils.isNotBlank(request.getFilePath())) {
                fileManagerHelper.delete(request.getFilePath());
                fileUploadRecordMapper.deleteByPath(request.getFilePath());
            } else if (StringUtils.isNotBlank(request.getBusiCode())) {
                List<FileUploadRecord> files = fileUploadRecordMapper.selectByBusiCode(request.getBusiCode());
                //批量删除
                log.info("文件删除-批量删除:{}|{}", request.getBusiCode(), files.size());
                for (FileUploadRecord record : files) {
                    fileManagerHelper.delete(record.getFilePath());
                    fileUploadRecordMapper.deleteByFileNo(record.getFileNo());
                }
            }
            return BaseRspUtils.createSuccessRsp(request.getFileId());
        } catch (Exception e) {
            log.error("文件删除-异常:{}", request, e);
        }
        return BaseRspUtils.createErrorRsp("文件删除失败!");
    }

    @Transactional
    @MethodParamVerifyEnable
    @Override
    @Deprecated
    public Rsp fileProxyLoad(FileRemoteLoadRequest request) {
        FileRemoteLoadResult result = new FileRemoteLoadResult();
        List<String> urls = new ArrayList<>();
        for (String url : request.getUrls()) {
            FileUploadRecord record = new FileUploadRecord();
            String fileId = NiccCommonUtil.createImUserId(true);
            String fileName = fileId + request.getFileType();
            String accessUrl = url;
            if (StringUtils.isNotBlank(accessUrl)) {
                record.setFileNo(fileId);
                record.setFileName(fileName);
                record.setFilePath(fileId);
                record.setAccessUrl(accessUrl);
                record.setUploadTime(new Date());
                record.setFileType(request.getFileType());
                record.setUploadType("remote");
                record.setUploadUser(request.getUserId());
                record.setTenantCode(request.getTenantCode());
                fileUploadRecordMapper.insertSelective(record);
            }
            urls.add(fileName);
        }
        result.setUrls(urls);
        return BaseRspUtils.createSuccessRsp(result);
    }

    @Override
    public Rsp<FileSaveBO> getFile(String fileId) {
        log.info("获取文件信息-开始:{}", fileId);
        if (StringUtils.isBlank(fileId)) {
            return BaseRspUtils.createErrorRsp("文件不存在!");
        }
        if (fileId.contains(".")) {
            fileId = fileId.split("\\.")[0];
        }
        FileUploadRecord record = fileUploadRecordMapper.selectByFileNo(fileId);
        if (record != null) {
            FileSaveBO saveBO = new FileSaveBO();
            BeanUtils.copyProperties(record, saveBO);
            return BaseRspUtils.createSuccessRsp(saveBO);
        }
        return BaseRspUtils.createErrorRsp("文件不存在!");
    }


    @Override
    public Rsp createPrePutUrl(PrePutUrlCreateRequest request) {
        log.info("创建预上传url-开始:{}", request);
        String objectName = request.getObjectName();
        String fileType = org.springframework.util.StringUtils.getFilenameExtension(request.getFileName());
        if (StringUtils.isBlank(fileType) || !fileManagerHelper.checkConfigSuffix(request.getFileName())) {
            return BaseRspUtils.createErrorRsp("文件格式不支持!");
        }
        if (StringUtils.isBlank(objectName)) {
            String dir = createDir(request.getTargetDir(), request.isAutoDir());
            if (request.isUseOriginalName()) {
                if (StringUtils.isBlank(dir)) {
                    objectName =  request.getFileName();
                } else {
                    objectName = dir + File.separator + request.getFileName();
                }
            } else {
                if (StringUtils.isBlank(dir)) {
                    objectName = NiccCommonUtil.createImUserId(true) + "." + fileType;
                } else {
                    objectName = dir + File.separator + NiccCommonUtil.createImUserId(true) + "." + fileType;
                }
            }
        }
        String prePutUrl = fileManagerHelper.getPrePutUrl(objectName, request.getMetadata());
        if (StringUtils.isNotBlank(prePutUrl)) {
            String uploadType = "pre_"+fileManagerHelper.getFileConfig().getUploadType();
            //截取文件名后缀，从最后一个.开始
            FileUploadRecord record = new FileUploadRecord();
            String fileId = NiccCommonUtil.createImUserId(true);
            String accessUrl = fileManagerHelper.getFileConfig().getAccessUrlPrefix() + objectName;
            record.setFileNo(fileId);
            record.setFileName(request.getFileName());
            record.setFilePath(objectName);
            record.setAccessUrl(accessUrl);
            record.setUploadTime(new Date());
            record.setFileType(fileType);
            record.setUploadType(uploadType);
            record.setUploadUser(request.getUserId());
            record.setTenantCode(request.getTenantCode());
            record.setFileSize(request.getFilesize());
            fileUploadRecordMapper.insertSelective(record);
            PrePutUrlCreateResponse response = PrePutUrlCreateResponse.builder().
                    objectName(objectName).
                    prePutUrl(prePutUrl).
                    fileNo(fileId).
                    accessUrl(accessUrl).
                    build();
            log.info("创建预上传url-完成:{}", response);
            return BaseRspUtils.createSuccessRsp(response);
        }
        return BaseRspUtils.createErrorRsp("获取预上传url失败!");
    }

    @Override
    public Rsp createPrePutOk(PrePutUrlOkRequest request) {
        log.info("上传完成-更新:{}", request);
        FileUploadRecord record = new FileUploadRecord();
        String uploadType = fileManagerHelper.getFileConfig().getUploadType();
        record.setFileNo(request.getFileNo());
        record.setUploadType(uploadType);
        record.setUploadTime(new Date());
        fileUploadRecordMapper.updateByFileNo(record);
        return BaseRspUtils.createSuccessRsp(request.getFileNo(), "上传完成!");
    }

    @Override
    public Rsp createGetUrl(PreDownloadUrlRequest request) {
        log.info("获取下载url-开始: {}", request);
        if (StringUtils.isAllBlank(request.getFileUrl(),request.getFileNo())) {
            return BaseRspUtils.createErrorRsp("获取下载url失败: 参数异常!");
        }
        String getUrl = "";
        if (StringUtils.isNotBlank(request.getFileNo())) {
            FileUploadRecord record = fileUploadRecordMapper.selectByFileNo(request.getFileNo());
            if (record != null) {
                Map<String, String> metadata = new HashMap<>();
                if (StringUtils.isBlank(request.getFileName())) {
                    request.setFileName(record.getFileName());
                }
                metadata.put("response-content-disposition", "attachment;filename=" + request.getFileName());
                getUrl = fileManagerHelper.getPreGetUrl(record.getFilePath(),metadata);
            } else {
                return BaseRspUtils.createErrorRsp("获取下载url失败: 文件不存在!");
            }
        } else {
            String objectName = request.getFileUrl();
            if (request.getFileUrl().startsWith("http") || request.getFileUrl().startsWith("https")) {
                UriComponents components = UriComponentsBuilder.fromHttpUrl(request.getFileUrl()).build();
                objectName = components.getPath();
                if (Objects.requireNonNull(components.getPath()).startsWith("/files/")) {
                    objectName = components.getPath().replace("/files/","");
                }
                if (objectName != null && objectName.startsWith("/")) {
                    objectName = objectName.substring(1);
                }
            }
            if(StringUtils.isBlank(objectName)){
                return BaseRspUtils.createErrorRsp("获取下载url失败: 文件不存在!");
            }
            Map<String, String> metadata = new HashMap<>();
            if (StringUtils.isBlank(request.getFileName())) {
                request.setFileName(org.springframework.util.StringUtils.getFilename(request.getFileUrl()));
            }
            metadata.put("response-content-disposition", "attachment;filename=" + request.getFileName());
            getUrl = fileManagerHelper.getPreGetUrl(objectName,metadata);
        }
        if (StringUtils.isNotBlank(getUrl)) {
            log.info("获取下载url-完成: {}", getUrl);
            return BaseRspUtils.createSuccessRsp(getUrl);
        }
        return BaseRspUtils.createErrorRsp("获取下载url失败!");
    }

}
