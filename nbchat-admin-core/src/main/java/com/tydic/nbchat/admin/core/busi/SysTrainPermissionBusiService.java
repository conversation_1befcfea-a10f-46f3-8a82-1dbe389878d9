package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.api.bo.eum.PermissionRoleType;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObject;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObjectUpdateBO;
import com.tydic.nbchat.admin.mapper.SysTrainPermissionMapper;
import com.tydic.nbchat.admin.mapper.po.PermissionObj;
import com.tydic.nbchat.admin.mapper.po.SysTrainPermission;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class SysTrainPermissionBusiService {

    @Resource
    private SysTrainPermissionMapper sysTrainPermissionMapper;
    private final RedisHelper redisHelper;

    public SysTrainPermissionBusiService(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }

    /**
     * 删除权限
     *
     * @param busiType
     * @param busiId
     */
    @Transactional
    public Integer deletePermission(String busiType, String busiId) {
        if (StringUtils.isAnyBlank(busiType, busiId)) {
            throw new IllegalArgumentException("busiType or busiId is blank");
        }
        return sysTrainPermissionMapper.deleteByBusiIdAndType(busiType, busiId);
    }

    @Transactional
    public Integer updatePermission(PermissionObjectUpdateBO updateBO) {
        if (StringUtils.isAnyBlank(updateBO.getTenantCode(), updateBO.getBusiType(), updateBO.getBusiId())) {
            throw new IllegalArgumentException("busiType or busiId is blank");
        }
        List<SysTrainPermission> list = new ArrayList<>();
        if (updateBO.getPermissionObjects() != null) {
            sysTrainPermissionMapper.deleteByBusiIdAndType(updateBO.getBusiType(),updateBO.getBusiId());
            for (PermissionObject permissionObject : updateBO.getPermissionObjects()) {
                SysTrainPermission permission = new SysTrainPermission();
                permission.setPermission(permissionObject.getPermission());
                permission.setBusiId(updateBO.getBusiId());
                permission.setBusiType(updateBO.getBusiType());
                permission.setTenantCode(updateBO.getTenantCode());
                permission.setCreateBy(updateBO.getCreateBy());
                permission.setCreateTime(new Date());
                if (PermissionRoleType.role.name().equals(permissionObject.getType())) {
                    permission.setRoleId(permissionObject.getObjId());
                } else if (PermissionRoleType.dept.name().equals(permissionObject.getType())) {
                    permission.setDeptId(permissionObject.getObjId());
                } else if (PermissionRoleType.post.name().equals(permissionObject.getType())) {
                    permission.setPostId(permissionObject.getObjId());
                } else if (PermissionRoleType.user.name().equals(permissionObject.getType())) {
                    permission.setUserId(permissionObject.getObjId());
                }
                list.add(permission);
            }
            if (!list.isEmpty()) {
                sysTrainPermissionMapper.batchInsert(list);
            }
            reloadPermissionCache(updateBO.getBusiType(),updateBO.getBusiId(),updateBO.getPermissionObjects());
        }
        return list.size();
    }

    private void reloadPermissionCache(String busiType, String busiId,List<PermissionObject> permissionObjs) {
        String key = "nbchat-admin:train_perm:" + busiType + ":" + busiId;
        redisHelper.set(key,permissionObjs, 60 * 60);
    }

    public List<PermissionObject> queryTrainPermission(String tenantCode, String busiType, String busiId) {
        String key = "nbchat-admin:train_perm:" + busiType + ":" + busiId;
        List<PermissionObject> list = null;
        try {
            list = (List<PermissionObject>) redisHelper.get(key);
        } catch (Exception e) {
            log.warn("Redis queryTrainPermission error: ",e);
        }
        if (list == null) {
            List<PermissionObj> permissionObjs = sysTrainPermissionMapper.queryTrainPermission(tenantCode, busiType, busiId);
            list = new ArrayList<>();
            NiccCommonUtil.copyList(permissionObjs,list,PermissionObject.class);
            redisHelper.set(key,list, 60 * 60);
            log.info("queryTrainPermission from db:{}",list);
        }
        return list;
    }
}
