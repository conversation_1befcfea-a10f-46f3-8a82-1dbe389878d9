package com.tydic.nbchat.admin.core.service.impl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysPicLibraryApi;
import com.tydic.nbchat.admin.api.bo.SysPicLibraryReqBO;
import com.tydic.nbchat.admin.api.bo.SysPicLibraryRsqBO;
import com.tydic.nbchat.admin.mapper.SysPicLibraryMapper;
import com.tydic.nbchat.admin.mapper.po.SysMenuTpl;
import com.tydic.nbchat.admin.mapper.po.SysPicLibraryPO;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @datetime：2024/9/9 15:27
 * @description:
 */
@Slf4j
@Service
public class SysPicLibraryServiceImpl implements SysPicLibraryApi {
    @Resource
    private SysPicLibraryMapper sysPicLibraryMapper;
    @Override
    public RspList getSysPicLibrary(SysPicLibraryReqBO reqBO) {
        log.info("检索图片请求参数 reqBO:{}", reqBO);
        String regex = "[\\u4e00-\\u9fa5]";
        boolean matches = reqBO.getKeyword().matches(".*" + regex + ".*");
        SysPicLibraryPO record = new SysPicLibraryPO();
        BeanUtils.copyProperties(reqBO, record);
        if (matches) {
            int chineseCharCount = countChineseChars(reqBO.getKeyword());
            if (chineseCharCount < 2) {
                record.setPhrase(reqBO.getKeyword());
            } else {
                record.setLabelZh(reqBO.getKeyword());
            }
        } else {
            record.setLabelEn(reqBO.getKeyword());
        }
        Page<SysMenuTpl> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        sysPicLibraryMapper.selectByKeyword(record);
        List<SysPicLibraryRsqBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, SysPicLibraryRsqBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    public static int countChineseChars(String text) {
        int count = 0;
        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);
            // 检查字符是否在中文范围内（基本汉字范围）
            if (ch >= '\u4e00' && ch <= '\u9fa5') {
                count++;
            }
            // 如果需要包含其他中文字符范围，可以在这里添加
        }
        return count;
    }
}
