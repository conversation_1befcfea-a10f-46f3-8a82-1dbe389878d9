package com.tydic.nbchat.admin.core.nls;


import com.tydic.nbchat.admin.api.bo.nls.AsrVoiceTaskContext;
import com.tydic.nbchat.admin.api.bo.nls.AsrVoiceTaskRequest;
import com.tydic.nbchat.admin.api.bo.nls.TtsVoiceTaskContext;
import com.tydic.nbchat.admin.api.bo.nls.TtsVoiceTaskRequest;

public interface NlsHelperApi {

    String anchorConfig();

    /**
     * 创建语音任务
     * @param courseId
     * @param sectionId
     * @param courseAll
     */
    void createAudioTask(String courseId, String sectionId, boolean courseAll);

    /**
     * 语音合成任务
     * @param courseId
     * @param sectionId
     * @param text
     * @param voice
     * @param async
     * @return
     */
    TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async);

    /**
     * 语音合成任务
     * @param request
     * @return
     */
    TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request);

    /**
     * 语音合成任务
     * @param courseId
     * @param sectionId
     * @param text
     * @param async
     * @return
     */
    TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text,boolean async);

    /**
     * 语音识别任务
     * @param courseId
     * @param fileId
     * @param filepath
     * @param async
     * @return
     */
    AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async);

    /**
     * 语音识别任务
     * @param request
     * @return
     */
    AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request);

}
