package com.tydic.nbchat.admin.core.config.utils;

import com.tydic.nbchat.admin.api.SysTenantManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementRspBO;
import com.tydic.nbchat.admin.api.bo.sentence.constants.SysTenantConstants;
import com.tydic.nbchat.user.api.bo.eums.UserSettingKey;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
public class CheckAdminPermissionUtils {
    @Resource
    private SysTenantManagementApi sysTenantManagementApi;

}
