package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysDictConfigService;
import com.tydic.nbchat.admin.api.bo.SysDictConfigQueryReqBO;
import com.tydic.nbchat.admin.api.bo.SysDictConfigRspBO;
import com.tydic.nbchat.admin.api.bo.SysDictInsertReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SaveCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SysDictConfigQueryBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin")
public class SysDictController {
    final SysDictConfigService sysDictConfigService;

    public SysDictController(SysDictConfigService sysDictConfigService) {
        this.sysDictConfigService = sysDictConfigService;
    }

    @PostMapping("/dict/list")
    public RspList saveCommSentenceType(@RequestBody SysDictConfigQueryReqBO reqBo) {
        return sysDictConfigService.getSysDictValues(reqBo);
    }

    /**
     * 根据字典名称和字典描述对dict_code分组查询
     */
    @PostMapping("/dict/config/list")
    public RspList listByCode(@RequestBody SysDictConfigQueryBo reqBo) {
        return sysDictConfigService.getSysDictValuesByGroup(reqBo);
    }

    /**
     * 新增字典配置根据dict_code（批量新增）
     */
    @PostMapping("/dict/config/save")
    public RspList insertDictCode(@RequestBody SysDictInsertReqBO reqBo) {
        return sysDictConfigService.insertDictCode(reqBo);
    }
    /**
     * 修改字典配置根据dict_code更新状态
     */
    @PostMapping("/dict/config/update")
    public RspList updateDictCode(@RequestBody SysDictConfigRspBO reqBo) {
        return sysDictConfigService.updateDictCode(reqBo);
    }
}
