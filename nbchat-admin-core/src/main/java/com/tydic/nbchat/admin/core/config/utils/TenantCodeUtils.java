package com.tydic.nbchat.admin.core.config.utils;

import net.sourceforge.pinyin4j.PinyinHelper;

import java.util.Random;

public class TenantCodeUtils {
    public static String generateMixedString(String input) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < input.length() && sb.length() < 8; i++) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(input.charAt(i));
            if (pinyinArray != null && pinyinArray.length > 0) {
                sb.append(pinyinArray[0]);
            }
        }
        Random random = new Random();
        while (sb.length() < 8) {
            sb.append(random.nextInt(10));  // append random digit
        }
        return sb.length() <= 8 ? sb.toString() : sb.substring(0, 8);
    }
}
