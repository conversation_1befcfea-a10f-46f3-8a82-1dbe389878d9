package com.tydic.nbchat.admin.core.busi;


import com.tydic.nbchat.admin.api.bo.sentence.CommSentence;
import com.tydic.nbchat.admin.api.bo.sentence.QueryCommSentencePageListReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.QueryKnowledgeConReq;
import com.tydic.nbchat.admin.api.bo.sentence.ShareKnowledgeReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * <AUTHOR>
 * @description 知识操作
 **/
public interface CommSentenceBusiService {
    /**
     * 知识增加
     * @param req
     * @return
     */
    Rsp saveCommSentence(CommSentence req);

    /**
     * 知识修改
     * @param req
     * @return
     */
    Rsp updateCommSentence(CommSentence req);

    /**
     * 知识删除
     * @param req
     * @return
     */
    Rsp deleteCommSentence(CommSentence req);

    /**
     * 知识列表分页查询
     * @param req
     * @return
     */
    RspList getCommSentencePageList(QueryCommSentencePageListReqBO req);

    Rsp getKnowledgeContentById(QueryKnowledgeConReq req);

    Rsp getShareUrl(ShareKnowledgeReqBO req);

    Rsp queryShare(ShareKnowledgeReqBO reqBO);

    Rsp checkShareKey(ShareKnowledgeReqBO reqBO);

    /**
     * 获取知识分类下拉列表
     * @param req
     * @return
     */
//    GetCommSentenceTypeListRsp getCommSentenceTypeList(GetCommSentenceTypeListReq req);

    /**
     * 知识详情查询
     * @param req
     * @return
     */
//    GetCommSentenceByIdRsp getCommSentenceById(GetCommSentenceByIdReq req);

    /**
     * 知识交换排序标识
     * @param req
     * @return
     */
//    SwapCommSentenceSortIdRsp swapCommSentenceSortId(SwapCommSentenceSortIdReq req);
}
