package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysDeptApi;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptQueryRspBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptSaveReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptTreeQueryReqBO;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/dept")
public class SysDeptController {
    private final SysDeptApi sysDeptApi;

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/list")
    public RspList list(@RequestBody SysDeptQueryReqBO reqBO) {
        return sysDeptApi.getDeptList(reqBO);
    }

    @PostMapping("/info")
    public Rsp<SysDeptQueryRspBO> info(@RequestBody SysDeptQueryReqBO reqBO) {
        return sysDeptApi.getDept(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/tree")
    public RspList tree(@RequestBody SysDeptTreeQueryReqBO reqBO) {
        return sysDeptApi.getDeptTree(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/childTree")
    public RspList childTree(@RequestBody SysDeptTreeQueryReqBO reqBO) {
        return sysDeptApi.getChildDeptTree(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/save")
    public Rsp save(@RequestBody SysDeptSaveReqBO reqBO) {
        return sysDeptApi.saveDept(reqBO);
    }

}
