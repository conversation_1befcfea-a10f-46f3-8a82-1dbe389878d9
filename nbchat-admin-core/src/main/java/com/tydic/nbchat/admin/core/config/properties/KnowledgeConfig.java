package com.tydic.nbchat.admin.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-admin.knowledge-config")
public class KnowledgeConfig {
    private String shareUrlPrefix = "http://127.0.0.1";
    private Integer shareKeyLength = 4;
}
