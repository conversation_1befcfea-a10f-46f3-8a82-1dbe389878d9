package com.tydic.nbchat.admin.core.utils;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Component
public class DingtalkUtil {

    static String dingtalkTitle = "";

    DingtalkUtil(NbchatAdminConfigProperties nbchatAdminConfigProperties) {
        dingtalkTitle = nbchatAdminConfigProperties.getDingtalkTitle();
    }

    public static void sendMessageWebhook(String content, String origin) throws ApiException {
        log.info("钉钉消息发送消息内容:{}", content);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=e24776c246df8ba90aa17999c3c4ec5a26833bd3930784d23ec3f0f4cf5030e2");
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(dingtalkTitle + content);
        request.setText(text);
        if ("tdh-portal".equals(origin)) {
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(Arrays.asList("13811072521"));
            at.setIsAtAll(false);
            request.setAt(at);
        }
        OapiRobotSendResponse response = client.execute(request);
        log.info("钉钉消息发送成功:{}", response.getBody());
    }


}
