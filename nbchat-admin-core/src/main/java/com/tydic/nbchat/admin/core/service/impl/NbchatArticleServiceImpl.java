package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.NbchatArticleApi;
import com.tydic.nbchat.admin.api.bo.ArticleQueryReqBO;
import com.tydic.nbchat.admin.api.bo.ArticleQueryRspBO;
import com.tydic.nbchat.admin.mapper.NbchatArticleMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatArticle;
import com.tydic.nbchat.admin.mapper.po.NbchatArticleSelectCondition;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class NbchatArticleServiceImpl implements NbchatArticleApi {

    @Resource
    private NbchatArticleMapper nbchatArticleMapper;

    @Override
    public RspList getArticles(ArticleQueryReqBO request) {
        log.info("查询文章列表:{}",request);
        List<ArticleQueryRspBO> msgBOList = Lists.newArrayList();
        NbchatArticleSelectCondition condition = new NbchatArticleSelectCondition();
        BeanUtils.copyProperties(request,condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<NbchatArticle> page = PageHelper.startPage(request.getPage(),request.getLimit());
        nbchatArticleMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(),msgBOList,ArticleQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(msgBOList);
    }
}
