package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.NbchatSysJobBindApi;
import com.tydic.nbchat.admin.api.bo.SysBindJobReqBO;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/job_no")
public class SysJobNoBindController {
    private final NbchatSysJobBindApi nbchatSysJobBindApi;

    public SysJobNoBindController(NbchatSysJobBindApi nbchatSysJobBindApi) {
        this.nbchatSysJobBindApi = nbchatSysJobBindApi;
    }

    @PostMapping("/bind")
    public Rsp bindJobNo(@RequestBody SysBindJobReqBO reqBO) {
        return nbchatSysJobBindApi.bindJobNo(reqBO);
    }

    @PostMapping("/bind/status")
    public Rsp bindJobNoStatus(@RequestBody BaseInfo reqBO) {
        return nbchatSysJobBindApi.bindJobNoStatus(reqBO);
    }

}
