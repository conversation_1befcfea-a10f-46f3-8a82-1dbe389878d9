package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.bo.ConsultReqBO;
import com.tydic.nbchat.admin.core.utils.DingtalkUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/talk")
public class DingtalkController {



    @PostMapping("msg")
    public Rsp sendMessage(@RequestBody ConsultReqBO request) {
        try {
            DingtalkUtil.sendMessageWebhook("线索数据如下:\n" + request.toString(),"tdh-portal");
        } catch (Exception e) {
            log.error("钉钉消息发送失败", e);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }


}
