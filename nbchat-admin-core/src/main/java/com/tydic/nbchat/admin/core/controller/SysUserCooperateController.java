package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysUserCooperateApi;
import com.tydic.nbchat.admin.api.bo.SysUserCooperateBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *用户合作申请
 */
@Slf4j
@RestController
@RequestMapping("/admin/user_cooperate")
public class SysUserCooperateController {
    private final SysUserCooperateApi sysUserCooperateApi;

    public SysUserCooperateController(SysUserCooperateApi sysUserCooperateApi) {
        this.sysUserCooperateApi = sysUserCooperateApi;
    }

    /**
     * 添加用户合作申请
     * @param recordBO
     * @return
     */
    @RequestMapping("/save")
    public Rsp save(@RequestBody SysUserCooperateBO recordBO) {
        return sysUserCooperateApi.addNewUserCooperate(recordBO);
    }
}
