package com.tydic.nbchat.admin.core.listener;

import com.tydic.nbchat.admin.api.TenantApplyListener;
import com.tydic.nbchat.admin.api.bo.TenantApplyBO;
import com.tydic.nbchat.admin.core.busi.SysUserRoleBusiService;
import com.tydic.nbchat.admin.mapper.NbchatEnterpriseTryApplyMapper;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper;
import com.tydic.nbchat.admin.mapper.ScriptSQLMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant;
import com.tydic.nbchat.admin.mapper.po.ScriptSQLPO;
import com.tydic.nbchat.user.api.UserManageService;
import com.tydic.nbchat.user.api.UserService;
import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.manage.UpdateUserRequest;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TenantApplyListenerImpl implements TenantApplyListener {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserManageService userManageService;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserService userService;

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    @Resource
    private NbchatEnterpriseTryApplyMapper tryApplyMapper;
    @Resource
    private ScriptSQLMapper scriptSQLMapper;
    @Resource
    private NbchatSysUserTenantMapper nbchatSysUserTenantMapper;

    private final SysUserRoleBusiService sysUserRoleBusiService;

    public TenantApplyListenerImpl(SysUserRoleBusiService sysUserRoleBusiService) {
        this.sysUserRoleBusiService = sysUserRoleBusiService;
    }

    @Override
    @Transactional
    public void prePass(TenantApplyBO bo) {
        log.info("租户预申请-通过: {}",bo);
        //自动创建对应租户信息，设置为体验租户
        NbchatSysTenant tenantRec = new NbchatSysTenant();
        tenantRec.setTenantCode(bo.getTenantCode());
        tenantRec.setTenantName(bo.getCompanyName());
        tenantRec.setCreateTime(new Date());
        tenantRec.setStatus(DEFAULT);
        log.info("租户预申请-保存租户:{}",tenantRec);
        nbchatSysTenantMapper.insertSelective(tenantRec);

        //执行创建默认课程脚本
        long id = Math.abs(IdWorker.nextAutoId() >> 1);
        long aId = Math.abs(IdWorker.nextAutoId() >> 1);
        long qId = Math.abs(IdWorker.nextAutoId() >> 1);

        ScriptSQLPO build = ScriptSQLPO.builder().tenantCode(bo.getTenantCode()).courseId(IdWorker.nextAutoIdStr())
                .id(Math.abs((int) id)).qId(Math.abs((int)qId) ).aId(Math.abs((int) aId)).build();
        log.info("租户预申请-初始化数据: {}",build);
        scriptSQLMapper.insertCategory(build);
        scriptSQLMapper.insertCourse(build);
        scriptSQLMapper.insertCatalog(build);
        scriptSQLMapper.insertSection(build);
        scriptSQLMapper.insertConfig(build);
        scriptSQLMapper.insertDialogue(build);
        scriptSQLMapper.insertQuestion(build);
        scriptSQLMapper.insertAnswer(build);

    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            long id = IdWorker.nextAutoId() >> 1;
            long aId = IdWorker.nextAutoId() >> 1;
            long qId = IdWorker.nextAutoId() >> 1;
            System.out.println(id);
            ScriptSQLPO build = ScriptSQLPO.builder()
                    .id((int) id).qId(Math.abs((int)qId) ).aId(Math.abs((int) aId)).build();
            System.out.println(build);
        }
    }

    @Override
    public void pass(TenantApplyBO bo) {
        log.info("租户申请-通过: {}",bo);
        //设置管理员权限
        NbchatEnterpriseTryApply applyRecord = tryApplyMapper.queryById(bo.getId());
        //设置用户角色
        sysUserRoleBusiService.addRole(applyRecord.getTenantCode(), applyRecord.getUserId(), UserRoleConstants.tenantAdmin);
        //设置用户归属租户
        NbchatSysUserTenant userTenant = NbchatSysUserTenant.builder().
                userId(applyRecord.getUserId()).
                userRealityName(applyRecord.getName()).
                tenantCode(applyRecord.getTenantCode()).
                createTime(new Date()).build();
        log.info("租户申请-绑定租户下用户: {}",userTenant);
        nbchatSysUserTenantMapper.insertSelective(userTenant);

        UpdateUserRequest update = new UpdateUserRequest();
        update.setTenant(applyRecord.getTenantCode());
        update.setTargetUid(applyRecord.getUserId());
        Rsp rsp = userManageService.updateInfo(update);
        log.info("租户申请-设置租户结果: {}",rsp);
        if (rsp.isSuccess()) {
            //发送短信
            AuthUserReqBO msg = AuthUserReqBO.builder().phone(applyRecord.getPhone()).keyword(applyRecord.getCompanyName()).build();
            Rsp rsp2 = userService.sendSMSApply(msg);
            log.info("租户申请-发送申请通知: {}",rsp2);
        }

    }

    @Override
    public void noPass(TenantApplyBO bo) {
        log.info("租户申请-拒绝:{}",bo);
    }

}
