package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.baidu.BaiduApiCoordinationService;
import com.tydic.nbchat.admin.api.bo.baidu.BaiduApiCoordinationReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/baidu")
public class BaiduApiCoordinationController {

    private final BaiduApiCoordinationService baiduApiCoordinationService;

    @PostMapping("/dataConversionCallback")
    public Rsp dataConversionCallback(@RequestBody BaiduApiCoordinationReqBO reqBo) {
        return baiduApiCoordinationService.dataConversionCallback(reqBo);
    }
}
