package com.tydic.nbchat.admin.core.busi;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.bo.SysTenantOptUserReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantUserInfoBO;
import com.tydic.nbchat.admin.api.bo.SysTenantUserQueryRspBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.eum.TenantUserStatusType;
import com.tydic.nbchat.admin.api.bo.post.SysPostBO;
import com.tydic.nbchat.admin.core.utils.AdminCommonUtil;
import com.tydic.nbchat.admin.core.utils.TimeUtil;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper;
import com.tydic.nbchat.admin.mapper.OpRpUserDetailMapper;
import com.tydic.nbchat.admin.mapper.SysDeptMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant;
import com.tydic.nbchat.admin.mapper.po.SysDeptUser;
import com.tydic.nbchat.admin.mapper.po.SysDeptUserSelectCondition;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.eums.SysPlatformDefine;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import com.tydic.nbchat.user.api.bo.setting.UserSettingFlashReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceAccountReqBO;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.common.nbchat.exception.CommonBusiException;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.exception.DcBusinessException;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SysTenantUserBuisService {

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    @Resource
    private NbchatSysUserTenantMapper nbchatSysUserTenantMapper;

    private final SysDeptUserBusiService sysDeptUserBusiService;
    private final SysUserRoleBusiService sysUserRoleBusiService;
    private final SysUserPostBusiService sysUserPostBusiService;
    private final RpUserDetailBusiService rpUserDetailBusiService;
    private final OpRpUserDetailMapper opRpUserDetailMapper;
    private final SysDeptMapper sysDeptMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBalanceApi tradeBalanceApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi nbchatUserApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserSettingsApi userSettingsApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserBaseInfoApi userBaseInfoApi;


    public SysTenantUserBuisService(SysDeptUserBusiService sysDeptUserBusiService,
                                    SysUserRoleBusiService sysUserRoleBusiService,
                                    SysUserPostBusiService sysUserPostBusiService,
                                    RpUserDetailBusiService rpUserDetailBusiService,
                                    OpRpUserDetailMapper opRpUserDetailMapper,
                                    SysDeptMapper sysDeptMapper) {
        this.sysDeptUserBusiService = sysDeptUserBusiService;
        this.sysUserRoleBusiService = sysUserRoleBusiService;
        this.sysUserPostBusiService = sysUserPostBusiService;
        this.rpUserDetailBusiService = rpUserDetailBusiService;
        this.opRpUserDetailMapper = opRpUserDetailMapper;
        this.sysDeptMapper = sysDeptMapper;
    }


    /**
     * 判断用户是否在租户下
     * @param userId
     * @param tenantCode
     * @return
     */
    public boolean existInTenant(String userId, String tenantCode) {
        NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.
                selectByUserIdAndTenantCode(userId, tenantCode);
        return userTenant != null;
    }

    /**
     * 把用户加入到默认租户
     * @param userInfo
     * @param joinType
     * @return
     */
    public boolean addDefaultTenant(SysTenantUserInfoBO userInfo, String joinType){
        NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.selectByUserIdAndTenantCode(
                userInfo.getUserId(),
                UserAttributeConstants.DEFAULT_TENANT_CODE);
        if (userTenant == null) {
            //查询记录是否存在
            NbchatSysUserTenant nbchatSysUserTenant = new NbchatSysUserTenant();
            nbchatSysUserTenant.setUserId(userInfo.getUserId());
            nbchatSysUserTenant.setUserRealityName(userInfo.getUserRealityName());
            nbchatSysUserTenant.setJoinType(joinType);
            nbchatSysUserTenant.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
            nbchatSysUserTenant.setCreateTime(new Date());
            nbchatSysUserTenant.setUserStatus(TenantUserStatusType.NORMAL.getCode());
            nbchatSysUserTenantMapper.insertSelective(nbchatSysUserTenant);
            UserBalanceAccountReqBO accountReqBO = new UserBalanceAccountReqBO();
            accountReqBO.setUserId(userInfo.getUserId());
            accountReqBO.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
            tradeBalanceApi.createBalanceAccount(accountReqBO);
            return true;
        }
        return false;
    }


    @Transactional(rollbackFor = Exception.class)
    public Rsp addUsers(SysTenantOptUserReqBO bindUserReqBO) {
        log.info("将用户加入到租户下-开始: {}", bindUserReqBO);
        String targetTenant = bindUserReqBO.getTargetTenant();
        bindUserReqBO.getUserInfos().forEach(userInfoBO -> {
            String targetUid = userInfoBO.getUserId();
            String userId = bindUserReqBO.getUserId();
            //查询记录是否存在
            NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.selectByUserIdAndTenantCode(targetUid, targetTenant);
            if (userTenant == null) {
                checkTenantCodeStatus(targetTenant);
                String phone =  nbchatSysUserTenantMapper.getPhone(userInfoBO.getUserId());
                NbchatSysUserTenant user = new NbchatSysUserTenant();
                BeanUtils.copyProperties(userInfoBO, user);
                user.setUserId(userInfoBO.getUserId());
                user.setUserRealityName(userInfoBO.getUserRealityName());
                user.setJoinType(bindUserReqBO.getJoinType());
                user.setTenantCode(targetTenant);
                user.setUserStatus(TenantUserStatusType.NORMAL.getCode());
                user.setCreateTime(new Date());
                if (userInfoBO.getBirthday() == null) {
                    user.setBirthday(AdminCommonUtil.getBirthdayFromIdCard(userInfoBO.getIdCard()));
                }
                nbchatSysUserTenantMapper.insertSelective(user);
                UserBalanceAccountReqBO accountReqBO = new UserBalanceAccountReqBO();
                accountReqBO.setUserId(targetUid);
                accountReqBO.setTenantCode(targetTenant);
                tradeBalanceApi.createBalanceAccount(accountReqBO);
                UserRegistContext userRegistContext = new UserRegistContext();
                BeanUtils.copyProperties(user, userRegistContext);
                userRegistContext.setPhone(phone);
                userRegistContext.setCreatedTime(new Date());
                try {
                    if (StringUtils.isBlank(userInfoBO.getUserRealityName())){
                        userRegistContext.setUserRealityName(PhoneNumberUtils.maskPhoneNum(phone));
                    }
                    rpUserDetailBusiService.saveRpUserDetail(userRegistContext);
                } catch (Exception e) {
                    log.error("将用户加入到租户下-报表初始化异常: {}", userRegistContext, e);
                }
            } else {
                //更新记录
                NbchatSysUserTenant update = new NbchatSysUserTenant();
                BeanUtils.copyProperties(userInfoBO, update);
                update.setId(userTenant.getId());
                update.setUpdateTime(new Date());
                if (userInfoBO.getBirthday() == null) {
                    update.setBirthday(AdminCommonUtil.getBirthdayFromIdCard(userInfoBO.getIdCard()));
                }
                log.info("将用户加入到租户下-记录已存在-更新:{}", update);
                nbchatSysUserTenantMapper.updateByPrimaryKeySelective(update);
            }
            //如果时邀请码进入，并且加入时间距离当前时间为4小时，更新个人租户为邀请码进入
            try {
                if (userTenant != null &&
                        StringUtils.isNotBlank(userTenant.getJoinType()) && userTenant.getCreateTime() != null &&
                        TimeUtil.isWithinFourHours(userTenant.getCreateTime()) &&
                        JoinTenantType.INVITATION_CODE.getCode().equals(bindUserReqBO.getJoinType())) {
                    int result = opRpUserDetailMapper.updateJoinType(targetUid,
                            UserAttributeConstants.DEFAULT_TENANT_CODE,
                            JoinTenantType.INVITATION_CODE.getCode());
                    log.info("将用户加入到租户下-邀请码进入-更新用户详细信息表个人租户:{}", result);
                }
            } catch (Exception e){
                log.error("将用户加入到租户下-邀请码进入-更新用户详细信息表个人租户时出错:{}", e.getMessage());
            }
            try {
                //更新岗位
                sysUserPostBusiService.updateUserPost(targetTenant, targetUid, userInfoBO.getPostIds());
                //更新部门
                sysDeptUserBusiService.addDeptUserRel(targetTenant, bindUserReqBO.getDeptId(), targetUid);
                //移除用户部门岗位信息
                userSettingsApi.removeUserCache(targetTenant,targetUid);
                //更新用户信息，用户租户现在进入平台提示选择
                /*if (!JoinTenantType.OUTER_API.getCode().equals(bindUserReqBO.getJoinType())) {
                    UpdateUserRequest updateUserRequest = new UpdateUserRequest();
                    updateUserRequest.setTargetUid(targetUid);
                    updateUserRequest.setTenant(targetTenant);
                    updateUserRequest.setUserId(userId);
                    userManageService.updateInfo(updateUserRequest);
                }*/
            } catch (Exception e){
                log.error("将用户加入到租户下-切换租户-异常: {}", e.getMessage());
            }
        });
        return BaseRspUtils.createSuccessRsp(targetTenant,"操作成功");
    }


    @Transactional(rollbackFor = Exception.class)
    public Rsp removeUsers(SysTenantOptUserReqBO removeUserReqBO) {
        log.info("移除租户下用户-开始: {}", removeUserReqBO);
        String targetTenant = removeUserReqBO.getTenantCode();
        if (removeUserReqBO.hasRole(UserRoleConstants.sysAdmin)) {
            targetTenant = removeUserReqBO.getTargetTenant();
        }
        //校验权限结束
        String finalTargetTenant = targetTenant;
        removeUserReqBO.getUserInfos().forEach(userInfoBO -> {
            //查询记录是否存在
            NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.selectByUserIdAndTenantCode(userInfoBO.getUserId(), finalTargetTenant);
            if (userTenant != null) {
                nbchatSysUserTenantMapper.deleteByPrimaryKey(userTenant.getId());
                //移除部门关系
                sysDeptUserBusiService.removeDeptUserRel(userTenant.getTenantCode(), userTenant.getUserId());
                //移除权限
                sysUserRoleBusiService.removeRole(userTenant.getTenantCode(), userTenant.getUserId(),null);
                //判断是否需要更新用户的当前租户
                Rsp rsp = nbchatUserApi.getUserInfo(userInfoBO.getUserId());
                if (rsp.isSuccess()) {
                    NbchatUserInfo nbchatUserInfo = (NbchatUserInfo) rsp.getData();
                    if (StringUtils.isNotBlank(nbchatUserInfo.getTenantCode()) &&
                            nbchatUserInfo.getTenantCode().equals(finalTargetTenant)) {
                        UserBO userBO = new UserBO();
                        userBO.setUserId(userInfoBO.getUserId());
                        userBO.setTargetTenant(UserAttributeConstants.DEFAULT_TENANT_CODE);
                        rsp = nbchatUserApi.updateById(userBO);
                        if (!rsp.isSuccess()) {
                            log.warn("将用户从租户移除-更新用户租户出错:{}", rsp);
                        }
                    }
                } else {
                    log.warn("将用户从租户移除-用户不存在:{}", userInfoBO.getUserId());
                }
                //移除平台设置
                UserSettingFlashReqBO reqBO = new UserSettingFlashReqBO();
                reqBO.setUserId(userInfoBO.getUserId());
                reqBO.setTenantCode(userInfoBO.getTenantCode());
                reqBO.setUserIds(Collections.singletonList(userInfoBO.getUserId()));
                reqBO.setRemoveKeys(SysPlatformDefine.platformSet());
                userSettingsApi.flashSettings(reqBO);
            }
        });
        nbchatUserApi.cleanCache(removeUserReqBO.getTenantCode(), removeUserReqBO.getUserId());
        log.info("将用户从租户移除-操作成功");
        return BaseRspUtils.createSuccessRsp("移除成功");
    }


    @MethodParamVerifyEnable
    public RspList getTenantUsers(SysDeptUserQueryReqBO reqBO) {
        List<SysTenantUserQueryRspBO> result = new ArrayList<>();
        log.info("查询租户-查询租户下的所有用户: {}", reqBO);
        SysDeptUserSelectCondition condition = new SysDeptUserSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        if (StringUtils.isNotBlank(reqBO.getTargetTenantCode())) {
            condition.setTenantCode(reqBO.getTargetTenantCode());
        }
        //判断用户是否为平台管理员
        Page<SysDeptUser> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatSysUserTenantMapper.selectDeptUserByCondition(condition);
        page.getResult().forEach(obj -> {

            SysTenantUserQueryRspBO rspBO = new SysTenantUserQueryRspBO();
            BeanUtils.copyProperties(obj, rspBO);
            rspBO.setJoinDays(calculateDays(obj.getCreateTime()));
            rspBO.setAge(AdminCommonUtil.getAgeFromIdCard(obj.getIdCard()));
            if (rspBO.getAge() == null) {
                rspBO.setAge(AdminCommonUtil.getAgeFromBirthday(obj.getBirthday()));
            }
            Rsp<UserBaseInfo> userRsp = userBaseInfoApi.getByUserId(obj.getTenantCode(),obj.getUserId());
            if (userRsp.isSuccess()) {
                //查询归属部门
                UserBaseInfo userBaseInfo = userRsp.getData();
                rspBO.setDeptId(userBaseInfo.getDept().getDeptId());
                //rspBO.setUserRealityName(userBaseInfo.getRealName());
                //查询岗位
                List<SysPostBO> postList = new ArrayList<>();
                userBaseInfo.getPostList().forEach(post -> {
                    SysPostBO sysPostBO = new SysPostBO();
                    sysPostBO.setPostId(Integer.valueOf(post.getPostId()));
                    sysPostBO.setPostName(post.getPostName());
                    postList.add(sysPostBO);
                });
                String deptName = sysDeptMapper.queryOrganizeName(userBaseInfo.getDept().getDeptId());
                rspBO.setDeptName(deptName);
                rspBO.setPostList(postList);
            }
            result.add(rspBO);
        });
        log.info("查询租户-查询租户下的所有用户-查询结果：{}", result.size());
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    /**
     * 计算天数
     *
     * @param @param createTime 创建时间
     * @return @return {@link String }
     */
    public String calculateDays(Date createTime) {
        Date currentDate = new Date(System.currentTimeMillis());
        long diffInMillies = Math.abs(currentDate.getTime() - createTime.getTime());
        long joinDays = Math.round((double) diffInMillies / (24 * 60 * 60 * 1000));
        return String.valueOf(joinDays);
    }


    public void checkTenantCodeStatus(String targetTenant){
        //查询租户是否存在
        NbchatSysTenant nbchatSysTenant = nbchatSysTenantMapper.selectByPrimaryKey(targetTenant);
        if (nbchatSysTenant == null) {
            log.error("将用户加入到租户下-租户不存在: {}", targetTenant);
            throw new CommonBusiException("租户不存在");
        }
        int userCount = nbchatSysUserTenantMapper.selectUserCount(targetTenant);
        //判断是否达到租户人数上限
        if (nbchatSysTenant.getUserLimit() != -1 && userCount >= nbchatSysTenant.getUserLimit()) {
            log.warn("将用户加入到租户下-企业用户人数已达上限: {}", targetTenant);
            throw new CommonBusiException("企业用户人数已达上限，请联系运营人员");
        }
    }


}
