package com.tydic.nbchat.admin.core.utils;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class TimeUtil {
    /**
     * 判断给定时间与当前时间的间隔是否小于4小时
     *
     * @param dateToCheck 要判断的时间 (java.util.Date)
     * @return 如果小于4小时返回true，否则返回false
     */
    public static boolean isWithinFourHours(Date dateToCheck) {
        // 将 Date 转换为 LocalDateTime
        LocalDateTime timeToCheck = convertToLocalDateTime(dateToCheck);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算两个时间之间的间隔
        Duration duration = Duration.between(timeToCheck, now);

        // 判断是否小于4小时
        return Math.abs(duration.toHours()) < 4;
    }

    /**
     * 将 java.util.Date 转换为 java.time.LocalDateTime
     *
     * @param date 要转换的 Date
     * @return 转换后的 LocalDateTime
     */
    private static LocalDateTime convertToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
