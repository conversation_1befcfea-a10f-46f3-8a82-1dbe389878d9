package com.tydic.nbchat.admin.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "nbchat-admin.config.qichacha")
public class QichachaConfigProperties {

    private String url;
    private String key;
    private String secretKey;

}
