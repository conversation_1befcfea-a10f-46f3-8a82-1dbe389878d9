package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysTenantManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementRspBO;
import com.tydic.nbchat.admin.api.bo.SysTenantSubsystemRspBo;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.mapper.*;
import com.tydic.nbchat.admin.mapper.po.*;
import com.tydic.nbchat.user.api.UserVipRightsApi;
import com.tydic.nbchat.user.api.bo.eums.CustomizeTypeEnum;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceAccountReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceBO;
import com.tydic.nbchat.user.api.bo.vip.NbchatUserVipRightsBO;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
public class SysTenantManagementImpl implements SysTenantManagementApi {

    private final String TENANT_CONFIG_KEY = "nbchat-admin:tenant-config:";

    private final NbchatAdminConfigProperties nbchatAdminConfigProperties;

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    @Resource
    private SysTenantSubsystemMapper sysTenantSubsystemMapper;
    @Resource
    private ScriptSQLMapper scriptSQLMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private RedisHelper redisHelper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBalanceApi tradeBalanceApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private UserVipRightsApi userVipRightsApi;


    public SysTenantManagementImpl(NbchatAdminConfigProperties nbchatAdminConfigProperties) {
        this.nbchatAdminConfigProperties = nbchatAdminConfigProperties;
    }

    /**
     * 创建租户
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp createTenant(SysTenantManagementReqBO reqBO) {
        log.info("创建租户-入参：{}", reqBO);
        String targetTenant = reqBO.getTargetTenantCode();
        if (StringUtils.isBlank(reqBO.getTenantName())) {
            return BaseRspUtils.createErrorRsp("租户名称不能为空");
        }
        if (StringUtils.isBlank(targetTenant)) {
            targetTenant = IdWorker.nextAutoIdStr();
        }
        NbchatSysTenant nbchatSysTenant = new NbchatSysTenant();
        BeanUtils.copyProperties(reqBO, nbchatSysTenant);
        nbchatSysTenant.setCreateTime(new Date());
        nbchatSysTenant.setUpdateTime(new Date());
        nbchatSysTenant.setIsValid(EntityValidType.NORMAL.getCode());
        nbchatSysTenant.setTenantCode(targetTenant);
        nbchatSysTenant.setTplCode(nbchatAdminConfigProperties.getDefaultMenuTpl());
        int tenant = nbchatSysTenantMapper.insertSelective(nbchatSysTenant);
        if (tenant == 0) {
            return BaseRspUtils.createErrorRsp("创建租户失败");
        }
        try {
            //初始化部门
            this.initDept(nbchatSysTenant);
            //初始化课程分类
            this.initTrainCategory(nbchatSysTenant);
            //创建默认系统题库分类
            this.initExamCategory(targetTenant);
            //创建默认岗位
            this.initPost(targetTenant);
            //创建租户算力点账户
            UserBalanceAccountReqBO accountReqBO = new UserBalanceAccountReqBO();
            accountReqBO.setTenantCode(targetTenant);
            accountReqBO.setUserId(targetTenant);
            tradeBalanceApi.createBalanceAccount(accountReqBO);
        } catch (Exception e) {
            log.error("创建租户-初始化数据异常：{}", nbchatSysTenant, e);
        }
        return BaseRspUtils.createSuccessRsp(nbchatSysTenant, "创建租户成功");
    }

    public void initPost(String tenantCode) {
        ScriptSQLPO po = new ScriptSQLPO();
        po.setTenantCode(tenantCode);
        scriptSQLMapper.insertPost(po);
    }

    public void initExamCategory(String tenantCode) {
        ScriptSQLPO po = new ScriptSQLPO();
        po.setTenantCode(tenantCode);
        po.setId((int) IdWorker.nextAutoId());
        scriptSQLMapper.insertExamCategory(po);
    }


    private void initTrainCategory(NbchatSysTenant tenant) {
        //初始化课程分类
        //执行创建默认课程脚本
        long id = Math.abs(IdWorker.nextAutoId() >> 1);
        long aId = Math.abs(IdWorker.nextAutoId() >> 1);
        long qId = Math.abs(IdWorker.nextAutoId() >> 1);
        ScriptSQLPO build = ScriptSQLPO.builder().tenantCode(tenant.getTenantCode()).
                courseId(IdWorker.nextAutoIdStr())
                .id(Math.abs((int) id)).qId(Math.abs((int) qId)).aId(Math.abs((int) aId)).build();
        log.info("脚本入参：{}", build);
        scriptSQLMapper.insertCategory(build);
    }


    private void initDept(NbchatSysTenant tenant) {
        SysDept dept = new SysDept();
        dept.setDeptId(tenant.getTenantCode());
        dept.setParentId("0");
        dept.setDeptName(tenant.getTenantName());
        dept.setTenantCode(tenant.getTenantCode());
        dept.setAncestors(tenant.getTenantCode());
        dept.setCreateTime(new Date());
        dept.setUpdateTime(new Date());
        dept.setDeptDesc("租户部门");
        dept.setLevel(0);
        dept.setStatus(EntityValidType.NORMAL.getCode());
        dept.setIsValid(EntityValidType.NORMAL.getCode());
        sysDeptMapper.insertSelective(dept);
    }

    /**
     * 查询租户信息
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @Override
    public RspList getTenantList(SysTenantManagementReqBO reqBO) {
        log.info("查询租户信息：{}", reqBO);
        List<SysTenantManagementRspBO> rspBOList = new ArrayList<>();
        // 查询租户信息
        NbchatSysTenantSelectCondition condition = new NbchatSysTenantSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setTenantCode(reqBO.getTargetTenantCode());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatSysTenantMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(), rspBOList, SysTenantManagementRspBO.class);
        for (SysTenantManagementRspBO bo : rspBOList) {
            bo.setScore(0);
            try {
                Rsp<UserBalanceBO> balanceBORsp = tradeBalanceApi.getBalance(bo.getTenantCode(), bo.getTenantCode());
                if (balanceBORsp.isSuccess()) {
                    bo.setScore(balanceBORsp.getData().getScore());
                }
            } catch (Exception e) {
            }
            // 查询并设置子系统配置
            bo.setSubsystems(queryAndSetSubsystems(bo.getTenantCode()));
        }

        log.info("查询租户信息-结果：{}", rspBOList);
        return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());
    }

    /**
     * 查询租户的子系统配置并转换为响应对象列表
     *
     * @param tenantCode 租户编码
     * @return 子系统配置响应对象列表
     */
    private List<SysTenantSubsystemRspBo> queryAndSetSubsystems(String tenantCode) {
        List<SysTenantSubsystemRspBo> subsystemRspList = new ArrayList<>();
        List<SysTenantSubsystem> subsystemList = sysTenantSubsystemMapper.selectByTenantCode(tenantCode);
        for (SysTenantSubsystem subsystem : subsystemList) {
            SysTenantSubsystemRspBo subsystemRspBo = new SysTenantSubsystemRspBo();
            BeanUtils.copyProperties(subsystem, subsystemRspBo);
            subsystemRspList.add(subsystemRspBo);
        }

        return subsystemRspList;
    }


    /**
     * 租户价格配置
     * {
     *     "custom": {
     *         "2.5d_mtk": {
     *             "price": "",
     *             "score": "",
     *             "usable": "",
     *             "allRights": ""
     *         },
     *         "2d": {
     *             "price": "",
     *             "score": "",
     *             "usable": "",
     *             "allRights": ""
     *         },
     *         "audio": {
     *             "price": "",
     *             "score": "",
     *             "usable": "",
     *             "allRights": ""
     *         }
     *     }
     * }
     */

    /**
     * 更新租户信息
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @MethodParamVerifyEnable
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @Transactional(rollbackFor = Exception.class)
    public Rsp updateTenant(SysTenantManagementReqBO reqBO) {
        log.info("更新租户信息：{}", reqBO);
        String targetTenant = reqBO.getTargetTenantCode();
        NbchatSysTenant nbchatSysTenant = new NbchatSysTenant();
        BeanUtils.copyProperties(reqBO, nbchatSysTenant);
        nbchatSysTenant.setTenantCode(targetTenant);
        nbchatSysTenant.setUpdateTime(new Date());
        nbchatSysTenantMapper.updateByPrimaryKeySelective(nbchatSysTenant);

        // 处理子系统配置
        List<SysTenantSubsystemRspBo> subsystemConfigs = reqBO.getSubsystems();
        if (subsystemConfigs != null && !subsystemConfigs.isEmpty()) {
            processSubsystemConfig(targetTenant, subsystemConfigs);
        }

        try {
            this.updateRights(reqBO);
        } catch (Exception e) {
            log.error("更新租户信息-更新权益异常：{}", reqBO, e);
        }
        return BaseRspUtils.createSuccessRsp("更新租户信息成功");
    }

    /**
     * 处理子系统配置的更新或插入逻辑
     *
     * @param targetTenant     租户编码
     * @param subsystemConfigs 子系统配置列表
     */
    private void processSubsystemConfig(String targetTenant, List<SysTenantSubsystemRspBo> subsystemConfigs) {
        // 删除已有的子系统配置
        sysTenantSubsystemMapper.deleteByTenantCode(targetTenant);

        for (SysTenantSubsystemRspBo subsystemConfig : subsystemConfigs) {
            SysTenantSubsystem subsystem = new SysTenantSubsystem();
            BeanUtils.copyProperties(subsystemConfig, subsystem);
            subsystem.setTenantCode(targetTenant);
            subsystem.setCreateTime(new Date());
            subsystem.setUpdateTime(new Date());
            sysTenantSubsystemMapper.insertSelective(subsystem);
            log.info("插入子系统配置：{}", subsystem);
        }
    }

    @Override
    public Rsp queryCustomConfig(SysTenantManagementReqBO reqBO) {
        log.info("查询租户价格配置：{}", reqBO);

        // 查询租户信息
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(reqBO.getTargetTenantCode());
        if (tenant == null) {
            return BaseRspUtils.createErrorRsp("租户不存在");
        }

        // 获取租户配置
        String tenantConfig = getTenantConfig(tenant);

        // 查询价格配置
        String standerPrice = commonMapper.queryStanderPrice();
        if (ObjectUtils.isEmpty(standerPrice)) {
            log.error("公网标准价格不存在");
            return BaseRspUtils.createErrorRsp("公网标准价格不存在");
        }

        // 检查并更新价格
        JSONObject tenantConf = JSON.parseObject(tenantConfig);
        JSONObject priceData = JSON.parseObject(standerPrice);
        putCustomPrices(tenantConf, priceData);

        // 查询权益并更新
        putCustomRights(tenantConf, reqBO.getTargetTenantCode());

        return BaseRspUtils.createSuccessRsp(tenantConf);
    }

    private String getTenantConfig(NbchatSysTenant tenant) {
        String config = tenant.getCustomConfig();
        if (StringUtils.isEmpty(config) || !JSON.isValidObject(config)) {
            config = readInitConfig();
        }
        return config;
    }

    private void putCustomPrices(JSONObject tenantConf, JSONObject priceData) {
        log.info("更新租户价格配置：{}|{}", tenantConf, priceData);
        JSONObject custom = tenantConf.getJSONObject("custom");
        if (custom == null) {
            return;
        }
        for (String key : custom.keySet()) {
            JSONObject jsonObject = custom.getJSONObject(key);
            if (jsonObject != null) {
                if (ObjectUtils.isEmpty(jsonObject.getString("price"))) {
                    Integer price = priceData.getInteger(key);
                    if (price != null) {
                        jsonObject.put("price", price);
                    }
                }
                if (ObjectUtils.isEmpty(jsonObject.getString("score"))) {
                    jsonObject.put("score", 20000);
                }
            }
        }
    }

    private void putCustomRights(JSONObject tenantConf, String tenantCode) {
        NbchatUserVipRightsBO bo = NbchatUserVipRightsBO.builder().targetTenantCode(tenantCode).build();
        RspList<NbchatUserVipRightsBO> rspList = userVipRightsApi.queryRights(bo);

        if (!rspList.isSuccess()) {
            return; // 没有权益，使用object默认配置
        }

        JSONObject custom = tenantConf.getJSONObject("custom");
        for (NbchatUserVipRightsBO rights : rspList.getRows()) {
            JSONObject customJSONObject = custom.getJSONObject(rights.getCustomizeType());
            if (customJSONObject != null) {
                customJSONObject.put("usable", rights.getUsableCount());
                customJSONObject.put("allRights", rights.getRightsCount());
            }
        }
    }

    /**
     * 读取租户默认配置文件
     */
    public String readInitConfig() {
        String config = "";
        try {
            ClassPathResource resource = new ClassPathResource("init/tenant_custom.json");
            config = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取租户配置文件失败", e);
        }
        return config;
    }


    private void updateRights(SysTenantManagementReqBO request) {
        String customConfig = request.getCustomConfig();
        if (!JSON.isValid(customConfig)) {
            return;
        }
        JSONObject json = JSON.parseObject(customConfig);
        if (json.containsKey("custom")) {
            JSONObject custom = json.getJSONObject("custom");
            this.updateRights(custom, request.getTargetTenantCode(), CustomizeTypeEnum._2D.getCode());
            this.updateRights(custom, request.getTargetTenantCode(), CustomizeTypeEnum._25DMTK.getCode());
            this.updateRights(custom, request.getTargetTenantCode(), CustomizeTypeEnum._AUDIO.getCode());
        }
    }

    public void updateRights(JSONObject custom, String targetTenantCode, String customizeType) {
        if (custom.containsKey(customizeType)) {
            NbchatUserVipRightsBO rightsBO = new NbchatUserVipRightsBO();
            rightsBO.setTenantCode(targetTenantCode);
            rightsBO.setUserId(targetTenantCode);
            rightsBO.setCustomizeType(customizeType);
            rightsBO.setRightsType(CustomizeTypeEnum.getRightsType(customizeType));

            JSONObject config = custom.getJSONObject(customizeType);
            rightsBO.setUsableCount(config.getInteger("usable"));
            rightsBO.setRightsCount(config.getInteger("allRights"));
            try {
                userVipRightsApi.saveRights(rightsBO);
            } catch (Exception e) {
                throw e;
            }
        }
    }

}
