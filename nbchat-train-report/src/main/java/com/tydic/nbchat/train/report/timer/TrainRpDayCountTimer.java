package com.tydic.nbchat.train.report.timer;

import com.tydic.nbchat.train.api.bo.report.CountRpDayItemRequest;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpDayItemApi;
import com.tydic.nbchat.train.report.config.NbchatTrainRpConfigProperties;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 日报统计任务
 */
@Slf4j
@ConditionalOnProperty(
        value = "nbchat-train.config.rp-config.timer-enable",
        havingValue = "true")
@EnableScheduling
@Component
public class TrainRpDayCountTimer {

    private final static String NBCHAT_TRAIN_RP_TIMER_LOCK_KEY = "NBCHAT_TRAIN_RP_TIMER_LOCK";
    private final RedisHelper redisHelper;
    private final NbchatTrainRpConfigProperties nbchatTrainRpConfigProperties;
    private final NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi;

    public TrainRpDayCountTimer(RedisHelper redisHelper,
                                NbchatTrainRpConfigProperties nbchatTrainRpConfigProperties,
                                NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi) {
        this.redisHelper = redisHelper;
        this.nbchatTrainRpConfigProperties = nbchatTrainRpConfigProperties;
        this.nbcahtTrainRpDayItemApi = nbcahtTrainRpDayItemApi;
    }

    @Scheduled(cron = "${nbchat-train.config.rp-config.timer-cron:0 0 2 * * ?}")
    public void doCheckSessionTimout() {
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(NBCHAT_TRAIN_RP_TIMER_LOCK_KEY).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity);
        if (locked) {
            try {
                log.info("报表统计任务,开始执行:{}", redisLockEntity);
                Date start = DateTimeUtil.createTime(DateTimeUtil.DateAddDayOfYear(-1),0,0,0);
                Date end = DateTimeUtil.getStartTimeOfDay();
                long startTime = System.currentTimeMillis();
                for (String itemCode : nbchatTrainRpConfigProperties.getItemCodes()) {
                    CountRpDayItemRequest request = CountRpDayItemRequest.builder().
                            itemCode(itemCode).
                            startTime(start).
                            endTime(end).build();
                    Rsp rsp = nbcahtTrainRpDayItemApi.countRpDayItem(request);
                    log.info("报表统计任务,指标统计:{}|{}",request,rsp);
                }
                long endTime = System.currentTimeMillis();
                log.info("报表统计任务,执行完成:{} ms", (endTime - startTime));
            } catch (Exception e) {
                log.error("报表统计任务,执行异常:", e);
            } finally {
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }


}
