package com.tydic.nbchat.train.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptQueryRspBO;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.train.api.NbchatTrainCourseScoreApi;
import com.tydic.nbchat.train.api.bo.constants.CourseScoreConstants;
import com.tydic.nbchat.train.api.bo.course.TrainCourseScoreQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TrainCourseScoreQueryRspBO;
import com.tydic.nbchat.train.api.bo.course.TrainUserQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TrainUserQueryRspBO;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseScoreMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainUserSelectCondition;
import com.tydic.nbchat.train.mapper.po.NbchatCourseScore;
import com.tydic.nbchat.train.mapper.po.NbchatCourseScoreSelectCondition;
import com.tydic.nbchat.train.mapper.po.NbchatTrainUserTenant;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class NbchatTrainCourseScoreImpl implements NbchatTrainCourseScoreApi {
    @Resource
    private NbchatTrainCourseScoreMapper nbchatTrainCourseScoreMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private FileManageService fileManageService;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi nbchatUserApi;

    @Resource
    NameMapper nameMapper;


    /**
     * 查询成绩
     *
     * @param
     * @return @return {@link Rsp }
     */
    @MethodParamVerifyEnable
    @Override
    public RspList getStudyRecordsByPage(TrainCourseScoreQueryReqBO reqBO) {
        log.info("课程统计-:{}", reqBO);
        List<TrainCourseScoreQueryRspBO> result = new ArrayList<>();
        NbchatCourseScoreSelectCondition nbchatCourseScoreSelectCondition = new NbchatCourseScoreSelectCondition();
        BeanUtils.copyProperties(reqBO, nbchatCourseScoreSelectCondition);
        Page<NbchatCourseScore> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<NbchatCourseScore> list = nbchatTrainCourseScoreMapper.getGrades(nbchatCourseScoreSelectCondition);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("课程统计-查询结果为空");
            return BaseRspUtils.createSuccessRspList(result, 0);
        }
        page.getResult().forEach(item -> {
            if (StringUtils.isNotBlank(item.getTestState()) && item.getTestState().equals(CourseScoreConstants.EXAM)) {
                //查询成绩
                NbchatCourseScore score = nbchatTrainCourseScoreMapper.getScore(item);
                item.setScore(score.getScore());
                item.setTestCount(score.getTestCount());
            } else {
                item.setScore(CourseScoreConstants.NO_SCORE_VALUE);
                item.setTestCount(CourseScoreConstants.ZERO);
            }
            String deptId = item.getDeptId();
            if (StringUtils.isBlank(item.getDeptId())){
                deptId = item.getTenantCode();
            }
            //查询归属部门
            String deptName = nameMapper.queryOrganizeName(deptId);
            item.setDeptName(deptName);
        });
        NiccCommonUtil.copyList(page.getResult(), result, TrainCourseScoreQueryRspBO.class);
        log.info("课程统计-查询成功:{}|{}", result, page.getTotal());
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    /**
     * 导出学习记录
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp export(TrainCourseScoreQueryReqBO reqBO) {
        log.info("课程统计-导出:{}", reqBO);
        NbchatCourseScoreSelectCondition nbchatCourseScoreSelectCondition = new NbchatCourseScoreSelectCondition();
        BeanUtils.copyProperties(reqBO, nbchatCourseScoreSelectCondition);
        List<NbchatCourseScore> result = nbchatTrainCourseScoreMapper.getGrades(nbchatCourseScoreSelectCondition);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("课程统计-查询结果为空");
            return BaseRspUtils.createErrorRsp("查询结果为空");
        }
        result.forEach(item -> {
            if (StringUtils.isNotBlank(item.getTestState()) && item.getTestState().equals(CourseScoreConstants.EXAM)) {
                //查询成绩
                NbchatCourseScore score = nbchatTrainCourseScoreMapper.getScore(item);
                item.setScore(score.getScore());
                item.setTestCount(score.getTestCount());
            } else {
                item.setScore(CourseScoreConstants.NO_SCORE_VALUE);
                item.setTestCount(CourseScoreConstants.ZERO);
            }
        });
        TrainCourseScoreQueryRspBO trainCourseScoreQueryRspBO = new TrainCourseScoreQueryRspBO();
        try {
            String fileName = CourseScoreConstants.EXCEL_NAME + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String tempPath = System.getProperty("java.io.tmpdir");
            File dirFile = new File(tempPath + "/" + fileName);
            EasyExcel.write(dirFile, NbchatCourseScore.class).sheet(CourseScoreConstants.EXCEL_NAME).doWrite(result);
            log.info("课程统计-导出:{}|{}",dirFile.exists(),dirFile.getAbsolutePath());
            MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(dirFile);
            FileUploadRequest uploadRequest = new FileUploadRequest();
            uploadRequest.setTenantCode(reqBO.getTenantCode());
            uploadRequest.setUploadUser(reqBO.get_userId());
            uploadRequest.setFileName(dirFile.getName());
            uploadRequest.setFile(multipartFile.getBytes());
            uploadRequest.setUploadUser(reqBO.get_userId());
            RspList<FileManageSaveBO> fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
            log.info("文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
            if (!fileManageSaveBOS.isSuccess()) {
                return BaseRspUtils.createErrorRsp("上传文件失败");
            }
            trainCourseScoreQueryRspBO.setFileManageSaveBO(fileManageSaveBOS.getRows());
            log.info("课程统计-导出成功");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BaseRspUtils.createSuccessRsp(trainCourseScoreQueryRspBO,"导出成功");
    }

    /**
     * 获取用户姓名-手机号
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public RspList getUserInformation(TrainUserQueryReqBO reqBO) {
        log.info("课程统计-获取用户姓名-手机号:{}", reqBO);
        List<TrainUserQueryRspBO> result = new ArrayList<>();
        NbchatTrainUserSelectCondition nbChatTrainUserSelectCondition = new NbchatTrainUserSelectCondition();
        BeanUtils.copyProperties(reqBO, nbChatTrainUserSelectCondition);
        Page<NbchatTrainUserTenant> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<NbchatTrainUserTenant> nbchatTrainUserTenantList = nbchatTrainCourseScoreMapper.getUserInformation(nbChatTrainUserSelectCondition);
        if (CollectionUtils.isEmpty(nbchatTrainUserTenantList)) {
            log.info("课程统计-获取用户姓名-手机号结果为空,该租户:{}下没有名字为:{}的用户",reqBO.getTenantCode(),reqBO);
            return BaseRspUtils.createSuccessRspList(result, Long.parseLong(CourseScoreConstants.ZERO));
        }
        page.getResult().forEach(nbchatTrainUserTenant->{
            String userId = nbchatTrainUserTenant.getUserId();
            String userRealityName = nbchatTrainUserTenant.getUserRealityName();
            Rsp<NbchatUserInfo> rsp = nbchatUserApi.getUserInfo(userId);
            if (rsp.isSuccess()){
                NbchatUserInfo nbchatUserInfo = rsp.getData();
                log.info("课程统计-获取用户姓名-手机号成功,用户信息:{}", nbchatUserInfo);
                TrainUserQueryRspBO trainUserQueryRspBO = new TrainUserQueryRspBO();
                trainUserQueryRspBO.setUserRealityName(userRealityName);
                trainUserQueryRspBO.setUserPhone(nbchatUserInfo.getPhone());
                result.add(trainUserQueryRspBO);
            }else {
                log.error("课程统计-获取用户姓名-手机号,查询用户信息失败:{}", userId);
            }
        });
        return BaseRspUtils.createSuccessRspList(result,page.getTotal());
    }
}
