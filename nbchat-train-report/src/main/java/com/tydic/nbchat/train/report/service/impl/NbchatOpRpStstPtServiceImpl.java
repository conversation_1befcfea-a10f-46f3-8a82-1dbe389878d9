package com.tydic.nbchat.train.report.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.SysTenantQueryReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantQueryRspBO;
import com.tydic.nbchat.train.api.NbchatOpRpStstPtService;
import com.tydic.nbchat.train.api.bo.constants.OpReportConstants;
import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.CountRpDayItemRequest;
import com.tydic.nbchat.train.api.bo.report.op.TrainOpReportRspBO;
import com.tydic.nbchat.train.api.bo.report.op.TrainOpStstPtRequestBO;
import com.tydic.nbchat.train.api.bo.report.op.TrainOpTenantRspBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpDayItemApi;
import com.tydic.nbchat.train.mapper.OpRpStstDayItemMapper;
import com.tydic.nbchat.train.mapper.OpRpStstPtMapper;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NbchatOpRpStstPtServiceImpl implements NbchatOpRpStstPtService {
    private final OpRpStstPtMapper opRpStstPtMapper;

    private final NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi;

    private final OpRpStstDayItemMapper opRpStstDayItemMapper;

    public NbchatOpRpStstPtServiceImpl(OpRpStstPtMapper opRpStstPtMapper,
                                       OpRpStstDayItemMapper opRpStstDayItemMapper,
                                       NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi) {
        this.opRpStstPtMapper = opRpStstPtMapper;
        this.opRpStstDayItemMapper = opRpStstDayItemMapper;
        this.nbcahtTrainRpDayItemApi = nbcahtTrainRpDayItemApi;
    }
    @Override
    public Rsp save(TrainOpStstPtRequestBO requestBO) {
        //判断用户是否在公共租户下
        int commonTenant = opRpStstPtMapper.selectTrueOrFalseCommon(requestBO.getUserId());
        if (commonTenant == 1){
            //查用户企业信息
            String companyName = opRpStstPtMapper.selectUserEnterprise(requestBO.getUserId());
            if (StringUtils.isNotBlank(companyName)){
                requestBO.setTenantCode(companyName);
            }
        }else {
            int tydic = opRpStstPtMapper.selectTrueOrFalseTydic(requestBO.getUserId());
            if (tydic ==1){
                requestBO.setTenantCode("000TYDIC");
            }else {
                String tenantCode = opRpStstPtMapper.selectTenantCode(requestBO.getUserId());
                requestBO.setTenantCode(tenantCode);
            }
        }
        OpRpStstPt opRpStstPt = new OpRpStstPt();
        BeanUtils.copyProperties(requestBO,opRpStstPt);
        opRpStstPtMapper.insertSelective(opRpStstPt);
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    @Override
    public Rsp queryReport(TrainOpStstPtRequestBO requestBO) {
        requestBO.setLimit(99999);
        //执行统计任务
        count();
        TrainOpReportRspBO reportRspBO = new TrainOpReportRspBO();
        SysTenantQueryReqBO reqBO = new SysTenantQueryReqBO();
        BeanUtils.copyProperties(requestBO,reqBO);
        RspList rspList = selectTenantList(reqBO,true);
        if (rspList.isSuccess()){
            List<SysTenantQueryRspBO> periodList= rspList.getRows();
            Map<String, Long> summaryByTenantCode = periodList.stream()
                    .collect(Collectors.groupingBy(
                            // 自定义的键映射函数
                            trainOpTenantRspBO -> UserAttributeConstants.DEFAULT_TENANT_CODE.equals(trainOpTenantRspBO.getTenantCode()) ? UserAttributeConstants.DEFAULT_TENANT_CODE : OpReportConstants.professionalTenantCode,
                            Collectors.counting() // 对每组元素进行计数
                    ));
            reportRspBO.setTenantNum(summaryByTenantCode.get(OpReportConstants.professionalTenantCode)==null?0:summaryByTenantCode.get(OpReportConstants.professionalTenantCode).intValue());
            reportRspBO.setEnterpriseNum(summaryByTenantCode.get(UserAttributeConstants.DEFAULT_TENANT_CODE)==null?0:summaryByTenantCode.get(UserAttributeConstants.DEFAULT_TENANT_CODE).intValue());
        }
        requestBO.setIsDate(false);
        RspList rspBOList = queryTenantDataList(requestBO);
        if (rspBOList.isSuccess()){
            List<TrainOpTenantRspBO> tenantRspBOList = (List<TrainOpTenantRspBO>)rspBOList.getRows();
            List<TrainOpTenantRspBO> trainOpTenantRspBOS = new ArrayList<>();
            for (TrainOpTenantRspBO tenantRspBO:tenantRspBOList){
                trainOpTenantRspBOS.add(tenantRspBO.deepCopy());
            }
            reportRspBO.setOpTenantRspBOList(trainOpTenantRspBOS);
        }
        //统计数据
        Map<String, TrainOpTenantRspBO> summaryByTenantCode = processorData(requestBO);
        //客户专业租户板块
        TrainOpTenantRspBO professionalTenantRspBO = summaryByTenantCode.get(OpReportConstants.professionalTenantCode);
        if (professionalTenantRspBO != null) {
            reportRspBO.setTenantUserNum(professionalTenantRspBO.getTenantUserNum());
            // 智能创作板块数据也依赖于此分支内的数据
            reportRspBO.setVideoNum(professionalTenantRspBO.getVideoNum());
            reportRspBO.setVideoSuccessNum(professionalTenantRspBO.getVideoSuccessNum());
            reportRspBO.setPptNum(professionalTenantRspBO.getPptNum());
            reportRspBO.setQuestionNum(professionalTenantRspBO.getQuestionNum());
        }

        //客户-公共租户板块
        TrainOpTenantRspBO commonOpTenantRspBO = summaryByTenantCode.get(UserAttributeConstants.DEFAULT_TENANT_CODE);
        if (commonOpTenantRspBO != null) {
            reportRspBO.setEnterpriseUserNum(commonOpTenantRspBO.getTenantUserNum());
            // 智能创作板块数据累加
            reportRspBO.setVideoNum(reportRspBO.getVideoNum() + commonOpTenantRspBO.getVideoNum());
            reportRspBO.setVideoSuccessNum(reportRspBO.getVideoSuccessNum() + commonOpTenantRspBO.getVideoSuccessNum());
            reportRspBO.setPptNum(reportRspBO.getPptNum() + commonOpTenantRspBO.getPptNum());
            reportRspBO.setQuestionNum(reportRspBO.getQuestionNum() + commonOpTenantRspBO.getQuestionNum());
        }
//        queryReportTenantList(requestBO,reportRspBO);
        // 总数据-租户
        requestBO.setEndTime(new Date());
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, OpReportConstants.startYear);
        calendar.set(Calendar.MONTH, Calendar.APRIL);
        calendar.set(Calendar.DAY_OF_MONTH, 2);
        calendar.set(Calendar.HOUR_OF_DAY, 15);
        calendar.set(Calendar.MINUTE, 30);
        Date startTime = calendar.getTime();
        requestBO.setStartTime(startTime);
        Map<String, TrainOpTenantRspBO> totalData = processorData(requestBO);

        TrainOpTenantRspBO professionalTenantTotal = totalData.get(OpReportConstants.professionalTenantCode);
        if (professionalTenantTotal != null) {
            reportRspBO.setTenantTotal(professionalTenantTotal.getTenantNum());
            reportRspBO.setTenantUserTotal(professionalTenantTotal.getTenantUserNum());
        }
        TrainOpTenantRspBO commonOpTenantTotal = totalData.get(UserAttributeConstants.DEFAULT_TENANT_CODE);
        if (commonOpTenantTotal != null) {
            reportRspBO.setEnterpriseTotal(commonOpTenantTotal.getTenantNum());
            reportRspBO.setEnterpriseUserTotal(commonOpTenantTotal.getTenantUserNum());
        }
        // 总数据-次数统计
        if (professionalTenantTotal != null && commonOpTenantTotal != null) {
            reportRspBO.setVideoTotal(professionalTenantTotal.getVideoNum() + commonOpTenantTotal.getVideoNum());
            reportRspBO.setVideoSuccessTotal(professionalTenantTotal.getVideoSuccessNum() + commonOpTenantTotal.getVideoSuccessNum());
            reportRspBO.setPptTotal(professionalTenantTotal.getPptNum() + commonOpTenantTotal.getPptNum());
            reportRspBO.setQuestionTotal(professionalTenantTotal.getQuestionNum() + commonOpTenantTotal.getQuestionNum());
        }

        Date lastLoginTime = opRpStstDayItemMapper.selectLastLoginTime(null);
        reportRspBO.setLastLoginTime(lastLoginTime);

        return BaseRspUtils.createSuccessRsp(reportRspBO);
    }

    @Override
    public RspList queryTenantDataList(TrainOpStstPtRequestBO requestBO){
        List<TrainOpTenantRspBO> tenantDataLists = new ArrayList<>();
        //查询租户（分页）
        SysTenantQueryReqBO reqBO = new SysTenantQueryReqBO();
        BeanUtils.copyProperties(requestBO,reqBO);
        RspList rspList = selectTenantList(reqBO,requestBO.getIsDate());
        if (rspList.isSuccess()){
            List<SysTenantQueryRspBO> list = rspList.getRows();
            //查询租户数据
            list.forEach(sysTenantQueryRspBO -> {
                TrainOpTenantRspBO rspBO = new TrainOpTenantRspBO();
                OpRpStstDayItem opRpStstDayItem = new OpRpStstDayItem();
                rspBO.setCreateTime(sysTenantQueryRspBO.getCreateTime());
                rspBO.setTenantName(sysTenantQueryRspBO.getTenantName());
                rspBO.setTenantCode(sysTenantQueryRspBO.getTenantCode());
                rspBO.setTenantNum(1);
                //判断是否为公共租户
                if (StringUtils.isNotBlank(sysTenantQueryRspBO.getTenantCode())&&sysTenantQueryRspBO.getTenantCode().equals(UserAttributeConstants.DEFAULT_TENANT_CODE)){
                    rspBO.setCommonTenant(true);
                    //是公共租户
                    opRpStstDayItem.setTenantCode(sysTenantQueryRspBO.getTenantName());
                    rspBO.setGroupTenantCode(sysTenantQueryRspBO.getTenantCode());
                    Date lastLoginTime = opRpStstDayItemMapper.selectCommonTenantLastLoginTime(sysTenantQueryRspBO.getTenantName());
                    rspBO.setLastLoginTime(lastLoginTime);
                }else {
                    opRpStstDayItem.setTenantCode(sysTenantQueryRspBO.getTenantCode());
                    rspBO.setGroupTenantCode(OpReportConstants.professionalTenantCode);
                    Date lastLoginTime = opRpStstDayItemMapper.selectLastLoginTime(sysTenantQueryRspBO.getTenantCode());
                    rspBO.setLastLoginTime(lastLoginTime);
                }
                //查询租户下的具体运营数据
                OpRpStstDayItemCondition condition = new OpRpStstDayItemCondition();
                BeanUtils.copyProperties(opRpStstDayItem,condition);
                condition.setStartTime(requestBO.getStartTime());
                condition.setEndTime(requestBO.getEndTime());
                selectTenantData(condition,rspBO);
                tenantDataLists.add(rspBO);
            });
        }
        rspList.setRows(tenantDataLists);
        return rspList;
    }

    public Rsp queryReportTenantList(TrainOpStstPtRequestBO requestBO,TrainOpReportRspBO reportRspBO) {
        count();
        List<TrainOpTenantRspBO> rspBOList = new ArrayList<>();
        OpRpStstDayItemCondition condition = new OpRpStstDayItemCondition();
        condition.setStartTime(requestBO.getStartTime());
        condition.setEndTime(requestBO.getEndTime());
        List<OpRpStstDayItem> tenantDataList = opRpStstDayItemMapper.selectTenantDataList(condition);
        tenantDataList.forEach(item->{
            TrainOpTenantRspBO rspBO = new TrainOpTenantRspBO();
            rspBO.setTenantCode(item.getTenantCode());
            analyticalItemCodeData(rspBO,item);
            //判断是否为专业租户
            NbchatSysTenant sysTenant = opRpStstDayItemMapper.decideProfessionalTenant(item.getTenantCode());
            if (sysTenant == null){
                rspBO.setCommonTenant(true);
                rspBO.setGroupTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
                sysTenant = opRpStstDayItemMapper.decideCommonTenant(item.getTenantCode());
            }else {
                rspBO.setGroupTenantCode(OpReportConstants.professionalTenantCode);
                rspBO.setCommonTenant(false);
            }
            rspBO.setCreateTime(sysTenant.getCreateTime());
            rspBO.setTenantName(sysTenant.getTenantName());
            rspBOList.add(rspBO);
        });
        Map<String, TrainOpTenantRspBO> tenantRspBOMap = rspBOList.stream().collect(
                Collectors.toMap(
                        TrainOpTenantRspBO::getTenantCode,
                        bo -> bo,
                        (bo1, bo2) -> {
                            bo1.addMetrics(bo2);
                            return bo1;
                        }
                )
        );
        List<TrainOpTenantRspBO> tenantRspBOList = new ArrayList<>();
        tenantRspBOMap.forEach((tenant,item)->{
            //判断是否为新创建的租户
            int result = item.getCreateTime().compareTo(requestBO.getStartTime());
            if (result>0){
                item.setTenantNum(1);
            }
            tenantRspBOList.add(item);
        });
        reportRspBO.setOpTenantRspBOList(tenantRspBOList);
        //拷贝list用作数据解析
        List<TrainOpTenantRspBO> trainOpTenantRspBOS = new ArrayList<>();
        for (TrainOpTenantRspBO tenantRspBO:tenantRspBOList){
            trainOpTenantRspBOS.add(tenantRspBO.deepCopy());
        }
        analytical(reportRspBO,trainOpTenantRspBOS,requestBO);
        return BaseRspUtils.createSuccessRsp(reportRspBO);
    }

    private Map<String, TrainOpTenantRspBO> processorData(TrainOpStstPtRequestBO requestBO){
        List<TrainOpTenantRspBO> tenantRspBOList = new ArrayList<>();
        //总数据的时候需要查询所有租户数据
        requestBO.setIsDate(false);
        RspList rspBOList = queryTenantDataList(requestBO);
        if (rspBOList.isSuccess()){
            tenantRspBOList = (List<TrainOpTenantRspBO>)rspBOList.getRows();
        }
        Map<String, TrainOpTenantRspBO> summaryByTenantCode = sumData(tenantRspBOList);
        log.debug("租户数据统计结果：{}",tenantRspBOList);
        return summaryByTenantCode;
    }

    private void count(){
        List<String> itemCodeList = new ArrayList<>();
        itemCodeList.add(RpCourseItemType.new_registrations.getCode());
        itemCodeList.add(RpCourseItemType.question_creators.getCode());
        itemCodeList.add(RpCourseItemType.ppt_creators.getCode());
        itemCodeList.add(RpCourseItemType.video_creators.getCode());
        itemCodeList.add(RpCourseItemType.video_success.getCode());
        for (String itemCode: itemCodeList){
            //执行统计任务
            Date start = DateTimeUtil.createTime(DateTimeUtil.DateAddDayOfYear(0),0,0,0);
            CountRpDayItemRequest request = CountRpDayItemRequest.builder().
                    itemCode(itemCode).
                    startTime(start).
                    endTime(new Date()).build();
            Rsp rsp = nbcahtTrainRpDayItemApi.countRpDayItem(request);
            log.info("报表统计任务,指标统计:{}|{}",request,rsp);
        }
    }


    /**
     * 查询租户列表
     * @param reqBO
     * @return
     */
    public RspList selectTenantList(SysTenantQueryReqBO reqBO,boolean isDate) {
        TenantSelectCondition condition = new TenantSelectCondition();
        log.info("查询租户-分页查询租户列表：{}", reqBO);
        if (isDate){
            condition.setStartTime(reqBO.getStartTime());
            condition.setEndTime(reqBO.getEndTime());
        }
        List<SysTenantQueryRspBO> result = new ArrayList<>();
        Page<NbchatSysTenant> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setTenantCode(reqBO.getTargetTenant());
        condition.setTenantName(reqBO.getTenantName());
        List<NbchatSysTenant> nbchatSysTenantList = opRpStstDayItemMapper.selectTenantList(condition);
        if (CollectionUtils.isEmpty(nbchatSysTenantList)) {
            return BaseRspUtils.createSuccessRspList(result);
        }
        NiccCommonUtil.copyList(page.getResult(), result, SysTenantQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    private void selectTenantData(OpRpStstDayItemCondition condition,TrainOpTenantRspBO rspBO){
        List<OpRpStstDayItem> tenantDataList = opRpStstDayItemMapper.selectTenantDataList(condition);
        tenantDataList.forEach(tenantData->{
            analyticalItemCodeData(rspBO,tenantData);
        });
    }

    private Map<String, TrainOpTenantRspBO> sumData(List<TrainOpTenantRspBO> tenantRspBOList){
        //汇总数据
        Map<String, TrainOpTenantRspBO> summaryByTenantCode = tenantRspBOList.stream().collect(
                Collectors.toMap(
                        TrainOpTenantRspBO::getGroupTenantCode,
                        bo -> bo,
                        (bo1, bo2) -> {
                            bo1.addMetrics(bo2);
                            return bo1;
                        }
                )
        );
        return summaryByTenantCode;
    }

    private void analyticalItemCodeData(TrainOpTenantRspBO rspBO,OpRpStstDayItem tenantData){
        if (RpCourseItemType.new_registrations.getCode().equals(tenantData.getItemCode())) {
            //注册用户数
            rspBO.setTenantUserNum(tenantData.getItemValue());
        }else if (RpCourseItemType.question_creators.getCode().equals(tenantData.getItemCode())) {
            //出题次数
            rspBO.setQuestionNum(tenantData.getItemValue());
        } else if (RpCourseItemType.ppt_creators.getCode().equals(tenantData.getItemCode())) {
            //ppt创作数量
            rspBO.setPptNum(tenantData.getItemValue());
        } else if (RpCourseItemType.video_creators.getCode().equals(tenantData.getItemCode())) {
            //制作视频数量
            rspBO.setVideoNum(tenantData.getItemValue());
        } else if (RpCourseItemType.video_success.getCode().equals(tenantData.getItemCode())) {
            int successVideoNum = tenantData.getItemValue();
            //视频制作成功数量
            rspBO.setVideoSuccessNum(tenantData.getItemValue());
            int videoNum = rspBO.getVideoNum();
            double success = (double) successVideoNum/videoNum;
            //视频制作失败率
            rspBO.setVideoFailProbability(rspBO.getVideoNum() > 0?(1.0-success)*100:0);
        }
    }

    private void analytical(TrainOpReportRspBO reportRspBO,List<TrainOpTenantRspBO> trainOpTenantRspBOS,TrainOpStstPtRequestBO requestBO){
        Map<String, TrainOpTenantRspBO> summaryByTenantCode = sumData(trainOpTenantRspBOS);
        //客户专业租户板块
        TrainOpTenantRspBO professionalTenantRspBO = summaryByTenantCode.get(OpReportConstants.professionalTenantCode);
        if (professionalTenantRspBO != null) {
            reportRspBO.setTenantUserNum(professionalTenantRspBO.getTenantUserNum());
            // 智能创作板块数据也依赖于此分支内的数据
            reportRspBO.setVideoNum(professionalTenantRspBO.getVideoNum());
            reportRspBO.setVideoSuccessNum(professionalTenantRspBO.getVideoSuccessNum());
            reportRspBO.setPptNum(professionalTenantRspBO.getPptNum());
            reportRspBO.setQuestionNum(professionalTenantRspBO.getQuestionNum());
        }
        //客户-公共租户板块
        TrainOpTenantRspBO commonOpTenantRspBO = summaryByTenantCode.get(UserAttributeConstants.DEFAULT_TENANT_CODE);
        if (commonOpTenantRspBO != null) {
            reportRspBO.setEnterpriseUserNum(commonOpTenantRspBO.getTenantUserNum());
            // 智能创作板块数据累加
            reportRspBO.setVideoNum(reportRspBO.getVideoNum() + commonOpTenantRspBO.getVideoNum());
            reportRspBO.setVideoSuccessNum(reportRspBO.getVideoSuccessNum() + commonOpTenantRspBO.getVideoSuccessNum());
            reportRspBO.setPptNum(reportRspBO.getPptNum() + commonOpTenantRspBO.getPptNum());
            reportRspBO.setQuestionNum(reportRspBO.getQuestionNum() + commonOpTenantRspBO.getQuestionNum());
        }
        SysTenantQueryReqBO reqBO = new SysTenantQueryReqBO();
        BeanUtils.copyProperties(requestBO,reqBO);
        RspList rspList = selectTenantList(reqBO,true);
        if (rspList.isSuccess()){
            List<SysTenantQueryRspBO> periodList= rspList.getRows();
            Map<String, Long> tenantMap = periodList.stream()
                    .collect(Collectors.groupingBy(
                            // 自定义的键映射函数
                            trainOpTenantRspBO -> UserAttributeConstants.DEFAULT_TENANT_CODE.equals(trainOpTenantRspBO.getTenantCode()) ? UserAttributeConstants.DEFAULT_TENANT_CODE : OpReportConstants.professionalTenantCode,
                            Collectors.counting() // 对每组元素进行计数
                    ));
            reportRspBO.setTenantNum(tenantMap.get(OpReportConstants.professionalTenantCode)==null?0:tenantMap.get(OpReportConstants.professionalTenantCode).intValue());
            reportRspBO.setEnterpriseNum(tenantMap.get(UserAttributeConstants.DEFAULT_TENANT_CODE)==null?0:tenantMap.get(UserAttributeConstants.DEFAULT_TENANT_CODE).intValue());
        }
    }
}
