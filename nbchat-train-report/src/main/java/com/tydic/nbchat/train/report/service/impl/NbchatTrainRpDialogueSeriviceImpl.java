package com.tydic.nbchat.train.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseCountItemReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseDataAnalysisRspBO;
import com.tydic.nbchat.train.api.bo.report.dialogue.TrainRpDialogueRankRspBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseCountApi;
import com.tydic.nbchat.train.api.rp_dialogue.NbchatTrainRpDialogueApi;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpDayItemMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourseSelectCondition;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItem;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatTrainRpDialogueSeriviceImpl implements NbchatTrainRpDialogueApi {

    @Resource
    NbcahtTrainRpCourseCountApi nbcahtTrainRpCourseCountApi;
    @Resource
    NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;

    @Override
    public Rsp countItem(QueryReportRequest request) {
        log.info("统计场景实践指标:{}",request);
        TrainRpCourseCountItemReqBO reqBO = new TrainRpCourseCountItemReqBO();
        reqBO.setTenantCode(request.getTenantCode());
        return nbcahtTrainRpCourseCountApi.countDialogueItem(reqBO);
    }

    @Override
    public Rsp circleScore(QueryReportRequest request) {
        log.info("统计场景实践-综合评分:{}",request);
        JSONObject res = new JSONObject(){{
            put(level_1,0);
            put(level_2,0);
            put(level_3,0);
            put(level_4,0);
            put(level_5,0);
        }};
        NbchatTrainRpDayItem cond = new NbchatTrainRpDayItem();
        cond.setTenantCode(request.getTenantCode());
        cond.setCountDay(DateTimeUtil.DateAddDayOfYear(-1));
        cond.setItemCode(RpCourseItemType.dialogue_level_count.getCode());
        List<NbchatTrainRpDayItem> items = nbchatTrainRpDayItemMapper.selectAll(cond);
        if (CollectionUtils.isEmpty(items)) {
            return BaseRspUtils.createSuccessRsp(res);
        }
        for (NbchatTrainRpDayItem item : items) {
            JSONObject entity = JSON.parseObject(item.getItemValues());
            res.put((String) entity.get("key"),item.getItemValue());
        }
        return BaseRspUtils.createSuccessRsp(res);
    }

    @Override
    public RspList analysis(QueryReportRequest request) {
        log.info("统计场景实践趋势：{}",request);
        if (request.getStartDate() == null) {
            request.setEndDate(DateTimeUtil.DateAddDayOfYear(-30));
        }
        if (request.getEndDate() == null) {
            request.setEndDate(new Date());
        }
        //学习人数趋势
        List<TrainRpCourseDataAnalysisRspBO> res = new ArrayList<>();
        for (RpCourseItemType value : RpCourseItemType.dialogueItems()) {
            TrainRpCourseDataAnalysisRspBO bo = nbcahtTrainRpCourseCountApi.calGraph(request, value);
            res.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public RspList rank(QueryReportRequest request) {
        log.info("场景实践排行：{}",request);
        ArrayList<TrainRpDialogueRankRspBO> res = new ArrayList<>();
        if (ObjectUtils.isEmpty(request.getStartDate())) {
            request.setStartDate(DateTimeUtil.DateAddDayOfYear(-30));
        }
        if (ObjectUtils.isEmpty(request.getEndDate())) {
            request.setEndDate(new Date());
        }
        NbchatTrainRpDayItem cond = new NbchatTrainRpDayItem();
        cond.setTenantCode(request.getTenantCode());
        cond.setItemCode(RpCourseItemType.dialogue_rank_count.getCode());
        cond.setStartDate(request.getStartDate());
        cond.setEndDate(request.getEndDate());
        List<NbchatTrainRpDayItem> rankList = nbchatTrainRpDayItemMapper.selectAll(cond);

        Map<String, List<NbchatTrainRpDayItem>> collect = rankList.stream().collect(Collectors.groupingBy(NbchatTrainRpDayItem::getCourseId));
        NbchatTrainCourseSelectCondition query = new NbchatTrainCourseSelectCondition();
        query.setTenantCode(request.getTenantCode());
        query.setCourseState(StateEnum.COURSE.ON.getCode());
        query.setIsValid(EntityValidType.NORMAL.getCode());
        query.setCourseTypes(request.getCourseTypes());
        List<NbchatTrainCourse> courses = nbchatTrainCourseMapper.selectByCondition(query);

        for (NbchatTrainCourse course : courses) {
            TrainRpDialogueRankRspBO bo = new TrainRpDialogueRankRspBO();
            bo.setCourseName(course.getCourseName());
            List<NbchatTrainRpDayItem> records = collect.get(course.getCourseId());
            if (CollectionUtils.isNotEmpty(records)) {
                List<JSONArray> itemValues = records.stream()
                        .map(NbchatTrainRpDayItem::getItemValues)
                        .map(JSONObject::parseArray)
                        .collect(Collectors.toList());
                HashMap<String, Integer> valuesMap = this.analysisValues(itemValues);
                bo.setUserTotal(valuesMap.get(userTotal));
                bo.setAvgScore(valuesMap.get(totalTimes) == 0 ? 0 : valuesMap.get(totalScore) / valuesMap.get(totalTimes));
                if (bo.getUserTotal() > 0) {
                    bo.setLevel_1(100 * valuesMap.get(level_1) / bo.getUserTotal());
                    bo.setLevel_2(100 * valuesMap.get(level_2) / bo.getUserTotal());
                    bo.setLevel_3(100 * valuesMap.get(level_3) / bo.getUserTotal());
                    bo.setLevel_4(100 * valuesMap.get(level_4) / bo.getUserTotal());
                    bo.setLevel_5(100 * valuesMap.get(level_5) / bo.getUserTotal());
                }
            }
            res.add(bo);
        }
        if (request.getOrderType().equals("asc")) {
            res = (ArrayList<TrainRpDialogueRankRspBO>) res.stream().sorted(Comparator.comparing(sortKey(request.getOrderCode())))
                    .collect(Collectors.toList());
        }
        if (request.getOrderType().equals("desc")) {
            res = (ArrayList<TrainRpDialogueRankRspBO>) res.stream().sorted(Comparator.comparing(sortKey(request.getOrderCode())).reversed())
                    .collect(Collectors.toList());
        }
        ArrayList resList = res.stream().skip((request.getPage() - 1) * request.getLimit()).limit(request.getLimit()).collect(Collectors.toCollection(ArrayList::new));
        return BaseRspUtils.createSuccessRspList(resList, res.size());
    }

    public HashMap<String,Integer> analysisValues(List<JSONArray> itemValues) {
        HashMap<String,Integer> map = new HashMap<>();
        DocumentContext context = JsonPath.parse(itemValues);
        for (String item : jsonItems) {
            List<Number> values = context.read("$.." + item, List.class);
            map.put(item,getSum(values));
        }
        return map;
    }

    public static Integer getSum(List<Number> arr){
        if (CollectionUtils.isEmpty(arr)) {
            return 0;
        }
        double sum = arr.stream().mapToDouble(Number::doubleValue).sum();
        return new Double(sum).intValue();
    }

    public Function<TrainRpDialogueRankRspBO, Integer> sortKey(String key){
        if (key.equals(level_1_code)) {
            return TrainRpDialogueRankRspBO::getLevel_1;
        }
        if (key.equals(level_2_code)) {
            return TrainRpDialogueRankRspBO::getLevel_2;
        }
        if (key.equals(level_3_code)) {
            return TrainRpDialogueRankRspBO::getLevel_3;
        }
        if (key.equals(level_4_code)) {
            return TrainRpDialogueRankRspBO::getLevel_4;
        }
        if (key.equals(level_5_code)) {
            return TrainRpDialogueRankRspBO::getLevel_5;
        }
        if (key.equals(avgScore)) {
            return TrainRpDialogueRankRspBO::getAvgScore;
        }
        if (key.equals(userTotal)) {
            return TrainRpDialogueRankRspBO::getUserTotal;
        }
        log.info("不支持的排序字段：{}",key);
        return null;
    }

}
