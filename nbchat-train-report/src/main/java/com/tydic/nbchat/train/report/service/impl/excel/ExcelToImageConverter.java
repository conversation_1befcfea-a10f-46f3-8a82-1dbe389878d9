package com.tydic.nbchat.train.report.service.impl.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class ExcelToImageConverter {
    public static void convertExcelToImage(String excelFilePath, String imageFilePath) {
        try (FileInputStream fis = new FileInputStream(excelFilePath)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 获取表格的总行数、总列数
            int totalRows = sheet.getPhysicalNumberOfRows();
            int totalCols = 0;
            for (int i = 0; i < totalRows; i++) {
                Row row = sheet.getRow(i);
                totalCols = Math.max(totalCols, row.getPhysicalNumberOfCells());
            }

            // 创建一个足够大的BufferedImage
            BufferedImage image = new BufferedImage(totalCols * 60, totalRows * 20, BufferedImage.TYPE_INT_RGB);
            Graphics2D graphics = image.createGraphics();

            // 设置背景色
            graphics.setColor(Color.WHITE);
            graphics.fillRect(0, 0, totalCols * 60, totalRows * 20);

            // 画出表格
            for (int i = 0; i < totalRows; i++) {
                Row row = sheet.getRow(i);
                for (int j = 0; j < totalCols; j++) {
                    Cell cell = row.getCell(j);
                    String cellValue = getCellValue(cell);

                    // 设置单元格的边框颜色
                    graphics.setColor(Color.BLACK);
                    graphics.drawRect(j * 60, i * 20, 60, 20);

                    // 设置单元格的背景色
                    graphics.setColor(Color.WHITE);
                    graphics.fillRect(j * 60 + 1, i * 20 + 1, 60 - 2, 20 - 2);

                    // 画出单元格的值
                    graphics.setColor(Color.BLACK);
                    graphics.drawString(cellValue, j * 60 + 5, i * 20 + 15);
                }
            }

            // 保存为图片
            ImageIO.write(image, "png", new File(imageFilePath));

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return Double.toString(cell.getNumericCellValue());
            case BOOLEAN:
                return Boolean.toString(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    public static void main(String[] args) {

        ExcelToImageConverter.convertExcelToImage("/tmp/1724902634271.xlsx","/tmp/1724902634271.png");
    }
}