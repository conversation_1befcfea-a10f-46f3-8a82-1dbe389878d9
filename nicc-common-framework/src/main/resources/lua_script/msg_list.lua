-- 查询未读消息
local chatObj = ARGV[1]
local chatType = ARGV[2]
local startTime = ARGV[3]
-- 全量数据
local list = redis.call("LRANGE", KEYS[1], 0, -1)
if list == nil then
    return "[]"
end
-- 按条件查询
local results = {}
for i, obj in ipairs(list) do
    repeat
        local msg = cjson.decode(obj)
        if msg.msgTime == nil then
            break;
        end
        if ( startTime ~= nil and startTime ~= "" and msg.msgTime < tonumber(startTime) ) then
            break;
        end
        if (chatObj == "" and chatType == "") then
            table.insert(results, msg)
            break;
        end
        if (chatObj == msg.fromNo and chatType == msg.chatType) then
            table.insert(results, msg)
            break;
        end
        if (chatObj == msg.fromNo and chatType == "") then
            table.insert(results, msg)
            break;
        end
        if (msg.chatType == chatType and chatObj == "") then
            table.insert(results, msg)
            break;
        end
    until true
end
return cjson.encode(results)