package com.tydic.nicc.framework.aspect;

import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.tydic.nicc.framework.annotitions.DistributedLock;

@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    @Autowired(required = false)
    private RedisHelper redisHelper; // 假设使用 Redis 实现分布式锁

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        if (redisHelper == null) {
            log.warn("redisHelper is null, distributed lock will not be applied.");
            return joinPoint.proceed();
        }
        String lockKey = distributedLock.lockKey();
        long timeout = distributedLock.timeout();
        long waitTime = distributedLock.waitTime();
        int retryCount = distributedLock.retryCount();
        boolean throwException = distributedLock.throwException();
        String requestId = String.valueOf(Thread.currentThread().getId());

        boolean locked = false;
        int attempts = 0;
        RedisLockEntity redisLockEntity = RedisLockEntity.builder()
                .lockKey(lockKey)
                .requestId(requestId)
                .build();
        // 尝试获取锁，支持重试机制
        while (!locked && attempts <= retryCount) {
            locked = redisHelper.lock(redisLockEntity, waitTime, timeout);
            if (!locked) {
                attempts++;
                if (attempts > retryCount) {
                    log.warn("获取锁失败，key: {}, 尝试次数: {}", lockKey, attempts);
                    if (throwException) {
                        throw new RuntimeException("获取锁失败，请稍后重试");
                    } else {
                        return null; // 或者根据业务需求返回默认值
                    }
                }
                Thread.sleep(waitTime); // 等待一段时间后重试
            }
        }
        try {
            // 执行目标方法
            return joinPoint.proceed();
        } finally {
            // 释放锁
            redisHelper.unlock(redisLockEntity);
        }
    }

}
