package com.tydic.nicc.framework.dubbo.monitor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DubboInfoStoreCenter {

    private static Map<Integer, DubboThreadInfoContext> dubboInfoStoreMap = new ConcurrentHashMap<>();

    public synchronized static void update(Integer port, DubboThreadInfoContext context) {
        if (context != null) {
            dubboInfoStoreMap.put(port,context);
        }
    }

    public DubboThreadInfoContext getInfo(Integer port){
        return dubboInfoStoreMap.get(port);
    }
    public void cleanInfo(Integer port) {
        dubboInfoStoreMap.remove(port);
    }

}
