package com.tydic.nicc.framework.permission;

import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.PermissionType;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.bo.user.SysRole;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.base.bo.constants.DcBaseConstants;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Aspect
public class RoleCheckAspect {

    public static Field[] getAllFields(Class<?> type) {
        List<Field> fields = new ArrayList<>();
        for (Class<?> c = type; c != null; c = c.getSuperclass()) {
            for (Field field : c.getDeclaredFields()) {
                if (field.getName().equals("roles")) {
                    fields.add(field);
                    break;
                }
            }
        }
        return fields.toArray(new Field[0]);
    }

    /**
     * 1. 读取方法上的注解 RequiresRole
     * 2. 读取方法参数里的角色列表
     * 3. 判断是否有权限
     */
    @Around(value = "@annotation(requiresRole)")
    public Object checkRequiresRole(ProceedingJoinPoint point, RequiresRole requiresRole) throws Throwable {
        //系统角色
        //private List<SysRole> roles;
        Object[] requestParams = point.getArgs();
        MethodSignature signature = (MethodSignature) point.getSignature();
        //获取method对象
        Method method = signature.getMethod();
        //获取方法的返回值的类型
        String returnType = method.getReturnType().getTypeName();
        Object requestParam = requestParams[0];
        //获取继承的 roles对象
        Field[] declaredFields = getAllFields(requestParam.getClass());
        // 遍历所有属性，判断如果 属性是SysRole类型，则获取角色列表
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object fieldObj = field.get(requestParam);
            String filedName = field.getName();
            if (filedName.equals("roles")) {
                List<SysRole> sysRoles = (List<SysRole>) fieldObj;
                if (sysRoles != null) {
                    List<String> roles = sysRoles.stream().
                            map(SysRole::getRoleCode).collect(Collectors.toList());
                    if (!checkRole(roles, requiresRole)) {
                        return createErrorReturn(returnType, "操作失败：权限异常");
                    }
                    return point.proceed();
                }
            }
        }

        return createErrorReturn(returnType, "操作失败：权限异常");
    }

    private Object createErrorReturn(String returnType, String msg) {
        if (Rsp.class.getName().equals(returnType)) {
            return BaseRspUtils.createErrorRsp(DcBaseConstants.RSP_ERROR_AUTH, msg);
        }
        if (RspList.class.getName().equals(returnType)) {
            return BaseRspUtils.createErrorRspList(DcBaseConstants.RSP_ERROR_AUTH, msg);
        }
        throw new RuntimeException(msg);
    }

    private boolean checkRole(List<String> sysRoles, RequiresRole requiresRole) {
        if (requiresRole.permissionType().equals(PermissionType.ACCESS)) {
            //判断是否有权限
            if (Logical.AND.name().equals(requiresRole.logical().name())) {
                //包含多个权限
                for (String role : requiresRole.value()) {
                    if (!sysRoles.contains(role)) {
                        return false;
                    }
                }
                return true;
            } else {
                //有任意权限即可
                boolean hasRole = false;
                for (String role : requiresRole.value()) {
                    if (sysRoles.contains(role)) {
                        hasRole = true;
                        break;
                    }
                }
                return hasRole;
            }
        } else {
            //判断是否拒绝
            if (Logical.AND.name().equals(requiresRole.logical().name())) {
                //包含多个权限
                for (String role : requiresRole.value()) {
                    if (sysRoles.contains(role)) {
                        return false;
                    }
                }
                return true;
            } else {
                //有任意权限即可
                boolean hasRole = false;
                for (String role : requiresRole.value()) {
                    if (sysRoles.contains(role)) {
                        hasRole = true;
                        break;
                    }
                }
                return !hasRole;
            }
        }

    }

}
