package com.tydic.nicc.framework.utils;

import com.tydic.nicc.common.eums.sensitive.SensitiveEncryptType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

@Slf4j
public class FileUtils {

    private static final HashMap<String,String> FILE_HEADER_TYPES = new HashMap();

    static {
        //images
        FILE_HEADER_TYPES.put("FFD8FF", "jpg");
        FILE_HEADER_TYPES.put("89504E47", "png");
        FILE_HEADER_TYPES.put("47494638", "gif");
        FILE_HEADER_TYPES.put("49492A00", "tif");
        FILE_HEADER_TYPES.put("424D", "bmp");
        // FILE_HEADER_TYPES.put("41433130", "dwg"); //CAD
        FILE_HEADER_TYPES.put("38425053", "psd");
        FILE_HEADER_TYPES.put("7B5C727466", "rtf");
        //日记本
        FILE_HEADER_TYPES.put("3C3F786D6C", "xml");
        FILE_HEADER_TYPES.put("68746D6C3E", "html");
        FILE_HEADER_TYPES.put("44656C69766572792D646174653A", "eml");
        FILE_HEADER_TYPES.put("D0CF11E0", "doc");
        FILE_HEADER_TYPES.put("5374616E64617264204A", "mdb");
        FILE_HEADER_TYPES.put("252150532D41646F6265", "ps");
        FILE_HEADER_TYPES.put("255044462D312E", "pdf");
        FILE_HEADER_TYPES.put("25504446", "pdf");
        FILE_HEADER_TYPES.put("504B0304", "docx");
        FILE_HEADER_TYPES.put("52617221", "rar");
        FILE_HEADER_TYPES.put("57415645", "wav");
        FILE_HEADER_TYPES.put("41564920", "avi");
        FILE_HEADER_TYPES.put("2E524D46", "rm");
        FILE_HEADER_TYPES.put("000001BA", "mpg");
        FILE_HEADER_TYPES.put("000001B3", "mpg");
        FILE_HEADER_TYPES.put("6D6F6F76", "mov");
        FILE_HEADER_TYPES.put("3026B2758E66CF11", "asf");
        FILE_HEADER_TYPES.put("4D546864", "mid");
        FILE_HEADER_TYPES.put("1F8B08", "gz");
        FILE_HEADER_TYPES.put("FFF328C4", "mp3");
    }


    /**
     * 下载文件
     *
     * @param urlPath
     * @param downloadDir
     * @param fileName
     * @return
     */
    public static File downloadFile(String urlPath, String downloadDir, String fileName) {
        String path = downloadDir + File.separatorChar + fileName;
        return downloadFile(urlPath, path);
    }

    /**
     * 说明：根据指定URL将文件下载到指定目标位置
     *
     * @param urlPath  下载路径
     * @param fullPath 完整文件路径
     * @return 返回下载文件
     */
    @SuppressWarnings("finally")
    public static File downloadFile(String urlPath, String fullPath) {
        File file = null;
        try {
            // 统一资源
            URL url = new URL(urlPath);
            // 连接类的父类，抽象类
            URLConnection urlConnection = url.openConnection();
            // http的连接类
            HttpURLConnection httpURLConnection = (HttpURLConnection) urlConnection;
            //设置超时
            httpURLConnection.setConnectTimeout(1000 * 5);
            //设置请求方式，默认是GET
            httpURLConnection.setRequestMethod("POST");
            // 设置字符编码
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            // 打开到此 URL引用的资源的通信链接（如果尚未建立这样的连接）。
            httpURLConnection.connect();
            // 文件大小
            int fileLength = httpURLConnection.getContentLength();
            // 控制台打印文件大小
            log.info("文件下载-开始:fileSize = ", fileLength / (1024 * 1024) + "MB");
            // 建立链接从请求中获取数据
            URLConnection con = url.openConnection();
            BufferedInputStream bin = new BufferedInputStream(httpURLConnection.getInputStream());
            file = new File(fullPath);
            // 校验文件夹目录是否存在，不存在就创建一个目录
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            OutputStream out = new FileOutputStream(file);
            int size = 0;
            int len = 0;
            byte[] buf = new byte[2048];
            while ((size = bin.read(buf)) != -1) {
                len += size;
                out.write(buf, 0, size);
                // 控制台打印文件下载的百分比情况
                log.info("文件下载-进度--> ", len * 100 / fileLength);
            }
            // 关闭资源
            bin.close();
            out.close();
            log.info("文件下载-成功: filePath = {}", file.getAbsoluteFile());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            return file;
        }
    }

    /**
     * 从网络读取文件
     *
     * @param urlPath
     * @return
     */
    public static String openFileFromUrl(String urlPath) {
        int HttpResult; // 服务器返回的状态
        StringBuffer buffer = new StringBuffer();
        try {
            log.error("读取网络文件-开始建立连接:urlPath = {}", urlPath);
            String encoding = "UTF-8";
            URL url = new URL(urlPath);
            URLConnection urlconn = url.openConnection();
            urlconn.connect();
            HttpURLConnection httpconn = (HttpURLConnection) urlconn;
            HttpResult = httpconn.getResponseCode();
            if (HttpResult != HttpURLConnection.HTTP_OK) {
                log.error("读取网络文件-网络连接失败:{},urlPath = {}", HttpResult, urlPath);
            } else {
                try (InputStreamReader read = new InputStreamReader(urlconn.getInputStream(), encoding)) {
                    BufferedReader bufferedReader = new BufferedReader(read);
                    String txt;
                    // 读取文件，将文件内容放入到set中
                    while ((txt = bufferedReader.readLine()) != null) {
                        buffer.append(txt);
                    }
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer.toString();
    }

    /**
     * 读取为set集合
     *
     * @param fileUrl
     * @return
     * @throws Exception
     */
    public static Set<String> readWordFromUrl(String fileUrl, String encrypt, String split) throws Exception {
        Set<String> set = new HashSet<>();
        String encoding = "UTF-8";
        URL url = new URL(fileUrl);
        URLConnection urlConnection = url.openConnection();
        urlConnection.connect();
        HttpURLConnection httpconn = (HttpURLConnection) urlConnection;
        int status = httpconn.getResponseCode();
        String content = "";
        if (status == HttpURLConnection.HTTP_OK) {
            try (InputStreamReader read = new InputStreamReader(urlConnection.getInputStream(), encoding)) {
                set = new HashSet<>();
                BufferedReader bufferedReader = new BufferedReader(read);
                String txt;
                // 读取文件，将文件内容放入到set中
                while ((txt = bufferedReader.readLine()) != null) {
                    txt = txt.trim();
                    if (SensitiveEncryptType.BASE64.matchCode(encrypt)) {
                        txt = NiccCommonUtil.decodeStr(txt).trim();
                    }
                    if (StringUtils.isEmpty(split)) {
                        set.add(txt);
                    } else {
                        content += txt;
                    }
                }
            }
            if (StringUtils.isNotEmpty(content)) {
                List<String> words = Arrays.asList(content.split(split));
                for (String word : words) {
                    if (SensitiveEncryptType.BASE64.matchCode(encrypt)) {
                        set.add(NiccCommonUtil.decodeStr(word).trim());
                    } else {
                        set.add(word);
                    }
                }
            }
        }

        return set;
    }

    /**
     * 是否为列表内的文件
     * @param stream
     * @return
     */
    public static boolean isAllowFile(InputStream stream){
        String header = getFileHeader(stream);
        boolean flag = FILE_HEADER_TYPES.get(header) != null;
        if(!flag){
            log.warn("文件头标识符非法:{}",header);
        }
        return flag;
    }

    /**
     * 获取真实文件类型
     * @param type
     * @return
     */
    public static String getRealFileType(String type){
        return FILE_HEADER_TYPES.get(type);
    }

    /**
     * 获取文件头
     * @param stream
     * @return
     */
    public static String getFileHeader(InputStream stream) {
        InputStream is = (InputStream) stream;
        String value = null;
        try {
            byte[] b = new byte[4];
            /** int read() 从此输入流中读取一个数据字节。int read(byte[] b) 从此输入流中将最多 b.length
             * 个字节的数据读入一个 byte 数组中。 int read(byte[] b, int off, int len)
             * 从此输入流中将最多 len 个字节的数据读入一个 byte 数组中。*/is.read(b, 0, b.length);
            value = bytesToHexString(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return value;
    }

    private static String bytesToHexString(byte[] src) {
        StringBuilder builder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        String hv;
        for (byte aSrc: src) {
            // 以十六进制(基数 16)无符号整数形式返回一个整数参数的字符串表示形式，并转换为大写
            hv = Integer.toHexString(aSrc & 0xFF).toUpperCase();
            if (hv.length() < 2) {
                builder.append(0);
            }
            builder.append(hv);
        }
        return builder.toString();
    }


}
