package com.tydic.nicc.framework.exception;


import com.tydic.nicc.common.eums.im.MsgSensitiveCodeDefine;

/**
 * <AUTHOR> <br>
 * @Description: RuntimeException  <br>
 * @date 2021/4/26 8:12 下午  <br>
 * @Copyright tydic.com
 */
public class ImSensitiveWordsException extends RuntimeException{
    /**
     * 错误编码
     */
    private String errorCode;

    public ImSensitiveWordsException(String message) {
        super(message);
        this.errorCode = MsgSensitiveCodeDefine.ERROR.getCode();
    }

    public ImSensitiveWordsException(String errorCode , String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode(){
        return this.errorCode;
    }
}
