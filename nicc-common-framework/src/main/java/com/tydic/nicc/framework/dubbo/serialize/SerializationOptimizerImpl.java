package com.tydic.nicc.framework.dubbo.serialize;

import com.tydic.nicc.common.bo.im.UserNoReadContext;
import com.tydic.nicc.common.bo.im.admin.*;
import com.tydic.nicc.common.bo.im.core.ImSendMessageBO;
import com.tydic.nicc.common.bo.im.core.ImSessionUserChangeBO;
import com.tydic.nicc.common.bo.im.core.LastMessageReqBO;
import com.tydic.nicc.common.bo.session.*;
import com.tydic.nicc.common.bo.tio.TioMessageBO;
import com.tydic.nicc.common.bo.tio.tools.UserTioContext;
import com.tydic.nicc.common.msg.ImMessage;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import org.apache.dubbo.common.serialize.support.SerializationOptimizer;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> <br>
 * @Description: 指定dubbo序列化类  <br>
 * @date 2021/6/17 3:27 下午  <br>
 * @Copyright tydic.com
 */
public class SerializationOptimizerImpl implements SerializationOptimizer {
    @Override
    public Collection<Class<?>> getSerializableClasses() {
        List<Class<?>> classes = new LinkedList<>();
        //im模块
        classes.add(Rsp.class);
        classes.add(RspList.class);
        classes.add(ImGroupInfoBO.class);
        classes.add(ImMessageHistoryBO.class);
        classes.add(ImC2bMessageHistoryReqBO.class);
        classes.add(MessageQueryReqBO.class);
        classes.add(CountUserNoReadReqBO.class);
        classes.add(UserNoReadContext.class);
        classes.add(ImSessionUserChangeBO.class);
        classes.add(ImMessage.class);
        classes.add(ImSendMessageBO.class);
        classes.add(LastMessageReqBO.class);
        classes.add(UserTioContext.class);
        classes.add(TioMessageBO.class);

        //会话中心
        classes.add(GetUserActiveSessionReqBO.class);
        classes.add(CreateNewSessionReqBO.class);
        classes.add(CreateNewSessionRspBO.class);
        classes.add(ChangeSessionUserRspBO.class);
        classes.add(ChangeSessionUserReqBO.class);
        classes.add(CloseSessionReqBO.class);
        classes.add(UpdateUserSessionReqBO.class);
        classes.add(ChatSessionBO.class);
        classes.add(GetUserSessionListBO.class);
        classes.add(GetUserSessionListReqBO.class);
        return classes;
    }
}
