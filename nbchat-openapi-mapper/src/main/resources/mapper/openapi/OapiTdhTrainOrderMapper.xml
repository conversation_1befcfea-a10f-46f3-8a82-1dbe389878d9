<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.openapi.mapper.OapiTdhTrainOrderMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder">
        <id column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="obj_type" property="objType" jdbcType="CHAR"/>
        <result column="obj_id" property="objId" jdbcType="VARCHAR"/>
        <result column="obj_name" property="objName" jdbcType="VARCHAR"/>
        <result column="obj_url" property="objUrl" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="CHAR"/>
        <result column="is_cutout" property="isCutout" jdbcType="CHAR"/>
        <result column="source_url" property="sourceUrl" jdbcType="VARCHAR"/>
        <result column="voice_url" property="voiceUrl" jdbcType="VARCHAR"/>
        <result column="demo_url" property="demoUrl" jdbcType="VARCHAR"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="CHAR"/>
        <result column="pay_price" property="payPrice" jdbcType="INTEGER"/>
        <result column="train_order_id" property="trainTaskId" jdbcType="VARCHAR"/>
        <result column="train_status" property="trainStatus" jdbcType="CHAR"/>
        <result column="train_error" property="trainError" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="CHAR"/>
        <result column="order_desc" property="orderDesc" jdbcType="VARCHAR"/>
        <result column="progressing" property="progressing" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        order_id, user_id, tenant_code, obj_type, obj_id, obj_name,obj_url, gender, is_cutout, source_url,
        voice_url, demo_url, order_time, pay_type, pay_price, order_status, order_desc, update_time,
        train_task_id, train_status, train_error, progressing
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from oapi_tdh_train_order
        where order_id = #{orderId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from oapi_tdh_train_order
        where order_id = #{orderId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder">
        insert into oapi_tdh_train_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="objType != null">
                obj_type,
            </if>
            <if test="objId != null">
                obj_id,
            </if>
            <if test="objName != null">
                obj_name,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="isCutout != null">
                is_cutout,
            </if>
            <if test="sourceUrl != null">
                source_url,
            </if>
            <if test="voiceUrl != null">
                voice_url,
            </if>
            <if test="demoUrl != null">
                demo_url,
            </if>
            <if test="orderTime != null">
                order_time,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="payPrice != null">
                pay_price,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="orderDesc != null">
                order_desc,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="objType != null">
                #{objType,jdbcType=CHAR},
            </if>
            <if test="objId != null">
                #{objId,jdbcType=VARCHAR},
            </if>
            <if test="objName != null">
                #{objName,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=CHAR},
            </if>
            <if test="isCutout != null">
                #{isCutout,jdbcType=CHAR},
            </if>
            <if test="sourceUrl != null">
                #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="voiceUrl != null">
                #{voiceUrl,jdbcType=VARCHAR},
            </if>
            <if test="demoUrl != null">
                #{demoUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null">
                #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=CHAR},
            </if>
            <if test="payPrice != null">
                #{payPrice,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="orderDesc != null">
                #{orderDesc,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder">
        update oapi_tdh_train_order
        <set>
            <if test="objType != null">
                obj_type = #{objType,jdbcType=CHAR},
            </if>
            <if test="objId != null">
                obj_id = #{objId,jdbcType=VARCHAR},
            </if>
            <if test="objName != null">
                obj_name = #{objName,jdbcType=VARCHAR},
            </if>
            <if test="objUrl != null">
                obj_url = #{objUrl,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=CHAR},
            </if>
            <if test="isCutout != null">
                is_cutout = #{isCutout,jdbcType=CHAR},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="voiceUrl != null">
                voice_url = #{voiceUrl,jdbcType=VARCHAR},
            </if>
            <if test="demoUrl != null">
                demo_url = #{demoUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null">
                order_time = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=CHAR},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice,jdbcType=INTEGER},
            </if>
            <if test="trainTaskId != null">
                train_task_id = #{trainTaskId,jdbcType=VARCHAR},
            </if>
            <if test="trainStatus != null">
                train_status = #{trainStatus,jdbcType=CHAR},
            </if>
            <if test="trainError != null">
                train_error = #{trainError,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="orderDesc != null">
                order_desc = #{orderDesc,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="progressing != null">
                progressing = #{progressing,jdbcType=VARCHAR},
            </if>
        </set>
        where order_id = #{orderId,jdbcType=VARCHAR}
    </update>

    <select id="selectQueueOrder" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from oapi_tdh_train_order
        where train_status = '0'
        and order_status = 'q'
        order by order_time limit 1
    </select>
</mapper>