package com.tydic.nbchat.robot;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;


@Slf4j
@ComponentScan(basePackages = {"com.tydic"})
@MapperScan("com.tydic.nbchat.robot.mapper")
@EnableDubbo
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
public class NbChatRobotApplication {


    public static void main(String[] args) {
        SpringApplication.run(NbChatRobotApplication.class, args);
    }


}
