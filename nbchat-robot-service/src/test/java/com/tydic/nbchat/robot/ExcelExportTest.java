package com.tydic.nbchat.robot;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.tydic.nbchat.robot.core.util.excel.ResearchQaExcel;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class ExcelExportTest {

    public static void main(String[] args) {
        new ExcelExportTest().testExcel();
    }

    @Test
    public void testExcel(){
        List<ResearchQaExcel> excelList = Lists.newArrayList();
        ResearchQaExcel qaExcel = new ResearchQaExcel();
        qaExcel.setQuestion("你好");
        qaExcel.setAnswer("哈哈");
        excelList.add(qaExcel);
        String reportName = "报表";
        String fileName = reportName + "_" + DateTimeUtil.getTimeShortString(new Date(),DateTimeUtil.TIME_FORMAT_SHORT)+".xlsx";
        String filePath = "/Users/<USER>/Downloads" + File.separator + fileName;
        File target = new File(filePath);
        WriteWorkbook workbook = new WriteWorkbook();
        workbook.setExcelType(ExcelTypeEnum.XLSX);
        workbook.setFile(target);
        ExcelWriter excelWriter = new ExcelWriter(workbook);
        List<List<String>> head = new ArrayList<>();
        head.add(Arrays.asList("问题"));
        head.add(Arrays.asList( "答案"));
        WriteSheet writeSheet = EasyExcel.writerSheet(reportName).head(head).build();
        excelWriter.write(excelList, writeSheet);
        excelWriter.finish();
    }
}
