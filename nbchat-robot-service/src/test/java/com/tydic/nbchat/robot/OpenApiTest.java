package com.tydic.nbchat.robot;

import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingRequest;
import com.tydic.nbchat.robot.api.openai.embedding.EmbeddingResult;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class OpenApiTest {
    public static void main(String[] args) {

    }


    public static void testEmbeddings(){
        OpenAiService openAiService = new OpenAiService(new ServiceConfigProperties());
        List<String> input = Lists.newArrayList();
        input.add("今天下雨了");
        EmbeddingRequest request = EmbeddingRequest.builder().model("text-embedding-ada-002").input(input).build();
        EmbeddingResult result = openAiService.createEmbeddings(request);
        System.out.println(result.getData().get(0).getEmbedding().size());
        System.out.println(result);
    }


    @Test
    public void testOpenApi() {
        OpenAiService service = new OpenAiService("http://47.245.96.78:8082/",
                "***************************************************",
                "chatgpt","gpt-3.5-turbo-16k-06131");
        final List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user","你好"));
        StringBuffer buffer = new StringBuffer();
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
                .builder().model(service.getConfigProperties().getRobotModel()).messages(messages)
                .n(1).maxTokens(2000).logitBias(new HashMap<>()).build();
        //ChatCompletionResult ees = helper.getOpenAiService().createChatCompletion(chatCompletionRequest);
        //System.out.println(ees);
        service.streamChatCompletion(chatCompletionRequest).doOnError(e -> {
            e.printStackTrace();
        }).blockingForEach(obj -> {
            System.out.println(obj.toString());
            buffer.append(obj.getChoices().get(0).getMessage().getContent());
        });
        System.out.printf(buffer.toString());
        System.out.println("--");
    }
}
