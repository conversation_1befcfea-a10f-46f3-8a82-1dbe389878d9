package com.tydic.nbchat.robot;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.openai.completion.chat.*;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import io.reactivex.Flowable;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class UnicomAiTest {

    @Autowired
    private RobotAiHelperFactory aiHelperFactory;

    public static AiRobotChatRequest buildRequest(){
        AiRobotChatRequest request = new AiRobotChatRequest();
        RobotMessageRequest robotMessageRequest = new RobotMessageRequest();
        robotMessageRequest.setRobotType("unicom");
        robotMessageRequest.setText("你好");
        robotMessageRequest.setUserId("1");
        robotMessageRequest.setTenantCode("00000000");
        request.setMessageRequest(robotMessageRequest);
        ChatMessage msg = new ChatMessage("user","你好");
        ChatMessageBuilder chatMessageBuilder = ChatMessageBuilder.builder().success(true).build();
        chatMessageBuilder.appendMessage(msg);
        request.setMessageBuilder(chatMessageBuilder);
        return request;
    }

    @Test
    public void testChat() {
        ChatCompletionResult result = aiHelperFactory.chat(buildRequest());
        System.out.println(result);
    }

    @Test
    public void testStreamChat(){
        Flowable<ChatCompletionChunk> results = aiHelperFactory.streamChat(buildRequest());
        results.blockingForEach(System.out::println);
    }

}
