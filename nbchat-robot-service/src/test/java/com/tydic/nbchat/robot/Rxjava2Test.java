package com.tydic.nbchat.robot;

import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Rxjava2Test {
    public static void main(String[] args) {
        new Rxjava2Test().streamChat().doOnError(throwable -> {
            log.error(" --------- {}",throwable.getMessage());
        }).forEach(chunk -> {
            log.info("{}",chunk);
        });
    }

    public Flowable<ChatCompletionChunk> streamChat() {
        Flowable<ChatCompletionChunk> flowable = Flowable.fromPublisher(subscriber -> {
            int i = 1;
            while (true) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                ChatCompletionChunk chunk = new ChatCompletionChunk();
                chunk.setId(String.valueOf(i ++ ));
                subscriber.onNext(chunk);
                if(i == 20){
                    subscriber.onComplete();
                    break;
                }
                if(i == 10){
                    subscriber.onError(new NumberFormatException("错误异常！"));
                    break;
                }
            }
        });
        return flowable;
    }

}
