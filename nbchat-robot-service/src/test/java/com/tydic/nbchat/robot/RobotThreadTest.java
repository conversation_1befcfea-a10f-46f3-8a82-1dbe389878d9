package com.tydic.nbchat.robot;

import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;

import java.util.ArrayList;
import java.util.List;

public class RobotThreadTest {
    public static ServiceConfigProperties buildConfig() {
        ServiceConfigProperties properties = new ServiceConfigProperties();
        properties.setTokenKey("abcd");
        properties.setRobotType("fastchat_qwen1.5-14b-chat");
        properties.setRobotModel("dibot-14-chat");
        properties.setApiUrl("http://172.168.1.247:8200/");
        return properties;
    }

    public static void chat(int id) {
        ServiceConfigProperties properties = buildConfig();
        OpenAiService service = new OpenAiService(properties);
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage user = new ChatMessage(ChatMessageRole.USER.value(), "鲁迅为什么打周树人？");
        messages.add(user);
        long start = System.currentTimeMillis();
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
                .builder().messages(messages).model("dibot-14-chat").build();
        ChatCompletionResult result = service.createChatCompletion(chatCompletionRequest);
        long end = System.currentTimeMillis();
        System.out.println(id + "," + (end-start)/1000 +"----->" + result.toString());
    }

    public static void main(String[] args) {
        //测试多线程
        for (int i = 0; i < 50; i++) {
            int finalI = i;
            new Thread(() -> chat(finalI)).start();
        }
    }
}
