package com.tydic.nbchat.robot;

import com.alibaba.fastjson.JSONArray;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.openai.completion.chat.*;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
@Slf4j
@SpringBootTest
public class ZpAiTest {

    @Autowired
    private RobotAiHelperFactory robotAiHelperFactory;

    public static void main(String[] args) {
        ServiceConfigProperties properties = new ServiceConfigProperties();
        properties.setTokenKey("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsInNpZ25fdHlwZSI6IlNJR04ifQ.eyJhcGlfa2V5IjoiODcxZWE4MjFlNGUyZDVhNWFlZWZlNjRkNDU3ZWQ0NWQiLCJpc3MiOiI4NzFlYTgyMWU0ZTJkNWE1YWVlZmU2NGQ0NTdlZDQ1ZCIsImV4cCI6MTcwODY1NTM1MCwiaWF0IjoxNzA4Mzk2MTUwLCJ0aW1lc3RhbXAiOjE3MDgzOTYxNTAzOTV9.fzedx-quTnZHJyd1agRZ4QKICAf_uq3BAsTAsD1-fFA");
        properties.setApiUrl("https://open.bigmodel.cn/");
        OpenAiService service = new OpenAiService(properties);
        final List<ChatMessage> messages = new ArrayList<>();
        JSONArray content = JSONArray.parseArray("[\n" +
                "      {\n" +
                "        \"type\": \"text\",\n" +
                "        \"text\": \"解释一下图中的现象\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"image_url\",\n" +
                "        \"image_url\": {\n" +
                "          \"url\" : \"https://img1.baidu.com/it/u=4230641656,1252759912&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500\"\n" +
                "        }\n" +
                "      }\n" +
                "    ]");
        String msg = "你好啊，今天是星期几？";
        final ChatMessage user = new ChatMessage(ChatMessageRole.USER.value(), content);
        messages.add(user);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
                .builder().model("glm-4v").messages(messages).build();

        //开启多线程测试
        ChatCompletionResult result = service.createZpChatCompletion(chatCompletionRequest);
        System.out.println(result.toString());
    }

    private AiRobotChatRequest buildRequest(String message){
        RobotMessageRequest messageRequest = new RobotMessageRequest();
        messageRequest.setUserId("1");
        messageRequest.setRobotType(RobotType.ZP_AI.getCode());
        messageRequest.setText(message);
        final List<ChatMessage> messages = new ArrayList<>();
        //final ChatMessage system = new ChatMessage(ChatMessageRole.SYSTEM.value(), "你是AI小助手,名为'迪问'");
        final ChatMessage user = new ChatMessage(ChatMessageRole.USER.value(), message);
        //messages.add(system);
        messages.add(user);
        ChatMessageBuilder messageBuilder = ChatMessageBuilder.builder().messages(messages).build();
        return AiRobotChatRequest.builder().messageRequest(messageRequest).messageBuilder(messageBuilder).build();
    }

    @Test
    public void testChat(){
        ChatCompletionResult result = robotAiHelperFactory.chat(buildRequest("你好啊"));
        System.out.println(result.toString());
    }

    @Test
    public void testChatStream(){
        String msg = "def generate_token(apikey: str, exp_seconds: int):\n" +
                "    try:\n" +
                "        id, secret = apikey.split(\".\")\n" +
                "    except Exception as e:\n" +
                "        raise Exception(\"invalid apikey\", e)\n" +
                "\n" +
                "    payload = {\n" +
                "        \"api_key\": id,\n" +
                "        \"exp\": int(round(time.time() * 1000)) + exp_seconds * 1000,\n" +
                "        \"timestamp\": int(round(time.time() * 1000)),\n" +
                "    }\n" +
                "    return jwt.encode(\n" +
                "        payload,\n" +
                "        secret,\n" +
                "        algorithm=\"HS256\",\n" +
                "        headers={\"alg\": \"HS256\", \"sign_type\": \"SIGN\"}\n" +
                "    )\n" +
                "这段代码帮我转换为java重写一下";
        Flowable<ChatCompletionChunk> result = robotAiHelperFactory.streamChat(buildRequest(msg));
        StringBuffer res = new StringBuffer("");
        result.doOnError(throwable -> {}).blockingForEach(obj->{
            log.info("{}",obj);
            res.append(obj.getChoices().get(0).getMessage().getContent());
        });
        System.out.println(res.toString());
    }

}
