package com.tydic.nbchat.robot;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class PDFChapterExtractor extends PDFTextStripper {

    private List<String> chapters = new ArrayList<String>();
    private StringBuilder buffer = new StringBuilder();
    private int currentPage = 0;
    private int lastYPosition = -1;
    private int lastChapterPageNumber = -1;

    public PDFChapterExtractor() throws IOException {
        super();
        super.setWordSeparator(" ");
    }

    public String getText(PDDocument doc) throws IOException {
        return super.getText(doc);
    }

    @Override
    protected void processTextPosition(TextPosition text) {
        int pageNumber = getCurrentPageNo();
        if (pageNumber != currentPage) {
            chapterTitle();
        }
        int yPosition = (int) text.getTextMatrix().getTranslateY();
        if (yPosition < lastYPosition) {
            if (pageNumber == lastChapterPageNumber) {
                chapterTitle();
            }
        }
        lastYPosition = yPosition;
        super.processTextPosition(text);
    }

    private void chapterTitle() {
        String text = buffer.toString();
        text = text.trim();
        if (text.length() > 0) {
            chapters.add(text);
            System.out.println(text);
            lastChapterPageNumber = getCurrentPageNo();
        }
        buffer = new StringBuilder();
    }

    public List<String> extractChapters(File file) throws IOException {
        try (PDDocument document = PDDocument.load(file)) {
            if (document.isEncrypted()) {
                document.setAllSecurityToBeRemoved(true);
            }
            int page = document.getNumberOfPages();
            super.setStartPage(1);
            super.setEndPage(page);
            System.out.println(page);
            PDPageTree pages = document.getPages();
            getText(document);
        }
        return chapters;
    }

    public static void main(String[] args) throws IOException {
        PDFChapterExtractor chapterExtractor = new PDFChapterExtractor();
        File file = new File("/Users/<USER>/Downloads/Java开发手册（嵩山版）.pdf");
        List<String> chapters = chapterExtractor.extractChapters(file);
        for (String chapter : chapters) {
            System.out.println(chapter);
        }
    }
}
