package com.tydic.nicc.common.eums.im;


/**
 * <AUTHOR> <br>
 * @Description: 消息类型定义  <br>
 * @date 2021/4/26 2:09 下午  <br>
 * @Copyright tydic.com
 */
public enum MsgFormType {

    DEFINE_YOU("0", "自定义消息"),
    TEXT("1", "文本"),
    VOICE("2", "语音"),
    JSON("3", "json"),
    IMAGE("4", "图片"),
    LINK_URL("5", "超链接"),
    HTML("6", "html"),
    FILE("7", "文件"),
    TRANS_QUEUE("8", "转接排队"),
    VIDEO("10", "视频消息"),
    TEXT_SENSITIVE("11", "敏感词文本"),
    INVITE_VIDEO_CARD("12", "视频邀请卡片"),
    CARD_MSG("13", "卡片消息"),
    MEDIA_MSG("14", "富媒体消息"),
    NOTICE_MSG("15", "通知类消息");

    private String code;
    private String name;


    public String getCode() {
        return code;
    }

    public Short getShortCode() {
        return Short.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private MsgFormType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (MsgFormType field : MsgFormType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
