package com.tydic.nicc.common.bo.im.admin;

import com.tydic.nicc.common.eums.im.MsgChatType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname MessageQueryReqBO
 * @Description MessageQueryReqBO
 * @Date 2021/5/13 2:18 下午
 * @Created by kangkang
 */
@Data
public class MessageQueryReqBO  implements Serializable {

    private String tenantCode;
    public String chatType = MsgChatType.C2B.getCode();
    private String chatKey;
    private String msgId;
    private String sessionId;
    private String userId;
    //聊天对象
    private String chatObjId;
    //按消息时间查询
    private Date startTime;

}
