package com.tydic.nicc.common.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <br>
 * @Description: msgType = 0 回执消息  <br>
 * @date 2021/4/26 2:26 下午  <br>
 * @Copyright tydic.com
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ImReceiptMessageBody extends ImMessageBody {
    // 0 正常
    private String code;
    // 已收到|已读
    private String status;
    // 业务消息id
    private String msgId;
    // 已读的消息类型
    private String msgType;
}
