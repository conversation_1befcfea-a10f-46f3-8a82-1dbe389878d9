package com.tydic.nicc.common.bo.user;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: UserOnlineReqBO  <br>
 * @date 2021/4/26 8:03 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class UserConnectReqBO implements Serializable {
    private Long connectId;
    private String authRsp;
    private String tenantCode;
    private String sceneCode;
    private String channelCode;
    private String userId;
    private String userType;
    private String userAgent;
    private String userIp;
    private Date connectTime;
    private String referer;
    private String connectParam;
    private String userBindIpPort;
}
