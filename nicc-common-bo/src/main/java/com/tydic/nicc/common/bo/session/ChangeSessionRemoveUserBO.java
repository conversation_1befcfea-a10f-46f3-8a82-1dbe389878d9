package com.tydic.nicc.common.bo.session;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 变更会话成员  <br>
 * @date 2021/4/26 6:20 下午  <br>
 * @Copyright tydic.com
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChangeSessionRemoveUserBO implements Serializable {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 0：会话超时；1：客服关闭；99：其它
     */
    private String exitType;
}
