package com.tydic.nicc.common.bo.event.trigger;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <br>
 * @Description: 自动应答触发  <br>
 * @date 2021/5/10 2:13 下午  <br>
 * @Copyright tydic.com
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AutoAnswerTrigger extends EventTriggerData {
    //发信人
    private String fromNo;
    //收信人
    private String toNo;
    //匹配到关键词所属编码
    private String keywordsNo;

    public AutoAnswerTrigger(String chatType,String chatKey){
        super(chatType,chatKey);
    }
}
