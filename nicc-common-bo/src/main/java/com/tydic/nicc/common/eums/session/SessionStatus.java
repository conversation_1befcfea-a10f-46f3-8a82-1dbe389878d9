package com.tydic.nicc.common.eums.session;

public enum SessionStatus {

    CLOSED("1", "已关闭"),
    OPENING("0", "会话中");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public Short getShortCode() {
        return Short.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private SessionStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (SessionStatus field : SessionStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
