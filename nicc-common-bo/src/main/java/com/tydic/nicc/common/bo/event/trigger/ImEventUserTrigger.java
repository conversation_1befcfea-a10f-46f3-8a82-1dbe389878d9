package com.tydic.nicc.common.bo.event.trigger;

import com.tydic.nicc.common.msg.event.EventMsgContent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Classname ImEventTrigger
 * @Description 来自im 通过用户来触发的
 * @Date 2021/5/25 3:04 下午
 * @Created by kangkang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImEventUserTrigger extends EventTriggerData {
    /**
     * USER_STATE_CHANGE 用户状态变更，
     * SESSION_MEMBER_CHANGE 会话成员变更，
     * SESSION_CLOSE 本次聊天会话结束，
     * MSG_READ  已读消息
     */
    private String eventType;
    /**
     * 事件触发时间
     */
    private Long eventTime;
    /**
     * 消息内容
     */
    private EventMsgContent eventContent;

    public ImEventUserTrigger(String chatType,String chatKey){
        super(chatType,chatKey);
    }
}
