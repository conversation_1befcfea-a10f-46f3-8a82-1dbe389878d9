package com.tydic.nicc.common.bo.csm;

import com.tydic.nicc.common.bo.session.ChangeSessionUserRspBO;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 分配返回参数  <br>
 * @date 2021/6/15 4:58 下午  <br>
 * @Copyright tydic.com
 */
@Builder
@Data
public class CsmAllotRspContext implements Serializable {
    //分配类型
    private String allotJoinType;

    //分配结果
    private RouteAllotRsp allotRsp;

    //排队信息
    private CsmUserQueueContext queueContext;

    //会话成员信息
    private ChangeSessionUserRspBO changeSessionUsers;
}
