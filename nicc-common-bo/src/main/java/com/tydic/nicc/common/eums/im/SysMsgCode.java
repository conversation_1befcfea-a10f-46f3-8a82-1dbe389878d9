package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: 系统消息编码  <br>
 * @date 2021/6/21 3:50 下午  <br>
 * @Copyright tydic.com
 */
public enum SysMsgCode {

    SKILL_CHOOSE("SKILL_CHOOSE", "技能组选择"),
    USER_QUEUE("USER_QUEUE", "用户排队消息"),
    FORCE_OFFLINE("FORCE_OFFLINE", "强制下线"),
    USER_EVALUATION_NOTICE("USER_EVALUATION_NOTICE","用户评价完成提示客服"),
    RECALL_MSG_NOTICE("RECALL_MSG_NOTICE", "消息撤回提醒"),
    CSM_ALLOT("CSM_ALLOT", "客服分配成功提示");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private SysMsgCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (SysMsgCode field : SysMsgCode.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
