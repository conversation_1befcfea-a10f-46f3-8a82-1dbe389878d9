package com.tydic.nicc.common.bo.event.trigger;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: EventTriggerBaseData  <br>
 * @date 2021/5/10 2:10 下午  <br>
 * @Copyright tydic.com
 */
@NoArgsConstructor
@Data
public class EventTriggerData implements Serializable {

    private String chatType;
    private String chatKey;

    public EventTriggerData(String chatType,String chatKey){
        this.chatType = chatType;
        this.chatKey = chatKey;
    }
}
