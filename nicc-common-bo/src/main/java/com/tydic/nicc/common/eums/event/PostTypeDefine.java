package com.tydic.nicc.common.eums.event;

/**
 * <AUTHOR> <br>
 * @Description: PostTypeDefine  <br>
 * @date 2021/4/26 2:09 下午  <br>
 * @Copyright tydic.com
 */
public enum PostTypeDefine {

    POST_IM("im", "推送给im"),
    POST_REST_API("rest", "推送给rest接口"),
    POST_DUBBO_API("dubbo", "推送给dubbo接口"),
    POST_LISTENER("listener", "提交给自定义的监听器");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private PostTypeDefine(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (PostTypeDefine field : PostTypeDefine.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
