package com.tydic.nicc.common.bo.im.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ImMessageEsDocument implements Serializable {

    private String chatKey;

    private String c2cKey;

    private String tenantCode;

    private String channelCode;

    private String msgId;

    private Short msgType;

    private String sessionId;

    private Short msgForm;

    @JsonFormat(pattern ="yyyy-MM-dd'T'HH:mm:ss.SSS",timezone ="GMT+8")
    private Date msgTime;

    private String fromNo;
    private Short fromType;

    private String toNo;
    private Short toType;

    private String csPhone;
    private String csName;
    private String csNick;
    private String userName;
    private String userNick;
    private String userPhone;
    //消息来源
    private String fromSource;
    //用户分类
    private String userSource;
    //分组
    private String group;
    //ip来源
    private String fromIp;
    //消息标识-备用
    private String msgTag;
    //省份地市
    private String province;
    private String city;
    //是否留言
    private String msgReceipt;
    //消息状态
    private Short msgStatus;
    //消息内容
    private String msgContent;
}
