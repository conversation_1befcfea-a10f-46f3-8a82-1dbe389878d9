package com.tydic.nicc.common.eums;

/**
 * @Classname SystemRoleType
 * @Description 系统角色定义
 * @Date 2021/11/17 10:48 上午
 * @Created by kangkang
 */
public enum SystemRoleType {

    ADMIN_PLATFORM("admin_platform", "平台管理员"),
    ADMIN_TENANT("admin_tenant", "租户管理员"),
    CS_ONLINE("cs_online", "在线客服"),
    CS_HOTLINE("cs_hotline", "热线客服");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private SystemRoleType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (SystemRoleType field : SystemRoleType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
