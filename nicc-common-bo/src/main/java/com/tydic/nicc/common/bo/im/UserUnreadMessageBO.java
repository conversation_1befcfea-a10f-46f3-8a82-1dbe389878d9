package com.tydic.nicc.common.bo.im;

import com.tydic.nicc.common.bo.user.UserAuthInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname NoReadMsg
 * @Description 用户未读消息
 * @Date 2021/6/9 5:08 下午
 * @Created by kangkang
 */
@Data
public class UserUnreadMessageBO implements Serializable {

    //chatKey
    private String chatKey;
    //所属会话
    private String sessionId;
    //消息id
    private String msgId;
    //消息时间
    private Long msgTime;
    //聊天类型 C2B|C2C|GROUP
    private String chatType;
    //消息发送人
    private String fromNo;
    //消息接收人
    private String toNo;
    //消息体格式
    private String msgForm;
    //消息内容
    private String msgContent;
    //未读数量
    private Integer unReadCount;
    //消息归属用户
    private UserAuthInfo fromUser;
}
