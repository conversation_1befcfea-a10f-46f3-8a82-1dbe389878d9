package com.tydic.nicc.common.eums;

/**
 * <AUTHOR> <br>
 * @Description: AutoAnswerTypeField  <br>
 * @date 2021/4/26 2:09 下午  <br>
 * @Copyright tydic.com
 */
public enum AutoAnswerTypeField {

    DUBBO("dubbo", "dubbo调用"),
    MQ("mq", "mq消息");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private AutoAnswerTypeField(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (AutoAnswerTypeField field : AutoAnswerTypeField.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
