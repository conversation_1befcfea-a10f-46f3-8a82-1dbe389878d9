package com.tydic.nicc.common.bo.im.admin;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: ImMsgSessionBO  <br>
 * @date 2021/4/29 5:16 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImMsgSessionBO implements Serializable {
    private String sessionId;

    private String fromNo;

    private String toNo;

    private String chatKey;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String lastMsgId;

    private Integer msgCount;

    private String tenantCode;

    private String channelCode;

    private String sceneCode;

    private String chatType;

    private Integer sessionStatus;
}
