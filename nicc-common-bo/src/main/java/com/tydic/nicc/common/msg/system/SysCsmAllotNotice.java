package com.tydic.nicc.common.msg.system;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 通知用户非工作时间  <br>
 * @date 2021/6/18 6:25 下午  <br>
 * @Copyright tydic.com
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SysCsmAllotNotice implements Serializable {
    //用户id
    private String userId;
    //客服id
    private String csId;
    //提示信息
    private String content;
}
