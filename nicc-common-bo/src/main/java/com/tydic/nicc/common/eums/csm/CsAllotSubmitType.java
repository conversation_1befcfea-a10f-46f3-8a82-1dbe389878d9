package com.tydic.nicc.common.eums.csm;

/**
 * <AUTHOR> <br>
 * @Description: 客服分配发起类型 cs 指定客服 skill 指定技能组）  <br>
 * @date 2021/6/22 5:38 下午  <br>
 * @Copyright tydic.com
 */
public enum CsAllotSubmitType {

    CS("cs", "指定客服"),
    SKILL("skill", "指定技能组");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean matchCode(String code){
        return this.getCode().equals(code);
    }

    private CsAllotSubmitType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (CsAllotSubmitType field : CsAllotSubmitType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
