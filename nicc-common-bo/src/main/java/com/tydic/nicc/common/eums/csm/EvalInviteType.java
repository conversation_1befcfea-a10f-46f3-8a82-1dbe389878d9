package com.tydic.nicc.common.eums.csm;

/**
 * <AUTHOR> <br>
 * @Description: EvalInviteType
 * @date 2021/6/22 5:38 下午  <br>
 * @Copyright tydic.com
 */
public enum EvalInviteType {

    SYSTEM("0", "系统自动邀请"),
    CS("1", "客服主动邀请");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean matchCode(String code){
        return this.getCode().equals(code);
    }

    private EvalInviteType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (EvalInviteType field : EvalInviteType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
