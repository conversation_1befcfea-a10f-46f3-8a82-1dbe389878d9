package com.tydic.nicc.common.bo.session;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <br>
 * @Description: Serializable  <br>
 * @date 2021/4/29 8:07 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ChatSessionHistoryBO implements Serializable {

    private String sessionId;

    private String fromNo;

    private String toNo;

    private String chatKey;

    private Date beginTime;

    private Date endTime;

    private String lastMsgId;

    private Integer msgCount;

    private String tenantCode;

    private String channelCode;

    private String sceneCode;

    private String chatType;

    private Integer sessionStatus;

    private Integer closeType;

    private String operUser;

    private String fromType;
    private String toType;

}
