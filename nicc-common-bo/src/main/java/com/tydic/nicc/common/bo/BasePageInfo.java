package com.tydic.nicc.common.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Classname BaseInfo
 * @Description BasePageInfo
 * @Date 2021/6/9 10:42 上午
 * @Created by kangkang
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BasePageInfo extends BaseInfo {
    private static final long serialVersionUID = 9058324718862551654L;

    private int limit = 10;
    private int page = 1;

    public void setPage(int page) {
        if (page < 0) {
            page = 1;
        }
        this.page = page;
    }

    public void setLimit(int limit) {
        if (limit < 0) {
            limit = 99999;
        }
        this.limit = limit;
    }
}
