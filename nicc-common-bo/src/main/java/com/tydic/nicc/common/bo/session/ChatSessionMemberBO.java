package com.tydic.nicc.common.bo.session;

import com.tydic.nicc.common.bo.user.UserAuthInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname ChatSessionMemberBO
 * @Description 会话成员信息
 * @Date 2021/6/30 4:16 下午
 * @Created by kangkang
 */
@Data
public class ChatSessionMemberBO implements Serializable {
    private String userId;

    private Short userType;

    private Date addTime;

    private Short addType;

    private Date exitTime;

    private Short exitType;

    private Integer totalDuration;

    private Short status;

    private UserAuthInfo userAuthInfo;
}
