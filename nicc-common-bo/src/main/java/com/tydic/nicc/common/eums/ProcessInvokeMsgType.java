package com.tydic.nicc.common.eums;

/**
 * <AUTHOR> <br>
 * @Description: ProcessMsgFrom  <br>
 * @date 2021/5/11 3:07 下午  <br>
 * @Copyright tydic.com
 */
public enum ProcessInvokeMsgType {

    WS("ws", "websocket直连"),
    EVENT("event", "事件触发"),
    API("api", "api触发");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ProcessInvokeMsgType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ProcessInvokeMsgType field : ProcessInvokeMsgType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
