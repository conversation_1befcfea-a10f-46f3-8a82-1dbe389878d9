package com.tydic.nicc.common.bo.premission;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Classname RoleAccess
 * @Description 角色权限
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RequiresRole {
    //角色列表
    String[] value();
    Logical logical() default Logical.OR;
    PermissionType permissionType() default PermissionType.ACCESS;
}
