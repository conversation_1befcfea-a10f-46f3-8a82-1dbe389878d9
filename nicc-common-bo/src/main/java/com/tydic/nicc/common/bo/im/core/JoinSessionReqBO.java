package com.tydic.nicc.common.bo.im.core;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Classname JoinSessionReqBO
 * @Description 临时加入到会话
 * @Date 2021/7/29 10:56 上午
 * @Created by kangkang
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class JoinSessionReqBO extends BaseInfo implements Serializable {

    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    @ParamNotEmpty
    private String sessionId;
}
