package com.tydic.nicc.common.bo.robot;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Classname HitContentResult
 * @Description HitContentResult
 * @Date 2021/7/26 4:45 下午
 * @Created by kangkang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HitContentResult implements Serializable {
    private String qaType;
    private String answerMsgType;
    private String question;
    private String content;
}
