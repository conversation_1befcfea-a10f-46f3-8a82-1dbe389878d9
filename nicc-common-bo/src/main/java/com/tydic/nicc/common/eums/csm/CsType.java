package com.tydic.nicc.common.eums.csm;

/**
 * @Classname CsType
 * @Description
 * 客服类型:0：热线客服；1：在线客服；2：工单客服；
 * @Date 2021/6/4 5:47 下午
 * @Created by kangkang
 */
public enum CsType {

    CS_HOTLINE(0, "热线客服"),
    CS_ONLINE(1, "在线客服"),
    CS_WORK_ORDER(2, "工单客服");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public boolean matchCode(Integer code){
        return this.getCode().equals(code);
    }

    private CsType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (CsType field : CsType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
