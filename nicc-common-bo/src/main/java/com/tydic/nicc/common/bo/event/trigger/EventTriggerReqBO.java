package com.tydic.nicc.common.bo.event.trigger;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 事件触发入参  <br>
 * @date 2021/5/10 2:08 下午  <br>
 * @Copyright tydic.com
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventTriggerReqBO implements Serializable {
    private String tenantCode;
    private String channelCode;
    private String sceneCode;
    private String eventCode;
    private EventTriggerData triggerData;
}
