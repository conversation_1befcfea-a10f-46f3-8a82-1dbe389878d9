package com.tydic.nicc.common.bo.user;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserAccessLogBO implements Serializable {

    private String tenantCode;

    private String channelCode;

    private String accessId;

    private String userId;

    private String userType;

    private String ipAddr;

    private String region;

    private String city;

    private String lat;

    private String lon;

    private String sip;

    private String extInfo;

}