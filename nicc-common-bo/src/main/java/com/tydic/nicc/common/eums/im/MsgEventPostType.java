package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: 事件消息投递类型
 * @date 2021/4/26 2:09 下午  <br>
 * @Copyright tydic.com
 */
public enum MsgEventPostType {


    USER_POST("user", "用户投递"),
    SYSTEM_POST("system", "系统投递");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private MsgEventPostType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (MsgEventPostType field : MsgEventPostType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
