package com.tydic.nicc.common.msg;

import com.tydic.nicc.common.eums.CardMessageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 评价卡片消息  <br>
 * @date 2021/7/14 3:59 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class CardEvalMessageInfo extends CardMessageInfo implements Serializable {

    //会话id
    private String sessionId;
    //评价结果
    private CardEvalResult evalResult;

    public CardEvalMessageInfo(String sessionId,String content,CardEvalResult evalResult){
        super(CardMessageType.EVALUATION_INVITE,content);
        this.sessionId = sessionId;
        this.evalResult = evalResult;
    }

}
