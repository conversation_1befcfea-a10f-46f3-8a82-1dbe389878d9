package com.tydic.nicc.common.eums.session;

/**
 * @Classname SessionIsQueue
 * @Description 是否排队
 * @Date 2022/1/13 5:28 下午
 * @Created by kangkang
 */
public enum SessionIsQueue {

    YES(1, "已排队"),
    NO(0, "未排队");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private SessionIsQueue(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (SessionIsQueue field : SessionIsQueue.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
