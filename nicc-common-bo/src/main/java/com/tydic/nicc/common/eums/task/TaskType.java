package com.tydic.nicc.common.eums.task;


/**
 * @Classname TaskType
 * @Description TaskType
 * @Date 2021/7/6 2:45 下午
 * @Created by kangkang
 */
public enum TaskType {

    IM_MSG_NOREAD_COUNT("IM_MSG_NOREAD_COUNT", "用户未读消息统计");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private TaskType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (TaskType field : TaskType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
