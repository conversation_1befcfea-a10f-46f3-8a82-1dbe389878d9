package com.tydic.nicc.common.bo.csm;

import lombok.Builder;
import lombok.Data;

/**
 * @Classname SkillGroupRouteRsp
 * @Description 技能组路由参数
 * @Date 2021/6/3 10:29 上午
 * @Created by kangkang
 */
@Builder
@Data
public class SkillGroupRouteRsp {
    //是是否阻止分配
    private boolean allotDeny;
    //是否匹配
    private boolean match;
    //技能组id
    private String skillGid;
    //技能组类型
    private Integer skillType;
    //技能组指定策略-可为空
    private String allotStrategy;
}
