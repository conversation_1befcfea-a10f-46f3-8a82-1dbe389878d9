package com.tydic.nicc.common.nbchat.msg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/29 15:07
 * @description 发放证书
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiCourseDegreeMsgContext extends MsgCommonInfo {
    private static final long serialVersionUID = 3313509643521599971L;

    /**
     * 表示与消息上下文关联的事件类型。此字段用于标识和分类触发此消息对象创建的特定事件。该值通常对应于系统内预定义的一组事件类型，允许区分各种动作或事件。
     */
    private String eventType;
    /**
     * 表示与消息上下文关联的事件的时间戳。此字段捕获事件发生的日期和时间，为正在处理或记录的事件提供时间参考。该值通常用于根据事件的发生时间跟踪、排序或过滤事件。
     */
    private Date eventTime;
    /**
     * 证书模版ID
     */
    private Integer degreeId;
    /**
     * 发证类型(考试发证/课程发证/学分达标发证/手动发证等)
     */
    private String grantType;
    /**
     * 证书来源(考试/课程/学分/管理台等)
     */
    private String source;
    /**
     * 业务名称
     */
    private String busiName;
    /**
     * 业务ID(考试 id/课程 id/培训 id)
     */
    private String busiId;
    /**
     * 证书展示的业务值(学分/成绩等)
     */
    private String busiValue;
    /**
     * 学分值
     */
    private Integer creditValue;
    /**
     * 考试成绩
     */
    private Float testScore;
    /**
     * 包含与消息上下文关联的其他参数的映射。此字段用作可扩展容器，用于存储补充信息，这些信息可能因特定用例或事件类型而异。键表示参数名称，而值保存相应的数据，允许灵活和动态地包含元数据或上下文细节。
     */
    private Map<String, Object> extParams;
}
