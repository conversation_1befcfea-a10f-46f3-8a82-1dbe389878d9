package com.tydic.nicc.common.eums.csm;

/**
 * <AUTHOR> <br>
 * @Description: 配置级别，标识该配置适用哪个维度 0:租户|1:渠道类型|2:渠道|3:坐席类型|4:技能组|5:坐席  <br>
 * @date 2021/6/25 2:45 下午  <br>
 * @Copyright tydic.com
 */
public enum CsWorkScheduleLevel {

    TENANT("0", "租户"),
    CHANNEL_TYPE("1", "渠道类型"),
    CHANNEL("2", "渠道"),
    CS_TYPE("3", "坐席类型"),
    SKILL_GROUP("4", "技能组"),
    CS("5", "坐席");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean matchCode(String code){
        return this.getCode().equals(code);
    }

    private CsWorkScheduleLevel(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (CsWorkScheduleLevel field : CsWorkScheduleLevel.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
