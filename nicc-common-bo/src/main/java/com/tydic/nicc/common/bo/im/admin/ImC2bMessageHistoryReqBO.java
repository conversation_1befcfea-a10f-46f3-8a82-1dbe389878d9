package com.tydic.nicc.common.bo.im.admin;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.eums.im.MsgChatType;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: c2b消息历史记录-入参  <br>
 * @date 2021/4/29 4:19 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImC2bMessageHistoryReqBO extends BaseInfo implements Serializable {
   /**
    * fromNo 必须传查询方自己
    */
   @ParamNotEmpty
   private String fromNo;
   /**
    * toNo 可以是具体聊天人 或者租户编码
    */
   @ParamNotEmpty
   private String toNo;
   /**
    * 租户编码必填
    */
   @ParamNotEmpty
   private String tenantCode;
   //聊天类型
   private String chatType = MsgChatType.C2B.getCode();
   //查询动作 默认查询 / init-拉取初始消息 / new-拉取最新消息
   private String action;
   /**
    * 查询人身份
    */
   private String fromType;
   /**
    * 以lastMsgId拉取
    */
   private String lastMsgId;
   /**
    * 消息时间戳
    */
   private String lastMsgTime;
   /**
    * 每次拉取条数,默认10
    */
   private Integer limit = 10;
   /**
    * 根据截至会话时间筛选
    */
   private Date lastSessionTime;
    /**
     * 根据时间条件查询,默认结束时间为当前
     */
   private Date startTime;
   private Date endTime = new Date();
}
