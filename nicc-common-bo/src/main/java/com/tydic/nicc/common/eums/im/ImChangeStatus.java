package com.tydic.nicc.common.eums.im;

public enum ImChangeStatus {

    OFFLINE("offline", "用户下线"),
    ONLINE("online", "用户上线");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public Short getShortCode() {
        return Short.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ImChangeStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ImChangeStatus field : ImChangeStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
