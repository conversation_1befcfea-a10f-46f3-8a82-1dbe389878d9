package com.tydic.nicc.common.bo.im.admin;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: 用户聊天列表查询  <br>
 * @date 2021/7/14 8:09 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImUserChatListQueryReqBO extends BasePageInfo implements Serializable {
    //用户id
    @ParamNotEmpty
    private String userId;
    @ParamNotEmpty
    private String tenantCode;
    //会话活动状态
    private Integer sessionStatus;
    //按最近活动时间查询
    private Date startTime;
    private Date endTime;
    //查询类型 0:手机号 1：姓名 2：聊天记录
    private String queryType;
    //查询关键字
    private String queryWords;
}
