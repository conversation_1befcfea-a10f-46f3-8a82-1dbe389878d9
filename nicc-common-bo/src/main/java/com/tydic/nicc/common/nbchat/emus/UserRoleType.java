package com.tydic.nicc.common.nbchat.emus;

import com.tydic.nicc.common.bo.user.SysRole;

import java.util.ArrayList;
import java.util.List;

public enum UserRoleType {
    /**
     * sysAdmin 系统管理员
     * tenantAdmin 租户管理员
     * orgAdmin	机构管理员
     * opUser 运营人员
     * user	普通用户
     */
    sysAdmin("sysAdmin", "系统管理员"),
    tenantAdmin("tenantAdmin", "租户管理员"),
    orgAdmin("orgAdmin", "机构管理员"),
    opUser("opUser", "运营人员"),
    user("user", "普通用户");

    private final String code;
    private final String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private UserRoleType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (UserRoleType field : UserRoleType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

    public static String getCodeByName(String name){
        for (UserRoleType field : UserRoleType.values()){
            if(field.name.equals(name)){
                return field.code;
            }
        }
        return "";
    }

    /**
     * 获取角色列表
     * @param role
     * @return
     */
    public static List<SysRole> getSysRoleList(String role){
        List<SysRole> sysRoleList = new ArrayList<>();
        if (role.equals(sysAdmin.getCode())){
            sysRoleList.add(getSysRole(tenantAdmin));
            sysRoleList.add(getSysRole(orgAdmin));
            sysRoleList.add(getSysRole(opUser));
            sysRoleList.add(getSysRole(user));
        } else if (role.equals(tenantAdmin.getCode())){
            sysRoleList.add(getSysRole(orgAdmin));
            sysRoleList.add(getSysRole(opUser));
            sysRoleList.add(getSysRole(user));
        } else if (role.equals(orgAdmin.getCode())){
            sysRoleList.add(getSysRole(orgAdmin));
            sysRoleList.add(getSysRole(opUser));
            sysRoleList.add(getSysRole(user));
        } else if (role.equals(opUser.getCode())){
            sysRoleList.add(getSysRole(user));
        }
        return sysRoleList;
    }

    private static SysRole getSysRole(UserRoleType roleType){
        return new SysRole(roleType.code, roleType.name);
    }
}
