package com.tydic.nicc.common.exception;

/**
 * @Classname EventHandleException
 * @Description EventHandleException
 * @Date 2021/6/9 3:39 下午
 * @Created by kangkang
 */
public class EventHandleException extends RuntimeException {

    /**
     * 错误编码
     */
    private String errorCode;

    public EventHandleException(String message) {
        super(message);
        this.errorCode = "1000";
    }

    public EventHandleException(String errorCode , String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode(){
        return this.errorCode;
    }
}
