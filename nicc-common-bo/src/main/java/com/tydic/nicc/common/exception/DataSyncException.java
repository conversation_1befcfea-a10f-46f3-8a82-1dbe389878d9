package com.tydic.nicc.common.exception;

/**
 * <AUTHOR> <br>
 * @Description: DataSyncException  <br>
 * @date 2021/9/13 7:56 下午  <br>
 * @Copyright tydic.com
 */
public class DataSyncException extends RuntimeException {
    /**
     * 错误编码
     */
    private String errorCode;

    public DataSyncException(String message) {
        super(message);
        this.errorCode = "2000";
    }

    public DataSyncException(String errorCode , String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode(){
        return this.errorCode;
    }
}