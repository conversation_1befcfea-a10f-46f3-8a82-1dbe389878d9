package com.tydic.nicc.common.msg.event;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 会话关闭  <br>
 * @date 2021/4/29 11:03 上午  <br>
 * @Copyright tydic.com
 */
@Data
public class EventSessionClose implements Serializable {
    //会话id
    private String sessionId;
    //关闭类型
    private String closeType;
    private String operUser;
    //加入类型
    private String joinType;
    //技能组
    private String skillGid;
    //是否排队: 0 否 1 是
    private int isQueue;
    //会话类型 1 一手会话 > 1 为转接会话
    private int sessionType;
}
