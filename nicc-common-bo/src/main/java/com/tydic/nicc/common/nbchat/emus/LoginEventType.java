package com.tydic.nicc.common.nbchat.emus;

public enum LoginEventType  {

    LOGIN("1", "登录"),
    LOGOUT("2", "登出");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private LoginEventType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (LoginEventType field : LoginEventType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
