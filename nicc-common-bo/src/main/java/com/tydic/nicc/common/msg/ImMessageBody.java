package com.tydic.nicc.common.msg;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: im消息定义
 * {
 * 	  msgId:'',//消息ID，客户端往服务段发送时，只需要通道内唯一即可
 *    msgType: '',//消息类型： 0:回执消息  1 通知类消息， 2 用户消息， 3 提示消息， 4 事件消息
 *    chatType: '',  // C2C  C2B  GROUP SYSTEM
 * 	  msgTime: timestamp,//消息事件，毫秒数，long
 *    fromNo: '', //当前消息发送人的userId
 *    fromInType: '', //发送方接入类型: 0 桌面网站，1 移动网站，2 app 网站，3 微信，4 微博，5 企业微信，6 微信小程序
 *    toNo: '', //接收方ID： 点对点时传接收方userid，群发消息为群id，C2B消息接收方为tenantCode
 * 	  sessionId:'',//非必填
 * 	  msgBody:{//根据不同消息类型，遵循不同消息协议
 *         msgForm: '',  // 0自定义消息，1 文本， 2 语音， 3 json， 4 图片， 5 超链接 , 6 html 7 文件 8 转接排队  10视频 11边聊边办  12视频邀请卡片
 *         msgContent: ''
 *     }
 * }
 * <br>
 * @date 2021/4/26 2:02 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImMessageBody implements Serializable {

}
