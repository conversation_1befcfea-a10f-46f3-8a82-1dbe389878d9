package com.tydic.nicc.common.bo.rocketmq;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ImSessionOutMsgContext implements Serializable {
    private String msgId;
    private Short msgType;
    private String sessionId;
    private Short msgForm;
    private Date msgTime;
    private String fromNo;
    private Short fromType;
    private String toNo;
    private String msgContent;
    private String nickName;
    private String userName;
    private String phone;
    private Short msgStatus;
    private String msgReceipt;
}
