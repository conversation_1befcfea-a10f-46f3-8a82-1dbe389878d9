package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: 用户状态  <br>
 * @date 2021/4/27 10:40 上午  <br>
 * @Copyright tydic.com
 */
public enum ImOnlineStatus {

    ONLINE("1", "在线"),
    OFFLINE("0", "离线");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ImOnlineStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (ImOnlineStatus field : ImOnlineStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
