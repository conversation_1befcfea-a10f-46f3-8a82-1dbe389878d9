package com.tydic.nicc.common.bo.session;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.common.eums.im.MsgChatType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <br>
 * @Description: 会话列表查询  <br>
 * @date 2021/4/26 6:24 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class GetUserSessionListReqBO extends BasePageInfo implements Serializable {
    private String channelCode;
    private String tenantCode;
    // 聊天类型
    private String chatType = MsgChatType.C2B.getCode();
    //用户编码
    private String userId;
    //开始时间
    private Date startTime;
    //结束时间
    private Date endTime;
    //用户分类
    private String userSource;
    //会话状态
    private Integer sessionStatus;
    //客服模式
    private boolean csModel = false;
    //查询类型 0:手机号 1：姓名 2：聊天记录
    private String queryType;
    //查询关键字
    private String queryWords;
    //chatObj
    private String chatObjId;

    @Override
    public String toString() {
        return "GetUserSessionListReqBO{" +
                "page='" + getPage() + '\'' +
                "limit='" + getLimit() + '\'' +
                "channelCode='" + channelCode + '\'' +
                ", tenantCode='" + tenantCode + '\'' +
                ", chatType='" + chatType + '\'' +
                ", userId='" + userId + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", userSource='" + userSource + '\'' +
                ", sessionStatus=" + sessionStatus +
                ", csModel=" + csModel +
                ", queryType='" + queryType + '\'' +
                ", queryWords='" + queryWords + '\'' +
                ", chatObjId='" + chatObjId + '\'' +
                '}';
    }
}
