package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: 回执消息  <br>
 * @date 2021/4/26 4:25 下午  <br>
 * @Copyright tydic.com
 */
public enum MsgReceiptDefine {

    /**
     * 0 回复客户端已收到
     * 1 客户端给服务端回执 可回复已读
     * 2 服务端收到消息，由于错误或敏感词校验等别拒绝投递
     */
    RECEIVED("0", "已收到"),
    CLIENT_READ_OK("1", "客户端发来已读"),
    REFUSED("2", "服务端拒绝");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private MsgReceiptDefine(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (MsgReceiptDefine field : MsgReceiptDefine.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
