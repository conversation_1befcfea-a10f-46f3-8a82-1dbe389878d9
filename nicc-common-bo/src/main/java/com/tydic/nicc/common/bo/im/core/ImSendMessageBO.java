package com.tydic.nicc.common.bo.im.core;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 发送消息参数  <br>
 * @date 2021/5/11 2:42 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImSendMessageBO implements Serializable {
    //触发类型
    private String invokeType = "api";
    //租户编码
    private String tenantCode;
    // 渠道编码
    private String channelCode;
    // 场景编码
    private String sceneCode;
    // 用户id
    private String fromNo;
    private String toNo;
    // 消息内容 json格式-参考 im消息格式规范
    @ParamNotEmpty(message = "消息内容不得为空!")
    private String message;
}
