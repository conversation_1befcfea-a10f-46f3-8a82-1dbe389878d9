package com.tydic.nicc.common.bo.csm;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 客服转接入参  <br>
 * @date 2021/6/21 10:42 上午  <br>
 * @Copyright tydic.com
 */
@Data
public class CsmTransferReqBO extends BaseInfo implements Serializable {

    //租户编码
    @ParamNotEmpty(message = "租户编码不得为空!")
    private String tenantCode;
    //当前客服id
    @ParamNotEmpty(message = "当前客服ID不得为空!")
    private String csId;
    //正在进行的会话
    @ParamNotEmpty(message = "当前会话ID不得为空!")
    private String sessionId;

    @ParamNotEmpty(message = "服务用户ID不得为空!")
    private String userId;
    //指定要转接的客服 - 客服ID
    private String allotCsId;
    //指定要转接的技能组 - 技能组ID
    private String allotSkillGid;
    //分配策略 - 可选
    private String allotStrategy;
    //分配类型 transfer（ 转接: 接入被邀请的客服,自己退出会话）| invite（邀请: 直接邀请指定客服加入,自己不退出）
    private String allotJoinType = "transfer";
}
