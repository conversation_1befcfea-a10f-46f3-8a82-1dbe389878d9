package com.tydic.nicc.common.msg.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <br>
 * @Description: 用户状态变更  <br>
 * @date 2021/4/29 11:03 上午  <br>
 * @Copyright tydic.com
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventUserStateChange {
    private String userId;
    //online = 1/offline = 0
    private String userStatus;
}
