package com.tydic.nicc.common.nbchat.msg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tydic.nicc.common.nbchat.emus.RebateInviteEnum;
import lombok.*;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:45
 * @description 邀请拉新消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RebateInviteMsgContext extends MsgCommonInfo {
    private static final long serialVersionUID = 4450747471547807431L;

    /**
     * 表示与消息上下文关联的事件类型。此字段用于标识和分类触发此消息对象创建的特定事件。该值通常对应于系统内预定义的一组事件类型，允许区分各种动作或事件。
     *
     * @see RebateInviteEnum
     */
    private String eventType;
    /**
     * 表示与消息上下文关联的事件的时间戳。此字段捕获事件发生的日期和时间，为正在处理或记录的事件提供时间参考。该值通常用于根据事件的发生时间跟踪、排序或过滤事件。
     */
    private Date eventTime;
    /**
     * 包含与消息上下文关联的其他参数的映射。此字段用作可扩展容器，用于存储补充信息，这些信息可能因特定用例或事件类型而异。键表示参数名称，而值保存相应的数据，允许灵活和动态地包含元数据或上下文细节。
     */
    private Map<String, Object> extParams;
}
