package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: VerifyErrCodeDefine  <br>
 * @date 2021/4/26 4:19 下午  <br>
 * @Copyright tydic.com
 */
public enum MsgVerifyCodeDefine {

    SUCCESS("0", "成功"),
    VERIFY_ERR("20", "校验错误"),
    TOO_LEN("21", "消息体太长"),
    ERR_FORMAT("22", "格式错误");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private MsgVerifyCodeDefine(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (MsgVerifyCodeDefine field : MsgVerifyCodeDefine.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
