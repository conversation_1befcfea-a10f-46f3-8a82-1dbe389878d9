package com.tydic.nbchat.pay.core.service.notify;


import com.tydic.nbchat.pay.core.busi.order.WxOrderService;
import com.tydic.nbchat.pay.core.helper.pay.WxPayHelper;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@SpringBootTest
class WxNotifyServiceImplTest {
    @Resource
    WxNotifyServiceImpl wxNotifyService;

    @Resource
    WxOrderService wxOrderService;

    @Resource
    WxPayHelper payHelper;

    @Test
    public void fun(){
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setOutTradeNo("451464249232535552");
        request.setMchid("1678975772");
        payHelper.orderQuery(request);
    }


    @Test
    public void PayTest() {

    }




    public static void main(String[] args) {
        Date date = DateTimeUtil.convertAsDate("2024-06-17T14:06:17+08:00", "yyyy-MM-dd'T'HH:mm:ss+08:00");
        System.out.println(date);
    }


}